import { BaseService } from '../../base/baseService';
import { API_DELAY, API_ACTIONS, LOG_PREFIXES, formatLogMessage } from './constants';

/**
 * 部门数据获取服务
 * 统一管理部门和人员数据的API调用，实现请求去重和缓存机制
 */
export class DataFetchService {
  private static instance: DataFetchService;
  private baseService: BaseService<any, any>;

  // 请求去重机制
  private pendingRequests: Map<string, Promise<any>> = new Map();
  
  private constructor(baseService: BaseService<any, any>) {
    this.baseService = baseService;
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(baseService?: BaseService<any, any>): DataFetchService {
    if (!DataFetchService.instance) {
      if (!baseService) {
        throw new Error('首次创建DataFetchService实例时必须提供baseService');
      }
      DataFetchService.instance = new DataFetchService(baseService);
    }
    return DataFetchService.instance;
  }
  
  /**
   * 获取部门数据（带请求去重）
   * @param forceRefresh 是否强制刷新，忽略正在进行的请求
   * @returns 部门数据
   */
  public async getDepartments(forceRefresh: boolean = false): Promise<{success: boolean, data: any[]}> {
    const requestKey = API_ACTIONS.GET_DEPARTMENT;

    // 如果不是强制刷新且有正在进行的请求，复用该请求
    if (!forceRefresh && this.pendingRequests.has(requestKey)) {
      console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '复用正在进行的部门数据请求'));
      return this.pendingRequests.get(requestKey)!;
    }

    console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '开始获取部门数据'));
    
    // 创建新的请求Promise
    const requestPromise = this.fetchDepartmentsFromAPI();
    
    // 保存到待处理请求中
    this.pendingRequests.set(requestKey, requestPromise);
    
    try {
      const result = await requestPromise;
      return result;
    } finally {
      // 请求完成后清除
      this.pendingRequests.delete(requestKey);
    }
  }
  
  /**
   * 获取人员数据（带请求去重）
   * @param forceRefresh 是否强制刷新，忽略正在进行的请求
   * @returns 人员数据
   */
  public async getPersons(forceRefresh: boolean = false): Promise<{success: boolean, data: any[]}> {
    const requestKey = API_ACTIONS.GET_PERSON;

    // 如果不是强制刷新且有正在进行的请求，复用该请求
    if (!forceRefresh && this.pendingRequests.has(requestKey)) {
      console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '复用正在进行的人员数据请求'));
      return this.pendingRequests.get(requestKey)!;
    }

    console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '开始获取人员数据'));
    
    // 创建新的请求Promise
    const requestPromise = this.fetchPersonsFromAPI();
    
    // 保存到待处理请求中
    this.pendingRequests.set(requestKey, requestPromise);
    
    try {
      const result = await requestPromise;
      return result;
    } finally {
      // 请求完成后清除
      this.pendingRequests.delete(requestKey);
    }
  }
  
  /**
   * 批量获取部门和人员数据（串行调用，避免并发问题）
   * @param forceRefresh 是否强制刷新
   * @returns 部门和人员数据
   */
  public async getDepartmentsAndPersons(forceRefresh: boolean = false): Promise<{
    departments: {success: boolean, data: any[]},
    persons: {success: boolean, data: any[]}
  }> {
    console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '开始批量获取部门和人员数据'));

    // 串行获取数据，避免并发问题
    const departments = await this.getDepartments(forceRefresh);

    // API调用间延迟
    await this.delay(API_DELAY);
    
    const persons = await this.getPersons(forceRefresh);
    
    return { departments, persons };
  }
  
  /**
   * 实际的部门数据API调用
   */
  private async fetchDepartmentsFromAPI(): Promise<{success: boolean, data: any[]}> {
    try {
      const result = await this.baseService.submitTask<{success: boolean, data: any[]}>('DbFun', {
        action: API_ACTIONS.GET_DEPARTMENT
      });

      console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, `部门数据获取成功，数量: ${result?.data?.length || 0}`));
      
      if (!result || !result.success) {
        console.warn(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '部门数据返回失败，使用空数据'));
        return { success: true, data: [] };
      }

      // 确保数据是数组
      if (!Array.isArray(result.data)) {
        console.warn(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '部门数据不是数组，使用空数组替代'));
        return { success: true, data: [] };
      }

      return result;
    } catch (error) {
      console.error(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '获取部门数据失败:'), error);
      return { success: true, data: [] };
    }
  }
  
  /**
   * 实际的人员数据API调用
   */
  private async fetchPersonsFromAPI(): Promise<{success: boolean, data: any[]}> {
    try {
      const result = await this.baseService.submitTask<{success: boolean, data: any[]}>('DbFun', {
        action: API_ACTIONS.GET_PERSON
      });

      console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, `人员数据获取成功，数量: ${result?.data?.length || 0}`));
      
      if (!result || !result.success) {
        console.warn(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '人员数据返回失败，使用空数据'));
        return { success: true, data: [] };
      }

      // 确保数据是数组
      if (!Array.isArray(result.data)) {
        console.warn(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '人员数据不是数组，使用空数组替代'));
        return { success: true, data: [] };
      }

      return result;
    } catch (error) {
      console.error(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '获取人员数据失败:'), error);
      return { success: true, data: [] };
    }
  }
  
  /**
   * 延迟工具方法
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 清除所有待处理的请求
   */
  public clearPendingRequests(): void {
    console.log(formatLogMessage(LOG_PREFIXES.DATA_FETCH_SERVICE, '清除所有待处理的请求'));
    this.pendingRequests.clear();
  }
  
  /**
   * 获取当前待处理请求的数量
   */
  public getPendingRequestsCount(): number {
    return this.pendingRequests.size;
  }
}
