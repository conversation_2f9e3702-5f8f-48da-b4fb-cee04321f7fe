import { useState, useEffect, useCallback } from 'react';
import InspectionSyncService, {
  InspectionSyncServiceState,
  InspectionDataItem,
  InspectionDataType
} from '../../services/Inspection/inspectionSyncService';

// 重新导出类型，这样UI层可以从Hook导入，而不是直接从Service层导入
export type { InspectionDataItem, InspectionDataType };

/**
 * 巡检同步Hook返回值接口
 */
interface UseInspectionSyncReturn {
  // 状态
  isLoading: boolean;
  error?: string;
  inspectionData: InspectionDataItem[];
  selectedItems: string[];
  isReading: boolean;
  readProgress: number;
  isSaving: boolean;
  saveProgress: number;
  isExporting: boolean;
  exportProgress: number;

  // 方法
  readInspectionData: () => Promise<InspectionDataItem[]>;
  importFromAndroidDevice: () => Promise<{ success: boolean; message: string; data?: any }>;
  saveSelectedData: () => Promise<{ success: boolean; message: string }>;
  deleteSelectedData: () => Promise<{ success: boolean; message: string }>;
  exportToAndroidDevice: () => Promise<{ success: boolean; message: string; data?: any }>;
  saveItem: (id: string) => Promise<{ success: boolean; message: string }>;
  deleteItem: (id: string) => Promise<{ success: boolean; message: string }>;
  toggleSelectItem: (id: string, selected?: boolean) => void;
  toggleSelectAll: (selected: boolean) => void;
  isItemSelected: (id: string) => boolean;
  getSelectedCount: () => number;

  // 辅助方法
  getNewItemsCount: () => number;
  getExceptionItemsCount: () => number;
  getSelectedNewItems: () => InspectionDataItem[];
  getSelectedExceptionItems: () => InspectionDataItem[];
}

/**
 * 巡检同步Hook
 * 提供巡检同步功能和状态管理
 */
export function useInspectionSync(): UseInspectionSyncReturn {
  // 获取服务实例
  const service = InspectionSyncService.getInstance();

  // 状态
  const [state, setState] = useState<InspectionSyncServiceState>(service.getState());

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: InspectionSyncServiceState) => {
      setState(newState);
    };

    // 订阅状态变化事件
    service.on('state-change', handleStateChange);

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
    };
  }, [service]);

  // 读取巡检数据
  const readInspectionData = useCallback(async () => {
    return await service.readInspectionData();
  }, [service]);

  // 保存选中的数据
  const saveSelectedData = useCallback(async () => {
    return await service.saveSelectedData();
  }, [service]);

  // 从Android设备导入数据
  const importFromAndroidDevice = useCallback(async () => {
    return await service.importFromAndroidDevice();
  }, [service]);

  // 导出到Android设备
  const exportToAndroidDevice = useCallback(async () => {
    return await service.exportToAndroidDevice();
  }, [service]);

  // 删除选中的数据
  const deleteSelectedData = useCallback(async () => {
    return await service.deleteSelectedData();
  }, [service]);

  // 保存单个数据项
  const saveItem = useCallback(async (id: string) => {
    return await service.saveItem(id);
  }, [service]);

  // 删除单个数据项
  const deleteItem = useCallback(async (id: string) => {
    return await service.deleteItem(id);
  }, [service]);

  // 选择/取消选择数据项
  const toggleSelectItem = useCallback((id: string, selected?: boolean) => {
    service.toggleSelectItem(id, selected);
  }, [service]);

  // 选择/取消选择所有数据项
  const toggleSelectAll = useCallback((selected: boolean) => {
    service.toggleSelectAll(selected);
  }, [service]);

  // 检查数据项是否被选中
  const isItemSelected = useCallback((id: string) => {
    return service.isItemSelected(id);
  }, [service]);

  // 获取选中的数据项数量
  const getSelectedCount = useCallback(() => {
    return service.getSelectedCount();
  }, [service]);

  // 获取新增数据项数量
  const getNewItemsCount = useCallback(() => {
    return state.inspectionData.filter(item => item.dataType === 'new').length;
  }, [state.inspectionData]);

  // 获取异常数据项数量
  const getExceptionItemsCount = useCallback(() => {
    return state.inspectionData.filter(item => item.dataType === 'exception').length;
  }, [state.inspectionData]);

  // 获取选中的新增数据项
  const getSelectedNewItems = useCallback(() => {
    return state.inspectionData.filter(
      item => item.dataType === 'new' && state.selectedItems.includes(item.id)
    );
  }, [state.inspectionData, state.selectedItems]);

  // 获取选中的异常数据项
  const getSelectedExceptionItems = useCallback(() => {
    return state.inspectionData.filter(
      item => item.dataType === 'exception' && state.selectedItems.includes(item.id)
    );
  }, [state.inspectionData, state.selectedItems]);

  return {
    // 状态
    isLoading: state.isLoading,
    error: state.error,
    inspectionData: state.inspectionData,
    selectedItems: state.selectedItems,
    isReading: state.isReading,
    readProgress: state.readProgress,
    isSaving: state.isSaving,
    saveProgress: state.saveProgress,
    isExporting: state.isExporting,
    exportProgress: state.exportProgress,

    // 方法
    readInspectionData,
    importFromAndroidDevice,
    saveSelectedData,
    deleteSelectedData,
    exportToAndroidDevice,
    saveItem,
    deleteItem,
    toggleSelectItem,
    toggleSelectAll,
    isItemSelected,
    getSelectedCount,

    // 辅助方法
    getNewItemsCount,
    getExceptionItemsCount,
    getSelectedNewItems,
    getSelectedExceptionItems
  };
}

export default useInspectionSync;
