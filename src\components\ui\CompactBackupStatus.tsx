import React from 'react';
import { GitBranch, Database } from 'lucide-react';
import { BackupData } from '../../services/Backup/backupService';

interface CompactBackupStatusProps {
  activeBackup: BackupData | null;
  formatFileSize: (bytes: number) => string;
  formatRelativeTime: (timestamp: number) => string;
}

/**
 * 紧凑的备份状态组件
 * 用于在底部状态栏显示当前活动备份信息
 */
export const CompactBackupStatus: React.FC<CompactBackupStatusProps> = ({
  activeBackup,
  formatFileSize,
  formatRelativeTime,
}) => {
  if (!activeBackup) {
    return (
      <div className="flex items-center space-x-2">
        <Database className="w-4 h-4 text-blue-600" />
        <span className="text-blue-600 font-medium text-sm">初始数据库</span>
        <span className="text-gray-500 text-xs">未设置活动备份</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <GitBranch className="w-4 h-4 text-green-600" />
      <span className="text-green-600 font-medium text-sm">
        活动版本 {activeBackup.backup_name || activeBackup.backup_filename}
      </span>
      <span className="text-gray-500 text-xs">
        ({formatRelativeTime(activeBackup.backup_timestamp)}, 大小: {formatFileSize(activeBackup.backup_size)})
      </span>
    </div>
  );
};
