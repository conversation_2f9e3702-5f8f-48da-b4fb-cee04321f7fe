/**
 * 注册服务类
 * Service层：只负责注册相关的数据操作
 */
class RegistrationService {
  private static instance: RegistrationService;

  // 注册地址配置
  private readonly REGISTRATION_URL = 'https://val.qq.com/act/a20230801download/index.html?adtag=2636';

  /**
   * 获取注册服务实例
   */
  public static getInstance(): RegistrationService {
    if (!RegistrationService.instance) {
      RegistrationService.instance = new RegistrationService();
    }
    return RegistrationService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 打开注册地址
   */
  public openRegistrationUrl(): void {
    console.log('打开注册地址:', this.REGISTRATION_URL);
    window.open(this.REGISTRATION_URL, '_blank');
  }

  /**
   * 获取注册地址
   */
  public getRegistrationUrl(): string {
    return this.REGISTRATION_URL;
  }
}

export default RegistrationService;
