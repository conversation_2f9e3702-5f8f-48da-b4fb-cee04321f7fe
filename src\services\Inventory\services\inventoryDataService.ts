import WebSocketManager from '../../../utils/websocket';
import TaskManager, { TaskInfo } from '../../../utils/taskManager';
import { InventoryItem, DeviceCategory } from '../../../types/inventory';
import { fetchCategoryTree } from '../fetchCategoryTree';
import ExtFieldService from '../extFieldService';
import GlobalConfigService from '../../globalConfigService';
import SystemInitService from '../../systemInitService';

/**
 * 库存数据服务
 * 专门负责数据的加载、转换和缓存管理
 */
export class InventoryDataService {
  private static instance: InventoryDataService;
  private ws: WebSocketManager;
  private task: TaskManager;
  private dllName = 'AccountTableDll';
  
  // 数据加载状态
  private isDbInitialized = false;
  private isLoadingInventory = false;
  private isEmptyDatabase = false;
  private _isEnsuringData = false;
  
  // 缓存相关
  private lastLoadTimestamp: number = 0;
  private readonly CACHE_DURATION: number = 5000; // 5秒缓存
  private cachedInventoryData: InventoryItem[] = []; // 本地缓存数据

  private constructor() {
    this.ws = WebSocketManager.getInstance();
    this.task = TaskManager.getInstance();
  }

  public static getInstance(): InventoryDataService {
    if (!InventoryDataService.instance) {
      InventoryDataService.instance = new InventoryDataService();
    }
    return InventoryDataService.instance;
  }

  /**
   * 获取数据库初始化状态
   */
  public getDbInitialized(): boolean {
    return this.isDbInitialized;
  }

  /**
   * 获取数据库是否为空
   */
  public getIsEmptyDatabase(): boolean {
    return this.isEmptyDatabase;
  }

  /**
   * 设置数据库为空状态
   */
  public setIsEmptyDatabase(isEmpty: boolean): void {
    this.isEmptyDatabase = isEmpty;
  }

  /**
   * 确保数据已加载 - 封装初始化和加载逻辑
   * @returns Promise<boolean> 数据加载是否成功
   */
  public async ensureDataLoaded(): Promise<boolean> {
    // 防止多次并发调用
    if (this._isEnsuringData) {
      // 等待当前加载完成
      let retryCount = 0;
      const maxRetries = 10;
      const retryInterval = 500; // 毫秒

      while (this._isEnsuringData && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, retryInterval));
        retryCount++;
      }

      // 如果数据库已初始化，返回成功
      return this.isDbInitialized;
    }

    this._isEnsuringData = true;
    let success = false;

    try {
      if (this.isDbInitialized) {
        success = true;
        return success;
      }

      try {
        // 确保WebSocket连接
        if (!this.ws.isConnected()) {
          await this.ws.connect();
        }

        // 子步骤间延迟时间（毫秒）
        const SUB_STEP_DELAY = 150;

        console.log('InventoryDataService: 步骤1 - 初始化数据库');
        // 步骤1: 初始化数据库
        await this.initializeDatabase();
        this.isDbInitialized = true;
        success = true;

        // 子步骤间延迟
        await new Promise(resolve => setTimeout(resolve, SUB_STEP_DELAY));

        console.log('InventoryDataService: 步骤2 - 加载全局配置');
        // 步骤2: 串行加载全局配置
        try {
          const globalConfigService = GlobalConfigService.getInstance();
          await globalConfigService.getGlobalConfig();
          console.log('InventoryDataService: 全局配置加载完成');
        } catch (configError) {
          console.warn('全局配置加载失败，继续后续步骤:', configError);
        }

        // 子步骤间延迟
        await new Promise(resolve => setTimeout(resolve, SUB_STEP_DELAY));

        console.log('InventoryDataService: 步骤3 - 加载所有扩展字段定义');
        // 步骤3: 串行加载所有扩展字段定义
        try {
          const extFieldService = ExtFieldService.getInstance();

          // 直接使用API获取扩展字段，而不是通过extFieldService
          const params = {
            action: "get_all_extended_fields"
          };

          // 提交任务并等待结果
          const result = await this.submitAndWaitTask<any>('DbFun', params);

          // 如果后端没有返回扩展字段定义，则返回空数组
          if (result && result.data) {
            const allExtFields = result.data;
            // 更新extFieldService中的数据
            extFieldService.updateAllExtFields(allExtFields);
            console.log('InventoryDataService: 扩展字段定义加载完成，共', allExtFields.length, '个分类');
          } else {
            console.log('InventoryDataService: 后端未返回扩展字段定义');
          }
        } catch (extFieldError) {
          console.error('加载所有扩展字段定义失败:', extFieldError);
          // 扩展字段加载失败不影响整体功能
        }

        return success;
      } catch (error) {
        console.error('数据加载失败:', error);
        success = false;
      }

      return success;
    } finally {
      this._isEnsuringData = false;
    }
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase(): Promise<void> {
    if (this.isDbInitialized) {
      return;
    }

    // 检查根节点验证状态
    try {
      // 使用静态导入的GlobalConfigService，避免动态导入
      if (GlobalConfigService.isRootNodeValidationFailed()) {
        console.log('公司名称验证失败，停止数据库初始化');
        throw new Error('公司名称验证失败，系统已停止所有API调用');
      }

      if (!GlobalConfigService.isRootNodeValidated()) {
        console.log('公司名称尚未验证通过，等待系统初始化完成');
        throw new Error('公司名称尚未验证通过，请等待系统初始化完成');
      }
    } catch (error) {
      console.error('检查公司名称验证状态失败:', error);
      throw error;
    }

    try {
      // 检查WebSocket连接状态
      const isConnected = this.ws.isConnected();

      if (!isConnected) {
        try {
          await this.ws.connect();
        } catch (connError) {
          console.error('WebSocket连接失败:', connError);
          throw new Error('WebSocket连接失败，无法初始化数据库');
        }
      }

      // 检查系统是否已初始化并可用
      if (!SystemInitService.isSystemAvailable()) {
        console.log('InventoryDataService: 系统尚未初始化或不可用，等待系统初始化完成...');
        const systemInitService = SystemInitService.getInstance();
        const systemInitResult = await systemInitService.initialize();

        if (!systemInitResult) {
          throw new Error('系统级数据库初始化失败');
        }
      }

      console.log('InventoryDataService: 系统已可用，数据库初始化完成');
      this.isDbInitialized = true;

    } catch (error) {
      console.error('数据库初始化失败:', error);
      this.isDbInitialized = false;
      throw error; // 将错误向上抛出，以便上层组件可以处理
    }
  }

  /**
   * 提交任务并等待结果
   */
  public async submitAndWaitTask<T>(funcName: string, params: any = {}, retryCount: number = 0): Promise<T> {
    // 检查根节点验证状态（除了根节点验证本身和激活相关的DLL调用）
    const isRootNodeValidation = funcName === 'QueryGlobalConfig';
    const isActivationCall = funcName === 'DecryptAndStoreToDatabaseWithCompany';

    if (!isRootNodeValidation && !isActivationCall) {
      try {
        // 检查全局状态而不是导入服务，避免循环依赖
        if ((globalThis as any).__rootNodeValidationFailed) {
          throw new Error('公司名称验证失败，系统已停止所有API调用');
        }

        if (!(globalThis as any).__rootNodeValidated) {
          throw new Error('公司名称尚未验证通过，请等待系统初始化完成');
        }
      } catch (error) {
        console.error('检查公司名称验证状态失败:', error);
        throw error;
      }
    }

    // 最大重试次数
    const MAX_RETRIES = 2;
    // 超时时间（毫秒），增加到60秒
    const TIMEOUT = 60000;

    // 确保WebSocket已连接
    if (!this.ws.isConnected()) {
      try {
        await this.ws.connect();
      } catch (error) {
        console.error('WebSocket连接失败:', error);
        throw new Error('WebSocket连接失败');
      }
    }

    try {
      const taskId = await this.task.submitTask(this.dllName, funcName, params);

      return new Promise((resolve, reject) => {
        // 设置超时
        const timeoutId = setTimeout(async () => {
          this.task.offTaskUpdate(taskId, handleTaskUpdate);
          console.warn(`任务超时: ${funcName}, taskId: ${taskId}, 超时时间: ${TIMEOUT}ms`);

          // 如果未达到最大重试次数，则重试
          if (retryCount < MAX_RETRIES) {
            try {
              // 重试任务
              const result = await this.submitAndWaitTask<T>(funcName, params, retryCount + 1);
              resolve(result);
            } catch (retryError) {
              console.error(`重试任务失败: ${funcName}, 错误:`, retryError);
              reject(retryError);
            }
          } else {
            console.error(`任务超时且达到最大重试次数: ${funcName}, taskId: ${taskId}`);
            reject(new Error(`任务超时: ${funcName}, taskId: ${taskId}, 已重试${retryCount}次`));
          }
        }, TIMEOUT);

        const handleTaskUpdate = (taskInfo: TaskInfo) => {
          if (taskInfo.status === 'completed') {
            clearTimeout(timeoutId);
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            if (taskInfo.result) {
              // 检查结果中的success字段
              let resultData = taskInfo.result;

              // 如果结果是字符串，尝试解析为JSON
              if (typeof resultData === 'string') {
                try {
                  resultData = JSON.parse(resultData);
                } catch (parseError) {
                  console.warn(`解析任务结果失败: ${funcName}, taskId: ${taskId}`, parseError);
                }
              }

              // 检查业务逻辑是否成功
              if (resultData && typeof resultData === 'object' && resultData.success === false) {
                const errorMessage = resultData.error_message || resultData.message || '操作失败';
                console.error(`任务业务逻辑失败: ${funcName}, taskId: ${taskId}, 错误: ${errorMessage}`);
                reject(new Error(errorMessage));
                return;
              }

              resolve(taskInfo.result as T);
            } else {
              console.error(`任务完成但结果为空: ${funcName}, taskId: ${taskId}`);
              reject(new Error('任务返回结果为空'));
            }
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            clearTimeout(timeoutId);
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            const errorMsg = taskInfo.error || '任务失败';
            console.error(`任务失败: ${funcName}, taskId: ${taskId}, 错误:`, errorMsg);
            reject(new Error(errorMsg));
          }
        };

        this.task.onTaskUpdate(taskId, handleTaskUpdate);
      });
    } catch (error) {
      console.error(`提交任务失败: ${funcName}, 错误:`, error);
      throw error;
    }
  }

  /**
   * 从后端加载设备数据
   */
  public async loadInventoryFromBackend(forceRefresh: boolean = false): Promise<InventoryItem[]> {
    // 如果已知数据库为空，直接返回
    if (this.isEmptyDatabase && !forceRefresh) {
      console.log('数据库已知为空，跳过从后端加载数据');
      return [];
    }

    // 检查缓存是否有效，如果强制刷新则忽略缓存
    const currentTime = Date.now();
    if (!forceRefresh &&
        this.lastLoadTimestamp > 0 &&
        currentTime - this.lastLoadTimestamp < this.CACHE_DURATION &&
        this.cachedInventoryData.length > 0) {
      console.log(`使用缓存数据，距离上次加载仅过去 ${currentTime - this.lastLoadTimestamp}ms，小于缓存有效期 ${this.CACHE_DURATION}ms，缓存数据量: ${this.cachedInventoryData.length}`);
      return this.cachedInventoryData;
    }

    if (forceRefresh) {
      console.log('强制刷新数据，忽略缓存');
    }

    // 如果正在加载数据，则直接返回，避免重复加载
    if (this.isLoadingInventory) {
      // 等待当前加载完成
      let retryCount = 0;
      const maxRetries = 10;
      const retryInterval = 300; // 毫秒

      while (this.isLoadingInventory && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, retryInterval));
        retryCount++;
      }

      return [];
    }

    // 如果数据库未初始化，先初始化数据库
    if (!this.isDbInitialized && !this._isEnsuringData) {
      console.log('数据库未初始化，先初始化数据库');
      const success = await this.ensureDataLoaded();
      if (!success) {
        console.error('数据库初始化失败，无法加载数据');
        throw new Error('数据库初始化失败');
      }
      return [];
    }

    this.isLoadingInventory = true; // 设置加载标志位

    try {
      // 准备查询参数，按照新的API格式
      const params = {
        "action": "get_device",
        "action_params": {}
      };

      try {
        // 调用后端API
        const response: any = await this.submitAndWaitTask('DbFun', params);

        // 解析返回的JSON字符串
        let resultData;
        try {
          // 直接使用response作为结果数据，因为它已经是一个对象
          resultData = response;
          console.log('使用响应数据:', resultData);
        } catch (parseError) {
          console.error('数据处理失败:', parseError);
          console.warn('由于数据处理失败，返回空数据');
          return [];
        }

        // 检查数据结构
        if (!resultData || !resultData.success) {
          console.error('API返回失败:', resultData);
          console.warn('由于API返回失败，返回空数据');
          return [];
        }

        // 获取设备数据 - 适配新的API数据结构
        let devices = [];
        if (Array.isArray(resultData.data)) {
          // 新的API格式：data直接是设备数组
          devices = resultData.data;
        } else if (resultData.data?.devices && Array.isArray(resultData.data.devices)) {
          // 旧的API格式：data.devices是设备数组
          devices = resultData.data.devices;
        } else {
          console.error('设备数据格式不符合预期:', resultData);
          console.warn('由于设备数据格式不符合预期，返回空数据');
          return [];
        }

        // 如果设备数据为空数组，标记数据库为空
        if (devices.length === 0) {
          this.isEmptyDatabase = true;
          return [];
        }

        // 重置空数据库标志
        this.isEmptyDatabase = false;

        // 转换设备数据为前端格式
        const inventoryList = this.convertDevicesToInventoryList(devices);

        // 检查转换后的数据是否为空
        if (!inventoryList || inventoryList.length === 0) {
          console.warn('转换后的设备列表为空，返回空数据');
          return [];
        }

        console.log('成功从后端加载数据，设备数量:', inventoryList.length);

        // 更新缓存和时间戳
        this.cachedInventoryData = inventoryList;
        this.lastLoadTimestamp = Date.now();

        return inventoryList;

      } catch (apiError) {
        console.error('调用后端API失败:', apiError);
        console.warn('由于调用后端API失败，返回空数据');
        return [];
      }

    } catch (error) {
      console.error('加载设备数据失败:', error);
      console.warn('由于加载数据失败，返回空数据');
      return [];
    } finally {
      this.isLoadingInventory = false; // 完成后重置标志位
    }
  }

  /**
   * 将后端设备数据转换为前端设备列表
   */
  public convertDevicesToInventoryList(devices: any[]): InventoryItem[] {
    // 检查扩展字段
    const hasExtFields = devices.some(device => device.extended_fields && Object.keys(device.extended_fields).length > 0);

    if (hasExtFields) {
      // 输出第一个有扩展字段的设备数据示例
      const sampleDevice = devices.find(device => device.extended_fields && Object.keys(device.extended_fields).length > 0);
      if (sampleDevice) {
        console.log('扩展字段示例:', sampleDevice.extended_fields);
      }
    }

    const inventoryList: InventoryItem[] = [];

    // 处理设备数组 - 使用统一的转换函数
    devices.forEach((device: any) => {
      const item = this.convertDeviceToInventoryItem(device);
      inventoryList.push(item);
    });

    return inventoryList;
  }

  /**
   * 将单个后端设备数据转换为前端设备项
   */
  public convertDeviceToInventoryItem(device: any): InventoryItem {
    // 确保设备数据包含必要的部门信息
    this.ensureDeviceDepartmentInfo(device);

    // 从responsible_person_departments数组中提取部门信息
    const departmentInfo = this.extractDepartmentFromResponsiblePersonDepartments(device.responsible_person_departments || []);

    // 获取分类信息
    const subCategoryName = device.sub_category_name || '';
    const parentCategoryName = device.parent_category_name || '';

    const result = {
      id: device.id.toString(), // 直接使用原始数字ID
      name: device.device_name_brand || '',
      type: subCategoryName, // 设备类型应该是二级分类
      parentCategory: parentCategoryName, // 保存一级分类信息
      status: device.usage_status || '',
      manufacturer: device.manufacturer || device.device_manufacturer || '', // 使用厂商字段，如果没有则使用空字符串
      model: device.model || '',
      department: departmentInfo,
      responsible: device.responsible_person_alias ?
        `${device.responsible_person_name} (${device.responsible_person_alias})` :
        (device.responsible_person_name || ''),
      location: device.location || '',
      startTime: this.formatTimestamp(device.activation_timestamp), // 转换时间戳
      securityCode: device.confidentiality_code || '',
      securityLevel: device.confidentiality_level || '',
      purpose: device.purpose || '',
      securityRfid: device.security_code_rfid || '',
      inspectionStatus: device.inspection_status_text || '', // 直接读取inspection_status_text字段

      // 巡检相关字段
      inspection_status: device.inspection_status,
      inspection_status_text: device.inspection_status_text || '',
      inspection_task_info: device.inspection_task_info || null,

      // 保存原始数据和ID信息，用于后续操作
      rawData: device,
      subCategoryId: device.sub_category_id,
      parentCategoryId: device.parent_category_id,

      // 添加扩展字段数据
      extendedFields: device.extended_fields || {}
    };

    return result;
  }

  /**
   * 确保设备数据包含必要的部门信息
   * @param device 设备数据
   */
  private ensureDeviceDepartmentInfo(device: any): void {
    // 如果设备数据中没有responsible_person_departments字段，尝试构造
    if (!device.responsible_person_departments ||
        !Array.isArray(device.responsible_person_departments) ||
        device.responsible_person_departments.length === 0) {

      // 尝试从其他字段构造部门信息
      let departmentInfo = null;

      // 从department_path字段构造
      if (device.department_path) {
        const pathParts = device.department_path.split('/');
        const departmentName = pathParts[pathParts.length - 1];
        if (departmentName) {
          departmentInfo = {
            name: departmentName,
            path_name: device.department_path
          };
        }
      }

      // 如果构造成功，设置到设备数据中
      if (departmentInfo) {
        device.responsible_person_departments = [departmentInfo];
      }
    }
  }

  /**
   * 从responsible_person_departments数组中提取部门信息
   * @param departments 部门数组，新格式为 [{"name": "开发组", "path_name": "管理部/开发组"}]
   * @returns 部门名称字符串，多个部门用逗号分隔，如果无有效部门则返回空字符串
   */
  private extractDepartmentFromResponsiblePersonDepartments(departments: any[]): string {
    if (!departments || !Array.isArray(departments) || departments.length === 0) {
      return '';
    }

    // 提取所有部门名称，支持新旧格式
    const departmentNames = departments
      .filter(dept => dept && (dept.name || dept.id)) // 过滤掉无效的部门对象
      .map(dept => {
        // 新格式：优先使用name字段
        if (dept.name && dept.path_name) {
          return dept.name; // 展示部门名称
        }
        // 兼容旧格式：使用name字段
        else if (dept.name) {
          return dept.name;
        }
        // 更旧的格式：如果只有id，返回空字符串
        else {
          return '';
        }
      })
      .filter(name => name !== ''); // 过滤掉空字符串

    // 如果有多个部门，用逗号分隔；如果只有一个部门，直接返回
    return departmentNames.join(', ');
  }

  /**
   * 将时间戳转换为日期字符串
   */
  private formatTimestamp(timestamp?: number): string {
    if (!timestamp) return '';
    // 将秒级时间戳转换为毫秒级时间戳
    const milliseconds = timestamp * 1000;
    const date = new Date(milliseconds);

    // 格式化为YYYY-MM-DD
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
}

export default InventoryDataService;
