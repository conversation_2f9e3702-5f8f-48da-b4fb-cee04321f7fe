import { BaseService } from '../base/baseService';
import TaskManager from '../../utils/taskManager';
import { InventoryUpdater } from './utils/inventoryUpdater';
import { handleTaskError } from '../../utils/errorHandler';

/**
 * RFID绑定结果接口
 */
export interface RfidBindResult {
  success: boolean;
  message: string;
  data: {
    device_count: number;      // 总设备数量
    rfid_count: number;        // 总RFID数据数量
    success_count: number;     // 成功绑定数量
    failed_count: number;      // 失败数量
    unassigned_devices: number[]; // 未分配RFID的设备ID
    unused_rfid: any[];        // 未使用的RFID数据
    failed_items: any[];       // 失败项详情
  };
}

/**
 * RFID删除结果接口
 */
export interface RfidDeleteResult {
  success: boolean;
  message: string;
  data: {
    deleted_device_count: number; // 删除的设备数量
    deleted_rfid_count: number;   // 删除的RFID记录数量
    device_ids: number[];         // 设备ID数组
  };
}

/**
 * RFID查询结果接口
 */
export interface RfidQueryResult {
  success: boolean;
  message: string;
  data: {
    total_count: number;
    devices: Array<{
      device_id: number;
      device_name: string;
      plain_code: string | null;
      cipher_code: string | null;
      has_rfid: boolean;
    }>;
  };
}

/**
 * RFID管理服务状态接口
 */
interface RfidManagementServiceState {
  isLoading: boolean;
  lastBindResult?: RfidBindResult;
  lastDeleteResult?: RfidDeleteResult;
}



/**
 * RFID管理服务事件接口
 */
interface RfidManagementServiceEvents {
  'bind-start': void;
  'bind-success': RfidBindResult;
  'bind-error': Error;
  'delete-start': void;
  'delete-success': RfidDeleteResult;
  'delete-error': Error;
  'rfid-updated': { deviceIds: number[]; type: 'bind' | 'delete' };
}

/**
 * RFID管理服务类
 * 提供RFID码的绑定和删除功能
 */
class RfidManagementService extends BaseService<RfidManagementServiceState, RfidManagementServiceEvents> {
  private static instance: RfidManagementService;
  private taskManager: TaskManager;

  /**
   * 获取RFID管理服务实例
   * @returns RFID管理服务实例
   */
  public static getInstance(): RfidManagementService {
    if (!RfidManagementService.instance) {
      RfidManagementService.instance = new RfidManagementService();
    }
    return RfidManagementService.instance;
  }

  /**
   * 构造函数
   * 初始化RFID管理服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false
    });
    this.taskManager = TaskManager.getInstance();
  }

  /**
   * 查询设备RFID码
   * @param deviceIds 设备ID数组
   * @returns 查询结果
   */
  public async queryDeviceRfid(deviceIds: number[]): Promise<RfidQueryResult> {
    try {
      console.log('开始查询设备RFID码:', deviceIds);

      // 提交任务
      const taskId = await this.taskManager.submitTask('AccountTableDll', 'DbFun', {
        action: 'query_device_rfid',
        action_params: {
          device_ids: deviceIds
        }
      });

      // 等待任务结果
      const result = await new Promise<RfidQueryResult>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            resolve(taskInfo.result);
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            reject(handleTaskError('RFID查询', taskInfo.error));
          }
        };
        this.taskManager.onTaskUpdate(taskId, handleTaskUpdate);
      });

      console.log('RFID查询结果:', result);
      return result;

    } catch (error) {
      throw handleTaskError('RFID查询', error);
    }
  }

  /**
   * 绑定设备RFID码
   * @param deviceIds 设备ID数组
   * @returns 绑定结果
   */
  public async bindDeviceRfid(deviceIds: number[]): Promise<RfidBindResult> {
    try {
      this.updateState({ isLoading: true });
      this.emitter.emit('bind-start');

      console.log('开始绑定设备RFID码:', deviceIds);

      // 提交任务
      const taskId = await this.taskManager.submitTask('AccountTableDll', 'DbFun', {
        action: 'bind_device_rfid',
        action_params: {
          device_ids: deviceIds
        }
      });

      // 等待任务结果
      const result = await new Promise<RfidBindResult>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            resolve(taskInfo.result);
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || 'RFID绑定任务执行失败'));
          }
        };
        this.taskManager.onTaskUpdate(taskId, handleTaskUpdate);
      });

      console.log('RFID绑定结果:', result);

      // 如果有设备成功绑定RFID（包括部分成功的情况），查询最新的RFID码信息并进行增量更新
      const hasSuccessfulBindings = result.success || (result.data && result.data.success_count > 0);
      if (hasSuccessfulBindings) {
        try {
          console.log('检测到有设备成功绑定RFID，开始增量更新，成功数量:', result.data?.success_count || 0);
          await this.performIncrementalUpdate(deviceIds);
        } catch (updateError) {
          console.error('增量更新RFID码失败:', updateError);
          // 增量更新失败不影响绑定操作的成功状态
        }
      } else {
        console.log('没有设备成功绑定RFID，跳过增量更新');
      }

      // 更新状态
      this.updateState({
        isLoading: false,
        lastBindResult: result
      });

      this.emitter.emit('bind-success', result);
      return result;

    } catch (error) {
      console.error('RFID绑定失败:', error);
      this.updateState({ isLoading: false });

      const errorObj = error instanceof Error ? error : new Error('RFID绑定失败');
      this.emitter.emit('bind-error', errorObj);
      throw errorObj;
    }
  }

  /**
   * 删除设备RFID码
   * @param deviceIds 设备ID数组
   * @returns 删除结果
   */
  public async deleteDeviceRfid(deviceIds: number[]): Promise<RfidDeleteResult> {
    try {
      this.updateState({ isLoading: true });
      this.emitter.emit('delete-start');

      console.log('开始删除设备RFID码:', deviceIds);

      // 提交任务
      const taskId = await this.taskManager.submitTask('AccountTableDll', 'DbFun', {
        action: 'delete_device_rfid',
        action_params: {
          device_ids: deviceIds
        }
      });

      // 等待任务结果
      const result = await new Promise<RfidDeleteResult>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            resolve(taskInfo.result);
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || 'RFID删除任务执行失败'));
          }
        };
        this.taskManager.onTaskUpdate(taskId, handleTaskUpdate);
      });

      console.log('RFID删除结果:', result);

      // 如果有设备成功删除RFID（包括部分成功的情况），直接在前端缓存中删除RFID码
      const hasSuccessfulDeletions = result.success || (result.data && result.data.success_count > 0);
      if (hasSuccessfulDeletions) {
        try {
          console.log('检测到有设备成功删除RFID，开始缓存更新，成功数量:', result.data?.success_count || 0);
          this.performCacheRfidDeletion(deviceIds);
        } catch (updateError) {
          console.error('前端缓存删除RFID码失败:', updateError);
          // 缓存更新失败不影响删除操作的成功状态
        }
      } else {
        console.log('没有设备成功删除RFID，跳过缓存更新');
      }

      // 更新状态
      this.updateState({
        isLoading: false,
        lastDeleteResult: result
      });

      this.emitter.emit('delete-success', result);
      return result;

    } catch (error) {
      console.error('RFID删除失败:', error);
      this.updateState({ isLoading: false });
      
      const errorObj = error instanceof Error ? error : new Error('RFID删除失败');
      this.emitter.emit('delete-error', errorObj);
      throw errorObj;
    }
  }

  /**
   * 获取加载状态
   * @returns 是否正在加载
   */
  public getIsLoading(): boolean {
    return this.state.isLoading;
  }

  /**
   * 获取最后的绑定结果
   * @returns 最后的绑定结果
   */
  public getLastBindResult(): RfidBindResult | undefined {
    return this.state.lastBindResult;
  }

  /**
   * 获取最后的删除结果
   * @returns 最后的删除结果
   */
  public getLastDeleteResult(): RfidDeleteResult | undefined {
    return this.state.lastDeleteResult;
  }

  /**
   * 清除结果缓存
   */
  public clearResults(): void {
    this.updateState({
      lastBindResult: undefined,
      lastDeleteResult: undefined
    });
  }

  /**
   * 执行增量更新 - 查询最新RFID码并更新前端缓存
   * @param deviceIds 设备ID数组
   */
  private async performIncrementalUpdate(deviceIds: number[]): Promise<void> {
    try {
      console.log('开始执行RFID码增量更新:', deviceIds);

      // 查询最新的RFID码信息
      const queryResult = await this.queryDeviceRfid(deviceIds);

      if (queryResult.success && queryResult.data?.devices) {
        console.log('RFID查询结果:', {
          deviceIds,
          devices: queryResult.data.devices,
          totalCount: queryResult.data.total_count
        });

        // 使用工具类更新设备RFID码
        await InventoryUpdater.updateRfidCodes(deviceIds, queryResult.data.devices);

        console.log('RFID码增量更新完成');
        this.emitter.emit('rfid-updated', { deviceIds, type: 'bind' });
      } else {
        console.warn('RFID查询失败或无数据:', queryResult);
      }
    } catch (error) {
      console.error('执行增量更新失败:', error);
      throw error;
    }
  }

  /**
   * 执行前端缓存RFID码删除
   * @param deviceIds 设备ID数组
   */
  private performCacheRfidDeletion(deviceIds: number[]): void {
    try {
      console.log('开始执行前端缓存RFID码删除:', deviceIds);

      // 使用工具类清除设备RFID码
      InventoryUpdater.clearRfidCodes(deviceIds).then(() => {
        console.log('前端缓存RFID码删除完成');
        this.emitter.emit('rfid-updated', { deviceIds, type: 'delete' });
      }).catch(error => {
        console.error('清除RFID码失败:', error);
      });
    } catch (error) {
      console.error('执行前端缓存删除失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): RfidManagementServiceState {
    return { ...this.state };
  }

  /**
   * 监听状态变化
   */
  public onStateChange(callback: (state: RfidManagementServiceState) => void): () => void {
    this.emitter.on('state-change', callback);
    return () => this.emitter.off('state-change', callback);
  }

  /**
   * 监听事件
   */
  public on<K extends keyof RfidManagementServiceEvents>(event: K, handler: RfidManagementServiceEvents[K]): void {
    this.emitter.on(event, handler);
  }

  /**
   * 取消监听事件
   */
  public off<K extends keyof RfidManagementServiceEvents>(event: K, handler: RfidManagementServiceEvents[K]): void {
    this.emitter.off(event, handler);
  }

  /**
   * 更新状态
   */
  private updateState(updates: Partial<RfidManagementServiceState>): void {
    this.state = { ...this.state, ...updates };
    this.emitter.emit('state-change', this.state);
  }
}

export default RfidManagementService;
