import GlobalConfigService from '../services/globalConfigService';

/**
 * 公司名称助手工具
 * 提供获取公司名称的统一接口，避免循环依赖
 */
export class CompanyNameHelper {
  /**
   * 获取公司名称，如果获取失败则返回默认名称
   * @param defaultName 默认名称
   * @returns 公司名称或默认名称
   */
  public static async getCompanyName(defaultName: string = '公司名称'): Promise<string> {
    try {
      const globalConfigService = GlobalConfigService.getInstance();
      const companyName = await globalConfigService.getCompanyName();
      
      if (companyName && companyName !== '公司名称') {
        return companyName;
      } else {
        return defaultName;
      }
    } catch (error) {
      console.warn('获取公司名称失败，使用默认名称:', error);
      return defaultName;
    }
  }

  /**
   * 获取设备分类树根节点名称
   * @returns 根节点名称
   */
  public static async getDeviceCategoryRootName(): Promise<string> {
    const companyName = await this.getCompanyName('全部设备');
    console.log('使用公司名称作为设备分类树根节点:', companyName);
    return companyName;
  }

  /**
   * 获取部门分类树根节点名称
   * @returns 根节点名称
   */
  public static async getDepartmentCategoryRootName(): Promise<string> {
    const companyName = await this.getCompanyName('全部部门');
    console.log('使用公司名称作为部门分类树根节点:', companyName);
    return companyName;
  }

  /**
   * 获取导出文件标题
   * @param defaultTitle 默认标题
   * @returns 文件标题
   */
  public static async getExportTitle(defaultTitle: string = '设备台账'): Promise<string> {
    const companyName = await this.getCompanyName();
    
    if (companyName && companyName !== '公司名称') {
      return companyName;
    } else {
      return defaultTitle;
    }
  }
}

export default CompanyNameHelper;
