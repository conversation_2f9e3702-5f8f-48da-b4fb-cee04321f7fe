# 缓存系统架构指南

## 📋 概述

本项目采用多层缓存架构，不同的缓存系统负责不同的职责。本文档说明各缓存系统的使用场景和最佳实践。

## 🏗️ 缓存系统架构

### 1. **CacheContext** - 全局应用级缓存
**位置**: `src/contexts/CacheContext.tsx`

**职责**:
- 路由组件缓存（避免重复渲染）
- 全局导航状态管理
- 跨组件的展开状态持久化
- 用户界面偏好设置

**使用场景**:
```typescript
import { useCacheContext } from '../contexts/CacheContext';

function MyComponent() {
  const { expandedItems, setExpandedItems } = useCacheContext();
  
  // 用于全局的展开状态管理
  const handleGlobalExpand = (itemId: string) => {
    setExpandedItems(prev => ({ ...prev, [itemId]: true }));
  };
}
```

**特点**:
- ✅ 跨路由持久化
- ✅ 全局状态共享
- ✅ React Context 实现
- ⚠️ 不适合频繁更新的数据

### 2. **CacheManager** - 部门业务专用缓存
**位置**: `src/services/Inventory/department/cacheManager.ts`

**职责**:
- 部门树相关数据缓存
- 人员信息缓存
- 路径计算结果缓存
- 部门查询结果缓存

**使用场景**:
```typescript
import { CacheManager } from './department/cacheManager';

const cacheManager = CacheManager.getInstance();

// 缓存人员信息
cacheManager.setPersonCache(personId, personInfo);

// 获取缓存的路径
const path = cacheManager.getPathCache(nodeId);

// 检查缓存有效性
if (cacheManager.isCacheValid('personCache')) {
  // 使用缓存数据
}
```

**特点**:
- ✅ 类型安全的缓存操作
- ✅ 自动过期管理
- ✅ 批量操作支持
- ✅ 缓存统计和监控

### 3. **DataManager** - 通用数据缓存
**位置**: `src/services/base/dataManager.ts`

**职责**:
- 通用API响应缓存
- 请求去重管理
- 其他业务模块的数据缓存
- 基础数据服务

**使用场景**:
```typescript
import { DataManager } from '../base/dataManager';

const dataManager = DataManager.getInstance();

// 通用数据获取（带缓存）
const data = await dataManager.getData('some-key', fetchFunction);

// 清除特定缓存
dataManager.clearCache('some-key');
```

**特点**:
- ✅ 通用性强
- ✅ 请求去重
- ✅ 灵活的缓存策略
- ⚠️ 类型安全性较弱

## 🎯 使用原则

### **选择合适的缓存系统**

#### 使用 CacheContext 当：
- ✅ 需要跨路由持久化的状态
- ✅ 全局UI状态（如侧边栏展开状态）
- ✅ 用户偏好设置
- ✅ 导航相关状态

#### 使用 CacheManager 当：
- ✅ 部门树相关的所有数据
- ✅ 人员信息缓存
- ✅ 需要类型安全的缓存操作
- ✅ 需要缓存生命周期管理

#### 使用 DataManager 当：
- ✅ 其他业务模块的数据缓存
- ✅ 通用API响应缓存
- ✅ 需要请求去重的场景
- ✅ 临时性数据缓存

### **避免的反模式**

❌ **不要在同一功能中混用多个缓存系统**
```typescript
// 错误示例
const cacheManager = CacheManager.getInstance();
const { expandedItems } = useCacheContext();
// 同时使用两个缓存系统管理相同的数据
```

❌ **不要绕过缓存系统直接操作数据**
```typescript
// 错误示例
const result = await baseService.submitTask('DbFun', { action: 'get_person' });
// 应该使用 DataFetchService 或 DepartmentService
```

❌ **不要在缓存中存储大量数据**
```typescript
// 错误示例
cacheManager.setPersonCache(id, hugeDataObject); // 避免存储过大的对象
```

## 🔄 缓存同步策略

### **数据一致性保证**

1. **部门数据更新时**:
```typescript
// 正确的缓存更新流程
await departmentService.updateDepartment(deptId, newData);
// 自动清除相关缓存
cacheManager.clearCache('pathCache');
cacheManager.clearCache('departmentPathsCache');
```

2. **人员数据更新时**:
```typescript
// 正确的缓存更新流程
await departmentService.updatePerson(personId, newData);
// 更新缓存中的人员信息
departmentService.updatePersonInCache(personId, newData);
```

### **缓存失效策略**

- **时间失效**: 默认5分钟自动过期
- **手动失效**: 数据更新时主动清除
- **版本失效**: 数据版本变化时清除

## 📊 性能监控

### **缓存命中率监控**
```typescript
// 获取缓存统计信息
const stats = cacheManager.getCacheStats();
console.log('缓存统计:', stats);
```

### **内存使用监控**
```typescript
// 定期清理过期缓存
setInterval(() => {
  cacheManager.clearExpiredCaches();
}, 5 * 60 * 1000); // 每5分钟清理一次
```

## 🚀 最佳实践

### **1. 缓存键命名规范**
- 使用有意义的键名
- 包含版本信息（如需要）
- 避免键名冲突

### **2. 缓存大小控制**
- 定期清理过期缓存
- 避免缓存过大的对象
- 监控内存使用情况

### **3. 错误处理**
- 缓存操作失败时的降级策略
- 避免缓存错误影响主流程
- 记录缓存相关的错误日志

### **4. 测试策略**
- 测试缓存命中和未命中的情况
- 测试缓存过期和清理逻辑
- 测试并发访问的安全性

## 🔧 故障排查

### **常见问题**

1. **缓存不一致**
   - 检查缓存更新逻辑
   - 确认缓存清除时机
   - 验证数据同步流程

2. **内存泄漏**
   - 检查缓存清理机制
   - 监控缓存大小增长
   - 确认组件卸载时的清理

3. **性能问题**
   - 分析缓存命中率
   - 优化缓存策略
   - 减少不必要的缓存操作

## 📝 总结

合理使用多层缓存架构可以显著提升应用性能，但需要明确各缓存系统的职责边界，避免混用和冲突。遵循本指南的原则和最佳实践，可以构建高效、可维护的缓存系统。
