import { BaseService, BaseServiceState, BaseServiceEvents } from '../../base/baseService';
import { FieldDefinition } from '../../../types/inventory';

// 导出设置服务状态
interface ExportSettingsServiceState extends BaseServiceState {
  selectedCategories: string[];
  selectedColumns: string[];
  columnOrder: string[];
  lastExportFormat: 'excel' | 'pdf' | 'word';
  lastExportFileName: string;
}

// 导出设置服务事件
interface ExportSettingsServiceEvents extends BaseServiceEvents<ExportSettingsServiceState> {
  'settings-changed': ExportSettingsServiceState;
  'categories-changed': string[];
  'columns-changed': string[];
  'column-order-changed': string[];
}

/**
 * 导出设置服务
 * 管理导出相关的设置，如分类选择、列选择和列顺序
 */
class ExportSettingsService extends BaseService<ExportSettingsServiceState, ExportSettingsServiceEvents> {
  private static instance: ExportSettingsService;

  private constructor() {
    super('ExportDll', {
      selectedCategories: [],
      selectedColumns: [],
      columnOrder: [],
      lastExportFormat: 'excel',
      lastExportFileName: '设备台账',
      isLoading: false
    });
  }

  /**
   * 获取导出设置服务实例
   * @returns 导出设置服务实例
   */
  public static getInstance(): ExportSettingsService {
    if (!ExportSettingsService.instance) {
      ExportSettingsService.instance = new ExportSettingsService();
    }
    return ExportSettingsService.instance;
  }

  /**
   * 设置选中的分类
   * @param categoryIds 分类ID数组
   */
  public setSelectedCategories(categoryIds: string[]): void {
    this.updateState({ selectedCategories: [...categoryIds] });
    this.emitter.emit('categories-changed', this.state.selectedCategories);
    this.emitter.emit('settings-changed', this.state);
  }

  /**
   * 设置选中的列
   * @param columnIds 列ID数组
   */
  public setSelectedColumns(columnIds: string[]): void {
    this.updateState({ selectedColumns: [...columnIds] });

    // 更新列顺序，保留已有顺序，添加新选中的列
    const currentOrder = [...this.state.columnOrder];
    const newOrder: string[] = [];

    // 先添加已有顺序中的列（如果它们仍然被选中）
    currentOrder.forEach(colId => {
      if (columnIds.includes(colId)) {
        newOrder.push(colId);
      }
    });

    // 添加新选中但尚未在顺序中的列
    columnIds.forEach(colId => {
      if (!newOrder.includes(colId)) {
        newOrder.push(colId);
      }
    });

    // 确保parentCategory在序号后面
    if (newOrder.includes('parentCategory') && newOrder.includes('id')) {
      const parentCategoryIndex = newOrder.indexOf('parentCategory');
      const idIndex = newOrder.indexOf('id');

      // 如果parentCategory不在序号后面，调整位置
      if (idIndex !== -1 && parentCategoryIndex !== idIndex + 1) {
        // 先移除parentCategory
        newOrder.splice(parentCategoryIndex, 1);
        // 然后在序号后面插入
        newOrder.splice(idIndex + 1, 0, 'parentCategory');
      }
    }

    this.updateState({ columnOrder: newOrder });

    this.emitter.emit('columns-changed', this.state.selectedColumns);
    this.emitter.emit('column-order-changed', this.state.columnOrder);
    this.emitter.emit('settings-changed', this.state);
  }

  /**
   * 设置列顺序
   * @param columnOrder 列ID数组，表示顺序
   */
  public setColumnOrder(columnOrder: string[]): void {
    // 注意：这里我们不强制调整parentCategory的位置
    // 因为这个方法是用户手动调整列顺序时调用的，我们应该尊重用户的选择
    this.updateState({ columnOrder: [...columnOrder] });
    this.emitter.emit('column-order-changed', this.state.columnOrder);
    this.emitter.emit('settings-changed', this.state);
  }

  /**
   * 设置上次导出格式
   * @param format 导出格式
   */
  public setLastExportFormat(format: 'excel' | 'pdf' | 'word'): void {
    this.updateState({ lastExportFormat: format });
    this.emitter.emit('settings-changed', this.state);
  }

  /**
   * 设置上次导出文件名
   * @param fileName 文件名
   */
  public setLastExportFileName(fileName: string): void {
    this.updateState({ lastExportFileName: fileName });
    this.emitter.emit('settings-changed', this.state);
  }

  /**
   * 初始化列选择
   * @param columns 可用列定义
   */
  public initializeColumnSelection(columns: FieldDefinition[]): void {
    // 默认只选中标准字段（非扩展字段）
    // 过滤掉UI控制列和扩展字段
    const standardColumnIds = columns
      .filter(col =>
        !col.key.startsWith('ext_') && // 不是扩展字段
        col.key !== 'select' && // 不是选择列
        col.key !== 'actions' && // 不是操作列
        col.key !== '_selected' // 不是选中状态列
      )
      .map(col => col.key);

    // 确保parentCategory（设备类型分类）字段被包含在导出列中，并放在序号后面
    if (!standardColumnIds.includes('parentCategory')) {
      // 找到序号字段的位置
      const idIndex = standardColumnIds.indexOf('id');
      if (idIndex !== -1) {
        // 在序号字段后面插入parentCategory
        standardColumnIds.splice(idIndex + 1, 0, 'parentCategory');
      } else {
        // 如果没有找到序号字段，将parentCategory放在最前面
        standardColumnIds.unshift('parentCategory');
      }
    } else {
      // 如果已经包含parentCategory，确保它在序号后面
      const parentCategoryIndex = standardColumnIds.indexOf('parentCategory');
      const idIndex = standardColumnIds.indexOf('id');

      if (idIndex !== -1 && parentCategoryIndex !== idIndex + 1) {
        // 先移除parentCategory
        standardColumnIds.splice(parentCategoryIndex, 1);
        // 然后在序号后面插入
        standardColumnIds.splice(idIndex + 1, 0, 'parentCategory');
      }
    }

    this.setSelectedColumns(standardColumnIds);
  }

  /**
   * 获取当前设置
   * @returns 当前导出设置
   */
  public getSettings(): ExportSettingsServiceState {
    return { ...this.state };
  }

  /**
   * 重置设置
   */
  public resetSettings(): void {
    this.updateState({
      selectedCategories: [],
      selectedColumns: [],
      columnOrder: []
    });

    this.emitter.emit('categories-changed', this.state.selectedCategories);
    this.emitter.emit('columns-changed', this.state.selectedColumns);
    this.emitter.emit('column-order-changed', this.state.columnOrder);
    this.emitter.emit('settings-changed', this.state);
  }
}

export default ExportSettingsService;
