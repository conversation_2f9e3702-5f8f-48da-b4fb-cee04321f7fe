import React, { useState, useEffect, useRef } from 'react';
import { Repeat, FileText, Users, Activity, ChevronLeft, ChevronRight, ClipboardCheck, AlertCircle, ClipboardList } from 'lucide-react';
import ResizeHandle from '../../components/ui/ResizeHandle';
import useInspection from '../../hooks/Inspection/useInspection';
import InspectionTask from './InspectionTask';
import InspectionSync from './InspectionSync';
import InspectionLog from './InspectionLog';
import InspectionAccount from './InspectionAccount';
import InspectionLogManagement from './InspectionLogManagement';

/**
 * 巡检管理页面
 * 采用卡片式布局，符合桌面应用风格
 * 版本: v2.0 - 卡片式布局 (2024-12-30)
 */
const InspectionManagement: React.FC = () => {
  // 使用巡检管理Hook
  const {
    isLoading,
    error,
    activeTab,
    setActiveTab
  } = useInspection();

  // 左侧面板宽度状态
  const [leftPanelWidth, setLeftPanelWidth] = useState(200); // 默认宽度，缩小
  const minLeftPanelWidth = 160; // 最小宽度，缩小
  const maxLeftPanelWidth = 280; // 最大宽度，缩小
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false); // 是否收起左侧面板
  const previousWidthRef = useRef(leftPanelWidth); // 保存收起前的宽度，用于恢复

  // 导航项配置
  const navigationItems = [
    {
      key: 'task',
      label: '巡检任务',
      icon: ClipboardList
    },
    {
      key: 'sync',
      label: '数据同步',
      icon: Repeat
    },
    {
      key: 'log',
      label: '巡检结果',
      icon: FileText
    },
    {
      key: 'account',
      label: '账户管理',
      icon: Users
    },
    {
      key: 'logManagement',
      label: '巡检日志',
      icon: Activity
    }
  ];

  // 获取当前活动项
  const currentItem = navigationItems.find(item => item.key === activeTab);

  // 处理左侧面板宽度调整
  const handleResize = (delta: number) => {
    if (isLeftPanelCollapsed) return; // 收起状态下不允许调整宽度

    setLeftPanelWidth(prevWidth => {
      // 计算新宽度，并确保在最小和最大宽度范围内
      const newWidth = Math.max(minLeftPanelWidth, Math.min(maxLeftPanelWidth, prevWidth + delta));
      previousWidthRef.current = newWidth; // 更新保存的宽度
      return newWidth;
    });
  };

  // 切换左侧面板收起/展开状态
  const toggleLeftPanel = () => {
    setIsLeftPanelCollapsed(prev => {
      if (prev) {
        // 从收起状态展开 - 使用setTimeout避免抖动
        requestAnimationFrame(() => {
          setLeftPanelWidth(previousWidthRef.current);
        });
        return false;
      } else {
        // 从展开状态收起 - 保存当前宽度
        previousWidthRef.current = leftPanelWidth;
        return true;
      }
    });
  };

  // 渲染内容区域
  const renderContent = () => {
    switch (activeTab) {
      case 'task':
        return <InspectionTask />;
      case 'sync':
        return <InspectionSync />;
      case 'log':
        return <InspectionLog />;
      case 'account':
        return <InspectionAccount />;
      case 'logManagement':
        return <InspectionLogManagement />;
      default:
        return <InspectionTask />;
    }
  };

  return (
    <div className="h-full bg-gray-50">
      {/* 版本信息 - 调试用 */}
      <div className="hidden">Vben风格布局版本 v3.0 - {new Date().toISOString()}</div>

      {/* 错误信息 */}
      {error && (
        <div className="m-4 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-md">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-400 mr-3" />
            <span className="text-red-700 font-medium">{error}</span>
          </div>
        </div>
      )}

      <div className="flex h-full">
        {/* 左侧导航面板 - Vben风格 */}
        <div
          className={`bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out ${
            isLeftPanelCollapsed ? 'opacity-0 invisible' : 'opacity-100 visible'
          }`}
          style={{
            width: `${leftPanelWidth}px`,
            flexShrink: 0,
            position: 'relative',
            marginLeft: isLeftPanelCollapsed ? `-${leftPanelWidth}px` : '0',
            transition: 'margin-left 0.3s ease, opacity 0.3s ease, visibility 0.3s ease'
          }}
        >
          {/* 导航标题 */}
          <div className="px-4 py-4 border-b border-gray-100">
            <h2 className="text-base font-semibold text-gray-800 flex items-center">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                <ClipboardCheck className="h-4 w-4 text-white" />
              </div>
              巡检管理
            </h2>
          </div>

          {/* 导航菜单 */}
          <div className="flex-1 py-2">
            <nav className="space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeTab === item.key;

                return (
                  <button
                    key={item.key}
                    onClick={() => setActiveTab(item.key)}
                    className={`w-full flex items-center px-4 py-3 text-sm font-medium transition-all duration-200 group ${
                      isActive
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-500'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className={`mr-3 h-5 w-5 transition-colors ${
                      isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                    }`} />
                    {item.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* 拖拽手柄容器 */}
        <div
          className="relative flex items-center"
          style={{
            width: '8px', // 保持8px宽度，已经比较合适
            marginLeft: isLeftPanelCollapsed ? '0' : '0px',
            zIndex: 30,
            transition: 'margin-left 0.3s ease-in-out',
            backgroundColor: 'transparent' // 添加透明背景
          }}
        >
          {/* 拖拽手柄 */}
          {!isLeftPanelCollapsed && (
            <ResizeHandle
              direction="horizontal"
              onResize={handleResize}
              className="opacity-15 hover:opacity-50 focus:opacity-50 active:opacity-70 transition-all duration-200 ease-in-out"
            />
          )}
        </div>

        {/* 右侧内容区域 - 优化后的布局 */}
        <div className="flex-1 flex flex-col bg-gray-50"
          style={{
            marginLeft: isLeftPanelCollapsed ? '0' : '0',
            transition: 'margin-left 0.3s ease'
          }}>

          {/* 页面头部 - 固定高度 */}
          <div className="flex-none bg-white border-b border-gray-200 px-4 sm:px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {/* 收起/展开面板按钮 */}
                <button
                  onClick={toggleLeftPanel}
                  className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors mr-4"
                  title={isLeftPanelCollapsed ? "展开导航面板" : "收起导航面板"}
                >
                  {isLeftPanelCollapsed ? (
                    <ChevronRight className="h-4 w-4" />
                  ) : (
                    <ChevronLeft className="h-4 w-4" />
                  )}
                </button>

                {/* 面包屑导航 */}
                <nav className="flex items-center space-x-2 text-lg">
                  <span className="text-gray-600 font-bold">巡检管理</span>
                  <span className="text-gray-400 font-bold">/</span>
                  {currentItem && (
                    <span className="text-gray-900 font-bold">{currentItem.label}</span>
                  )}
                </nav>
              </div>

              {/* 页面操作区域 */}
              <div className="flex items-center space-x-3">
                {currentItem && (
                  <div className="flex items-center text-gray-600">
                    <currentItem.icon className="h-4 w-4 mr-2" />
                    <span className="text-sm">{currentItem.label}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 内容区域 - 白色框延伸到底部 */}
          <div className="flex-1 overflow-hidden">
            <div className="h-full pt-3 sm:pt-4 md:pt-6 px-3 sm:px-4 md:px-6">
              <div className="bg-white rounded-t-lg shadow-sm border border-gray-200 border-b-0 h-full min-h-[400px] overflow-hidden flex flex-col">
                {renderContent()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InspectionManagement;
