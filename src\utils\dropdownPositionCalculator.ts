/**
 * 下拉框位置计算工具
 * 统一处理所有下拉组件的位置计算逻辑，避免代码重复
 */

export interface DropdownPosition {
  top: number;
  left: number;
  width: number;
  maxHeight: number;
  direction: 'up' | 'down';
}

export interface PositionCalculatorOptions {
  /** 触发元素的DOM引用 */
  triggerElement: HTMLElement;
  /** 下拉框的最大高度，默认240px */
  maxDropdownHeight?: number;
  /** 边距，默认10px */
  margin?: number;
  /** 是否允许超出右边界，默认false */
  allowOverflowRight?: boolean;
  /** 最小宽度，默认使用触发元素宽度 */
  minWidth?: number;
}

/**
 * 计算下拉框的最佳位置
 * @param options 位置计算选项
 * @returns 计算得到的位置信息
 */
export function calculateDropdownPosition(options: PositionCalculatorOptions): DropdownPosition {
  const {
    triggerElement,
    maxDropdownHeight = 240,
    margin = 10,
    allowOverflowRight = false,
    minWidth
  } = options;

  const rect = triggerElement.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;
  const spaceBelow = viewportHeight - rect.bottom;
  const spaceAbove = rect.top;

  // 计算最大高度和位置
  let maxHeight = maxDropdownHeight;
  let top = rect.bottom + window.scrollY;
  let direction: 'up' | 'down' = 'down';

  // 如果下方空间不足，考虑向上展开
  if (spaceBelow < maxDropdownHeight && spaceAbove > spaceBelow) {
    direction = 'up';
    maxHeight = Math.min(maxDropdownHeight, spaceAbove - margin);
    top = rect.top + window.scrollY - maxHeight;
  } else {
    maxHeight = Math.min(maxDropdownHeight, spaceBelow - margin);
  }

  // 计算左侧位置，确保不超出右边界
  let left = rect.left + window.scrollX;
  const dropdownWidth = minWidth ? Math.max(rect.width, minWidth) : rect.width;
  
  if (!allowOverflowRight && left + dropdownWidth > viewportWidth) {
    left = viewportWidth - dropdownWidth - margin;
  }

  return {
    top,
    left,
    width: dropdownWidth,
    maxHeight,
    direction
  };
}

/**
 * 创建一个React Hook来处理下拉框位置计算
 * @param triggerRef 触发元素的ref
 * @param isOpen 下拉框是否打开
 * @param options 位置计算选项
 * @returns 位置信息和重新计算函数
 */
export function useDropdownPosition(
  triggerRef: React.RefObject<HTMLElement>,
  isOpen: boolean,
  options: Omit<PositionCalculatorOptions, 'triggerElement'> = {}
) {
  const [position, setPosition] = React.useState<DropdownPosition>({
    top: 0,
    left: 0,
    width: 0,
    maxHeight: 240,
    direction: 'down'
  });

  const calculatePosition = React.useCallback(() => {
    if (!triggerRef.current) return;

    const newPosition = calculateDropdownPosition({
      triggerElement: triggerRef.current,
      ...options
    });
    setPosition(newPosition);
  }, [triggerRef, options]);

  // 当下拉框打开时计算位置
  React.useEffect(() => {
    if (isOpen) {
      calculatePosition();
    }
  }, [isOpen, calculatePosition]);

  // 监听窗口变化事件
  React.useEffect(() => {
    if (!isOpen) return;

    const handleResize = () => calculatePosition();
    const handleScroll = () => calculatePosition();

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, true);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [isOpen, calculatePosition]);

  return {
    position,
    calculatePosition
  };
}

/**
 * 创建一个通用的点击外部关闭Hook
 * @param refs 需要检查的DOM引用数组
 * @param isOpen 是否打开状态
 * @param onClose 关闭回调
 */
export function useClickOutside(
  refs: React.RefObject<HTMLElement>[],
  isOpen: boolean,
  onClose: () => void
) {
  React.useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const isOutside = refs.every(ref => 
        !ref.current || !ref.current.contains(event.target as Node)
      );

      if (isOutside) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose, refs]);
}

/**
 * 获取下拉框的样式对象
 * @param position 位置信息
 * @param zIndex z-index值，默认9000
 * @returns 样式对象
 */
export function getDropdownStyles(position: DropdownPosition, zIndex: number = 9000): React.CSSProperties {
  return {
    position: 'fixed',
    top: `${position.top}px`,
    left: `${position.left}px`,
    width: `${position.width}px`,
    maxHeight: `${position.maxHeight}px`,
    zIndex
  };
}

// 导入React用于Hook
import React from 'react';
