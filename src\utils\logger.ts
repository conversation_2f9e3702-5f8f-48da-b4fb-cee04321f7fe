/**
 * 日志工具
 * 提供统一的日志记录功能
 */

// 日志级别
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

// 当前日志级别
let currentLogLevel = LogLevel.INFO;

/**
 * 设置日志级别
 * @param level 日志级别
 */
export function setLogLevel(level: LogLevel): void {
  currentLogLevel = level;
}

/**
 * 记录调试日志
 * @param message 日志消息
 * @param context 上下文信息
 */
export function logDebug(message: any, context?: string): void {
  if (currentLogLevel <= LogLevel.DEBUG) {
    const contextStr = context ? `[${context}] ` : '';
    console.debug(`${contextStr}${message}`);
  }
}

/**
 * 记录信息日志
 * @param message 日志消息
 * @param context 上下文信息
 */
export function logInfo(message: any, context?: string): void {
  if (currentLogLevel <= LogLevel.INFO) {
    const contextStr = context ? `[${context}] ` : '';
    console.info(`${contextStr}${message}`);
  }
}

/**
 * 记录警告日志
 * @param message 日志消息
 * @param context 上下文信息
 */
export function logWarn(message: any, context?: string): void {
  if (currentLogLevel <= LogLevel.WARN) {
    const contextStr = context ? `[${context}] ` : '';
    console.warn(`${contextStr}${message}`);
  }
}

/**
 * 记录错误日志
 * @param error 错误对象或消息
 * @param context 上下文信息
 */
export function logError(error: any, context?: string): void {
  if (currentLogLevel <= LogLevel.ERROR) {
    const contextStr = context ? `[${context}] ` : '';
    
    if (error instanceof Error) {
      console.error(`${contextStr}${error.message}`);
      console.error(error.stack);
    } else {
      console.error(`${contextStr}${error}`);
    }
  }
}

/**
 * 记录API请求日志
 * @param method 请求方法
 * @param url 请求URL
 * @param params 请求参数
 * @param response 响应数据
 */
export function logApiRequest(method: string, url: string, params?: any, response?: any): void {
  if (currentLogLevel <= LogLevel.DEBUG) {
    console.group(`API请求: ${method} ${url}`);
    if (params) {
      console.debug('参数:', params);
    }
    if (response) {
      console.debug('响应:', response);
    }
    console.groupEnd();
  }
}

/**
 * 记录WebSocket消息日志
 * @param direction 方向 ('sent' | 'received')
 * @param message 消息内容
 */
export function logWebSocketMessage(direction: 'sent' | 'received', message: any): void {
  if (currentLogLevel <= LogLevel.DEBUG) {
    console.group(`WebSocket ${direction === 'sent' ? '发送' : '接收'}`);
    console.debug(message);
    console.groupEnd();
  }
}

// 默认导出所有日志函数
export default {
  setLogLevel,
  logDebug,
  logInfo,
  logWarn,
  logError,
  logApiRequest,
  logWebSocketMessage
};
