/**
 * 数据管理器 - 统一处理数据获取、缓存和刷新机制
 */

export interface DataCacheItem<T = any> {
  data: T;
  timestamp: number;
  loading: boolean;
  error?: string;
  version: number;
}

export interface DataManagerConfig {
  // 最大重试次数
  maxRetries: number;
  // 重试延迟（毫秒）
  retryDelay: number;
}

export type DataFetcher<T> = () => Promise<T>;
export type DataListener<T> = (data: DataCacheItem<T>) => void;

class DataManager {
  private cache = new Map<string, DataCacheItem>();
  private listeners = new Map<string, Set<DataListener<any>>>();
  private fetchPromises = new Map<string, Promise<any>>();

  private defaultConfig: DataManagerConfig = {
    maxRetries: 3,
    retryDelay: 1000
  };

  /**
   * 获取数据
   */
  async getData<T>(
    key: string,
    fetcher: DataFetcher<T>,
    config: Partial<DataManagerConfig> = {}
  ): Promise<T> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const cached = this.cache.get(key) as DataCacheItem<T> | undefined;
    const now = Date.now();

    // 如果有正在进行的请求，等待它完成
    if (this.fetchPromises.has(key)) {
      try {
        return await this.fetchPromises.get(key);
      } catch (error) {
        // 如果正在进行的请求失败，继续执行新的请求
      }
    }

    // 检查缓存是否存在且有效
    if (cached && !cached.loading && cached.data) {
      // 直接返回缓存数据（单机版不需要过期检查）
      return cached.data;
    }

    // 执行数据获取
    return this.fetchData(key, fetcher, finalConfig);
  }

  /**
   * 强制刷新数据
   */
  async refreshData<T>(
    key: string,
    fetcher: DataFetcher<T>,
    config: Partial<DataManagerConfig> = {}
  ): Promise<T> {
    const finalConfig = { ...this.defaultConfig, ...config };
    this.clearCache(key);
    return this.fetchData(key, fetcher, finalConfig);
  }

  /**
   * 执行数据获取
   */
  private async fetchData<T>(
    key: string,
    fetcher: DataFetcher<T>,
    config: DataManagerConfig,
    retryCount = 0
  ): Promise<T> {
    // 设置loading状态
    this.updateCache(key, {
      data: this.cache.get(key)?.data,
      timestamp: Date.now(),
      loading: true,
      error: undefined,
      version: (this.cache.get(key)?.version || 0) + 1
    });

    const fetchPromise = this.executeFetch(key, fetcher, config, retryCount);
    this.fetchPromises.set(key, fetchPromise);

    try {
      const data = await fetchPromise;
      
      // 更新缓存
      this.updateCache(key, {
        data,
        timestamp: Date.now(),
        loading: false,
        error: undefined,
        version: (this.cache.get(key)?.version || 0)
      });

      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '数据获取失败';
      
      // 更新错误状态
      this.updateCache(key, {
        data: this.cache.get(key)?.data,
        timestamp: Date.now(),
        loading: false,
        error: errorMessage,
        version: (this.cache.get(key)?.version || 0)
      });

      throw error;
    } finally {
      this.fetchPromises.delete(key);
    }
  }

  /**
   * 执行实际的数据获取
   */
  private async executeFetch<T>(
    key: string,
    fetcher: DataFetcher<T>,
    config: DataManagerConfig,
    retryCount: number
  ): Promise<T> {
    try {
      return await fetcher();
    } catch (error) {
      console.error(`数据获取失败 [${key}] (尝试 ${retryCount + 1}/${config.maxRetries + 1}):`, error);
      
      // 如果还有重试次数，进行重试
      if (retryCount < config.maxRetries) {
        await this.delay(config.retryDelay * Math.pow(2, retryCount)); // 指数退避
        return this.executeFetch(key, fetcher, config, retryCount + 1);
      }
      
      throw error;
    }
  }

  /**
   * 增量更新缓存数据
   */
  updateCacheData<T>(key: string, updater: (data: T) => T): void {
    const cached = this.cache.get(key) as DataCacheItem<T> | undefined;
    if (cached && cached.data) {
      const updatedData = updater(cached.data);
      this.updateCache(key, {
        ...cached,
        data: updatedData,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 添加单个项目到缓存数组
   * @param key 缓存键
   * @param item 要添加的项目
   * @param prepend 是否添加到数组开头（默认false，添加到末尾）
   */
  addItemToCache<T>(key: string, item: T, prepend: boolean = false): void {
    this.updateCacheData<T[]>(key, (data) =>
      prepend ? [item, ...data] : [...data, item]
    );
  }

  /**
   * 更新缓存数组中的单个项目
   */
  updateItemInCache<T extends { id: any }>(key: string, itemId: any, updater: (item: T) => T): void {
    this.updateCacheData<T[]>(key, (data) =>
      data.map(item => item.id === itemId ? updater(item) : item)
    );
  }

  /**
   * 从缓存数组中删除项目
   */
  removeItemFromCache<T extends { id: any }>(key: string, itemId: any): void {
    this.updateCacheData<T[]>(key, (data) =>
      data.filter(item => item.id !== itemId)
    );
  }

  /**
   * 更新缓存并通知监听器
   */
  private updateCache<T>(key: string, cacheItem: DataCacheItem<T>): void {
    this.cache.set(key, cacheItem);
    this.notifyListeners(key, cacheItem);
  }

  /**
   * 通知监听器
   */
  private notifyListeners<T>(key: string, cacheItem: DataCacheItem<T>): void {
    const listeners = this.listeners.get(key);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(cacheItem);
        } catch (error) {
          console.error('监听器执行失败:', error);
        }
      });
    }
  }

  /**
   * 添加数据监听器
   */
  addListener<T>(key: string, listener: DataListener<T>): () => void {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    
    this.listeners.get(key)!.add(listener);
    
    // 如果有缓存数据，立即通知
    const cached = this.cache.get(key);
    if (cached) {
      listener(cached);
    }
    
    // 返回取消监听的函数
    return () => {
      const listeners = this.listeners.get(key);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          this.listeners.delete(key);
        }
      }
    };
  }

  /**
   * 清除缓存
   */
  clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
      this.fetchPromises.delete(key);
    } else {
      this.cache.clear();
      this.fetchPromises.clear();
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取缓存状态
   */
  getCacheInfo(key: string): DataCacheItem | undefined {
    return this.cache.get(key);
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.cache.clear();
    this.listeners.clear();
    this.fetchPromises.clear();
  }
}

// 全局数据管理器实例
export const dataManager = new DataManager();

export default DataManager;
