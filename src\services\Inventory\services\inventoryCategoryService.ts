import { DeviceCategory, InventoryItem } from '../../../types/inventory';
import { fetchCategoryTree, updateCategoryTreeCounts } from '../fetchCategoryTree';

/**
 * 库存分类服务
 * 专门负责管理设备分类树和计数
 */
export class InventoryCategoryService {
  private static instance: InventoryCategoryService;
  
  // 当前选中的分类信息
  private currentSelectedCategory?: { parentCategory?: string; subCategory?: string };
  
  // 计数更新防抖相关
  private countUpdateTimeout: NodeJS.Timeout | null = null;
  private isCountUpdateInProgress: boolean = false;

  private constructor() {}

  public static getInstance(): InventoryCategoryService {
    if (!InventoryCategoryService.instance) {
      InventoryCategoryService.instance = new InventoryCategoryService();
    }
    return InventoryCategoryService.instance;
  }

  /**
   * 获取当前选中的分类信息
   * @returns 当前选中的分类信息
   */
  public getCurrentSelectedCategory(): { parentCategory?: string; subCategory?: string } | undefined {
    return this.currentSelectedCategory ? { ...this.currentSelectedCategory } : undefined;
  }

  /**
   * 设置当前选中的分类信息
   */
  public setCurrentSelectedCategory(category?: { parentCategory?: string; subCategory?: string }): void {
    this.currentSelectedCategory = category ? { ...category } : undefined;
  }

  /**
   * 清除当前选中的分类信息
   */
  public clearCurrentSelectedCategory(): void {
    this.currentSelectedCategory = undefined;
  }

  /**
   * 生成设备分类树结构 - 从数据库获取设备分类树
   */
  public async generateCategoryTree(inventoryList: InventoryItem[], forceRefresh: boolean = false): Promise<DeviceCategory[]> {
    try {
      // 从数据库获取设备分类树 - 使用缓存机制
      const deviceCategories = await fetchCategoryTree(forceRefresh);

      // 无论设备列表是否为空，都更新分类树计数
      // 这样可以确保在删除所有设备后，分类树计数也会被重置为0
      // 使用类方法而不是外部函数，避免混淆
      const updatedCategories = this.updateCategoryTreeCounts(deviceCategories, inventoryList, true, true) || deviceCategories;

      return updatedCategories;
    } catch (error) {
      console.error('获取设备分类树失败:', error);

      // 如果获取失败，初始化空的分类树
      const emptyCategories: DeviceCategory[] = [{
        id: 'all',
        name: '全部设备',
        count: inventoryList.length,
        children: []
      }];

      return emptyCategories;
    }
  }

  /**
   * 更新设备分类树计数（带防抖机制）
   * @param deviceCategories 当前设备分类树
   * @param inventoryList 设备列表
   * @param forceUpdate 是否强制更新UI，即使计数没有变化
   * @param immediate 是否立即执行，忽略防抖
   */
  public updateCategoryTreeCounts(
    deviceCategories: DeviceCategory[], 
    inventoryList: InventoryItem[], 
    forceUpdate: boolean = false, 
    immediate: boolean = false
  ): DeviceCategory[] | null {
    // 如果正在更新计数，跳过重复请求
    if (this.isCountUpdateInProgress && !immediate) {
      return null;
    }

    // 清除之前的防抖定时器
    if (this.countUpdateTimeout) {
      clearTimeout(this.countUpdateTimeout);
      this.countUpdateTimeout = null;
    }

    const executeUpdate = (): DeviceCategory[] | null => {
      if (this.isCountUpdateInProgress) {
        return null;
      }

      this.isCountUpdateInProgress = true;

      try {
        // 如果设备分类树为空，直接返回
        if (deviceCategories.length === 0) {
          return null;
        }

        // 使用外部工具函数更新设备分类树中的设备数量
        const updatedCategories = updateCategoryTreeCounts(
          deviceCategories,
          inventoryList.map(item => item.rawData || {})
        );

        // 检查计数是否有变化
        let hasChanges = false;

        // 递归检查节点计数是否有变化
        const checkForChanges = (oldNodes: DeviceCategory[], newNodes: DeviceCategory[]): boolean => {
          if (oldNodes.length !== newNodes.length) return true;

          for (let i = 0; i < oldNodes.length; i++) {
            if (oldNodes[i].count !== newNodes[i].count) return true;

            const oldChildren = oldNodes[i].children || [];
            const newChildren = newNodes[i].children || [];
            if (checkForChanges(oldChildren, newChildren)) return true;
          }
          return false;
        };

        hasChanges = checkForChanges(deviceCategories, updatedCategories);

        // 如果有变化或强制更新，则返回更新后的分类树
        if (hasChanges || forceUpdate) {
          return updatedCategories;
        }

        return null;
      } catch (countError) {
        console.error('更新设备分类树计数失败:', countError);
        return null;
      } finally {
        this.isCountUpdateInProgress = false;
      }
    };

    // 如果是立即执行或强制更新，直接执行
    if (immediate || forceUpdate) {
      return executeUpdate();
    } else {
      // 使用防抖机制，300ms后执行
      this.countUpdateTimeout = setTimeout(() => {
        executeUpdate();
      }, 300);
      return null;
    }
  }

  /**
   * 重新计算分类树计数
   * @deprecated 建议使用 updateCategoryTreeCounts(deviceCategories, inventoryList, true, true) 替代
   * @param deviceCategories 当前设备分类树
   * @param inventoryList 设备列表
   */
  public recalculateCategoryTreeCounts(deviceCategories: DeviceCategory[], inventoryList: InventoryItem[]): DeviceCategory[] {
    try {
      // 获取当前设备分类树
      const categories = [...deviceCategories];

      // 如果设备分类树为空，直接返回
      if (categories.length === 0) {
        return categories;
      }

      // 使用类方法更新设备分类树中的设备数量
      const updatedCategories = this.updateCategoryTreeCounts(categories, inventoryList, true, true) || categories;

      return updatedCategories;
    } catch (countError) {
      console.error('重新计算设备分类树计数失败:', countError);
      return deviceCategories;
    }
  }

  /**
   * 根据二级分类名称查找所属的一级分类名称
   * @param subCategoryName 二级分类名称
   * @param deviceCategories 设备分类树
   */
  public findParentCategoryBySubCategory(subCategoryName: string, deviceCategories: DeviceCategory[]): string | null {
    for (const parentCategory of deviceCategories) {
      if (parentCategory.children) {
        for (const subCategory of parentCategory.children) {
          if (subCategory.name === subCategoryName) {
            return parentCategory.name;
          }
        }
      }
    }
    return null;
  }

  /**
   * 在分类树中查找指定的分类
   * @param categories 分类树
   * @param categoryId 分类ID
   */
  public findCategory(categories: DeviceCategory[], categoryId: string): DeviceCategory | undefined {
    for (const category of categories) {
      if (category.id === categoryId) {
        return category;
      }
      if (category.children?.length) {
        const found = this.findCategory(category.children, categoryId);
        if (found) return found;
      }
    }
    return undefined;
  }

  /**
   * 获取分类下的所有子分类ID（包括自身）
   * @param category 分类
   */
  public getAllChildCategoryIds(category: DeviceCategory): string[] {
    const ids = [category.id];
    if (category.children?.length) {
      category.children.forEach(child => {
        ids.push(...this.getAllChildCategoryIds(child));
      });
    }
    return ids;
  }

  /**
   * 初始化空的分类树
   * @param inventoryListLength 设备列表长度
   */
  public createEmptyCategories(inventoryListLength: number): DeviceCategory[] {
    return [{
      id: 'all',
      name: '全部设备',
      count: inventoryListLength,
      children: []
    }];
  }
}

export default InventoryCategoryService;
