import { BaseService, BaseServiceState, BaseServiceEvents, AppError } from '../base/baseService';
import { DepartmentCategory } from './department/types';
import { logError } from '../../utils/errorHandler';
import DepartmentService from './departmentService';

// 部门修改服务状态接口
export interface DepartmentModifyServiceState extends BaseServiceState {
  isModifying: boolean;
}

// 部门修改服务事件类型
export interface DepartmentModifyServiceEvents extends BaseServiceEvents<DepartmentModifyServiceState> {
  'node-renamed': { nodeId: string, newName: string };
}

/**
 * 部门修改服务
 * 处理部门和人员的重命名功能
 */
class DepartmentModifyService extends BaseService<DepartmentModifyServiceState, DepartmentModifyServiceEvents> {
  private static instance: DepartmentModifyService;
  private departmentService: DepartmentService;

  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      isModifying: false
    });
    this.departmentService = DepartmentService.getInstance();
  }

  /**
   * 获取部门修改服务实例
   * @returns 部门修改服务实例
   */
  public static getInstance(): DepartmentModifyService {
    if (!DepartmentModifyService.instance) {
      DepartmentModifyService.instance = new DepartmentModifyService();
    }
    return DepartmentModifyService.instance;
  }

  /**
   * 从部门树中查找节点
   * @param categories 部门分类树
   * @param nodeId 节点ID
   * @returns 找到的节点或null
   */
  private findNodeById(categories: DepartmentCategory[], nodeId: string): DepartmentCategory | null {
    for (const category of categories) {
      if (category.id === nodeId) {
        return category;
      }
      if (category.children?.length) {
        const found = this.findNodeById(category.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 从人员节点中提取人员信息
   * @param categories 部门分类树
   * @param personId 人员节点ID
   * @returns 人员信息或null
   */
  private extractPersonInfo(categories: DepartmentCategory[], personId: string): { name: string, alias?: string } | null {
    for (const category of categories) {
      if (category.id === personId) {
        // 从人员名称中提取备注（如果有）
        const fullName = category.name;
        let personName = fullName;
        let alias = '';

        // 如果名称中包含备注（格式为"姓名 (备注)"或"姓名 （备注）"）
        // 支持中文和英文括号
        const nameMatch = fullName.match(/^([^(（]+)/);
        if (nameMatch) {
          personName = nameMatch[1].trim();
        }

        // 分别匹配英文括号和中文括号中的备注
        const englishAliasMatch = fullName.match(/\(([^)]+)\)/);
        const chineseAliasMatch = fullName.match(/（([^）]+)）/);

        if (englishAliasMatch) {
          alias = englishAliasMatch[1];
        } else if (chineseAliasMatch) {
          alias = chineseAliasMatch[1];
        }

        // 处理特殊情况：如果名称包含括号但提取失败
        if ((fullName.includes('(') || fullName.includes('（')) && !alias) {
          console.log('警告: 名称中包含括号但无法提取备注，尝试使用更宽松的方法');

          // 尝试使用更宽松的方法
          const lastOpenBracketIndex = Math.max(fullName.lastIndexOf('('), fullName.lastIndexOf('（'));
          if (lastOpenBracketIndex > 0) {
            // 提取括号前的部分作为名称
            personName = fullName.substring(0, lastOpenBracketIndex).trim();

            // 提取括号中的内容作为备注
            const bracketContent = fullName.substring(lastOpenBracketIndex + 1);
            const closeBracketIndex = Math.min(
              bracketContent.indexOf(')') >= 0 ? bracketContent.indexOf(')') : Number.MAX_SAFE_INTEGER,
              bracketContent.indexOf('）') >= 0 ? bracketContent.indexOf('）') : Number.MAX_SAFE_INTEGER
            );

            if (closeBracketIndex < Number.MAX_SAFE_INTEGER) {
              alias = bracketContent.substring(0, closeBracketIndex);
            }
          }
        }

        console.log(`提取人员信息: 完整名称="${fullName}", 姓名="${personName}", 备注="${alias}"`);

        // 确保名称字段只包含 UTF-8 格式的纯文本，不包含括号和备注部分
        return {
          name: personName, // 纯文本名称，不包含括号和备注部分
          // 如果备注为空字符串，返回undefined
          alias: alias ? alias : undefined
        };
      }
      if (category.children?.length) {
        const found = this.extractPersonInfo(category.children, personId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 重命名部门或人员
   * @param nodeId 节点ID
   * @param newName 新名称
   * @returns 更新后的部门树
   */
  public async renameDepartmentOrPerson(nodeId: string, newName: string): Promise<DepartmentCategory[]> {
    try {
      this.updateState({ isLoading: true, isModifying: true });

      // 不允许重命名根节点
      if (nodeId === 'all-dept') {
        throw new Error('不能重命名公司名称');
      }

      const departmentCategories = this.departmentService.getState().departmentCategories;

      if (nodeId.startsWith('dept-')) {
        // 更新部门
        // 使用findNodeById方法查找部门
        const department = this.findNodeById(departmentCategories, nodeId);
        if (!department) {
          throw new Error(`未找到部门: ${nodeId}`);
        }

        // 创建路径解析器并获取当前部门路径
        const { DepartmentPathResolver } = require('./department/pathResolver');
        const pathResolver = new DepartmentPathResolver(departmentCategories);
        const currentDepartmentPath = pathResolver.getApiPath(department);

        // 构建新的部门路径
        const pathParts = currentDepartmentPath.split('/');
        pathParts[pathParts.length - 1] = newName; // 替换最后一部分为新名称
        const newDepartmentPath = pathParts.join('/');

        // 使用新的API格式更新部门
        const params = {
          action: "update_department",
          action_params: {
            department_path: currentDepartmentPath,
            new_department_path: newDepartmentPath
          }
        };

        // 提交任务并等待结果
        await this.submitTask('DbFun', params);

        // 触发节点重命名事件
        this.emitter.emit('node-renamed', { nodeId, newName });
      } else if (nodeId.startsWith('person-')) {
        // 更新人员
        // 使用extractPersonInfo方法提取原人员信息
        const personInfo = this.extractPersonInfo(departmentCategories, nodeId);
        if (!personInfo) {
          throw new Error(`未找到人员: ${nodeId}`);
        }

        // 从新名称中提取实际名称和备注
        let newUserName = newName;
        let newAlias = ''; // 默认新备注为空

        // 检查新名称是否包含备注格式（支持中文和英文括号）
        // 先提取括号前的部分作为名称
        const newNameMatch = newName.match(/^([^(（]+)/);

        // 分别匹配英文括号和中文括号中的备注
        const englishAliasMatch = newName.match(/\(([^)]+)\)/);
        const chineseAliasMatch = newName.match(/（([^）]+)）/);

        if (newNameMatch) {
          newUserName = newNameMatch[1].trim();
        }

        // 如果新名称中包含备注（括号部分），则提取备注
        if (englishAliasMatch) {
          newAlias = englishAliasMatch[1];
        } else if (chineseAliasMatch) {
          newAlias = chineseAliasMatch[1];
        }
        // 如果新名称中不包含备注，则备注为空字符串

        console.log(`检测备注: 英文括号=${!!englishAliasMatch}, 中文括号=${!!chineseAliasMatch}, 提取的备注="${newAlias}"`);

        // 如果名称中包含括号但提取失败，尝试使用更宽松的方法
        if ((newName.includes('(') || newName.includes('（')) && !newAlias) {
          console.log('警告: 新名称中包含括号但无法提取备注，尝试使用更宽松的方法');

          // 尝试使用更宽松的正则表达式
          const lastOpenBracketIndex = Math.max(newName.lastIndexOf('('), newName.lastIndexOf('（'));
          if (lastOpenBracketIndex > 0) {
            // 提取括号前的部分作为名称
            newUserName = newName.substring(0, lastOpenBracketIndex).trim();

            // 提取括号中的内容作为备注
            const bracketContent = newName.substring(lastOpenBracketIndex + 1);
            const closeBracketIndex = Math.min(
              bracketContent.indexOf(')') >= 0 ? bracketContent.indexOf(')') : Number.MAX_SAFE_INTEGER,
              bracketContent.indexOf('）') >= 0 ? bracketContent.indexOf('）') : Number.MAX_SAFE_INTEGER
            );

            if (closeBracketIndex < Number.MAX_SAFE_INTEGER) {
              newAlias = bracketContent.substring(0, closeBracketIndex);
              console.log(`使用备用方法提取备注: 名称="${newUserName}", 备注="${newAlias}"`);
            }
          }
        }

        console.log(`解析新名称: 原姓名="${personInfo.name}", 原备注="${personInfo.alias || ''}", 新姓名="${newUserName}", 新备注="${newAlias}"`);

        // 获取人员所在部门的路径信息
        const currentDepartmentNode = this.findParentDepartment(departmentCategories, nodeId);
        if (!currentDepartmentNode) {
          throw new Error(`未找到人员的所属部门: ${nodeId}`);
        }

        // 创建路径解析器并获取部门路径信息
        const { DepartmentPathResolver } = require('./department/pathResolver');
        const pathResolver = new DepartmentPathResolver(departmentCategories);
        const currentDepartmentPath = pathResolver.getApiPath(currentDepartmentNode);

        // 确保名称字段只包含 UTF-8 格式的纯文本，不包含括号和备注部分
        // 使用新的API格式更新人员
        const params: any = {
          action: "update_person",
          action_params: {
            user_name: personInfo.name, // 原姓名，纯文本
            alias: personInfo.alias || '', // 原备注，如果没有则传空字符串
            current_department_path: currentDepartmentPath, // 当前部门路径
            new_user_name: newUserName, // 新姓名，纯文本（不包含括号和备注部分）
            new_alias: newAlias // 新备注，如果没有则传空字符串
          }
        };

        console.log('开始更新人员:', JSON.stringify(params));

        // 提交任务并等待结果
        await this.submitTask('DbFun', params);

        // 触发节点重命名事件
        this.emitter.emit('node-renamed', { nodeId, newName });
      } else {
        throw new Error(`不支持的节点类型: ${nodeId}`);
      }

      // 强制更新部门树
      console.log('节点重命名成功，强制刷新部门树');
      await this.departmentService.loadDepartmentTree(true);

      return this.departmentService.getState().departmentCategories;
    } catch (error) {
      logError(error, 'DepartmentModifyService.renameDepartmentOrPerson');
      throw this.handleError(error);
    } finally {
      this.updateState({ isLoading: false, isModifying: false });
    }
  }

  /**
   * 重写错误处理方法，增加日志记录
   * @param error 错误对象
   * @returns 标准化的AppError
   */
  protected handleError(error: unknown): AppError {
    // 记录错误
    logError(error, `DepartmentModifyService`);

    // 调用父类的错误处理方法
    return super.handleError(error);
  }
}

export default DepartmentModifyService;
