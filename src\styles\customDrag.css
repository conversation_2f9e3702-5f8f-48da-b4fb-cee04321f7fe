/* 自定义拖拽样式 */

/* 拖拽时的光标样式 */
.custom-dragging {
  cursor: move !important;
}

/* 可放置目标的样式 */
.custom-drag-over {
  background-color: #dbeafe !important;
  border: 1px solid #93c5fd !important;
  border-radius: 4px !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

/* 不可放置目标的样式 */
.custom-drag-no-drop {
  background-color: #fee2e2 !important;
  border: 1px solid #fca5a5 !important;
  border-radius: 4px !important;
  cursor: not-allowed !important;
}

/* 拖拽中的元素样式 */
.custom-drag-source {
  opacity: 0.5 !important;
}

/* 拖拽图像样式 */
.custom-drag-image {
  position: absolute;
  pointer-events: none;
  z-index: 9999;
  opacity: 0.7;
  background-color: #f0f9ff;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 4px;
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
