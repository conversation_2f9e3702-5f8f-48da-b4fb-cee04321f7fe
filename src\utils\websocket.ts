import { mapTaskStatus } from './taskUtils';

type EventCallback = (data?: any) => void;

class WebSocketManager {
    private static instance: WebSocketManager;
    private ws: WebSocket | null = null;
    private reconnectTimer: number | null = null;
    private url: string = 'ws://localhost:9002';
    private eventListeners: Map<string, EventCallback[]> = new Map();
    private connectionPromise: Promise<void> | null = null;
    private connectionResolve: (() => void) | null = null;
    private connectionReject: ((error: any) => void) | null = null;

    // 添加分块数据处理相关的属性
    private chunkBuffer: Map<string, {
        totalSize: number,
        chunks: Map<number, string>,
        receivedSize: number,
        contentType: string
    }> = new Map();

    private constructor() {
        this.eventListeners = new Map();
    }

    public static getInstance(): WebSocketManager {
        if (!WebSocketManager.instance) {
            WebSocketManager.instance = new WebSocketManager();
        }
        return WebSocketManager.instance;
    }

    public on(event: string, callback: EventCallback): void {
        const listeners = this.eventListeners.get(event) || [];
        listeners.push(callback);
        this.eventListeners.set(event, listeners);
    }

    public off(event: string, callback: EventCallback): void {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index !== -1) {
                listeners.splice(index, 1);
                this.eventListeners.set(event, listeners);
            }
        }
    }

    private emit(event: string, data?: any): void {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => callback(data));
        }
    }

    // Base64解码函数
    private base64Decode(str: string): string {
        try {
            // 使用浏览器内置的atob函数
            return atob(str);
        } catch (e) {
            this.emit('error', 'Base64解码失败: ' + e);
            return '';
        }
    }

    private handleChunkedMessage(data: any) {
        try {
            const messageType = data.type;
            const taskId = data.taskId || 'default';

            switch (messageType) {
                case 'chunk_start':
                    // 初始化新的分块传输
                    this.chunkBuffer.set(taskId, {
                        totalSize: data.total_size,
                        chunks: new Map(),
                        receivedSize: 0,
                        contentType: data.content_type || 'text'
                    });
                    break;

                case 'chunk_data':
                    // 处理数据块
                    const bufferInfo = this.chunkBuffer.get(taskId);
                    if (bufferInfo) {
                        bufferInfo.chunks.set(data.offset, data.data);
                        bufferInfo.receivedSize += data.data.length;

                        // 发送进度事件
                        const progress = (bufferInfo.receivedSize / bufferInfo.totalSize) * 100;
                        this.emit('progress', { taskId, progress });
                    }
                    break;

                case 'chunk_end':
                    // 合并所有数据块
                    const finalBufferInfo = this.chunkBuffer.get(taskId);
                    if (finalBufferInfo) {
                        let result = '';
                        const sortedOffsets = Array.from(finalBufferInfo.chunks.keys()).sort((a, b) => a - b);

                        for (const offset of sortedOffsets) {
                            result += finalBufferInfo.chunks.get(offset);
                        }

                        // 清理缓存
                        this.chunkBuffer.delete(taskId);

                        // 如果是JSON类型,需要先解码Base64然后解析JSON
                        if (finalBufferInfo.contentType === 'json') {
                            try {
                                const decodedResult = this.base64Decode(result);
                                const parsedResult = JSON.parse(decodedResult);
                                this.emit('message', parsedResult);
                            } catch (e) {
                                this.emit('error', e);
                                this.emit('info', '解析JSON数据失败: ' + e);
                            }
                        } else {
                            this.emit('message', { result });
                        }
                    }
                    break;

                default:
                    // 处理普通消息
                    if (data.error) {
                        this.emit('error', data.error);
                        this.emit('info', '错误: ' + data.error);
                    } else {
                        this.emit('message', data);
                    }
                    break;
            }
        } catch (e) {
            this.emit('error', e);
            this.emit('info', '处理分块消息失败: ' + e);
        }
    }

    private handleWebSocketMessage(event: MessageEvent) {
        try {
            const response = JSON.parse(event.data);
            if (response.type && ['chunk_start', 'chunk_data', 'chunk_end'].includes(response.type)) {
                this.handleChunkedMessage(response);
            } else if (response.type === 'task_update') {
                // 处理任务状态更新通知
                this.emit('task_update', {
                    id: response.taskId,
                    status: mapTaskStatus(response.status),
                    progress: response.progress || 0,
                    result: response.result,
                    error: response.error
                });
            } else if (response.type === 'execute_response') {
                // 处理统一API响应
                this.emit('message', response);

                // 如果有错误信息，也发送错误事件
                if (response.status === 'error' && response.message) {
                    this.emit('error', response.message);
                    this.emit('info', '错误: ' + response.message);
                }
            } else {
                // 未知消息类型，记录日志但仍然发送消息事件
                this.emit('info', '收到未知类型的消息: ' + JSON.stringify(response));
                this.emit('message', response);
            }
        } catch (e) {
            this.emit('error', e);
            this.emit('info', '解析消息失败: ' + e);
        }
    }

    public connect(): Promise<void> {
        // 如果已经连接，直接返回成功
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.emit('info', 'WebSocket已经连接');
            return Promise.resolve();
        }

        // 如果正在连接中，返回现有的Promise
        if (this.connectionPromise) {
            this.emit('info', 'WebSocket正在连接中，复用现有连接Promise');
            return this.connectionPromise;
        }

        // 如果WebSocket实例存在但状态不是OPEN，先关闭它
        if (this.ws) {
            this.emit('info', `关闭现有WebSocket连接(状态: ${this.ws.readyState})`);
            this.ws.close();
            this.ws = null;
        }

        this.connectionPromise = new Promise<void>((resolve, reject) => {
            this.connectionResolve = resolve;
            this.connectionReject = reject;

            try {
                this.ws = new WebSocket(this.url);

                this.ws.onopen = () => {
                    this.emit('connected');
                    this.emit('status', '已连接');
                    this.emit('info', 'WebSocket连接成功');
                    if (this.connectionResolve) {
                        this.connectionResolve();
                    }
                    this.connectionPromise = null;
                    this.connectionResolve = null;
                    this.connectionReject = null;
                };

                this.ws.onclose = () => {
                    this.emit('disconnected');
                    this.emit('status', '未连接');
                    this.emit('info', 'WebSocket连接关闭');
                    this.ws = null;
                    if (this.connectionReject) {
                        this.connectionReject(new Error('WebSocket连接关闭'));
                    }
                    this.connectionPromise = null;
                    this.connectionResolve = null;
                    this.connectionReject = null;
                    this.startReconnect();
                };

                this.ws.onerror = (error) => {
                    this.emit('error', error);
                    this.emit('info', 'WebSocket错误: ' + error);
                    this.ws = null;
                    if (this.connectionReject) {
                        this.connectionReject(error);
                    }
                    this.connectionPromise = null;
                    this.connectionResolve = null;
                    this.connectionReject = null;
                };

                this.ws.onmessage = this.handleWebSocketMessage.bind(this);
            } catch (e) {
                this.emit('error', e);
                this.emit('info', '连接失败: ' + e);
                if (this.connectionReject) {
                    this.connectionReject(e);
                }
                this.connectionPromise = null;
                this.connectionResolve = null;
                this.connectionReject = null;
            }
        });

        return this.connectionPromise;
    }

    public disconnect(): void {
        if (this.reconnectTimer) {
            window.clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.ws) {
            this.ws.close();
            this.ws = null;
            this.emit('status', '未连接');
            this.emit('info', 'WebSocket已断开');
        }
    }

    public async send(message: any): Promise<void> {
        // 如果未连接，尝试连接
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            this.emit('info', '发送消息前尝试连接WebSocket');
            try {
                await this.connect();
            } catch (error) {
                this.emit('error', `连接失败，无法发送消息: ${error.message || '未知错误'}`);
                throw new Error('未连接到服务器，无法发送消息');
            }
        }

        try {
            this.ws!.send(JSON.stringify(message));
        } catch (e) {
            const errorMsg = e instanceof Error ? e.message : String(e);
            this.emit('error', e);
            this.emit('info', '发送消息失败: ' + errorMsg);
            throw e; // 重新抛出错误，让调用者知道发送失败
        }
    }

    private startReconnect(): void {
        if (this.reconnectTimer) return;

        this.reconnectTimer = window.setTimeout(() => {
            this.reconnectTimer = null;
            if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                this.connect().catch(error => {
                    this.emit('error', `重连失败: ${error.message || '未知错误'}`);
                    // 如果重连失败，继续尝试重连
                    this.startReconnect();
                });
            }
        }, 5000);
    }

    public isConnected(): boolean {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
    }

    // 使用工具函数中的mapTaskStatus
}

export default WebSocketManager;