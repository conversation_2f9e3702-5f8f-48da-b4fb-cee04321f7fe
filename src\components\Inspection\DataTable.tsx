import React from 'react';

/**
 * 表格数据行的基础接口
 */
export interface TableRecord {
  [key: string]: unknown;
}

/**
 * 表格列定义接口
 */
export interface TableColumn<T extends TableRecord = TableRecord> {
  key: string;
  title: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: unknown, record: T, index: number) => React.ReactNode;
}

/**
 * 数据表格组件的属性接口
 */
interface DataTableProps<T extends TableRecord = TableRecord> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  className?: string;
}

/**
 * 数据表格组件
 * 提供统一的Vben风格表格，消除重复代码
 */
const DataTable = <T extends TableRecord = TableRecord>({
  columns,
  data,
  loading = false,
  emptyText = '暂无数据',
  emptyIcon,
  className = ''
}: DataTableProps<T>) => {
  return (
    <div className={`flex-1 px-6 pb-6 ${className}`}>
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={`px-4 py-3 text-${column.align || 'left'} text-xs font-medium text-gray-500 uppercase tracking-wider ${column.width || ''}`}
                  >
                    {column.title}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.length === 0 ? (
                <tr>
                  <td colSpan={columns.length} className="px-4 py-12 text-center">
                    <div className="flex flex-col items-center">
                      {emptyIcon}
                      <p className="text-gray-500 text-sm">
                        {loading ? '正在加载...' : emptyText}
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                data.map((record, index) => (
                  <tr key={record.id || index} className="hover:bg-gray-50 transition-colors">
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className={`px-4 py-3 whitespace-nowrap text-${column.align || 'left'} text-sm`}
                      >
                        {column.render
                          ? column.render(record[column.key], record, index)
                          : record[column.key] || '-'
                        }
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DataTable;
