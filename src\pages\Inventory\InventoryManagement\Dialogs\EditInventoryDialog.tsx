import React, { useState, useEffect, useRef } from 'react';
import { InventoryItem, ExtFieldDefinition } from '../../../../types/inventory';
import InventoryForm from './InventoryForm';
import { useInventory } from '../../../../hooks/Inventory/useInventory';
import DialogBase from '../../../../components/ui/DialogBase';
import { Alert, Spin } from 'antd';

interface EditInventoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (id: string, updates: Partial<InventoryItem>) => Promise<void>;
  item: InventoryItem | null;
  extFields?: ExtFieldDefinition[];  // 扩展字段定义
  extFieldValues?: Record<string, any>;  // 扩展字段值
  onExtFieldChange?: (key: string, value: any) => void;  // 扩展字段变更回调
}

const EditInventoryDialog: React.FC<EditInventoryDialogProps> = ({
  isOpen,
  onClose,
  onUpdate,
  item,
  extFields = [],
  extFieldValues = {},
  onExtFieldChange
}) => {
  // 状态管理
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  // 表单引用
  const formRef = useRef<HTMLDivElement>(null);

  // 获取扩展字段相关函数（使用表单扩展字段）
  const { formExtFields, clearFormExtFields } = useInventory();

  // 在对话框关闭时清除扩展字段和状态
  const handleClose = () => {
    // 清除表单扩展字段
    clearFormExtFields();
    // 清除错误状态
    setApiError(null);
    setIsLoading(false);
    setProgress(0);
    // 调用原有的关闭函数
    onClose();
  };

  // 添加键盘事件监听，按ESC键关闭对话框
  useEffect(() => {
    // 只有在对话框打开时才添加事件监听
    if (isOpen) {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          handleClose();
        }
      };

      window.addEventListener('keydown', handleKeyDown);

      // 清理函数
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isOpen]);

  // 如果对话框未打开或没有编辑项，则不渲染内容
  if (!isOpen || !item) return null;

  // 处理表单提交
  const handleSubmit = async (data: Omit<InventoryItem, 'id'>) => {
    try {
      setIsLoading(true);
      setApiError(null);
      setProgress(0);

      // 模拟进度更新
      setProgress(20);

      // 过滤掉空值字段，只保留有值的字段
      const filteredData: Record<string, any> = {};

      // 遍历表单数据，只保留非空字段
      Object.entries(data).forEach(([key, value]) => {
        // 判断值是否为空
        const isEmpty =
          value === undefined ||
          value === null ||
          (typeof value === 'string' && value.trim() === '');

        // 如果不为空，则保留
        if (!isEmpty) {
          filteredData[key] = value;
        }
      });

      setProgress(50);

      // 如果有扩展字段值，添加到更新数据中
      if (extFieldValues && Object.keys(extFieldValues).length > 0) {
        console.log('添加扩展字段值到更新数据:', extFieldValues);
        filteredData.extendedFields = extFieldValues;
      }

      setProgress(80);

      console.log('过滤后的更新数据:', filteredData);
      await onUpdate(item.id, filteredData);

      setProgress(100);
      handleClose(); // 成功更新后关闭对话框
    } catch (error) {
      console.error('更新设备失败:', error);
      setApiError(error instanceof Error ? error.message : '更新设备失败，请重试');
    } finally {
      setIsLoading(false);
      setProgress(0);
    }
  };

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleClose}
      width="100%"
      maxWidth="64rem"
      maxHeight="95vh"
      autoFocus={true}
      restoreFocus={true}
      closeOnOverlayClick={false}
    >
      <div className="flex flex-col h-full max-h-[95vh]">
        {/* 对话框标题 - 固定高度 */}
        <div className="flex-shrink-0 flex justify-between items-center px-4 sm:px-6 py-3 border-b">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800">编辑设备台账 - {item.name}</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 focus-ring rounded-full p-1 flex-shrink-0"
            aria-label="关闭对话框"
          >
            <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 - 可滚动区域 */}
        <div className="flex-1 overflow-y-auto min-h-0 px-4 sm:px-6 py-4">{/* 响应式内边距 */}

          {/* 错误提示 */}
          {apiError && (
            <Alert
              message="错误"
              description={apiError}
              type="error"
              showIcon
              className="mb-3 sm:mb-4"
              closable
              onClose={() => setApiError(null)}
            />
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="mb-3 sm:mb-4 flex items-center space-x-2">
              <Spin />
              <span className="text-blue-600 text-sm sm:text-base">正在更新设备信息 ({Math.round(progress)}%)</span>
            </div>
          )}

          {/* 表单内容区域 - 不包含按钮 */}
          <div ref={formRef}>
            <InventoryForm
              initialData={item}
              extFields={formExtFields}
              extFieldValues={extFieldValues}
              onExtFieldChange={onExtFieldChange}
              onSubmit={handleSubmit}
              onCancel={handleClose}
              disabled={isLoading}
              disableTypeSelection={true} // 编辑模式下禁用设备类型选择
              hideButtons={true} // 新增属性，隐藏表单内的按钮
            />
          </div>
        </div>

        {/* 对话框底部按钮 - 固定在底部，与导出菜单保持一致 */}
        <div className="flex-shrink-0 px-3 sm:px-4 py-2.5 border-t bg-white flex justify-end space-x-3">
          <button
            onClick={handleClose}
            className="px-6 py-3 border border-gray-300 rounded text-base font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            disabled={isLoading}
          >
            取消
          </button>
          <button
            onClick={() => {
              // 触发表单提交 - 使用ref确保选择正确的表单
              const form = formRef.current?.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
            className="px-6 py-3 bg-blue-600 text-white rounded text-base font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            disabled={isLoading}
          >
            保存
          </button>
        </div>
      </div>
    </DialogBase>
  );
};

export default EditInventoryDialog;