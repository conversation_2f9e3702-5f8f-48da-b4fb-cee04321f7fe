import React from 'react';
import DialogBase from '../../../../components/ui/DialogBase';

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  onCancel?: () => void; // 添加取消回调
  itemsCount: number;
  isLoading?: boolean;
  isSingleDelete?: boolean; // 是否是单行删除
}

const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  onCancel,
  itemsCount,
  isLoading = false,
  isSingleDelete = false
}) => {
  // 处理取消操作
  const handleCancel = () => {
    // 如果提供了取消回调，则调用
    if (onCancel) {
      onCancel();
    }
    // 关闭对话框
    onClose();
  };

  // 使用DialogBase组件，不需要手动添加ESC键监听

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  // 处理确认删除
  const handleConfirm = async () => {
    try {
      await onConfirm();
      onClose(); // 成功删除后关闭对话框
    } catch (error) {
      console.error('删除设备失败:', error);
      // 错误处理可以在这里添加，例如显示错误消息
    }
  };

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleCancel}
      width="100%"
      maxWidth="28rem"
      animation="fade"
    >
        <div className="p-6">
          {/* 对话框标题 */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800">确认删除</h2>
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 确认信息 */}
          <div className="mb-6">
            <p className="text-gray-700">
              {isSingleDelete
                ? '您确定要删除当前行的设备吗？'
                : itemsCount === 1
                  ? '您确定要删除选中的设备吗？'
                  : <>您确定要删除选中的 <span className="text-red-600 font-medium">{itemsCount}</span> 台设备吗？</>}
            </p>
            {/* <p className="text-red-600 text-sm mt-2">
              此操作不可逆，删除后数据将无法恢复。
            </p> */}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              disabled={isLoading}
              className={`px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500
                ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              disabled={isLoading}
              className={`px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500
                ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoading ? '删除中...' : '确认删除'}
            </button>
          </div>
        </div>
    </DialogBase>
  );
};

export default DeleteConfirmDialog;