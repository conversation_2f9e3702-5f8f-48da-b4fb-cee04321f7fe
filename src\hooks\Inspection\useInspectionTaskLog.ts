import { useState, useEffect, useCallback } from 'react';
import InspectionTaskLogService, {
  InspectionTaskLogServiceState,
  InspectionTaskLogItem,
  InspectionTaskLogFilter
} from '../../services/Inspection/inspectionTaskLogService';

// 重新导出类型，这样UI层可以从Hook导入，而不是直接从Service层导入
export type { InspectionTaskLogItem, InspectionTaskLogFilter };

/**
 * 巡检任务日志Hook返回值接口
 */
interface UseInspectionTaskLogReturn {
  // 状态
  isLoading: boolean;
  error?: string;
  logs: InspectionTaskLogItem[];
  filter: InspectionTaskLogFilter;
  filteredLogs: InspectionTaskLogItem[];

  // 分页相关
  currentPage: number;
  pageSize: number;
  totalPages: number;
  paginatedLogs: InspectionTaskLogItem[];

  // 方法
  getInspectionTaskLogs: (logType?: number) => Promise<InspectionTaskLogItem[]>;
  forceRefreshInspectionTaskLogs: (logType?: number) => Promise<InspectionTaskLogItem[]>;
  setFilter: (filter: Partial<InspectionTaskLogFilter>) => void;
  clearFilter: () => void;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;

  // 辅助方法
  getLogTypes: () => string[];
  getPersons: () => string[];
  getTaskNames: () => string[];
}

/**
 * 巡检任务日志Hook
 * 提供巡检任务日志功能和状态管理
 */
export function useInspectionTaskLog(): UseInspectionTaskLogReturn {
  // 获取服务实例
  const service = InspectionTaskLogService.getInstance();

  // 状态
  const [state, setState] = useState<InspectionTaskLogServiceState>(service.getState());

  // 分页状态
  const [currentPage, setCurrentPage] = useState<number>(0); // 0-based index
  const [pageSize, setPageSize] = useState<number>(10); // 默认每页10条

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: InspectionTaskLogServiceState) => {
      setState(newState);
    };

    // 订阅状态变化事件
    service.on('state-change', handleStateChange);

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
    };
  }, [service]);

  // 获取巡检任务日志
  const getInspectionTaskLogs = useCallback(async (logType?: number) => {
    return await service.getInspectionTaskLogs(logType);
  }, [service]);

  // 强制刷新巡检任务日志（忽略缓存）
  const forceRefreshInspectionTaskLogs = useCallback(async (logType?: number) => {
    return await service.forceRefreshInspectionTaskLogs(logType);
  }, [service]);

  // 设置过滤条件
  const setFilter = useCallback((filter: Partial<InspectionTaskLogFilter>) => {
    service.setFilter(filter);
  }, [service]);

  // 清除所有过滤条件
  const clearFilter = useCallback(() => {
    service.clearFilter();
  }, [service]);

  // 获取所有日志类型
  const getLogTypes = useCallback(() => {
    return service.getLogTypes();
  }, [service]);

  // 获取所有责任人
  const getPersons = useCallback(() => {
    return service.getPersons();
  }, [service]);

  // 获取所有任务名称
  const getTaskNames = useCallback(() => {
    return service.getTaskNames();
  }, [service]);

  // 计算分页数据
  const getPaginatedLogs = useCallback(() => {
    // 如果pageSize为-1，表示显示全部
    if (pageSize === -1) {
      return state.filteredLogs;
    }

    const start = currentPage * pageSize;
    const end = start + pageSize;
    return state.filteredLogs.slice(start, end);
  }, [state.filteredLogs, currentPage, pageSize]);

  // 计算总页数
  const getTotalPages = useCallback(() => {
    if (pageSize === -1) {
      return 1; // 显示全部时只有1页
    }
    return Math.ceil(state.filteredLogs.length / pageSize);
  }, [state.filteredLogs.length, pageSize]);

  // 处理页码变化
  const handleSetPage = useCallback((page: number) => {
    const totalPages = getTotalPages();
    if (page >= 0 && page < totalPages) {
      setCurrentPage(page);
    }
  }, [getTotalPages]);

  // 处理每页大小变化
  const handleSetPageSize = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(0); // 重置到第一页
  }, []);

  // 计算分页相关数据
  const paginatedLogs = getPaginatedLogs();
  const totalPages = getTotalPages();

  return {
    // 状态
    isLoading: state.isLoading,
    error: state.error,
    logs: state.logs,
    filter: state.filter,
    filteredLogs: state.filteredLogs,

    // 分页相关
    currentPage,
    pageSize,
    totalPages,
    paginatedLogs,

    // 方法
    getInspectionTaskLogs,
    forceRefreshInspectionTaskLogs,
    setFilter,
    clearFilter,
    setPage: handleSetPage,
    setPageSize: handleSetPageSize,

    // 辅助方法
    getLogTypes,
    getPersons,
    getTaskNames
  };
}

export default useInspectionTaskLog;
