import React, { useState, useRef, useMemo, useCallback } from 'react';
import { MapPin, User, Shield, CheckCircle, XCircle, Search, X } from 'lucide-react';
import { InspectionDetailItem } from '../../hooks/Inspection/useInspectionLog';

interface InspectionDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  taskName: string;
  details: InspectionDetailItem[];
}

/**
 * 巡检详情弹窗组件
 * 显示巡检任务的详细设备信息
 */
const InspectionDetailModal: React.FC<InspectionDetailModalProps> = ({
  isOpen,
  onClose,
  taskName,
  details
}) => {
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // 固定每页显示10条
  const pageInputRef = useRef<HTMLInputElement>(null);

  // 搜索状态
  const [searchQuery, setSearchQuery] = useState('');

  // 调试信息
  console.log('弹窗组件接收到的数据:', { taskName, details, detailsLength: details?.length });

  // 确保details是数组
  const safeDetails = Array.isArray(details) ? details : [];

  // 搜索过滤逻辑
  const filteredDetails = useMemo(() => {
    if (!searchQuery.trim()) {
      return safeDetails;
    }

    // 将搜索查询按中英文逗号分隔成多个条件
    const queries = searchQuery
      .replace(/，/g, ',') // 先将中文逗号替换为英文逗号
      .split(',')
      .map(q => q.toLowerCase().trim())
      .filter(q => q !== ''); // 过滤掉空字符串

    // 如果有有效的搜索条件
    if (queries.length > 0) {
      return safeDetails.filter(item => {
        // 对于每个条件，都需要至少有一个字段匹配
        return queries.every(query => {
          // 搜索所有相关字段
          const searchableFields = [
            item.device_name,
            item.confidentiality_code,
            item.device_id?.toString(),
            item.location,
            item.responsible_person,
            item.responsible_person_alias,
            item.inspection_result,
            item.rfid,
            item.department_info?.department_path,
            item.usage_status,
            item.confidentiality_level
          ];

          return searchableFields.some(field => {
            if (typeof field !== 'string' && typeof field !== 'number') return false;
            return field.toString().toLowerCase().includes(query);
          });
        });
      });
    }

    return safeDetails;
  }, [safeDetails, searchQuery]);

  // 计算分页数据
  const totalItems = filteredDetails.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  const currentPageData = filteredDetails.slice(startIndex, endIndex);

  // 重置分页当数据变化时
  React.useEffect(() => {
    if (isOpen) {
      setCurrentPage(1);
    }
  }, [safeDetails.length, searchQuery, isOpen]);

  // 同步页码输入框的值
  React.useEffect(() => {
    if (isOpen && pageInputRef.current) {
      pageInputRef.current.value = currentPage.toString();
    }
  }, [currentPage, isOpen]);

  // 页码跳转处理
  const handlePageJump = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (pageInputRef.current) {
      const value = parseInt(pageInputRef.current.value, 10);
      if (!isNaN(value) && value > 0 && value <= totalPages) {
        setCurrentPage(value);
      } else {
        pageInputRef.current.value = currentPage.toString();
      }
    }
  }, [totalPages, currentPage]);

  // 简化的状态配置
  const getStatusConfig = useCallback((status: number) => {
    return status === 0
      ? { className: 'status-success', icon: CheckCircle, text: '正常' }
      : { className: 'status-error', icon: XCircle, text: '异常' };
  }, []);

  // 简化的密级配置
  const getConfidentialityConfig = useCallback((level: string) => {
    const configs: Record<string, string> = {
      '绝密': 'status-error',
      '机密': 'status-warning',
      '秘密': 'status-warning',
      '内部': 'status-info',
      '公开': 'status-success',
      '非密专用': 'status-info',
      '涉密专用': 'status-warning'
    };
    return configs[level] || 'status-neutral';
  }, []);

  // 条件渲染：如果弹窗未打开，则不渲染任何内容
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[95vh] overflow-hidden">
        {/* 弹窗头部 - 降低高度 */}
        <div className="flex items-center px-3 py-2 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <Shield className="h-3.5 w-3.5 text-blue-600" />
            </div>
            <div className="ml-2.5">
              <h3 className="text-sm font-medium text-gray-900">
                巡检任务详情
              </h3>
              <p className="text-xs text-gray-500">
                {taskName}
              </p>
            </div>
          </div>
        </div>

        {/* 搜索栏 - 降低高度 */}
        <div className="px-3 py-1.5 border-b border-gray-200 bg-gray-50">
          <div className="relative">
            <input
              type="text"
              placeholder="搜索设备信息（多条件用逗号,或，分隔）..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-7 pr-7 py-1.5 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              title="支持多条件搜索，使用中文逗号，或英文逗号,分隔不同的搜索条件"
            />
            <Search className="absolute search-icon left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-3.5 w-3.5" />
              </button>
            )}
          </div>

          {/* 搜索提示 */}
          {searchQuery && (
            <div className="mt-1 text-xs text-gray-600">
              <span>搜索范围：设备名称、保密编号、位置、责任人、巡检结果、RFID码、部门、使用状态、密级</span>
            </div>
          )}
        </div>

        {/* 弹窗内容 */}
        <div className="p-3 max-h-[calc(95vh-180px)] overflow-y-auto">
          {safeDetails.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-10 w-10 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">暂无巡检详情数据</p>
            </div>
          ) : filteredDetails.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-10 w-10 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">未找到匹配的设备</p>
              <p className="text-gray-400 text-sm mt-2">
                请尝试其他搜索条件或{' '}
                <button
                  onClick={() => setSearchQuery('')}
                  className="text-blue-500 hover:text-blue-700 underline"
                >
                  清除搜索
                </button>
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="data-table">
                <thead>
                  <tr>
                    <th>设备信息</th>
                    <th>位置</th>
                    <th>责任人</th>
                    <th>巡检状态</th>
                    <th>使用状态</th>
                    <th>密级</th>
                    <th>RFID码</th>
                  </tr>
                </thead>
                <tbody>
                  {currentPageData.map((detail) => {
                    const statusConfig = getStatusConfig(detail.inspection_status);
                    const StatusIcon = statusConfig.icon;
                    const confidentialityClass = getConfidentialityConfig(detail.confidentiality_level);

                    return (
                      <tr key={detail.device_id}>
                        {/* 设备信息 */}
                        <td>
                          <div className="flex flex-col">
                            <div className="font-medium">{detail.device_name}</div>
                            <div className="text-xs text-gray-500">{detail.confidentiality_code}</div>
                            <div className="text-xs text-gray-400">ID: {detail.device_id}</div>
                          </div>
                        </td>

                        {/* 位置 */}
                        <td>
                          <div className="flex items-center">
                            <MapPin className="h-3 w-3 mr-1 text-gray-400" />
                            <div className="flex flex-col">
                              <span>{detail.location}</span>
                              <span className="text-xs text-gray-500">{detail.department_info.department_path}</span>
                            </div>
                          </div>
                        </td>

                        {/* 责任人 */}
                        <td>
                          <div className="flex items-center">
                            <User className="h-3 w-3 mr-1 text-gray-400" />
                            <div className="flex flex-col">
                              <span>{detail.responsible_person}</span>
                              <span className="text-xs text-gray-500">{detail.responsible_person_alias}</span>
                            </div>
                          </div>
                        </td>

                        {/* 巡检状态 */}
                        <td>
                          <div className={`status-label ${statusConfig.className}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {statusConfig.text}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">{detail.inspection_result}</div>
                        </td>

                        {/* 使用状态 */}
                        <td>{detail.usage_status}</td>

                        {/* 密级 */}
                        <td>
                          <span className={`status-label ${confidentialityClass}`}>
                            <Shield className="h-3 w-3 mr-1" />
                            {detail.confidentiality_level}
                          </span>
                        </td>

                        {/* RFID码 */}
                        <td>
                          <span className="font-mono">{detail.rfid}</span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 分页组件 */}
        {safeDetails.length > 0 && (
          <div className="flex items-center justify-between px-4 py-2 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center text-sm text-gray-700">
              <span>
                {searchQuery ? (
                  <>找到 {filteredDetails.length} 个设备，共 {safeDetails.length} 个设备</>
                ) : (
                  <>共 {safeDetails.length} 个设备</>
                )}
              </span>
            </div>

            <div className="flex items-center" style={{ gap: '4px' }}>
              {/* 首页按钮 */}
              <button
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                style={{
                  padding: '3px 6px',
                  fontSize: '13px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                  opacity: currentPage === 1 ? 0.5 : 1
                }}
                onMouseEnter={(e) => {
                  if (currentPage !== 1) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                }}
                title="首页"
              >
                «
              </button>

              {/* 上一页按钮 */}
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                style={{
                  padding: '3px 6px',
                  fontSize: '13px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                  opacity: currentPage === 1 ? 0.5 : 1
                }}
                onMouseEnter={(e) => {
                  if (currentPage !== 1) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                }}
                title="上一页"
              >
                ‹
              </button>

              {/* 页码信息和输入框 */}
              <span className="text-sm text-gray-700 flex items-center">
                第
                <form onSubmit={handlePageJump} className="inline-block mx-1">
                  <input
                    ref={pageInputRef}
                    type="text"
                    defaultValue={currentPage}
                    style={{
                      width: '35px',
                      padding: '2px 3px',
                      fontSize: '13px',
                      textAlign: 'center',
                      border: '1px solid #d1d5db',
                      borderRadius: '4px'
                    }}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9]/g, '');
                      if (value !== e.target.value) {
                        e.target.value = value;
                      }
                    }}
                    onBlur={(e) => {
                      const value = parseInt(e.target.value, 10);
                      if (isNaN(value) || value < 1 || value > totalPages) {
                        e.target.value = currentPage.toString();
                      }
                    }}
                    title="输入页码后按回车键跳转"
                  />
                </form>
                页，共 <span className="font-medium">{totalPages}</span> 页
              </span>

              {/* 下一页按钮 */}
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                style={{
                  padding: '3px 6px',
                  fontSize: '13px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
                  opacity: currentPage === totalPages ? 0.5 : 1
                }}
                onMouseEnter={(e) => {
                  if (currentPage !== totalPages) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                }}
                title="下一页"
              >
                ›
              </button>

              {/* 末页按钮 */}
              <button
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                style={{
                  padding: '3px 6px',
                  fontSize: '13px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
                  opacity: currentPage === totalPages ? 0.5 : 1
                }}
                onMouseEnter={(e) => {
                  if (currentPage !== totalPages) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                }}
                title="末页"
              >
                »
              </button>
            </div>
          </div>
        )}

        {/* 弹窗底部 */}
        <div className="flex items-center justify-end px-4 py-3 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default InspectionDetailModal;
