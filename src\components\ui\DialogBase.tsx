import React from 'react';
import { useDialogBase } from '../../hooks/base/useDialogBase';

interface DialogBaseProps {
  // 对话框是否打开
  isOpen: boolean;
  // 关闭对话框的回调
  onClose: () => void;
  // 对话框内容
  children: React.ReactNode;
  // 对话框宽度
  width?: string | number;
  // 对话框最大宽度
  maxWidth?: string | number;
  // 对话框最大高度
  maxHeight?: string | number;
  // 对话框背景色
  bgColor?: string;
  // 是否显示遮罩层
  showOverlay?: boolean;
  // 点击遮罩层是否关闭对话框
  closeOnOverlayClick?: boolean;
  // 是否自动聚焦到第一个元素
  autoFocus?: boolean;
  // 关闭时是否恢复焦点
  restoreFocus?: boolean;
  // 对话框CSS类名
  className?: string;
  // 对话框样式
  style?: React.CSSProperties;
  // 对话框动画
  animation?: 'fade' | 'slide' | 'scale' | 'none';
  // ESC键处理器的优先级
  escPriority?: number;
  // Tab键处理器的优先级
  tabPriority?: number;
}

/**
 * 对话框基础组件
 * 提供统一的对话框UI结构
 *
 * 架构说明：
 * 这个组件只负责渲染，所有逻辑都在useDialogBase Hook中处理
 * 遵循"UI层只负责渲染，Hook层只负责处理逻辑"的架构原则
 */
const DialogBase: React.FC<DialogBaseProps> = ({
  isOpen,
  onClose,
  children,
  width = 'auto',
  maxWidth = '90vw',
  maxHeight = '90vh',
  bgColor = 'bg-white',
  showOverlay = true,
  closeOnOverlayClick = true,
  autoFocus = true,
  restoreFocus = true,
  className = '',
  style = {},
  animation = 'fade',
  escPriority = 100,
  tabPriority = 100
}) => {
  // 使用对话框基础Hook
  const { dialogRef, handleOverlayClick, getAnimationClass, shouldRender } = useDialogBase({
    isOpen,
    onClose,
    closeOnOverlayClick,
    autoFocus,
    restoreFocus,
    animation,
    escPriority,
    tabPriority
  });

  // 如果对话框未打开，则不渲染内容
  if (!shouldRender) return null;

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center ${showOverlay ? 'bg-black bg-opacity-50' : ''}`}
      style={{ zIndex: 1000000 }}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
    >
      <div
        ref={dialogRef}
        className={`${bgColor} rounded-lg shadow-xl overflow-hidden focus-container ${getAnimationClass()} ${className}`}
        style={{
          width,
          maxWidth,
          maxHeight,
          zIndex: 1000000,
          ...style
        }}
        tabIndex={-1}
      >
        {children}
      </div>
    </div>
  );
};

export default DialogBase;
