import React, { useState, useEffect } from 'react';
import { X, FileText, Clock, Calendar, AlertCircle } from 'lucide-react';
import DatePicker from '../Common/DatePicker';
import { InspectionTaskItem } from '../../services/Inspection/inspectionTaskService';

/**
 * 任务更新数据接口
 */
export interface TaskUpdateData {
  task_id: number;
  task_name?: string;
  task_description?: string;
  start_date?: number;
  end_date?: number;
}

interface TaskEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  task: InspectionTaskItem | null;
  onSave: (updateData: TaskUpdateData) => Promise<void>;
}

const TaskEditDialog: React.FC<TaskEditDialogProps> = ({
  isOpen,
  onClose,
  task,
  onSave
}) => {
  const [formData, setFormData] = useState({
    task_name: '',
    task_description: '',
    start_date: '',
    end_date: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  // 格式化日期为DatePicker组件所需的格式（支持秒级精度）
  const formatDateForPicker = (timestamp: number | undefined): string => {
    if (!timestamp || timestamp <= 0) return '';

    // 判断是秒级还是毫秒级时间戳
    // 如果时间戳小于等于10位数，认为是秒级时间戳
    const milliseconds = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;

    try {
      const date = new Date(milliseconds);

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '';
      }

      // 使用本地时间格式化，避免时区问题
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      // DatePicker组件期望的格式是 "YYYY-MM-DDTHH:mm:ss"（用T分隔，本地时间）
      return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  };

  // 当任务数据变化时更新表单
  useEffect(() => {
    if (task) {
      setFormData({
        task_name: task.task_name || '',
        task_description: task.task_description || '',
        start_date: formatDateForPicker(task.start_date),
        end_date: formatDateForPicker(task.end_date)
      });
    }
  }, [task]);

  // 解析日期字符串为时间戳（兼容DatePicker格式，支持秒级精度）
  const parseDateTime = (dateStr: string): number => {
    if (!dateStr) return 0;

    // 处理DatePicker返回的格式 "YYYY-MM-DD HH:mm:ss" 或 "YYYY-MM-DD HH:mm"
    const normalizedStr = dateStr.replace(' ', 'T');
    const date = new Date(normalizedStr);

    if (isNaN(date.getTime())) {
      throw new Error('无效的日期格式');
    }

    return date.getTime();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!task) return;

    try {
      setIsLoading(true);
      setError(undefined);

      // 构建更新数据，只包含有变化的字段
      const updateData: TaskUpdateData = {
        task_id: task.task_id
      };

      if (formData.task_name !== task.task_name) {
        updateData.task_name = formData.task_name;
      }

      if (formData.task_description !== task.task_description) {
        updateData.task_description = formData.task_description;
      }

      if (formData.start_date) {
        const startTimestamp = parseDateTime(formData.start_date);
        if (startTimestamp !== task.start_date) {
          updateData.start_date = startTimestamp;
        }
      }

      if (formData.end_date) {
        const endTimestamp = parseDateTime(formData.end_date);
        if (endTimestamp !== task.end_date) {
          updateData.end_date = endTimestamp;
        }
      }

      // 检查是否有变化
      if (Object.keys(updateData).length === 1) {
        setError('没有检测到任何变化');
        return;
      }

      await onSave(updateData);
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : '更新失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setError(undefined);
    onClose();
  };

  if (!isOpen || !task) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">编辑巡检任务</h2>
              <p className="text-sm text-gray-500 mt-1">任务ID: {task.task_id}</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 错误提示 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* 任务信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <FileText className="h-5 w-5 mr-2 text-gray-600" />
              任务信息
            </h3>

            {/* 任务名称 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                任务名称
              </label>
              <input
                type="text"
                value={formData.task_name}
                onChange={(e) => setFormData(prev => ({ ...prev, task_name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入任务名称"
              />
            </div>

            {/* 任务描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                任务描述
              </label>
              <textarea
                value={formData.task_description}
                onChange={(e) => setFormData(prev => ({ ...prev, task_description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入任务描述"
              />
            </div>
          </div>

          {/* 时间信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Clock className="h-5 w-5 mr-2 text-gray-600" />
              时间安排
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 开始时间 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">
                  <span className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1.5 text-blue-600" />
                    开始时间
                  </span>
                </label>
                <DatePicker
                  value={formData.start_date}
                  onChange={(date) => setFormData(prev => ({ ...prev, start_date: date }))}
                  placeholder="请选择开始日期和时间"
                  disabled={isLoading}
                  showTime={true}
                  showSeconds={true}
                  className="text-sm"
                />
              </div>

              {/* 结束时间 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">
                  <span className="flex items-center">
                    <Clock className="h-4 w-4 mr-1.5 text-blue-600" />
                    结束时间
                  </span>
                </label>
                <DatePicker
                  value={formData.end_date}
                  onChange={(date) => setFormData(prev => ({ ...prev, end_date: date }))}
                  placeholder="请选择结束日期和时间"
                  disabled={isLoading}
                  showTime={true}
                  showSeconds={true}
                  className="text-sm"
                />
              </div>
            </div>
          </div>



          {/* 按钮 */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? '保存中...' : '保存'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskEditDialog;
