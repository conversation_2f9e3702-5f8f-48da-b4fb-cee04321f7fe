import { InventoryItem } from '../../../types/inventory';

/**
 * 库存选择服务
 * 专门负责管理设备选择状态
 */
export class InventorySelectionService {
  private static instance: InventorySelectionService;
  
  // 当前选中的项目ID列表
  private selectedItems: string[] = [];

  private constructor() {}

  public static getInstance(): InventorySelectionService {
    if (!InventorySelectionService.instance) {
      InventorySelectionService.instance = new InventorySelectionService();
    }
    return InventorySelectionService.instance;
  }

  /**
   * 获取选中的项目ID列表
   */
  public getSelectedItems(): string[] {
    return [...this.selectedItems];
  }

  /**
   * 设置选中的项目ID列表
   */
  public setSelectedItems(selectedItems: string[]): void {
    this.selectedItems = [...selectedItems];
  }

  /**
   * 清除所有选择
   */
  public clearSelection(): void {
    this.selectedItems = [];
  }

  /**
   * 切换单个项目的选中状态
   * @param itemId 项目ID
   * @returns 更新后的选中项目列表
   */
  public toggleSelectItem(itemId: string): string[] {
    const selectedItems = [...this.selectedItems];
    const index = selectedItems.indexOf(itemId);
    
    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(itemId);
    }
    
    this.selectedItems = selectedItems;
    return this.getSelectedItems();
  }

  /**
   * 全选/取消全选
   * @param filteredItems 当前筛选后的项目列表
   * @returns 更新后的选中项目列表
   */
  public toggleSelectAll(filteredItems: InventoryItem[]): string[] {
    const filteredIds = filteredItems.map(item => item.id);

    // 如果当前选中的项目包含所有过滤后的项目，则取消全选
    if (filteredIds.every(id => this.selectedItems.includes(id))) {
      // 取消选中过滤后的项目
      this.selectedItems = this.selectedItems.filter(id => !filteredIds.includes(id));
    } else {
      // 选中所有过滤后的项目
      this.selectedItems = [...new Set([...this.selectedItems, ...filteredIds])];
    }
    
    return this.getSelectedItems();
  }

  /**
   * 检查项目是否被选中
   * @param itemId 项目ID
   */
  public isItemSelected(itemId: string): boolean {
    return this.selectedItems.includes(itemId);
  }

  /**
   * 检查是否全选了指定的项目列表
   * @param itemIds 项目ID列表
   */
  public isAllSelected(itemIds: string[]): boolean {
    return itemIds.length > 0 && itemIds.every(id => this.selectedItems.includes(id));
  }

  /**
   * 检查是否部分选中了指定的项目列表
   * @param itemIds 项目ID列表
   */
  public isPartiallySelected(itemIds: string[]): boolean {
    const selectedCount = itemIds.filter(id => this.selectedItems.includes(id)).length;
    return selectedCount > 0 && selectedCount < itemIds.length;
  }

  /**
   * 获取选中项目的数量
   */
  public getSelectedCount(): number {
    return this.selectedItems.length;
  }

  /**
   * 选中指定的项目列表
   * @param itemIds 要选中的项目ID列表
   */
  public selectItems(itemIds: string[]): string[] {
    // 合并当前选中的项目和新的项目，去重
    this.selectedItems = [...new Set([...this.selectedItems, ...itemIds])];
    return this.getSelectedItems();
  }

  /**
   * 取消选中指定的项目列表
   * @param itemIds 要取消选中的项目ID列表
   */
  public deselectItems(itemIds: string[]): string[] {
    this.selectedItems = this.selectedItems.filter(id => !itemIds.includes(id));
    return this.getSelectedItems();
  }

  /**
   * 反选指定的项目列表
   * @param itemIds 要反选的项目ID列表
   */
  public toggleItems(itemIds: string[]): string[] {
    itemIds.forEach(id => {
      const index = this.selectedItems.indexOf(id);
      if (index > -1) {
        this.selectedItems.splice(index, 1);
      } else {
        this.selectedItems.push(id);
      }
    });
    
    return this.getSelectedItems();
  }

  /**
   * 获取选中的项目详情
   * @param allItems 所有项目列表
   */
  public getSelectedItemDetails(allItems: InventoryItem[]): InventoryItem[] {
    return allItems.filter(item => this.selectedItems.includes(item.id));
  }

  /**
   * 从选中列表中移除不存在的项目ID
   * @param existingItemIds 当前存在的项目ID列表
   */
  public cleanupSelection(existingItemIds: string[]): string[] {
    this.selectedItems = this.selectedItems.filter(id => existingItemIds.includes(id));
    return this.getSelectedItems();
  }

  /**
   * 重置选择状态
   */
  public reset(): void {
    this.selectedItems = [];
  }

  /**
   * 获取选择状态的统计信息
   * @param totalItems 总项目数
   */
  public getSelectionStats(totalItems: number): {
    selectedCount: number;
    totalCount: number;
    isAllSelected: boolean;
    isNoneSelected: boolean;
    isPartiallySelected: boolean;
    selectionPercentage: number;
  } {
    const selectedCount = this.selectedItems.length;
    const isAllSelected = selectedCount === totalItems && totalItems > 0;
    const isNoneSelected = selectedCount === 0;
    const isPartiallySelected = selectedCount > 0 && selectedCount < totalItems;
    const selectionPercentage = totalItems > 0 ? (selectedCount / totalItems) * 100 : 0;

    return {
      selectedCount,
      totalCount: totalItems,
      isAllSelected,
      isNoneSelected,
      isPartiallySelected,
      selectionPercentage: Math.round(selectionPercentage * 100) / 100 // 保留两位小数
    };
  }
}

export default InventorySelectionService;
