import React from 'react';
import { LucideIcon } from 'lucide-react';

/**
 * 操作按钮配置接口
 */
export interface ActionButton {
  key: string;
  icon: LucideIcon;
  title: string;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'gray';
  onClick: () => void;
  disabled?: boolean;
}

/**
 * 操作按钮组属性接口
 */
interface ActionButtonsProps {
  buttons: ActionButton[];
  className?: string;
}

/**
 * 巡检管理通用操作按钮组件
 * 统一的按钮样式和交互
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  buttons,
  className = ''
}) => {
  // 获取按钮颜色样式
  const getButtonColorClass = (color: ActionButton['color'], disabled?: boolean) => {
    if (disabled) {
      return 'text-gray-400 hover:text-gray-400 hover:bg-gray-50 cursor-not-allowed';
    }

    switch (color) {
      case 'blue':
        return 'text-blue-600 hover:text-blue-800 hover:bg-blue-50';
      case 'green':
        return 'text-green-600 hover:text-green-800 hover:bg-green-50';
      case 'yellow':
        return 'text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50';
      case 'red':
        return 'text-red-600 hover:text-red-800 hover:bg-red-50';
      case 'gray':
        return 'text-gray-600 hover:text-gray-800 hover:bg-gray-50';
      default:
        return 'text-gray-600 hover:text-gray-800 hover:bg-gray-50';
    }
  };

  return (
    <div className={`flex items-center justify-end space-x-1 ${className}`}>
      {buttons.map((button) => {
        const IconComponent = button.icon;
        return (
          <button
            key={button.key}
            onClick={button.onClick}
            disabled={button.disabled}
            className={`p-1 rounded transition-colors ${getButtonColorClass(button.color, button.disabled)}`}
            title={button.title}
          >
            <IconComponent className="h-3.5 w-3.5" />
          </button>
        );
      })}
    </div>
  );
};

export default ActionButtons;
