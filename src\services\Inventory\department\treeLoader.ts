import { DepartmentCategory } from './types';
import { DepartmentUtils } from './utils';
import { TreeUtils } from './treeUtils';
import { BaseService } from '../../base/baseService';
import { DataFetchService } from './dataFetchService';
import { CacheManager } from './cacheManager';
import {
  ROOT_NODE_ID,
  NODE_ID_PREFIXES,
  CACHE_TYPES,
  LOG_PREFIXES,
  formatLogMessage,
  generateDepartmentNodeId,
  generatePersonNodeId
} from './constants';

/**
 * 部门树加载器
 * 负责从数据库加载和构建部门树
 */
export class DepartmentTreeLoader {
  private baseService: BaseService<any, any>;
  private isLoadingDepartmentTree = false;
  private dataFetchService: DataFetchService;
  private cacheManager: CacheManager;

  constructor(baseService: BaseService<any, any>) {
    this.baseService = baseService;
    this.dataFetchService = DataFetchService.getInstance(baseService);
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 获取人员的最新信息（优先从统一缓存获取）
   * @param personId 人员ID
   * @param fallbackData 备用数据（来自数据库查询）
   * @returns 人员信息
   */
  private getPersonInfo(personId: number, fallbackData: any): { user_name: string, alias?: string } {
    // 尝试从统一缓存管理器获取最新的人员信息
    try {
      if (this.cacheManager.hasPersonCache(personId)) {
        const cachedPerson = this.cacheManager.getPersonCache(personId);
        if (cachedPerson) {
          console.log(`使用缓存中的人员信息: ID=${personId}, 姓名=${cachedPerson.user_name}`);
          return {
            user_name: cachedPerson.user_name,
            alias: cachedPerson.alias
          };
        }
      }
    } catch (error) {
      console.warn('从缓存获取人员信息失败，使用数据库数据:', error);
    }

    // 如果缓存中没有，使用数据库查询的数据
    console.log(`使用数据库中的人员信息: ID=${personId}, 姓名=${fallbackData.user_name}`);
    return {
      user_name: fallbackData.user_name,
      alias: fallbackData.alias
    };
  }

  /**
   * 从数据库加载部门树
   * 使用get_department和get_person API获取部门和人员数据
   * @param forceRefresh 是否强制刷新，即使已有数据也重新加载
   * @param currentCategories 当前的部门分类树
   */
  public async loadDepartmentTree(
    forceRefresh: boolean = false,
    currentCategories: DepartmentCategory[] = []
  ): Promise<DepartmentCategory[]> {
    // 如果已经有部门树数据且不是强制刷新，直接返回
    if (currentCategories.length > 0 && !forceRefresh) {
      console.log('部门树已存在，跳过重复加载');
      return currentCategories;
    }

    // 如果正在加载，等待当前加载完成
    if (this.isLoadingDepartmentTree) {
      console.log('部门树正在加载中，等待完成...');
      // 等待当前加载完成
      let retryCount = 0;
      const maxRetries = 10;
      const retryInterval = 300; // 毫秒

      while (this.isLoadingDepartmentTree && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, retryInterval));
        retryCount++;
      }

      // 如果还在加载或已经加载完成，则返回当前状态
      if (this.isLoadingDepartmentTree || currentCategories.length > 0) {
        return currentCategories;
      }
    }

    // 设置加载标志
    this.isLoadingDepartmentTree = true;

    try {
      console.log('从数据库加载部门树...');

      // 使用统一的数据获取服务批量获取数据
      console.log(formatLogMessage(LOG_PREFIXES.TREE_LOADER, '使用统一数据获取服务获取部门和人员数据'));
      const { departments: departmentsResult, persons: personsResult } =
        await this.dataFetchService.getDepartmentsAndPersons(forceRefresh);

      console.log(formatLogMessage(LOG_PREFIXES.TREE_LOADER, '数据获取完成'));
      console.log(formatLogMessage(LOG_PREFIXES.TREE_LOADER, `部门数据数量: ${departmentsResult?.data?.length || 0}`));
      console.log(formatLogMessage(LOG_PREFIXES.TREE_LOADER, `人员数据数量: ${personsResult?.data?.length || 0}`));

      // 将人员数据更新到缓存中
      if (personsResult.success && Array.isArray(personsResult.data)) {
        this.cacheManager.setPersonCacheBatch(personsResult.data);
        console.log(formatLogMessage(LOG_PREFIXES.TREE_LOADER, '人员数据已更新到统一缓存'));
      }

      // 3. 构建部门树
      console.log('开始构建部门树...');
      const departmentCategories: DepartmentCategory[] = [];

      // 获取公司名称作为根节点名称
      const CompanyNameHelper = (await import('../../../utils/companyNameHelper')).default;
      const rootNodeName = await CompanyNameHelper.getDepartmentCategoryRootName();

      // 添加动态根节点
      const allDepartmentsNode: DepartmentCategory = {
        id: ROOT_NODE_ID,
        name: rootNodeName,
        count: 0,
        children: []
      };

      // 部门映射，用于快速查找
      const departmentMap: Record<number, DepartmentCategory> = {};

      // 处理部门数据，使用新的department_path格式构建部门树
      if (departmentsResult.data.length > 0) {
        console.log(`开始处理${departmentsResult.data.length}个部门数据...`);

        // 使用department_path构建部门树
        const pathNodeMap: Record<string, DepartmentCategory> = {};

        // 按路径长度排序，确保父部门先创建
        const sortedDepartments = departmentsResult.data.sort((a: any, b: any) => {
          const aPathLength = (a.department_path || '').split('/').length;
          const bPathLength = (b.department_path || '').split('/').length;
          return aPathLength - bPathLength;
        });

        sortedDepartments.forEach((dept: any) => {
          try {
            if (!dept || !dept.id || !dept.department_name || !dept.department_path) {
              console.warn('跳过无效的部门数据:', dept);
              return;
            }

            const departmentPath = dept.department_path;
            const pathParts = departmentPath.split('/');

            // 创建当前部门节点
            const departmentNode: DepartmentCategory = {
              id: generateDepartmentNodeId(dept.id),
              name: dept.department_name,
              count: 0, // 将在后面计算
              children: [],
              departmentPath: departmentPath // 存储部门路径
            };

            // 保存到映射中，方便后续查找
            departmentMap[dept.id] = departmentNode;
            pathNodeMap[departmentPath] = departmentNode;

            // 根据路径确定父子关系
            if (pathParts.length === 1) {
              // 根部门，直接添加到根节点
              allDepartmentsNode.children!.push(departmentNode);
              console.log(`添加根部门: ${dept.department_name} (路径: ${departmentPath})`);
            } else {
              // 子部门，找到父部门路径
              const parentPath = pathParts.slice(0, -1).join('/');
              const parentNode = pathNodeMap[parentPath];

              if (parentNode && parentNode.children) {
                parentNode.children.push(departmentNode);
                console.log(`添加子部门: ${dept.department_name} 到父部门路径: ${parentPath}`);
              } else {
                // 如果找不到父部门，作为根部门处理
                console.warn(`找不到部门 ${dept.department_name} 的父部门路径 ${parentPath}，将其作为根部门处理`);
                allDepartmentsNode.children!.push(departmentNode);
              }
            }
          } catch (error) {
            console.error('处理部门数据时出错:', error, dept);
          }
        });
      } else {
        console.log('部门数据为空，不创建示例部门');
        // 部门数据为空，不创建示例部门，保持空的部门树
      }

      // 处理人员数据，支持人员属于多个部门
      if (personsResult.data.length > 0) {
        console.log(`开始处理${personsResult.data.length}个人员数据...`);
        personsResult.data.forEach((person: any) => {
          try {
            if (!person || !person.id || !person.user_name) {
              console.warn('跳过无效的人员数据:', person);
              return;
            }

            // 新API格式：人员可以属于多个部门
            if (person.departments && Array.isArray(person.departments)) {
              console.log(`处理人员 ${person.user_name} 的多部门关联:`, person.departments);

              // 遍历人员的所有部门
              person.departments.forEach((dept: any) => {
                const departmentNode = departmentMap[dept.id];

                if (departmentNode) {
                  // 获取人员的最新信息（优先从缓存获取）
                  const personInfo = this.getPersonInfo(person.id, person);

                  // 创建人员节点，使用部门ID和人员ID组合确保唯一性
                  const personNode: DepartmentCategory = {
                    id: generatePersonNodeId(person.id, dept.id),
                    name: personInfo.user_name + (personInfo.alias ? ` (${personInfo.alias})` : ''),
                    count: 0, // 人员节点暂时不计数
                    // 添加原始人员ID，用于后续查询
                    originalPersonId: person.id
                  };

                  // 添加到部门节点
                  if (!departmentNode.children) {
                    departmentNode.children = [];
                  }

                  // 检查是否已经存在该人员节点（避免重复添加）
                  const existingPerson = departmentNode.children.find(child => child.id === personNode.id);
                  if (!existingPerson) {
                    departmentNode.children.push(personNode);
                    console.log(`将人员 ${person.user_name} 添加到部门 ${dept.name} ${dept.is_primary === 1 ? '(主要部门)' : ''}`);
                  }
                } else {
                  console.warn(`找不到人员 ${person.user_name} 的所属部门 ${dept.id} (${dept.name})`);
                }
              });
            } else {
              // 兼容旧格式：使用primary_department_id
              const departmentId = person.primary_department_id;
              const departmentNode = departmentMap[departmentId];

              if (departmentNode) {
                // 获取人员的最新信息（优先从缓存获取）
                const personInfo = this.getPersonInfo(person.id, person);

                // 创建人员节点
                const personNode: DepartmentCategory = {
                  id: generatePersonNodeId(person.id),
                  name: personInfo.user_name + (personInfo.alias ? ` (${personInfo.alias})` : ''),
                  count: 0 // 人员节点暂时不计数
                };

                // 添加到部门节点
                if (!departmentNode.children) {
                  departmentNode.children = [];
                }
                departmentNode.children.push(personNode);
                console.log(`将人员 ${person.user_name} 添加到主要部门 ${person.primary_department_name}`);
              } else {
                console.warn(`找不到人员 ${person.user_name} 的主要部门 ${departmentId}`);

                // 如果找不到部门，将人员添加到第一个部门
                if (allDepartmentsNode.children && allDepartmentsNode.children.length > 0) {
                  const firstDept = allDepartmentsNode.children[0];
                  if (!firstDept.children) {
                    firstDept.children = [];
                  }

                  // 获取人员的最新信息（优先从缓存获取）
                  const personInfo = this.getPersonInfo(person.id, person);

                  const personNode: DepartmentCategory = {
                    id: generatePersonNodeId(person.id),
                    name: personInfo.user_name + (personInfo.alias ? ` (${personInfo.alias})` : ''),
                    count: 0
                  };

                  firstDept.children.push(personNode);
                  console.log(`将人员 ${person.user_name} 添加到第一个部门: ${firstDept.name}`);
                }
              }
            }
          } catch (error) {
            console.error('处理人员数据时出错:', error, person);
          }
        });
      } else if (Object.keys(departmentMap).length > 0) {
        console.log('人员数据为空，不创建示例人员');
        // 人员数据为空，不创建示例人员，保持部门树结构
      }

      // 对部门树进行排序，确保人员节点排在部门节点前面
      TreeUtils.sortDepartmentTree([allDepartmentsNode]);
      console.log('部门树排序完成，人员节点将显示在部门节点上方');

      // 只添加根节点到部门分类树中
      departmentCategories.push(allDepartmentsNode);

      // 清除旧的缓存（保留人员缓存，因为刚刚更新过）
      this.cacheManager.clearCache(CACHE_TYPES.PATH_CACHE);
      this.cacheManager.clearCache(CACHE_TYPES.DEPARTMENT_PATHS_CACHE);
      this.cacheManager.clearCache(CACHE_TYPES.SAME_NAME_DEPARTMENTS_CACHE);
      this.cacheManager.clearCache(CACHE_TYPES.DEPARTMENT_PERSONS_CACHE);

      console.log(formatLogMessage(LOG_PREFIXES.TREE_LOADER, `部门树加载完成，部门数量: ${allDepartmentsNode.children?.length || 0}`));

      return departmentCategories;
    } catch (error) {
      console.error('加载部门树失败:', error);

      // 获取公司名称作为根节点名称（错误处理情况）
      const CompanyNameHelper = (await import('../../../utils/companyNameHelper')).default;
      const rootNodeName = await CompanyNameHelper.getDepartmentCategoryRootName();

      // 初始化空的部门树
      const emptyTree: DepartmentCategory[] = [{
        id: ROOT_NODE_ID,
        name: rootNodeName,
        count: 0,
        children: []
      }];

      return emptyTree;
    } finally {
      // 释放加载标志
      this.isLoadingDepartmentTree = false;
    }
  }
}
