# 跨浏览器兼容性指南

## 📋 概述

本文档详细说明了焦点管理和键盘操作系统在不同浏览器内核（特别是m108版本miniblink）中的兼容性处理方案。

## 🔍 浏览器兼容性检测

### 自动检测功能
系统会在初始化时自动检测浏览器的支持能力：

```typescript
interface BrowserCapabilities {
  // 焦点管理能力
  supportsFocus: boolean;              // 是否支持element.focus()
  supportsActiveElement: boolean;      // 是否支持document.activeElement
  supportsScrollIntoView: boolean;     // 是否支持scrollIntoView()
  supportsFocusVisible: boolean;       // 是否支持:focus-visible CSS
  supportsTabIndex: boolean;           // 是否支持tabindex属性
  
  // 键盘事件能力
  supportsKeyboardEvents: boolean;     // 是否支持KeyboardEvent
  supportsKeyProperty: boolean;        // 是否支持event.key
  supportsWhichProperty: boolean;      // 是否支持event.which
  supportsKeyCode: boolean;            // 是否支持event.keyCode
  supportsPreventDefault: boolean;     // 是否支持preventDefault()
  supportsStopPropagation: boolean;    // 是否支持stopPropagation()
}
```

### 检测结果示例
```javascript
// 现代浏览器
{
  supportsFocus: true,
  supportsActiveElement: true,
  supportsScrollIntoView: true,
  supportsFocusVisible: true,
  supportsTabIndex: true,
  supportsKeyboardEvents: true,
  supportsKeyProperty: true,
  supportsWhichProperty: true,
  supportsKeyCode: true,
  supportsPreventDefault: true,
  supportsStopPropagation: true
}

// m108 miniblink（可能的情况）
{
  supportsFocus: true,
  supportsActiveElement: true,
  supportsScrollIntoView: false,
  supportsFocusVisible: false,
  supportsTabIndex: true,
  supportsKeyboardEvents: true,
  supportsKeyProperty: false,
  supportsWhichProperty: true,
  supportsKeyCode: true,
  supportsPreventDefault: true,
  supportsStopPropagation: true
}
```

## 🛠️ 兼容性处理策略

### 1. 焦点设置兼容性

#### 现代浏览器
```typescript
element.focus({ preventScroll: true });
```

#### 降级处理
```typescript
// 如果不支持focus()方法
element.setAttribute('data-focused', 'true');
// 配合CSS样式实现视觉焦点效果
```

### 2. 键盘事件兼容性

#### 键名标准化
```typescript
function normalizeKey(event: KeyboardEvent): string {
  // 优先使用现代的key属性
  if (event.key) {
    return event.key;
  }
  
  // 降级到keyCode
  if (event.keyCode) {
    const keyCodeMap = {
      9: 'Tab',
      13: 'Enter',
      27: 'Escape',
      37: 'ArrowLeft',
      38: 'ArrowUp',
      39: 'ArrowRight',
      40: 'ArrowDown'
    };
    return keyCodeMap[event.keyCode] || String.fromCharCode(event.keyCode);
  }
  
  // 最后降级到which属性
  if (event.which) {
    return String.fromCharCode(event.which);
  }
  
  return 'Unknown';
}
```

### 3. 滚动兼容性

#### 现代浏览器
```typescript
element.scrollIntoView({
  behavior: 'smooth',
  block: 'start'
});
```

#### 降级处理
```typescript
// 如果不支持scrollIntoView
const rect = element.getBoundingClientRect();
window.scrollTo(0, rect.top + window.pageYOffset);
```

### 4. CSS兼容性

#### 现代浏览器
```css
/* 使用:focus-visible */
.element:focus-visible {
  outline: 2px solid #3b82f6;
}
```

#### 降级处理
```css
/* 使用类名控制 */
body.keyboard-navigation .element:focus {
  outline: 2px solid #3b82f6;
}

/* 手动焦点标记 */
[data-focused="true"] {
  outline: 2px solid #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}
```

## 🎯 m108 miniblink 特殊处理

### 已知问题和解决方案

#### 1. 键盘事件差异
**问题**: m108可能不支持`event.key`属性
**解决方案**: 使用`keyCode`映射表进行转换

#### 2. 焦点设置时机
**问题**: 焦点设置可能需要延迟执行
**解决方案**: 使用`setTimeout`确保DOM更新完成

```typescript
const setFocus = (element: HTMLElement) => {
  setTimeout(() => {
    try {
      element.focus();
    } catch (error) {
      // 降级处理
      element.setAttribute('data-focused', 'true');
    }
  }, 0);
};
```

#### 3. CSS样式支持
**问题**: 可能不支持现代CSS特性
**解决方案**: 提供降级样式

```css
/* 现代浏览器 */
@supports (selector(:focus-visible)) {
  .element:focus-visible {
    outline: 2px solid #3b82f6;
  }
}

/* 降级支持 */
@supports not (selector(:focus-visible)) {
  body.keyboard-navigation .element:focus {
    outline: 2px solid #3b82f6;
  }
}
```

#### 4. 事件监听器
**问题**: 事件监听器行为可能不一致
**解决方案**: 使用捕获阶段和错误处理

```typescript
// 使用捕获阶段确保优先处理
document.addEventListener('keydown', handleKeyDown, true);

// 添加错误处理
const handleKeyDown = (event: KeyboardEvent) => {
  try {
    // 处理逻辑
  } catch (error) {
    console.warn('键盘事件处理失败:', error);
  }
};
```

## 📊 兼容性测试

### 测试用例

#### 1. 基础功能测试
```typescript
// 测试焦点设置
const testFocusCapability = () => {
  const testElement = document.createElement('button');
  testElement.textContent = '测试按钮';
  document.body.appendChild(testElement);
  
  try {
    testElement.focus();
    const isSupported = document.activeElement === testElement;
    console.log('焦点设置支持:', isSupported);
  } catch (error) {
    console.log('焦点设置不支持:', error);
  } finally {
    document.body.removeChild(testElement);
  }
};

// 测试键盘事件
const testKeyboardEvents = () => {
  const handler = (e: KeyboardEvent) => {
    console.log('键盘事件:', {
      key: e.key,
      keyCode: e.keyCode,
      which: e.which
    });
  };
  
  document.addEventListener('keydown', handler);
  
  // 模拟按键
  const event = new KeyboardEvent('keydown', { key: 'Tab' });
  document.dispatchEvent(event);
  
  document.removeEventListener('keydown', handler);
};
```

#### 2. 兼容性验证
```typescript
// 验证所有功能
const validateCompatibility = () => {
  const capabilities = detectBrowserCapabilities();
  
  console.log('浏览器兼容性报告:');
  console.table(capabilities);
  
  // 检查关键功能
  const criticalFeatures = [
    'supportsFocus',
    'supportsActiveElement',
    'supportsKeyboardEvents'
  ];
  
  const unsupportedFeatures = criticalFeatures.filter(
    feature => !capabilities[feature]
  );
  
  if (unsupportedFeatures.length > 0) {
    console.warn('不支持的关键功能:', unsupportedFeatures);
    console.log('将使用降级处理方案');
  } else {
    console.log('所有关键功能都支持');
  }
};
```

## 🔧 调试工具

### 兼容性调试
```typescript
// 启用调试模式
window.FOCUS_DEBUG = true;

// 查看当前能力
console.log('焦点管理器能力:', getFocusManager().capabilities);
console.log('键盘管理器能力:', getKeyboardManager().capabilities);

// 模拟低版本浏览器
window.SIMULATE_OLD_BROWSER = true;
```

### 性能监控
```typescript
// 监控焦点变化
let focusChangeCount = 0;
document.addEventListener('focusin', () => {
  focusChangeCount++;
  console.log(`焦点变化次数: ${focusChangeCount}`);
});

// 监控键盘事件处理时间
const originalHandler = handleKeyDown;
const handleKeyDown = (event: KeyboardEvent) => {
  const start = performance.now();
  originalHandler(event);
  const end = performance.now();
  console.log(`键盘事件处理耗时: ${end - start}ms`);
};
```

## 📝 最佳实践

### 1. 渐进增强
- 确保基础功能在所有浏览器中可用
- 在支持的浏览器中提供增强体验
- 优雅降级到基础实现

### 2. 错误处理
- 所有API调用都包装在try-catch中
- 提供有意义的错误信息
- 实现降级处理方案

### 3. 性能优化
- 缓存能力检测结果
- 避免重复的DOM查询
- 使用事件委托减少监听器数量

### 4. 用户体验
- 确保键盘导航在所有浏览器中一致
- 提供清晰的焦点指示
- 支持屏幕阅读器

## 🚀 部署建议

### 1. 构建配置
```javascript
// webpack.config.js
module.exports = {
  target: ['web', 'es5'], // 支持旧版浏览器
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                targets: {
                  chrome: '49' // 支持m108内核
                }
              }]
            ]
          }
        }
      }
    ]
  }
};
```

### 2. Polyfill配置
```javascript
// 添加必要的polyfill
import 'core-js/stable';
import 'regenerator-runtime/runtime';

// 自定义polyfill
if (!window.KeyboardEvent) {
  window.KeyboardEvent = function(type, options) {
    const event = document.createEvent('Event');
    event.initEvent(type, true, true);
    Object.assign(event, options);
    return event;
  };
}
```

---

**版本**: 1.0.0  
**适用浏览器**: Chrome 49+, m108 miniblink, IE 11+  
**最后更新**: 2025-01-09
