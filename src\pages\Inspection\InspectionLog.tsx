import React, { useState, useEffect, useRef } from 'react';
import {
  RefreshCw,
  Search,
  Calendar,
  Filter,
  X,
  AlertCircle,
  ChevronDown,
  Eye,
  User,
  Building,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import useInspectionLog, {
  InspectionLogFilter,
  InspectionTaskLogItem
} from '../../hooks/Inspection/useInspectionLog';
import DatePicker from '../../components/ui/DatePicker';
import CustomSelect from '../../components/ui/CustomSelect';
import InspectionDetailModal from '../../components/Inspection/InspectionDetailModal';
import CustomScrollbar from '../../components/ui/CustomScrollbar';
import DepartmentTooltip from '../../components/ui/DepartmentTooltip';
import { usePageFocus, useListFocus } from '../../hooks/base';
import { formatInspectionDateTime } from '../../utils/formatUtils';

/**
 * 获取任务执行状态配置 - 基于后端返回的task_execution_status字段
 */
const getTaskExecutionStatusConfig = (executionStatus: string) => {
  switch (executionStatus) {
    case '已完成':
      return {
        icon: CheckCircle,
        text: '已完成',
        className: 'bg-green-100 text-green-800 border-green-200'
      };
    case '进行中':
      return {
        icon: Clock,
        text: '进行中',
        className: 'bg-blue-100 text-blue-800 border-blue-200'
      };
    case '待开始':
    case '待执行':
      return {
        icon: AlertCircle,
        text: executionStatus,
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
      };
    case '已逾期':
      return {
        icon: XCircle,
        text: '已逾期',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    case '已取消':
      return {
        icon: XCircle,
        text: '已取消',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    default:
      return {
        icon: Clock,
        text: executionStatus || '未知',
        className: 'bg-gray-100 text-gray-800 border-gray-200'
      };
  }
};

/**
 * 获取任务状态配置（保留用于兼容性）
 */
const getTaskStatusConfig = (status: number) => {
  switch (status) {
    case 0:
      return {
        icon: CheckCircle,
        text: '已完成',
        className: 'bg-green-100 text-green-800 border-green-200'
      };
    case 1:
      return {
        icon: Clock,
        text: '进行中',
        className: 'bg-blue-100 text-blue-800 border-blue-200'
      };
    case 2:
      return {
        icon: XCircle,
        text: '已取消',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    default:
      return {
        icon: Clock,
        text: '未知',
        className: 'bg-gray-100 text-gray-800 border-gray-200'
      };
  }
};

/**
 * 巡检结果页面
 * 提供查看和筛选巡检结果的功能
 */
const InspectionLog: React.FC = () => {
  // 页面焦点管理
  const { pageRef, titleRef } = usePageFocus({
    title: '巡检结果',
    autoFocus: true
  });

  // 表格焦点管理
  const { listRef, handleKeyDown: handleTableKeyDown } = useListFocus({
    itemSelector: 'tr[data-row-id]',
    autoFocusOnAdd: true
  });

  // 使用巡检结果Hook
  const {
    error,
    filter,
    filteredTaskLogs,
    isReading,
    selectedTaskDetails,
    getInspectionResults,
    setFilter,
    clearFilter,
    setSelectedTaskDetails,
    getOperationTypes,
    getOperators,
    getDepartments,
    // 分页相关
    currentPage,
    pageSize,
    totalPages,
    paginatedTaskLogs,
    setPage,
    setPageSize
  } = useInspectionLog();

  // 本地状态
  const [searchText, setSearchText] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [localFilter, setLocalFilter] = useState<InspectionLogFilter>({});
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedTaskName, setSelectedTaskName] = useState('');

  // 页码输入框引用
  const pageInputRef = React.useRef<HTMLInputElement>(null);

  // 分页下拉框状态
  const [showPageSizeDropdown, setShowPageSizeDropdown] = useState(false);
  const pageSizeDropdownRef = useRef<HTMLDivElement>(null);

  // 组件挂载时自动获取巡检结果
  useEffect(() => {
    // 使用一个标志来避免重复调用
    let isMounted = true;

    const fetchData = async () => {
      try {
        if (isMounted) {
          await getInspectionResults();
        }
      } catch (err) {
        if (isMounted) {
          console.error('获取巡检结果失败:', err);
        }
      }
    };

    fetchData();

    // 清理函数
    return () => {
      isMounted = false;
    };
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 当页码变化时，更新页码输入框的值
  useEffect(() => {
    if (pageInputRef.current) {
      pageInputRef.current.value = (currentPage + 1).toString();
    }
  }, [currentPage]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pageSizeDropdownRef.current && !pageSizeDropdownRef.current.contains(event.target as Node)) {
        setShowPageSizeDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理搜索
  const handleSearch = () => {
    setFilter({ ...filter, searchText });
  };

  // 处理按回车键搜索
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理清除搜索
  const handleClearSearch = () => {
    setSearchText('');
    setFilter({ ...filter, searchText: '' });
  };

  // 处理应用筛选
  const handleApplyFilter = () => {
    setFilter(localFilter);
    setShowFilters(false);
  };

  // 处理清除筛选
  const handleClearFilter = () => {
    setLocalFilter({});
    clearFilter();
  };

  // 处理刷新数据（强制刷新，忽略缓存）
  const handleGetResults = async () => {
    try {
      await getInspectionResults();
    } catch (err) {
      console.error('刷新巡检结果失败:', err);
    }
  };

  // 处理查看任务详情
  const handleViewDetails = (task: InspectionTaskLogItem) => {
    setSelectedTaskName(task.task_name);
    setSelectedTaskDetails(task.inspection_details || []);
    setShowDetailModal(true);
  };

  // 使用统一的时间戳格式化函数，自动处理秒级/毫秒级时间戳
  const formatTimestamp = formatInspectionDateTime;





  return (
    <div ref={pageRef as any} className="h-full flex flex-col">
      {/* 页面标题（屏幕阅读器用） */}
      <h1 ref={titleRef as any} className="sr-only">巡检结果</h1>

      {/* 操作工具栏 - 降低高度 */}
      <div className="flex-none bg-gray-50 border-b border-gray-200 sticky top-0 z-10">
        <div className="px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={handleGetResults}
                disabled={isReading}
                className="btn btn-primary"
              >
                <RefreshCw className={`h-3.5 w-3.5 mr-1.5 ${isReading ? 'animate-spin' : ''}`} />
                刷新数据
              </button>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`btn ${
                  Object.keys(filter).length > 0
                    ? 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                } border`}
              >
                <Filter className="h-3.5 w-3.5 mr-1.5" />
                筛选
                {Object.keys(filter).length > 0 && (
                  <span className="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-white bg-blue-600 rounded-full">
                    {Object.keys(filter).length}
                  </span>
                )}
              </button>
            </div>

            <div className="relative flex-1 max-w-md ml-4">
              <input
                type="text"
                placeholder="搜索结果内容..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full pl-8 pr-8 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-all duration-200"
              />
              <Search className="absolute search-icon left-2.5 top-2 h-3.5 w-3.5 text-gray-400" />
              {searchText && (
                <button
                  onClick={handleClearSearch}
                  className="absolute right-2.5 top-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 错误信息卡片 - 统一风格 */}
      {error && (
        <div className="mx-6 mt-6 bg-white rounded-lg border border-red-200 shadow-md overflow-hidden">
          <div className="px-6 py-4 bg-gradient-to-r from-red-50 to-pink-50 border-b border-red-200">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">错误信息</span>
            </div>
          </div>
          <div className="p-6">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-600 mr-3 flex-shrink-0" />
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          </div>
        </div>
      )}

      {/* 筛选面板卡片 - 紧凑优化版 */}
      {showFilters && (
        <div className="mx-4 mt-3 mb-3 bg-white rounded border border-gray-200 shadow-sm overflow-hidden">
          {/* 筛选面板头部 - 进一步减小高度 */}
          <div className="px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <h4 className="text-sm font-medium text-gray-800">筛选条件</h4>
            </div>
            <button
              onClick={() => setShowFilters(false)}
              className="p-1 text-gray-400 hover:text-gray-600 hover:bg-white rounded transition-all duration-200"
              title="关闭筛选"
            >
              <X className="h-3.5 w-3.5" />
            </button>
          </div>

          {/* 筛选表单 - 紧凑布局 */}
          <div className="px-4 py-3">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {/* 开始日期 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  <span className="flex items-center space-x-1">
                    <span>开始日期</span>
                    <span className="text-red-400">*</span>
                  </span>
                </label>
                <DatePicker
                  name="startDate"
                  value={localFilter.startDate || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, startDate: e.target.value })}
                  placeholder="请选择开始日期"
                />
              </div>

              {/* 结束日期 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  <span className="flex items-center space-x-1">
                    <span>结束日期</span>
                    <span className="text-red-400">*</span>
                  </span>
                </label>
                <DatePicker
                  name="endDate"
                  value={localFilter.endDate || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, endDate: e.target.value })}
                  placeholder="请选择结束日期"
                />
              </div>

              {/* 操作类型 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  操作类型
                </label>
                <CustomSelect
                  name="operationType"
                  value={localFilter.operationType || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, operationType: e.target.value || undefined })}
                  options={[
                    { value: '', label: '全部类型' },
                    ...getOperationTypes().map(type => ({ value: type, label: type }))
                  ]}
                  placeholder="请选择操作类型"
                />
              </div>

              {/* 巡检人 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  巡检人
                </label>
                <CustomSelect
                  name="operator"
                  value={localFilter.operator || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, operator: e.target.value || undefined })}
                  options={[
                    { value: '', label: '全部人员' },
                    ...getOperators().map(operator => ({ value: operator, label: operator }))
                  ]}
                  placeholder="请选择巡检人"
                />
              </div>

              {/* 巡检部门 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  巡检部门
                </label>
                <CustomSelect
                  name="department"
                  value={localFilter.department || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, department: e.target.value || undefined })}
                  options={[
                    { value: '', label: '全部部门' },
                    ...getDepartments().map(department => ({ value: department, label: department }))
                  ]}
                  placeholder="请选择巡检部门"
                />
              </div>
            </div>

            {/* 操作按钮 - 紧凑布局 */}
            <div className="mt-4 flex items-center justify-between">
              <div className="text-xs text-gray-500">
                {Object.keys(filter).length > 0 && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    已应用 {Object.keys(filter).length} 个条件
                  </span>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleClearFilter}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-all duration-200"
                >
                  清除筛选
                </button>
                <button
                  onClick={handleApplyFilter}
                  className="inline-flex items-center px-4 py-1.5 border border-transparent rounded text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm hover:shadow transition-all duration-200"
                >
                  应用筛选
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 自适应表格容器 */}
      <div className="flex-1 flex flex-col min-h-0 relative">
        {/* 简化的表格中间刷新指示器 */}
        {isReading && filteredTaskLogs.length > 0 && (
          <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-20">
            <div className="flex flex-col items-center">
              <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mb-2" />
              <p className="text-blue-600 text-sm font-medium">获取数据中...</p>
            </div>
          </div>
        )}

        {/* 自适应数据表格 */}
        <div className="flex-1 min-h-0 bg-white shadow-sm adaptive-table-container">
          <div className="adaptive-table-content">
            <CustomScrollbar
              className="h-full"
              horizontal={true}
              vertical={true}
            >
              <div ref={listRef as any} onKeyDown={handleTableKeyDown}>
                <table className="w-full adaptive-table" style={{ minWidth: '1200px' }}>
            {/* 表头 */}
            <thead>
              <tr>
                <th className="table-header-cell table-cell-left w-16">任务ID</th>
                <th className="table-header-cell table-cell-left w-32">任务名称</th>
                <th className="table-header-cell table-cell-left w-24">责任人</th>
                <th className="table-header-cell table-cell-left w-40">巡检部门</th>
                <th className="table-header-cell table-cell-left w-32">开始时间</th>
                <th className="table-header-cell table-cell-left w-32">结束时间</th>
                <th className="table-header-cell table-cell-right w-20">设备总数</th>
                <th className="table-header-cell table-cell-right w-20">已巡检数</th>
                <th className="table-header-cell table-cell-right w-20">正常数量</th>
                <th className="table-header-cell table-cell-right w-20">异常数量</th>
                <th className="table-header-cell table-cell-center w-20">任务状态</th>
                <th className="table-header-cell table-cell-center w-16">操作</th>
              </tr>
            </thead>

            {/* 表体 */}
            <tbody className="bg-white">
              {filteredTaskLogs.length === 0 ? (
                <tr className="adaptive-table-empty">
                  <td colSpan={12} className="px-4 py-12 text-center">
                    <div className="flex flex-col items-center justify-center h-full min-h-[200px]">
                      <RefreshCw className={`h-8 w-8 mb-3 ${
                        isReading ? 'text-blue-600 animate-spin' : 'text-gray-400'
                      }`} />
                      <p className="text-gray-500 text-sm font-medium">
                        {isReading ? '获取数据中...' : '暂无巡检结果数据'}
                      </p>
                      {!isReading && (
                        <p className="text-gray-400 text-xs mt-1">
                          数据已自动获取，如需最新数据请点击"刷新数据"按钮
                        </p>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                paginatedTaskLogs.map((task) => (
                  <tr
                    key={task.task_id}
                    data-row-id={task.task_id}
                    className="relative focus:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                    tabIndex={0}
                  >
                    {/* 任务ID */}
                    <td className="table-cell whitespace-nowrap w-16">
                      <span className="text-sm font-medium text-blue-600">
                        #{task.task_id}
                      </span>
                    </td>

                    {/* 任务名称 */}
                    <td className="table-cell w-32">
                      <div className="flex items-center">
                        <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <Building className="h-3 w-3" />
                        </div>
                        <span className="text-sm font-medium text-gray-900 truncate" title={task.task_name}>
                          {task.task_name}
                        </span>
                      </div>
                    </td>

                    {/* 责任人 */}
                    <td className="table-cell whitespace-nowrap w-24">
                      <div className="flex items-center">
                        <div className="w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                          <User className="h-3 w-3" />
                        </div>
                        <div className="flex flex-col">
                          <span className="text-sm text-gray-900 truncate" title={task.person_name}>
                            {task.person_name}
                          </span>
                          {task.person_alias && (
                            <span className="text-xs text-gray-500 truncate" title={task.person_alias}>
                              {task.person_alias}
                            </span>
                          )}
                        </div>
                      </div>
                    </td>

                    {/* 巡检部门 - 使用Portal悬浮框 */}
                    <td className="table-cell w-40">
                      {task.departments.length > 0 ? (
                        <DepartmentTooltip departments={task.departments}>
                          <div className="flex items-center">
                            <span className="inline-block text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded truncate max-w-32" title={task.departments[0]}>
                              {task.departments[0]}
                            </span>
                            {task.departments.length > 1 && (
                              <span className="ml-1 text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded">
                                +{task.departments.length - 1}
                              </span>
                            )}
                          </div>
                        </DepartmentTooltip>
                      ) : (
                        <span className="text-xs text-gray-400">-</span>
                      )}
                    </td>

                    {/* 开始时间 */}
                    <td className="table-cell whitespace-nowrap w-32">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                        <span className="text-xs text-gray-700" title={formatTimestamp(task.start_date)}>
                          {formatTimestamp(task.start_date)}
                        </span>
                      </div>
                    </td>

                    {/* 结束时间 */}
                    <td className="table-cell whitespace-nowrap w-32">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                        <span className="text-xs text-gray-700" title={formatTimestamp(task.end_date)}>
                          {formatTimestamp(task.end_date)}
                        </span>
                      </div>
                    </td>

                    {/* 设备总数 */}
                    <td className="table-cell table-cell-right whitespace-nowrap w-20">
                      <span className="text-sm font-medium text-gray-900">
                        {task.device_count}
                      </span>
                    </td>

                    {/* 已巡检数 */}
                    <td className="table-cell table-cell-right whitespace-nowrap w-20">
                      <span className="text-sm font-medium text-blue-600">
                        {task.inspected_count}
                      </span>
                    </td>

                    {/* 正常数量 */}
                    <td className="table-cell table-cell-right whitespace-nowrap w-20">
                      <span className="text-sm font-medium text-green-600">
                        {task.normal_count}
                      </span>
                    </td>

                    {/* 异常数量 */}
                    <td className="table-cell table-cell-right whitespace-nowrap w-20">
                      <span className="text-sm font-medium text-red-600">
                        {task.exception_count}
                      </span>
                    </td>

                    {/* 任务状态 - 使用后端返回的task_execution_status */}
                    <td className="table-cell table-cell-center whitespace-nowrap w-20">
                      {(() => {
                        const config = getTaskExecutionStatusConfig(task.task_execution_status);
                        const Icon = config.icon;

                        return (
                          <span className={`inline-flex items-center px-1.5 py-1 rounded-full text-xs font-medium border ${config.className}`} title={config.text}>
                            <Icon className="h-3 w-3" />
                            <span className="ml-1 hidden sm:inline">{config.text}</span>
                          </span>
                        );
                      })()}
                    </td>

                    {/* 操作 */}
                    <td className="table-cell table-cell-center whitespace-nowrap w-16">
                      <button
                        onClick={() => handleViewDetails(task)}
                        className="inline-flex items-center justify-center w-8 h-8 text-blue-600 bg-blue-50 border border-blue-200 hover:bg-blue-100 transition-colors"
                        title="查看详情"
                      >
                        <Eye className="h-3 w-3" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
                </table>
              </div>
            </CustomScrollbar>
          </div>
        </div>

        {/* 自适应分页组件 */}
        {filteredTaskLogs.length > 0 && (
          <div className="flex-shrink-0 flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-200">
              <div className="flex items-center text-sm text-gray-600">
                <span className="mr-2">每页显示</span>
                <div className="relative inline-block mx-1" style={{ width: '65px' }} ref={pageSizeDropdownRef}>
                  <div
                    className="bg-white border border-slate-300 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer relative"
                    style={{ padding: '3px 24px 3px 6px', fontSize: '14px', textAlign: 'center', height: '28px', lineHeight: '22px' }}
                    onClick={() => setShowPageSizeDropdown(!showPageSizeDropdown)}
                  >
                    <span className="font-medium text-slate-700">
                      {pageSize === -1 ? '全部' : pageSize}
                    </span>
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <ChevronDown className={`h-4 w-4 text-slate-500 transition-transform duration-200 ${showPageSizeDropdown ? 'rotate-180' : ''}`} />
                    </div>
                  </div>

                  {/* 下拉选项 */}
                  {showPageSizeDropdown && (
                    <div className="absolute z-20 bg-white border border-slate-300 shadow-lg" style={{ top: '-160px', left: '0px', width: '100%', maxHeight: '160px', overflowY: 'auto' }}>
                      <div className="py-0.5">
                        <div
                          className={`px-2.5 py-1.5 cursor-pointer transition-all duration-150 flex items-center justify-between text-slate-700 hover:bg-slate-50 ${pageSize === 5 ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-400' : ''}`}
                          onClick={() => {
                            setPageSize(5);
                            setShowPageSizeDropdown(false);
                          }}
                        >
                          <span className="text-sm font-medium">5</span>
                          {pageSize === 5 && <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>}
                        </div>
                        <div
                          className={`px-2.5 py-1.5 cursor-pointer transition-all duration-150 flex items-center justify-between text-slate-700 hover:bg-slate-50 ${pageSize === 10 ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-400' : ''}`}
                          onClick={() => {
                            setPageSize(10);
                            setShowPageSizeDropdown(false);
                          }}
                        >
                          <span className="text-sm font-medium">10</span>
                          {pageSize === 10 && <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>}
                        </div>
                        <div
                          className={`px-2.5 py-1.5 cursor-pointer transition-all duration-150 flex items-center justify-between text-slate-700 hover:bg-slate-50 ${pageSize === 20 ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-400' : ''}`}
                          onClick={() => {
                            setPageSize(20);
                            setShowPageSizeDropdown(false);
                          }}
                        >
                          <span className="text-sm font-medium">20</span>
                          {pageSize === 20 && <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>}
                        </div>
                        <div
                          className={`px-2.5 py-1.5 cursor-pointer transition-all duration-150 flex items-center justify-between text-slate-700 hover:bg-slate-50 ${pageSize === 30 ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-400' : ''}`}
                          onClick={() => {
                            setPageSize(30);
                            setShowPageSizeDropdown(false);
                          }}
                        >
                          <span className="text-sm font-medium">30</span>
                          {pageSize === 30 && <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>}
                        </div>
                        <div
                          className={`px-2.5 py-1.5 cursor-pointer transition-all duration-150 flex items-center justify-between text-slate-700 hover:bg-slate-50 ${pageSize === 40 ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-400' : ''}`}
                          onClick={() => {
                            setPageSize(40);
                            setShowPageSizeDropdown(false);
                          }}
                        >
                          <span className="text-sm font-medium">40</span>
                          {pageSize === 40 && <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>}
                        </div>
                        <div
                          className={`px-2.5 py-1.5 cursor-pointer transition-all duration-150 flex items-center justify-between text-slate-700 hover:bg-slate-50 rounded-b-md ${pageSize === 50 ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-400' : ''}`}
                          onClick={() => {
                            setPageSize(50);
                            setShowPageSizeDropdown(false);
                          }}
                        >
                          <span className="text-sm font-medium">50</span>
                          {pageSize === 50 && <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>}
                        </div>
                        <div
                          className={`px-2.5 py-1.5 cursor-pointer transition-all duration-150 flex items-center justify-between text-slate-700 hover:bg-slate-50 rounded-b-md ${pageSize === -1 ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-400' : ''}`}
                          onClick={() => {
                            setPageSize(-1);
                            setShowPageSizeDropdown(false);
                          }}
                        >
                          <span className="text-sm font-medium">全部</span>
                          {pageSize === -1 && <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <span className="ml-2">条，共 <span className="font-medium text-gray-800">{filteredTaskLogs.length}</span> 条记录</span>
              </div>

              <div className="flex items-center space-x-1">
                {/* 第一页按钮 */}
                <button
                  disabled={currentPage === 0}
                  onClick={() => setPage(0)}
                  className={`p-1 border transition-all duration-200 ${
                    currentPage === 0
                      ? 'text-slate-300 cursor-not-allowed border-slate-200 bg-slate-100'
                      : 'text-slate-600 hover:bg-blue-50 border-slate-300 hover:border-blue-300 hover:text-blue-600'
                  }`}
                  title="第一页"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <path d="m11 17-5-5 5-5"></path>
                    <path d="m18 17-5-5 5-5"></path>
                  </svg>
                </button>

                {/* 上一页按钮 */}
                <button
                  disabled={currentPage === 0}
                  onClick={() => setPage(currentPage - 1)}
                  className={`p-1 border transition-all duration-200 ${
                    currentPage === 0
                      ? 'text-slate-300 cursor-not-allowed border-slate-200 bg-slate-100'
                      : 'text-slate-600 hover:bg-blue-50 border-slate-300 hover:border-blue-300 hover:text-blue-600'
                  }`}
                  title="上一页"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <path d="m15 18-6-6 6-6"></path>
                  </svg>
                </button>

                {/* 页码信息 */}
                <span className="text-sm text-slate-600 flex items-center mx-1.5">
                  <span className="mr-1.5">第</span>
                  <form className="mx-0.5" onSubmit={(e) => {
                    e.preventDefault();
                    if (pageInputRef.current) {
                      const value = parseInt(pageInputRef.current.value, 10);
                      if (!isNaN(value) && value > 0 && value <= totalPages) {
                        setPage(value - 1);
                      } else {
                        pageInputRef.current.value = (currentPage + 1).toString();
                      }
                    }
                  }}>
                    <input
                      ref={pageInputRef}
                      type="text"
                      className="border border-slate-300 w-10 py-0.5 px-1 text-center text-sm font-medium focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200"
                      title="输入页码后按回车键跳转"
                      defaultValue={currentPage + 1}
                    />
                  </form>
                  <span className="mx-0.5">页，共</span>
                  <span className="font-medium text-slate-800 mx-0.5">{totalPages}</span>
                  <span>页</span>
                </span>

                {/* 下一页按钮 */}
                <button
                  disabled={currentPage >= totalPages - 1}
                  onClick={() => setPage(currentPage + 1)}
                  className={`p-1 border transition-all duration-200 ${
                    currentPage >= totalPages - 1
                      ? 'text-slate-300 cursor-not-allowed border-slate-200 bg-slate-100'
                      : 'text-slate-600 hover:bg-blue-50 border-slate-300 hover:border-blue-300 hover:text-blue-600'
                  }`}
                  title="下一页"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <path d="m9 18 6-6-6-6"></path>
                  </svg>
                </button>

                {/* 最后一页按钮 */}
                <button
                  disabled={currentPage >= totalPages - 1}
                  onClick={() => setPage(totalPages - 1)}
                  className={`p-1 border transition-all duration-200 ${
                    currentPage >= totalPages - 1
                      ? 'text-slate-300 cursor-not-allowed border-slate-200 bg-slate-100'
                      : 'text-slate-600 hover:bg-blue-50 border-slate-300 hover:border-blue-300 hover:text-blue-600'
                  }`}
                  title="最后一页"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <path d="m6 17 5-5-5-5"></path>
                    <path d="m13 17 5-5-5-5"></path>
                  </svg>
                </button>
              </div>
            </div>
          )}
      </div>

      {/* 巡检详情弹窗 */}
      <InspectionDetailModal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        taskName={selectedTaskName}
        details={selectedTaskDetails || []}
      />
    </div>
  );
};

export default InspectionLog;
