import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useCache } from '../contexts/CacheContext';
import {
  ChevronDown,
  Monitor,
  Shield,
  Network,
  FileText,
  FileCheck,
  Database,
  Package,
  HardDrive,
  Server,
  FileWarning,
  Mail,
  History,
  Search,
  FolderSearch,
  Users,
  Key,
  Lock,
  ShieldAlert,
  FileArchive,
  Image,
  File,
  LucideIcon
} from 'lucide-react';

interface SubNavItem {
  name: string;
  path: string;
  icon?: LucideIcon;
  children?: SubNavItem[];
}

interface SidebarProps {
  items: SubNavItem[];
  basePath: string;
}

const Sidebar: React.FC<SidebarProps> = ({ items, basePath }) => {
  const location = useLocation();
  const { expandedItems, toggleExpanded } = useCache();

  const isActive = (path: string) => {
    return location.pathname === `${basePath}${path}`;
  };

  const isExpanded = (path: string) => {
    return expandedItems.includes(path);
  };

  const getIcon = (item: SubNavItem) => {
    if (item.icon) {
      const Icon = item.icon;
      return <Icon className="h-5 w-5 mr-3" />;
    }
    
    // 默认图标映射
    const defaultIcons: { [key: string]: LucideIcon } = {
      // 系统检查
      'basic-info': Monitor,
      'software': Package,
      'device': HardDrive,
      'virtual-machine': Server,
      'system-log': FileWarning,
      
      // 安全防护
      'run-security': Shield,
      'security-policy': Lock,
      'security-products': ShieldAlert,
      
      // 网络检查
      'status': Network,
      'web-history': History,
      'email-check': Mail,
      'deep-check': Search,
      
      // 文件检查
      'access-records': FileText,
      'file-history': History,
      'cache-check': Database,
      'backup-check': FileArchive,
      'content-check': FolderSearch,
      
      // 系统设置
      'account-manage': Users,
      'policy-manage': Key,
      'keyword-manage': Search,
      
      // 检查报告
      'smart-report': FileCheck,
      'normal-report': File,
      'qr-code': Image,
    };

    const pathKey = item.path.split('/')[1];
    return defaultIcons[pathKey] ? 
      React.createElement(defaultIcons[pathKey], { className: "h-5 w-5 mr-3" }) :
      <File className="h-5 w-5 mr-3" />;
  };

  const renderNavItem = (item: SubNavItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isItemActive = isActive(item.path);
    const isItemExpanded = isExpanded(item.path);
    const fullPath = `${basePath}${item.path}`;
    
    const content = (
      <div
        className={`flex items-center py-2 px-4 mx-2 my-1 rounded-lg transition-all duration-200 ${
          isItemActive
            ? 'text-blue-600 bg-blue-50 font-medium shadow-sm'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        }`}
      >
        {getIcon(item)}
        <span className="flex-1">{item.name}</span>
        {hasChildren && (
          <div className={`transition-transform duration-200 ${isItemExpanded ? 'rotate-180' : ''}`}>
            <ChevronDown className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
          </div>
        )}
      </div>
    );
    
    return (
      <div key={item.path} className="relative">
        <div
          className={`group cursor-pointer ${hasChildren ? 'pr-4' : ''}`}
          onClick={() => hasChildren ? toggleExpanded(item.path) : null}
        >
          {hasChildren ? (
            content
          ) : (
            <Link to={fullPath}>
              {content}
            </Link>
          )}
        </div>
        
        {hasChildren && isItemExpanded && item.children && (
          <div className="ml-4 mt-1 space-y-1 border-l-2 border-gray-100">
            {item.children.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full overflow-y-auto">
      <nav className="py-4 px-2">
        {items.map(item => renderNavItem(item))}
      </nav>
    </div>
  );
};

export default React.memo(Sidebar);