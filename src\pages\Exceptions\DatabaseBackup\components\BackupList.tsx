import React from 'react';
import { CheckCircle, GitBranch, Clock, HardDrive, Hash, Loader2 } from 'lucide-react';
import { BackupData } from '../../../../services/Backup/backupService';

interface BackupListProps {
  backups: BackupData[];
  activeBackup: BackupData | null;
  isSwitching: boolean;
  onSwitchBackup: (backupId: number) => void;
  formatFileSize: (bytes: number) => string;
  formatRelativeTime: (timestamp: number) => string;
}

export const BackupList: React.FC<BackupListProps> = ({
  backups,
  activeBackup,
  isSwitching,
  onSwitchBackup,
  formatFileSize,
  formatRelativeTime,
}) => {
  if (backups.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="p-6 bg-gray-50 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
          <GitBranch className="w-12 h-12 text-gray-300" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-3">暂无备份版本</h3>
        <p className="text-gray-500 max-w-md mx-auto leading-relaxed">
          还没有创建任何数据库备份。点击上方的"创建备份"按钮来创建您的第一个备份版本，开始使用版本管理功能。
        </p>
        <div className="mt-6 flex justify-center">
          <div className="px-4 py-2 bg-blue-50 text-blue-700 rounded-lg text-sm border border-blue-200">
            💡 提示：备份可以帮助您快速恢复数据
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {backups.map((backup, index) => {
        const isActive = activeBackup?.backup_id === backup.backup_id || backup.is_active === 1;
        const canSwitch = !isActive && !isSwitching;

        return (
          <div
            key={backup.backup_id}
            className={`border rounded-lg transition-all duration-200 ${
              isActive
                ? 'border-green-300 bg-green-50'
                : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50/30'
            }`}
          >
            {/* 主要信息行 */}
            <div className="p-4">
              <div className="flex items-center justify-between">
                {/* 左侧：版本信息 */}
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  {/* 版本序号 */}
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">#{backups.length - index}</span>
                    </div>
                  </div>

                  {/* 版本图标和名称 */}
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <div className={`p-1.5 rounded ${isActive ? 'bg-green-100' : 'bg-blue-100'}`}>
                      <GitBranch className={`w-4 h-4 ${isActive ? 'text-green-600' : 'text-blue-600'}`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 truncate text-sm">
                        {backup.backup_name || backup.backup_filename}
                      </h3>
                      <div className="flex items-center space-x-4 text-xs text-gray-500 mt-0.5">
                        <span>{formatRelativeTime(backup.backup_timestamp)}</span>
                        <span>{formatFileSize(backup.backup_size)}</span>
                        {backup.md5_hash && (
                          <span className="font-mono">MD5: {backup.md5_hash}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 右侧：状态和操作 */}
                <div className="flex items-center space-x-3 flex-shrink-0">
                  {/* 活动状态标识 */}
                  {isActive && (
                    <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-700 rounded-full border border-green-200">
                      <CheckCircle className="w-3 h-3" />
                      <span className="text-xs font-medium">当前版本</span>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  {canSwitch && (
                    <button
                      onClick={() => onSwitchBackup(backup.backup_id)}
                      className="px-3 py-1.5 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                    >
                      切换到此版本
                    </button>
                  )}

                  {isSwitching && !isActive && (
                    <div className="px-3 py-1.5 text-xs text-gray-600 bg-gray-100 rounded flex items-center">
                      <Loader2 className="w-3 h-3 animate-spin mr-1" />
                      切换中...
                    </div>
                  )}
                </div>
              </div>

              {/* 详细信息（可展开） */}
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                  {/* 创建时间 */}
                  <div>
                    <div className="flex items-center space-x-1 text-gray-500 mb-1">
                      <Clock className="w-3 h-3" />
                      <span>创建时间</span>
                    </div>
                    <p className="text-gray-700 font-medium">{backup.backup_time_str}</p>
                  </div>

                  {/* 文件大小 */}
                  <div>
                    <div className="flex items-center space-x-1 text-gray-500 mb-1">
                      <HardDrive className="w-3 h-3" />
                      <span>文件大小</span>
                    </div>
                    <p className="text-gray-700 font-medium">{formatFileSize(backup.backup_size)}</p>
                  </div>

                  {/* MD5校验 */}
                  <div>
                    <div className="flex items-center space-x-1 text-gray-500 mb-1">
                      <Hash className="w-3 h-3" />
                      <span>MD5校验</span>
                    </div>
                    <p className="text-gray-700 font-mono text-xs" title={backup.md5_hash || '未提供'}>
                      {backup.md5_hash || '未提供'}
                    </p>
                  </div>
                </div>

                {/* 完整路径（如果需要） */}
                {backup.backup_path && (
                  <div className="mt-2 pt-2 border-t border-gray-100">
                    <p className="text-xs text-gray-500 mb-1">完整路径:</p>
                    <p className="text-xs text-gray-600 font-mono break-all bg-gray-50 p-2 rounded">
                      {backup.backup_path}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};