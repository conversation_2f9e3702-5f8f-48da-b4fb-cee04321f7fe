import React, { useEffect, useState } from 'react';
import { X, Check, AlertCircle, ChevronRight, Info, HelpCircle } from 'lucide-react';

// 对话框属性类型
interface ImportDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'success' | 'error';
  details?: string[];
  hasFailedRecords?: boolean;
  failedRecordsCount?: number;
}

/**
 * 导入详情对话框组件
 * 用于显示导入成功或失败的详细信息
 */
const ImportDetailDialog: React.FC<ImportDetailDialogProps> = ({
  isOpen,
  onClose,
  type,
  details = [],
  hasFailedRecords = false,
  failedRecordsCount = 0
}) => {
  // 添加状态管理
  const [activeTab, setActiveTab] = useState<'errors' | 'solutions'>('errors');
  const [expandedErrors, setExpandedErrors] = useState<number[]>([]);

  // ESC键处理已由统一的焦点管理系统处理，无需重复实现

  // 切换错误展开状态
  const toggleErrorExpand = (index: number) => {
    if (expandedErrors.includes(index)) {
      setExpandedErrors(expandedErrors.filter(i => i !== index));
    } else {
      setExpandedErrors([...expandedErrors, index]);
    }
  };

  // 展开所有错误
  const expandAllErrors = () => {
    const allIndexes = details
      .map((detail, index) => ({ detail, index }))
      .filter(({ detail }) =>
        detail.startsWith('第') ||
        detail.includes('责任人所在部门') ||
        detail.includes('数据不完整')
      )
      .map(({ index }) => index);

    setExpandedErrors(allIndexes);
  };

  // 收起所有错误
  const collapseAllErrors = () => {
    setExpandedErrors([]);
  };

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  // 提取错误和解决方案
  const errorDetails = details.filter(detail =>
    !detail.startsWith('常见解决方案:') &&
    !detail.startsWith('1.') &&
    !detail.startsWith('2.') &&
    !detail.startsWith('3.') &&
    !detail.startsWith('4.') &&
    detail !== ''
  );

  const solutionDetails = details.filter(detail =>
    detail.startsWith('常见解决方案:') ||
    detail.startsWith('1.') ||
    detail.startsWith('2.') ||
    detail.startsWith('3.') ||
    detail.startsWith('4.')
  );

  // 计算错误数量
  const errorCount = errorDetails.filter(detail =>
    detail.startsWith('第') ||
    detail.includes('责任人所在部门') ||
    detail.includes('数据不完整')
  ).length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-[480px] max-w-full animate-fadeIn">
        {/* 对话框标题 */}
        <div className="flex items-center justify-between px-4 py-3 border-b">
          <h2 className="text-base font-semibold flex items-center">
            {type === 'success' ? (
              <Check className="h-4 w-4 mr-2 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
            )}
            {type === 'success' ? '导入成功详情' : '导入失败详情'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors rounded-full p-1 hover:bg-gray-100"
            title="关闭"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-0">
          {/* 标签页导航 - 简化版 */}
          {type === 'error' && (
            <div className="flex border-b">
              <button
                className={`px-4 py-2 text-sm flex items-center ${activeTab === 'errors' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'}`}
                onClick={() => setActiveTab('errors')}
              >
                <AlertCircle className={`h-3.5 w-3.5 mr-1.5 ${activeTab === 'errors' ? 'text-red-500' : 'text-gray-500'}`} />
                错误详情
                {errorCount > 0 && (
                  <span className="ml-1.5 bg-red-100 text-red-800 text-xs px-1.5 py-0.5 rounded-full">{errorCount}</span>
                )}
              </button>
              <button
                className={`px-4 py-2 text-sm flex items-center ${activeTab === 'solutions' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'}`}
                onClick={() => setActiveTab('solutions')}
              >
                <HelpCircle className={`h-3.5 w-3.5 mr-1.5 ${activeTab === 'solutions' ? 'text-blue-500' : 'text-gray-500'}`} />
                解决方案
              </button>
            </div>
          )}

          {/* 错误详情内容 - 精简版 */}
          {activeTab === 'errors' && (
            <div className="px-4 py-3">
              {/* 错误标题和操作按钮 */}
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium flex items-center text-gray-700">
                  <AlertCircle className="h-3.5 w-3.5 mr-1.5 text-red-500" />
                  导入失败原因
                </h3>
                {/* 只有当有行级错误时才显示展开/收起按钮 */}
                {errorDetails.some(detail =>
                  detail.startsWith('第') ||
                  detail.includes('责任人所在部门') ||
                  detail.includes('数据不完整')
                ) && (
                  <div className="flex space-x-1">
                    <button
                      onClick={expandAllErrors}
                      className="text-xs flex items-center px-1.5 py-0.5 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                    >
                      <ChevronRight className="h-3 w-3 mr-0.5" />
                      展开
                    </button>
                    <button
                      onClick={collapseAllErrors}
                      className="text-xs flex items-center px-1.5 py-0.5 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                    >
                      <ChevronRight className="h-3 w-3 mr-0.5 transform rotate-90" />
                      收起
                    </button>
                  </div>
                )}
              </div>

              {/* 错误列表 */}
              <div className="bg-white rounded border border-gray-200 max-h-[250px] overflow-y-auto">
                {errorDetails.map((detail, index) => {
                  // 跳过标题行
                  if (detail === '导入失败原因:') {
                    return null;
                  }

                  // 提取行号和序号的通用函数
                  const extractLineAndRowNumber = (text) => {
                    const match = text.match(/第(\d+)行，序号:\s*(\d+)/);
                    return match ? { lineNum: match[1], rowNum: match[2] } : null;
                  };

                  // 渲染错误项的通用函数
                  const renderErrorItem = (lineNum, rowNum, errorMsg) => {
                    const isExpanded = expandedErrors.includes(index);
                    return (
                      <div key={index} className="hover:bg-gray-50 transition-colors border-b border-gray-100">
                        <div
                          className="p-2 flex items-start cursor-pointer"
                          onClick={() => toggleErrorExpand(index)}
                        >
                          <ChevronRight className={`h-3.5 w-3.5 mt-0.5 mr-1.5 text-gray-500 transition-transform ${isExpanded ? 'transform rotate-90' : ''}`} />
                          <div className="flex-1">
                            <div className="text-sm text-gray-700">第{lineNum}行，序号: {rowNum}</div>
                            {isExpanded && (
                              <div className="mt-1.5 ml-1 p-1.5 bg-red-50 text-red-700 rounded text-xs border-l-2 border-red-500">
                                {errorMsg}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  };

                  // 处理包含"第X行，序号: Y"格式的错误信息
                  if (detail.startsWith('第') && detail.includes('行') && detail.includes('序号')) {
                    const match = detail.match(/第(\d+)行，序号:\s*(\d+)\s*(.*)/);
                    if (match) {
                      const [_, lineNum, rowNum, errorMsg] = match;
                      return renderErrorItem(lineNum, rowNum, errorMsg || '责任人所在部门与当前指定部门不一致');
                    }
                  }

                  // 处理"数据不完整，缺少必要字段"格式的错误
                  if (detail.includes('数据不完整') && detail.includes('缺少必要字段')) {
                    const info = extractLineAndRowNumber(detail);
                    if (info) {
                      const errorMsg = '数据不完整，缺少必要字段: ' + (detail.split('缺少必要字段:')[1]?.trim() || '使用情况');
                      return renderErrorItem(info.lineNum, info.rowNum, errorMsg);
                    }
                  }

                  // 处理"责任人所在部门与当前指定部门不一致"格式的错误
                  if (detail.includes('责任人所在部门') && detail.includes('与当前指定部门') && detail.includes('不一致')) {
                    const info = extractLineAndRowNumber(detail);
                    if (info) {
                      return renderErrorItem(info.lineNum, info.rowNum, '责任人所在部门与当前指定部门不一致');
                    }
                  }

                  // 处理文件格式错误信息
                  if (detail.includes('文件格式无效') || detail.includes('Excel文件缺少必要表头字段')) {
                    return (
                      <div key={index} className="p-3 bg-red-50 rounded-md">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mr-2">
                            <AlertCircle className="h-5 w-5 text-red-500" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-red-700 mb-1">
                              {detail.includes('Excel文件缺少必要表头字段') ? '表头字段缺失' : '文件格式无效'}
                            </p>
                            <p className="text-xs text-red-600">
                              {detail.includes('Excel文件缺少必要表头字段')
                                ? '导入文件缺少必要的表头字段，无法完成导入'
                                : '文件格式无效，请使用正确的导入模板'}
                            </p>
                            {detail.includes('Excel文件缺少必要表头字段') && (
                              <div className="mt-2 p-2 bg-white border border-red-100 rounded text-xs">
                                <p className="font-medium text-gray-700 mb-1">请确保导入文件包含以下必要字段：</p>
                                <ul className="list-disc pl-4 text-gray-600 space-y-0.5">
                                  {detail.split(':')[1].split(',').map((field, i) => (
                                    <li key={i}>{field.trim()}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  }

                  // 处理其他通用错误信息
                  return (
                    <div key={index} className="hover:bg-gray-50 transition-colors border-b border-gray-100">
                      <div
                        className="p-2 flex items-start cursor-pointer"
                        onClick={() => toggleErrorExpand(index)}
                      >
                        <ChevronRight className={`h-3.5 w-3.5 mt-0.5 mr-1.5 text-gray-500 transition-transform ${expandedErrors.includes(index) ? 'transform rotate-90' : ''}`} />
                        <div className="flex-1">
                          <div className="text-sm text-gray-700">{detail}</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* 没有错误时显示的内容 - 精简版 */}
              {errorDetails.filter(d => d !== '导入失败原因:').length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  <Info className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">没有详细的错误信息</p>
                </div>
              )}
            </div>
          )}

          {/* 解决方案内容 - 精简版 */}
          {activeTab === 'solutions' && (
            <div className="px-4 py-3">
              <h3 className="text-sm font-medium flex items-center mb-2 text-gray-700">
                <HelpCircle className="h-3.5 w-3.5 mr-1.5 text-blue-500" />
                常见解决方案
              </h3>

              <div className="bg-blue-50 p-3 rounded border border-blue-200">
                <ul className="space-y-2 text-sm">
                  {/* 渲染解决方案项的通用函数 */}
                  {(() => {
                    const renderSolutionItem = (num, text) => (
                      <li key={num} className="flex items-start">
                        <div className="h-4 w-4 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-2 flex-shrink-0 mt-0.5 font-medium text-xs">
                          {num}
                        </div>
                        <span className="text-blue-700 text-xs">{text}</span>
                      </li>
                    );

                    // 默认解决方案
                    const defaultSolutions = [
                      '确保使用正确的导入模板',
                      '检查必填字段是否已填写',
                      '验证数据格式是否符合要求',
                      '确保责任人所在部门与当前指定部门一致'
                    ];

                    // 如果有自定义解决方案，则使用自定义的
                    if (solutionDetails.length > 0) {
                      return solutionDetails
                        .filter(solution => !solution.startsWith('常见解决方案:'))
                        .filter(solution => /^\d\./.test(solution))
                        .map(solution => {
                          const num = solution.substring(0, 1);
                          const text = solution.substring(2).trim();
                          return renderSolutionItem(num, text);
                        });
                    }

                    // 否则使用默认解决方案
                    return defaultSolutions.map((solution, index) =>
                      renderSolutionItem(index + 1, solution)
                    );
                  })()}
                </ul>
              </div>
            </div>
          )}

          {/* 失败记录提示 - 精简版 */}
          {hasFailedRecords && failedRecordsCount > 0 && (
            <div className="mx-4 mb-3 p-2 bg-yellow-50 text-yellow-700 rounded border border-yellow-200">
              <div className="flex items-center">
                <span className="text-xs flex items-center">
                  <AlertCircle className="h-3.5 w-3.5 mr-1 text-yellow-600" />
                  有 {failedRecordsCount} 条记录导入失败
                </span>
              </div>
            </div>
          )}
        </div>

        {/* 对话框按钮 - 精简版 */}
        <div className="px-4 py-3 border-t flex justify-end">
          {type === 'error' && (
            <button
              onClick={() => setActiveTab(activeTab === 'errors' ? 'solutions' : 'errors')}
              className="px-3 py-1.5 mr-2 bg-white text-blue-600 border border-blue-200 rounded hover:bg-blue-50 transition-colors flex items-center text-sm"
            >
              {activeTab === 'errors' ? (
                <>
                  <HelpCircle className="h-3.5 w-3.5 mr-1" />
                  查看解决方案
                </>
              ) : (
                <>
                  <AlertCircle className="h-3.5 w-3.5 mr-1" />
                  查看错误详情
                </>
              )}
            </button>
          )}
          <button
            onClick={onClose}
            className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors flex items-center text-sm"
          >
            <X className="h-3.5 w-3.5 mr-1" />
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImportDetailDialog;
