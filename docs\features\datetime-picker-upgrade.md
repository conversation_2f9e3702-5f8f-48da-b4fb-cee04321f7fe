# 扩展字段时间选择器升级

## 📋 功能概述

将添加设备台账中扩展字段的时间选择器升级为巡检管理页面使用的时间选择器，支持日期和时间的同时选择，提供更好的用户体验。

## 🎯 修改范围

### **只修改扩展字段**
- ✅ **扩展字段的时间选择器**：升级为支持日期+时间选择
- ✅ **基础字段保持不变**：启用时间字段继续使用原有的日期选择器

### **不修改的部分**
- ❌ 基础字段的"启用时间"选择器
- ❌ 其他页面的时间选择器
- ❌ 表格显示的时间格式

## 🔧 技术实现

### **1. 使用的组件**
- **新时间选择器**：`src/components/Common/DatePicker.tsx`
- **支持属性**：`showTime={true}` 启用时间选择功能
- **API风格**：现代化的函数式API

### **2. 修改的文件**
`src/pages/Inventory/InventoryManagement/Dialogs/InventoryForm.tsx`

#### **扩展字段时间选择器**
```typescript
// 扩展字段使用新的时间选择器
<DatePicker
  value={dateValue}
  onChange={(date) => {
    onChange(field.key, date);
  }}
  placeholder={`请选择${field.title}`}
  disabled={disabled}
  showTime={true}        // 🔥 关键：启用时间选择
  className="text-sm"
/>
```

#### **基础字段兼容性处理**
```typescript
// 基础字段的启用时间保持兼容
<DatePicker
  value={formData.startTime}
  onChange={(date) => {
    // 创建合成事件对象以兼容原有的handleInputChange
    const syntheticEvent = {
      target: { name: 'startTime', value: date }
    } as React.ChangeEvent<HTMLInputElement>;
    handleInputChange(syntheticEvent);
  }}
  placeholder="请选择启用时间"
  error={!!errors.startTime}
  disabled={disabled}
  // 注意：没有showTime属性，保持只选择日期
/>
```

### **3. API差异处理**

#### **新API vs 旧API**
```typescript
// 新API（巡检管理风格）
interface NewDatePickerProps {
  value?: string;
  onChange: (date: string) => void;  // 直接传递日期字符串
  showTime?: boolean;                // 支持时间选择
}

// 旧API（原台账风格）
interface OldDatePickerProps {
  name?: string;
  value?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;  // 事件对象
}
```

#### **兼容性适配**
```typescript
// 为基础字段创建事件对象适配器
const syntheticEvent = {
  target: { name: 'startTime', value: date }
} as React.ChangeEvent<HTMLInputElement>;
```

## 🎨 用户体验改进

### **扩展字段时间选择器特点**
- **日期选择**：月历形式，直观易用
- **时间选择**：小时和分钟滚动选择
- **实时预览**：选择过程中实时显示格式化结果
- **键盘支持**：完整的键盘导航支持
- **移动友好**：适配小屏幕设备

### **时间格式**
- **输入格式**：`YYYY-MM-DD HH:mm`
- **显示格式**：`2024-01-15 14:30`
- **存储格式**：与后端API保持一致

## 🔍 识别逻辑

### **扩展字段时间检测**
```typescript
// 检测扩展字段是否为时间字段
if (isTimeField(field.title) || isTimeField(field.key) || field.type === 'date') {
  // 使用带时间选择的DatePicker
  return <DatePicker showTime={true} ... />;
}
```

### **检测条件**
1. **字段标题包含时间关键词**：`isTimeField(field.title)`
2. **字段键名包含时间关键词**：`isTimeField(field.key)`
3. **字段类型为日期**：`field.type === 'date'`

## 📱 界面对比

### **升级前（扩展字段）**
```
┌─────────────────────────┐
│ 创建时间               │
├─────────────────────────┤
│ 2024-01-15        ▼   │  ← 只能选择日期
└─────────────────────────┘
```

### **升级后（扩展字段）**
```
┌─────────────────────────┐
│ 创建时间               │
├─────────────────────────┤
│ 2024-01-15 14:30  ▼   │  ← 可以选择日期+时间
└─────────────────────────┘
```

### **保持不变（基础字段）**
```
┌─────────────────────────┐
│ 启用时间               │
├─────────────────────────┤
│ 2024-01-15        ▼   │  ← 继续只选择日期
└─────────────────────────┘
```

## 🧪 测试场景

### **扩展字段测试**
1. **创建包含时间字段的扩展字段**
2. **在添加设备台账时验证时间选择器**
3. **确认可以同时选择日期和时间**
4. **验证时间格式正确保存**

### **基础字段测试**
1. **验证启用时间字段仍然只选择日期**
2. **确认原有功能不受影响**
3. **验证数据格式兼容性**

### **兼容性测试**
1. **新旧数据格式兼容**
2. **表格显示正常**
3. **编辑功能正常**

## 🚀 扩展性

### **未来可扩展功能**
- **时区支持**：可添加时区选择
- **快捷时间**：常用时间快速选择
- **时间范围**：开始时间和结束时间联动
- **格式自定义**：支持不同的时间显示格式

### **配置化支持**
- **字段级配置**：每个扩展字段可独立配置是否显示时间
- **全局配置**：统一的时间选择器行为配置
- **主题定制**：时间选择器样式主题化

## 📝 维护说明

### **关键依赖**
- `src/components/Common/DatePicker.tsx` - 新时间选择器组件
- `src/utils/fieldUtils.ts` - 时间字段识别逻辑
- `isTimeField()` 函数 - 字段类型判断

### **注意事项**
- 确保扩展字段和基础字段使用不同的时间选择器配置
- 维护API兼容性，避免破坏现有功能
- 时间格式需要与后端API保持一致

### **性能考虑**
- 时间选择器按需渲染，不影响页面性能
- 事件处理优化，避免不必要的重新渲染
- 内存管理良好，组件卸载时正确清理
