import React from 'react';

interface ActivationErrorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  errorMessage: string;
  onRetry: () => void;
}

/**
 * 激活错误弹窗组件
 * UI层：只负责渲染界面，不包含业务逻辑
 */
const ActivationErrorDialog: React.FC<ActivationErrorDialogProps> = ({
  isOpen,
  onClose,
  errorMessage,
  onRetry
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white border border-gray-300 rounded shadow-lg max-w-md w-full mx-4 relative overflow-hidden">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-red-600">
              激活失败
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-4">
            {/* 错误图标 */}
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 border border-red-200 mb-4">
              <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>

            {/* 错误信息 */}
            <div className="text-center">
              <h3 className="text-lg font-bold text-gray-900 mb-3">
                系统激活失败
              </h3>
              <div className="p-3 bg-red-50 border border-red-200 rounded mb-4">
                <p className="text-sm text-red-700 font-medium">{errorMessage}</p>
              </div>
              <p className="text-sm text-gray-600">
                请检查公司名称是否和注册信息公司名称一致，然后重试激活
              </p>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center pt-4">
              <button
                onClick={onRetry}
                className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 font-medium"
              >
                <span className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>重试激活</span>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivationErrorDialog;
