import { DeviceCategory } from '../../types/inventory';
import TaskManager from '../../utils/taskManager';
import { categoryIconService } from './categoryIconService';

// 缓存和防抖相关变量
let categoryTreeCache: DeviceCategory[] | null = null;
let cacheTimestamp = 0;
const CACHE_TTL = 5000; // 缓存有效期5秒
let pendingPromise: Promise<DeviceCategory[]> | null = null;
let isRequestInProgress = false;

/**
 * 从数据库获取设备分类树（带缓存和防抖）
 * @param forceRefresh 是否强制刷新，忽略缓存
 * @returns 设备分类树
 */
export async function fetchCategoryTree(forceRefresh = false): Promise<DeviceCategory[]> {
  // 如果有缓存且未过期且不强制刷新，直接返回缓存
  const now = Date.now();
  if (!forceRefresh && categoryTreeCache && (now - cacheTimestamp < CACHE_TTL)) {
    console.log('使用缓存的设备分类树数据');
    return categoryTreeCache;
  }

  // 强制刷新时输出日志
  if (forceRefresh) {
    console.log('强制刷新设备分类树，忽略缓存，从数据库重新获取');
  }

  // 如果已经有请求在进行中，复用该请求的Promise
  if (isRequestInProgress && pendingPromise) {
    console.log('已有请求在进行中，等待结果...');
    return pendingPromise;
  }

  console.log('从数据库获取设备分类树 - 开始');
  isRequestInProgress = true;

  // 创建新的Promise并保存
  pendingPromise = fetchCategoryTreeFromDB();

  try {
    // 等待请求完成
    const result = await pendingPromise;
    // 更新缓存
    categoryTreeCache = result;
    cacheTimestamp = Date.now();
    return result;
  } finally {
    // 无论成功失败，都标记请求已完成
    isRequestInProgress = false;
  }
}

/**
 * 实际从数据库获取分类树的函数
 */
async function fetchCategoryTreeFromDB(): Promise<DeviceCategory[]> {
  const taskManager = TaskManager.getInstance();
  const deviceCategories: DeviceCategory[] = [];

  console.log('获取到TaskManager实例:', !!taskManager);

  try {
    // API调用间延迟时间（毫秒）
    const API_DELAY = 100;

    console.log('fetchCategoryTree: 步骤1 - 获取一级分类');
    // 步骤1: 串行获取所有一级分类
    let parentCategoriesResult;

    try {
      // 使用固定参数，允许TaskManager缓存机制工作
      const parentCategoriesTaskId = await taskManager.submitTask('AccountTableDll', 'DbFun', {
        action: "get_parent_category",
        action_params: {}
      });
      console.log('fetchCategoryTree: 一级分类任务提交成功，taskId:', parentCategoriesTaskId);

      // 等待任务结果
      parentCategoriesResult = await new Promise<any>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            taskManager.offTaskUpdate(parentCategoriesTaskId, handleTaskUpdate);
            resolve(taskInfo.result);
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            taskManager.offTaskUpdate(parentCategoriesTaskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || '任务执行失败'));
          }
        };
        taskManager.onTaskUpdate(parentCategoriesTaskId, handleTaskUpdate);
      });

      console.log('fetchCategoryTree: 一级分类获取成功，数量:', parentCategoriesResult?.data?.length || 0);
    } catch (error) {
      console.error('fetchCategoryTree: 获取一级分类时发生错误:', error);
      // 创建空的分类树
      return createEmptyDeviceCategories();
    }

    if (!parentCategoriesResult || !parentCategoriesResult.success || !parentCategoriesResult.data) {
      console.error('获取一级分类失败:', parentCategoriesResult);
      return createEmptyDeviceCategories();
    }

    // 确保 parentCategories 是一个数组
    const parentCategories = Array.isArray(parentCategoriesResult.data) ? parentCategoriesResult.data : [];
    console.log('fetchCategoryTree: 处理一级分类数量:', parentCategories.length);

    // API调用间延迟
    await new Promise(resolve => setTimeout(resolve, API_DELAY));

    console.log('fetchCategoryTree: 步骤2 - 获取二级分类');
    // 步骤2: 串行获取所有二级分类（等待一级分类完成后再执行）
    let subCategoriesResult;

    try {
      // 使用固定参数，允许TaskManager缓存机制工作
      const subCategoriesTaskId = await taskManager.submitTask('AccountTableDll', 'DbFun', {
        action: "get_sub_category",
        action_params: {}
      });
      console.log('fetchCategoryTree: 二级分类任务提交成功，taskId:', subCategoriesTaskId);

      // 等待任务结果
      subCategoriesResult = await new Promise<any>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            taskManager.offTaskUpdate(subCategoriesTaskId, handleTaskUpdate);
            resolve(taskInfo.result);
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            taskManager.offTaskUpdate(subCategoriesTaskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || '任务执行失败'));
          }
        };
        taskManager.onTaskUpdate(subCategoriesTaskId, handleTaskUpdate);
      });

      console.log('fetchCategoryTree: 二级分类获取成功，数量:', subCategoriesResult?.data?.length || 0);
    } catch (error) {
      console.error('fetchCategoryTree: 获取二级分类时发生错误:', error);
      // 如果获取二级分类失败，但有一级分类，仍然可以创建树
      subCategoriesResult = { success: true, data: [] };
    }

    if (!subCategoriesResult || !subCategoriesResult.success) {
      console.error('fetchCategoryTree: 获取二级分类失败:', subCategoriesResult);
      // 如果获取二级分类失败，但有一级分类，仍然可以创建树
      subCategoriesResult = { success: true, data: [] };
    }

    console.log('fetchCategoryTree: 步骤3 - 构建分类树结构');
    // 步骤3: 构建分类树结构
    // 确保 subCategories 是一个数组
    const subCategories = Array.isArray(subCategoriesResult.data) ? subCategoriesResult.data : [];
    console.log('fetchCategoryTree: 处理二级分类数量:', subCategories.length);

    // 获取公司名称作为根节点名称
    const CompanyNameHelper = (await import('../../utils/companyNameHelper')).default;
    const rootNodeName = await CompanyNameHelper.getDeviceCategoryRootName();

    // 添加动态根节点
    const allDevicesNode: DeviceCategory = {
      id: 'all',
      name: rootNodeName,
      count: 0, // 将在下面计算
      children: []
    };

    // 创建一级分类节点
    // 确保 parentCategories 是一个数组并且可以使用 forEach 方法
    (Array.isArray(parentCategories) ? parentCategories : []).forEach((parent: any) => {
      // 创建一级分类节点
      const categoryId = `parent-${parent.id}`;
      const categoryNode: DeviceCategory = {
        id: categoryId,
        name: parent.parent_category_name,
        count: 0, // 将在下面计算
        children: [],
        // 保存原始 ID
        originalId: parent.id
      };

      // 优先使用后端返回的图标，如果没有则使用前端存储的图标
      let customIcon = parent.custom_icon; // 后端返回的图标
      if (!customIcon) {
        customIcon = categoryIconService.getIcon(categoryId); // 前端存储的图标
      }

      if (customIcon) {
        categoryNode.customIcon = customIcon;
        console.log(`一级分类 ${parent.parent_category_name} 有自定义图标: ${customIcon} (来源: ${parent.custom_icon ? '后端' : '前端'})`);
      }

      // 找到属于该一级分类的所有二级分类
      // 确保 subCategories 是一个数组并且可以使用 filter 方法
      const relatedSubCategories = Array.isArray(subCategories) ? subCategories.filter(
        (sub: any) => sub && sub.parent_category_id === parent.id
      ) : [];



      // 创建二级分类节点
      // 确保 relatedSubCategories 是一个数组并且可以使用 forEach 方法
      (Array.isArray(relatedSubCategories) ? relatedSubCategories : []).forEach((sub: any) => {
        const subCategoryId = `parent-${parent.id}-${sub.id}`;
        const subCategoryNode: DeviceCategory = {
          id: subCategoryId,
          name: sub.sub_category_name,
          count: 0, // 设备数量，后续可以通过API获取或在前端计算
          children: [],
          // 保存原始 ID
          originalId: sub.id,
          parentId: parent.id
        };

        // 优先使用后端返回的图标，如果没有则使用前端存储的图标
        let customIcon = sub.custom_icon; // 后端返回的图标
        if (!customIcon) {
          customIcon = categoryIconService.getIcon(subCategoryId); // 前端存储的图标
        }

        // 如果还没有图标，检查是否有临时保存的图标
        if (!customIcon) {
          const tempKey = `temp_sub_${sub.id}_${parent.parent_category_name}`;
          customIcon = categoryIconService.getIcon(tempKey);
          if (customIcon) {
            // 找到临时图标，转移到正确的categoryId
            categoryIconService.setIcon(subCategoryId, customIcon);
            categoryIconService.removeIcon(tempKey); // 删除临时键
            console.log(`二级分类图标从临时存储转移: ${tempKey} -> ${subCategoryId}`);
          }
        }

        if (customIcon) {
          subCategoryNode.customIcon = customIcon;
          console.log(`二级分类 ${sub.sub_category_name} 有自定义图标: ${customIcon} (来源: ${sub.custom_icon ? '后端' : '前端'})`);
        }

        // 将二级分类节点添加到一级分类节点
        categoryNode.children!.push(subCategoryNode);
        categoryNode.count += subCategoryNode.count;
      });

      // 将一级分类节点添加到根节点
      allDevicesNode.children!.push(categoryNode);
      allDevicesNode.count += categoryNode.count;
    });

    // 添加根节点到分类树
    deviceCategories.push(allDevicesNode);

    console.log('设备分类树生成完成，一级分类数量:', allDevicesNode.children?.length || 0);

    return deviceCategories;
  } catch (error) {
    console.error('获取设备分类树失败:', error);
    return await createEmptyDeviceCategories();
  }
}

/**
 * 创建空的设备分类树
 * @returns 空的设备分类树
 */
async function createEmptyDeviceCategories(): Promise<DeviceCategory[]> {
  console.log('创建空的设备分类树');

  // 获取公司名称作为根节点名称
  const CompanyNameHelper = (await import('../../utils/companyNameHelper')).default;
  const rootNodeName = await CompanyNameHelper.getDeviceCategoryRootName();

  return [{
    id: 'all',
    name: rootNodeName,
    count: 0,
    children: [],
    originalId: 'all'
  }];
}

/**
 * 更新设备分类树中的设备数量
 * @param categories 设备分类树
 * @param devices 设备数据
 * @returns 更新后的设备分类树
 */
export function updateCategoryTreeCounts(categories: DeviceCategory[], devices: any[]): DeviceCategory[] {
  console.log('更新设备分类树中的设备数量');

  // 确保 categories 和 devices 都是数组
  const safeCategories = Array.isArray(categories) ? categories : [];
  const safeDevices = Array.isArray(devices) ? devices : [];

  // 如果没有设备数据，创建一个新的分类树并将所有计数重置为0
  if (safeDevices.length === 0) {
    console.log('没有设备数据，重置所有分类计数为0');

    // 创建一个新的分类树，避免修改原始数据
    const resetCategories = JSON.parse(JSON.stringify(safeCategories)) as DeviceCategory[];

    // 递归重置所有节点的计数为0
    const resetCounts = (nodes: DeviceCategory[]) => {
      for (const node of nodes) {
        node.count = 0;
        if (node.children && node.children.length > 0) {
          resetCounts(node.children);
        }
      }
    };

    // 重置所有节点的计数
    resetCounts(resetCategories);

    return resetCategories;
  }

  // 创建一个新的分类树，避免修改原始数据
  const updatedCategories = JSON.parse(JSON.stringify(safeCategories)) as DeviceCategory[];

  if (updatedCategories.length === 0) return updatedCategories;

  // 获取根节点
  const rootNode = updatedCategories[0];
  rootNode.count = safeDevices.length; // 总设备数量

  // 按一级分类和二级分类统计设备数量
  const categoryCountMap = new Map<string, number>();
  const subCategoryCountMap = new Map<string, number>();

  // 记录没有匹配到分类的设备
  const unmatchedDevices: any[] = [];

  // 创建设备分类映射，避免重复计数
  const deviceCategoryMap = new Map<string, Set<string>>(); // 分类ID -> 设备ID集合
  const deviceSubCategoryMap = new Map<string, Set<string>>(); // 子分类ID -> 设备ID集合

  safeDevices.forEach((device: any, index: number) => {
    // 获取设备的分类信息 - 先尝试直接使用字段
    let parentCategoryId = device.parent_category_id;
    let subCategoryId = device.sub_category_id;
    let parentCategoryName = device.parent_category_name;
    let subCategoryName = device.sub_category_name;

    // 如果设备数据中没有这些字段，尝试从 rawData 中获取
    if (device.rawData) {
      if (parentCategoryId === undefined) parentCategoryId = device.rawData.parent_category_id;
      if (subCategoryId === undefined) subCategoryId = device.rawData.sub_category_id;
      if (parentCategoryName === undefined) parentCategoryName = device.rawData.parent_category_name;
      if (subCategoryName === undefined) subCategoryName = device.rawData.sub_category_name;
    }

    // 如果还是没有找到，尝试从其他字段获取
    if (parentCategoryId === undefined && device.parentCategory) {
      parentCategoryName = device.parentCategory;
    }
    if (subCategoryId === undefined && device.type) {
      subCategoryName = device.type;
    }



    // 如果还是没有找到，记录这个设备
    if (!subCategoryName) {
      unmatchedDevices.push({ index, device });
      return; // 跳过这个设备
    }

    // 生成设备唯一标识
    const deviceKey = device.id || `device_${index}`;

    // 优化的键值匹配策略 - 使用优先级匹配，避免重复计数
    // 一级分类计数 - 按优先级选择最佳匹配键
    let parentKey: string | null = null;
    if (parentCategoryId) {
      parentKey = `parent-${parentCategoryId}`;
    } else if (parentCategoryName) {
      parentKey = parentCategoryName;
    }

    if (parentKey) {
      if (!deviceCategoryMap.has(parentKey)) {
        deviceCategoryMap.set(parentKey, new Set());
      }
      deviceCategoryMap.get(parentKey)!.add(deviceKey);
    }

    // 二级分类计数 - 按优先级选择最佳匹配键
    // 需要考虑父分类名称可能已经更新的情况
    const subKeys: string[] = [];

    if (parentCategoryId && subCategoryId) {
      subKeys.push(`parent-${parentCategoryId}-${subCategoryId}`);
    }

    if (parentCategoryName && subCategoryName) {
      // 添加设备数据中的父分类名称组合键
      subKeys.push(`${parentCategoryName}-${subCategoryName}`);

      // 查找当前分类树中对应的父分类，使用更新后的名称
      const matchingParentCategory = safeCategories.find(cat =>
        cat.originalId === parentCategoryId || cat.id === `parent-${parentCategoryId}`
      );

      if (matchingParentCategory && matchingParentCategory.name !== parentCategoryName) {
        // 如果分类树中的父分类名称与设备数据中的不同，添加新的组合键
        subKeys.push(`${matchingParentCategory.name}-${subCategoryName}`);
      }
    }

    if (subCategoryName) {
      subKeys.push(subCategoryName); // 只使用二级分类名称作为键
    }

    // 为所有可能的键添加设备
    subKeys.forEach(subKey => {
      if (!deviceSubCategoryMap.has(subKey)) {
        deviceSubCategoryMap.set(subKey, new Set());
      }
      deviceSubCategoryMap.get(subKey)!.add(deviceKey);
    });
  });

  // 将设备集合转换为计数
  deviceCategoryMap.forEach((deviceSet, key) => {
    categoryCountMap.set(key, deviceSet.size);
  });

  deviceSubCategoryMap.forEach((deviceSet, key) => {
    subCategoryCountMap.set(key, deviceSet.size);
  });

  // 不输出详细的统计信息
  if (unmatchedDevices.length > 0) {
    console.warn(`有 ${unmatchedDevices.length} 个设备没有匹配到分类信息`);
    console.warn('第一个未匹配设备:', unmatchedDevices[0].device);
  }

  // 更新一级分类和二级分类的设备数量
  // 确保 rootNode.children 是一个数组
  const rootChildren = rootNode.children || [];
  rootChildren.forEach(categoryNode => {
    // 优化的键匹配策略 - 按优先级匹配，避免重复计数
    let categoryCount = 0;

    // 按优先级尝试匹配键
    const priorityKeys = [
      `parent-${categoryNode.originalId}`, // 最高优先级：使用原始ID
      categoryNode.id,                     // 次优先级：使用节点ID
      categoryNode.name                    // 最低优先级：使用名称
    ].filter(Boolean);

    for (const key of priorityKeys) {
      const count = categoryCountMap.get(key) || 0;
      if (count > 0) {
        categoryCount = count;
        break; // 找到第一个有效计数就停止
      }
    }

    // 先更新所有子分类的计数
    let totalSubCategoryCount = 0;

    // 确保 categoryNode.children 是一个数组
    const categoryChildren = categoryNode.children || [];
    categoryChildren.forEach(subCategoryNode => {
      // 优化的子分类键匹配策略
      let subCategoryCount = 0;

      // 按优先级尝试匹配键
      const prioritySubKeys = [
        `parent-${categoryNode.originalId}-${subCategoryNode.originalId}`, // 最高优先级：完整ID路径
        `${categoryNode.name}-${subCategoryNode.name}`,                    // 次优先级：完整名称路径
        subCategoryNode.name,                                              // 最低优先级：仅子分类名称
        subCategoryNode.id                                                 // 备用：节点ID
      ].filter(Boolean);

      for (const key of prioritySubKeys) {
        const count = subCategoryCountMap.get(key) || 0;
        if (count > 0) {
          subCategoryCount = count;
          // 输出匹配成功的调试信息
          console.log(`二级分类 ${subCategoryNode.name} 通过键 "${key}" 匹配，计数为 ${count}`);
          break; // 找到第一个有效计数就停止
        }
      }

      // 更新节点计数
      subCategoryNode.count = subCategoryCount;
      // 累加到一级分类总计数
      totalSubCategoryCount += subCategoryCount;

      // 详细调试信息
      if (subCategoryCount > 0) {
        console.log(`二级分类 ${subCategoryNode.name} 累加计数: ${subCategoryCount}, 当前总计数: ${totalSubCategoryCount}`);
      }
    });

    // 一级分类的计数应该是其所有子分类计数的总和
    // 如果直接匹配的计数大于子分类总和，使用直接匹配的计数（可能包含未分类的设备）
    // 否则使用子分类总和
    categoryNode.count = Math.max(categoryCount, totalSubCategoryCount);

    console.log(`一级分类 ${categoryNode.name}: 直接计数=${categoryCount}, 子分类总计数=${totalSubCategoryCount}, 最终计数=${categoryNode.count}`);
  });

  // 重新计算根节点的计数，应该是所有一级分类计数的总和
  let totalRootCount = 0;
  rootChildren.forEach(categoryNode => {
    totalRootCount += categoryNode.count;
  });

  // 根节点计数取设备总数和一级分类总计数的最大值
  rootNode.count = Math.max(safeDevices.length, totalRootCount);

  console.log(`根节点计数更新: 设备总数=${safeDevices.length}, 一级分类总计数=${totalRootCount}, 最终计数=${rootNode.count}`);

  return updatedCategories;
}
