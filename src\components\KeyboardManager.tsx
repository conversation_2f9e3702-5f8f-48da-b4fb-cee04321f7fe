import React, { useEffect } from 'react';
import { useInitKeyboardManager } from '../hooks/base/useKeyboardManager';
import { useInitFocusManager } from '../hooks/base/useFocusManager';
import keyboardDebugger from '../utils/keyboardDebug';
import TabKeyFix from './TabKeyFix';

/**
 * 扩展Window接口以包含测试方法
 */
declare global {
  interface Window {
    testTabKey?: () => void;
  }
}

interface KeyboardManagerProps {
  children: React.ReactNode;
}

/**
 * 全局键盘事件管理组件
 * 初始化键盘事件管理器和焦点管理器
 * 应在应用根组件中使用
 */
const KeyboardManager: React.FC<KeyboardManagerProps> = ({ children }) => {
  // 初始化键盘事件管理器（增强版）
  const keyboardManager = useInitKeyboardManager();

  // 初始化焦点管理器（增强版）
  const focusManager = useInitFocusManager();

  // 添加全局键盘事件处理
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 KeyboardManager初始化完成');
      console.log('🎹 键盘管理器能力:', keyboardManager.capabilities);
      console.log('🎯 焦点管理器能力:', focusManager.capabilities);
    }

    // 启用键盘调试（只在明确需要调试时）
    if (window.location.search.includes('debug=keyboard')) {
      keyboardDebugger.enable();
      console.log('🔧 键盘调试器已启用');
    }

    // 处理Tab键，确保焦点样式可见
    const handleKeyDown = (e: KeyboardEvent) => {
      // 当用户按下Tab键时，添加一个类到body，表示用户正在使用键盘导航
      if (e.key === 'Tab' || e.keyCode === 9) {
        document.body.classList.add('keyboard-navigation');
        if (process.env.NODE_ENV === 'development') {
          console.log('🎹 Tab键检测，启用键盘导航模式');
        }
      }
    };

    // 处理鼠标点击，隐藏焦点样式
    const handleMouseDown = () => {
      // 当用户使用鼠标时，移除键盘导航类
      document.body.classList.remove('keyboard-navigation');
      if (process.env.NODE_ENV === 'development') {
        console.log('🖱️ 鼠标点击检测，禁用键盘导航模式');
      }
    };

    // 添加事件监听器（使用较低优先级，让键盘管理器先处理）
    document.addEventListener('keydown', handleKeyDown, false);
    document.addEventListener('mousedown', handleMouseDown, false);

    // 添加Tab键测试功能
    const testTabKey = () => {
      console.log('🔧 测试Tab键功能...');
      const focusableElements = document.querySelectorAll(
        'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      );
      console.log(`🔧 找到 ${focusableElements.length} 个可聚焦元素`);

      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
        console.log('🔧 聚焦到第一个元素:', focusableElements[0]);
      }
    };

    // 添加全局测试方法
    window.testTabKey = testTabKey;
    console.log('🔧 添加了全局测试方法: window.testTabKey()');

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown, false);
      document.removeEventListener('mousedown', handleMouseDown, false);
      delete window.testTabKey;
    };
  }, [keyboardManager.capabilities, focusManager.capabilities]);

  return (
    <>
      <TabKeyFix />
      {children}
    </>
  );
};

export default KeyboardManager;
