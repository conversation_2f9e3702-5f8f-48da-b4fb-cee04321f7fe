import { FieldDefinition, DictionaryItem, DictionaryMap } from '../../../types/inventory';
import ColumnVisibilityService from '../columnVisibilityService';
import ExtFieldService from '../extFieldService';
import { calculateColumnsWidth, getRecommendedWidthByFieldType } from '../../../utils/columnWidthCalculator';

/**
 * 库存字段服务
 * 专门负责管理表格字段定义和字典
 */
export class InventoryFieldService {
  private static instance: InventoryFieldService;
  
  // 字段字典表
  private fieldDictionary: Record<string, string> = {
    id: '序号',
    parentCategory: '设备类型分类', // 保持与导出数据中相同的名称
    type: '设备类型',
    securityCode: '保密编号',
    securityLevel: '密级',
    responsible: '责任人',
    name: '名称',
    model: '型号',
    department: '所属部门',
    location: '放置地点',
    startTime: '启用时间',
    status: '使用情况',
    purpose: '用途',
    securityRfid: '安全码',
    inspectionStatus: '巡检状态' // 新增巡检状态字段
  };

  // 数据字典表 - 存储各类选项数据
  private dataDictionary: DictionaryMap = {
    // 设备状态选项
    'STATUS': [
      { code: '在用', value: '在用', sort: 1 },
      { code: '停用', value: '停用', sort: 2 },
      { code: '归库', value: '归库', sort: 3 },
      { code: '维修', value: '维修', sort: 4 },
      { code: '报废', value: '报废', sort: 5 },
    ],
    // 密级选项
    'SECURITY_LEVEL': [
      { code: '公开', value: '公开', sort: 1 },
      { code: '内部', value: '内部', sort: 2 },
      { code: '秘密', value: '秘密', sort: 3 },
      { code: '机密', value: '机密', sort: 4 },
      { code: '绝密', value: '绝密', sort: 5 },
      { code: '非密专用', value: '非密专用', sort: 6 },
      { code: '涉密专用', value: '涉密专用', sort: 7 }
    ],
  };

  // 列可见性服务实例
  private columnVisibilityService: ColumnVisibilityService;

  // 当前选中的分类信息
  private currentSelectedCategory?: { parentCategory?: string; subCategory?: string };

  private constructor() {
    // 初始化列可见性服务
    this.columnVisibilityService = ColumnVisibilityService.getInstance();
  }

  public static getInstance(): InventoryFieldService {
    if (!InventoryFieldService.instance) {
      InventoryFieldService.instance = new InventoryFieldService();
    }
    return InventoryFieldService.instance;
  }

  /**
   * 获取字段字典表
   */
  public getFieldDictionary(): Record<string, string> {
    return { ...this.fieldDictionary };
  }

  /**
   * 设置字段字典
   */
  public setFieldDictionary(dictionary: Record<string, string>): void {
    this.fieldDictionary = { ...dictionary };
  }

  /**
   * 获取数据字典
   */
  public getDataDictionary(): DictionaryMap {
    return { ...this.dataDictionary };
  }

  /**
   * 获取指定类型的字典项
   */
  public getDictionaryItems(dictType: string): DictionaryItem[] {
    return this.dataDictionary[dictType]
      ? [...this.dataDictionary[dictType]].sort((a, b) => (a.sort || 0) - (b.sort || 0))
      : [];
  }

  /**
   * 设置数据字典
   */
  public setDataDictionary(dictType: string, items: DictionaryItem[]): void {
    this.dataDictionary[dictType] = [...items];
  }

  /**
   * 获取当前选中的分类信息
   */
  public getCurrentSelectedCategory(): { parentCategory?: string; subCategory?: string } | undefined {
    return this.currentSelectedCategory ? { ...this.currentSelectedCategory } : undefined;
  }

  /**
   * 设置当前选中的分类信息
   */
  public setCurrentSelectedCategory(category?: { parentCategory?: string; subCategory?: string }): void {
    this.currentSelectedCategory = category ? { ...category } : undefined;
  }

  /**
   * 清除当前选中的分类信息
   */
  public clearCurrentSelectedCategory(): void {
    this.currentSelectedCategory = undefined;
  }

  /**
   * 计算基于标题宽度的最小列宽
   * @param title 列标题
   * @returns 最小宽度（像素）
   */
  private calculateMinWidthByTitle(title: string): number {
    // 计算中文字符和英文字符的数量
    const chineseChars = (title.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishChars = title.length - chineseChars;

    // 中文字符约16px，英文字符约8px，加上一些padding
    const titleWidth = chineseChars * 16 + englishChars * 8 + 32; // 32px padding

    // 最小宽度至少要能显示完整的标题，但不超过200px
    return Math.min(Math.max(titleWidth, 60), 200);
  }

  /**
   * 初始化基本表格字段，不包含扩展字段
   * 这个方法只在构造函数中调用，用于初始化基本表格字段
   */
  public initializeBasicTableFields(): FieldDefinition[] {
    const fields: FieldDefinition[] = [];

    // 添加选择列（固定在最左侧）
    fields.push({
      key: 'select',
      title: '选择',
      width: 40,
      fixed: true,
      titleStyle: { whiteSpace: 'nowrap' }
    });

    // 添加标准字段
    this.addStandardFields(fields);

    // 添加操作列（固定在最右侧）
    fields.push({
      key: 'actions',
      title: '操作',
      width: 50,
      fixed: true,
      titleStyle: { whiteSpace: 'nowrap' }
    });

    return fields;
  }

  /**
   * 字段宽度预设配置
   */
  private static readonly FIELD_WIDTHS: Record<string, number> = {
    'id': 60,                    // 序号通常很短，减少宽度
    'type': 100,                 // 设备类型通常是固定选项，减少宽度
    'name': 150,                 // 名称需要保持足够宽度
    'department': 130,           // 部门名称可能较长，但可以适当减少
    'responsible': 100,          // 责任人名称通常较短
    'location': 100,             // 位置信息可以适当减少
    'purchaseDate': 90,          // 日期格式固定，减少宽度
    'status': 70,                // 状态通常是短文本，如"在用"
    'model': 120,                // 型号可能较长
    'serialNumber': 130,         // 序列号可能较长
    'manufacturer': 120,         // 厂商名称可能较长
    'securityLevel': 70,         // 密级通常是短文本，如"秘密"
    'inspectionStatus': 80,      // 巡检状态通常是短文本，如"正常"、"异常"
    'maintenanceStatus': 90,     // 维护状态通常是短文本
    'lastMaintenanceDate': 90,   // 日期格式固定，减少宽度
    'nextMaintenanceDate': 90,   // 日期格式固定，减少宽度
    'warrantyStartDate': 90,     // 日期格式固定，减少宽度
    'warrantyEndDate': 90,       // 日期格式固定，减少宽度
    'price': 80,                 // 价格通常较短
    'isNetworked': 70,           // 是否联网只有"是"或"否"
    'description': 200           // 描述需要保持较宽
  };

  /**
   * 自适应列宽配置
   */
  private adaptiveWidthConfig = {
    enabled: true, // 是否启用自适应列宽
    preserveManualWidths: false, // 禁用保留手动设置的宽度，优先使用自适应计算
    minDataRows: 1, // 最少需要多少行数据才启用自适应
  };

  /**
   * 设置自适应列宽配置
   */
  public setAdaptiveWidthConfig(config: Partial<typeof this.adaptiveWidthConfig>): void {
    this.adaptiveWidthConfig = { ...this.adaptiveWidthConfig, ...config };
  }

  /**
   * 启用自适应列宽
   */
  public enableAdaptiveWidth(): void {
    this.adaptiveWidthConfig.enabled = true;
  }

  /**
   * 禁用自适应列宽
   */
  public disableAdaptiveWidth(): void {
    this.adaptiveWidthConfig.enabled = false;
  }

  /**
   * 获取自适应列宽配置
   */
  public getAdaptiveWidthConfig(): typeof this.adaptiveWidthConfig {
    return { ...this.adaptiveWidthConfig };
  }

  /**
   * 计算自适应列宽（融合现有宽度设置）
   * @param fields 字段定义数组
   * @param data 数据数组
   * @returns 更新了宽度的字段定义数组
   */
  public calculateAdaptiveColumnWidths(fields: FieldDefinition[], data: any[]): FieldDefinition[] {
    // 如果未启用自适应列宽，直接返回原字段
    if (!this.adaptiveWidthConfig.enabled) {
      return fields;
    }

    // 如果数据不足，返回原字段
    if (!data || data.length < this.adaptiveWidthConfig.minDataRows) {
      return fields;
    }

    try {
      // 准备列配置
      const columns = fields.map(field => ({
        key: field.key,
        title: field.title,
        render: field.render,
        ...getRecommendedWidthByFieldType(field.key, field.title, data)
      }));

      // 计算所有列的最佳宽度
      const calculatedWidths = calculateColumnsWidth({
        columns,
        data,
        fontSize: 14,
        defaultPadding: 16, // 适中的内边距
        hasSortIcon: true
      });

      // 更新字段定义的宽度
      const updatedFields = fields.map(field => {
        const originalWidth = field.width;
        const calculatedWidth = calculatedWidths[field.key];

        // 特殊字段保持原有宽度（选择列、操作列等）
        if (field.key === 'select' || field.key === 'actions') {
          return field;
        }

        // 优先使用自适应计算的宽度，确保列宽始终适应内容
        if (calculatedWidth) {
          return {
            ...field,
            width: calculatedWidth
          };
        }

        // 如果自适应计算失败，使用预设宽度或合理的默认值
        return {
          ...field,
          width: originalWidth || 120
        };
      });

      return updatedFields;
    } catch (error) {
      console.warn('自适应列宽计算失败，使用原有宽度:', error);
      return fields; // 计算失败时返回原字段
    }
  }

  /**
   * 动态生成表格字段
   * @param currentCategory 当前选中的分类信息，如果提供则只显示该分类的扩展字段
   * @param data 可选的数据数组，用于计算基于内容的列宽
   */
  public generateTableFields(currentCategory?: { parentCategory?: string; subCategory?: string }, data?: any[]): FieldDefinition[] {
    // 如果提供了分类信息，更新当前选中的分类信息
    if (currentCategory) {
      this.currentSelectedCategory = { ...currentCategory };
    } else if (!currentCategory) {
      // 如果当前是根节点且没有提供分类信息，清除当前选中的分类信息
      this.currentSelectedCategory = undefined;
    }

    const fields: FieldDefinition[] = [];

    // 添加选择列（固定在最左侧）
    fields.push({
      key: 'select',
      title: '选择',
      width: 40,
      fixed: true,
      titleStyle: { whiteSpace: 'nowrap' }
    });

    // 添加标准字段
    this.addStandardFields(fields);

    // 添加扩展字段列
    this.addExtendedFields(fields, currentCategory);

    // 添加操作列（固定在最右侧）
    fields.push({
      key: 'actions',
      title: '操作',
      width: 50,
      fixed: true,
      titleStyle: { whiteSpace: 'nowrap' }
    });

    // 如果提供了数据，则根据内容自适应调整列宽
    if (data && data.length > 0) {
      const fieldsWithAdaptiveWidth = this.calculateAdaptiveColumnWidths(fields, data);
      return fieldsWithAdaptiveWidth;
    }

    return fields;
  }

  /**
   * 计算字段宽度
   * @param key 字段键名
   * @param title 字段标题
   * @returns 计算后的宽度
   */
  private calculateFieldWidth(key: string, title: string): number {
    const fieldWidths = InventoryFieldService.FIELD_WIDTHS;

    // 使用预设宽度，如果没有预设，则根据字段类型和名称估算合适的宽度
    let estimatedWidth = 100; // 默认宽度
    let minWidth = 80;        // 默认最小宽度

    // 如果没有预设宽度，根据字段名称判断类型
    if (!fieldWidths[key]) {
      const fieldNameLower = title.toLowerCase();

      // 日期类型字段
      if (fieldNameLower.includes('日期') || fieldNameLower.includes('时间')) {
        estimatedWidth = 90;
        minWidth = 80;
      }
      // 是否类型字段（通常只有是/否两个选项）
      else if (fieldNameLower.includes('是否')) {
        estimatedWidth = 70;
        minWidth = 60;
      }
      // 级别类型字段（如密级、安全级别等）
      else if (fieldNameLower.includes('级别') || fieldNameLower.includes('密级')) {
        estimatedWidth = 70;
        minWidth = 60;
      }
      // 状态类型字段
      else if (fieldNameLower.includes('状态')) {
        estimatedWidth = 80;
        minWidth = 70;
      }
      // 编号类型字段
      else if (fieldNameLower.includes('编号') || fieldNameLower.includes('序号')) {
        estimatedWidth = 80;
        minWidth = 70;
      }
      // 其他字段根据标题长度估算
      else {
        estimatedWidth = Math.min(title.length * 18, 150); // 每个字符约18px，但最大不超过150px
        minWidth = 80;
      }
    } else {
      // 使用预设宽度
      estimatedWidth = fieldWidths[key];
      minWidth = Math.min(estimatedWidth, 70); // 最小宽度不超过预设宽度
    }

    // 计算基于标题宽度的最小宽度
    const titleBasedMinWidth = this.calculateMinWidthByTitle(title);

    // 使用预设宽度、估算宽度、标题最小宽度中的最大值
    return Math.max(
      fieldWidths[key] || estimatedWidth,
      minWidth,
      titleBasedMinWidth
    );
  }

  /**
   * 添加标准字段到表格
   * @param fields 字段数组
   */
  private addStandardFields(fields: FieldDefinition[]): void {
    const fieldOrder = Object.keys(this.fieldDictionary);
    const columnVisibility = this.columnVisibilityService.getColumnVisibility();

    // 遍历fieldDictionary中的每个字段
    fieldOrder.forEach(key => {
      // 特殊处理：parentCategory（设备类型分类）字段始终不显示
      if (key === 'parentCategory') {
        return; // 跳过此字段
      }

      // 如果列可见性字典中未定义该键或值为true，则添加该列
      if (columnVisibility[key] !== false) {
        const title = this.fieldDictionary[key];
        const width = this.calculateFieldWidth(key, title);

        fields.push({
          key,
          title,
          sortable: true,
          width,
          titleStyle: { whiteSpace: 'nowrap' }
        });
      }
    });
  }

  /**
   * 添加扩展字段到表格
   * @param fields 字段数组
   * @param currentCategory 当前选中的分类信息
   */
  private addExtendedFields(fields: FieldDefinition[], currentCategory?: { parentCategory?: string; subCategory?: string }): void {
    try {
      const extFieldService = ExtFieldService.getInstance();

      // 直接使用ExtFieldService获取当前分类的扩展字段
      const extFieldsToShow = extFieldService.getExtFieldsToShow(currentCategory);

      // 如果没有找到匹配的扩展字段，清空当前扩展字段并返回
      if (!extFieldsToShow || extFieldsToShow.length === 0) {
        extFieldService.clearCurrentExtFields();
        return;
      }

      // 获取列可见性服务
      const columnVisibilityService = this.columnVisibilityService;
      const columnVisibility = columnVisibilityService.getColumnVisibility();

      // 跟踪已添加的扩展字段，避免重复
      const addedExtFields = new Set<string>();

      // 批量初始化所有扩展字段的列可见性
      extFieldService.initExtFieldsColumnVisibility(
        columnVisibilityService,
        extFieldsToShow,
        true // 默认可见
      );

      // 处理每个扩展字段
      extFieldsToShow.forEach(field => {
        const key = extFieldService.getExtFieldColumnKey(field.key);

        // 如果已经添加过，跳过
        if (addedExtFields.has(key)) return;

        // 添加到已添加集合
        addedExtFields.add(key);

        // 只有当用户没有明确隐藏该字段时才添加
        if (columnVisibility[key] !== false) {
          const title = field.title;
          const width = this.calculateFieldWidth(key, title);

          fields.push({
            key,
            title,
            sortable: true,
            width,
            titleStyle: { whiteSpace: 'nowrap' }
          });
        }
      });

      // 更新ExtFieldService中的当前扩展字段
      // 只有当扩展字段有变化时才更新
      const currentExtFields = extFieldService.getState().currentExtFields;
      if (!extFieldService.areExtFieldsEqual(currentExtFields, extFieldsToShow)) {
        // 判断是否是全部设备总表
        const isAllDevices = !currentCategory || (!currentCategory.parentCategory && !currentCategory.subCategory);
        // 更新当前扩展字段，并标记是否是全部设备总表
        extFieldService.setCurrentExtFields(extFieldsToShow, isAllDevices);
      }
    } catch (error) {
      console.error('生成扩展字段列失败:', error);
    }
  }
}

export default InventoryFieldService;
