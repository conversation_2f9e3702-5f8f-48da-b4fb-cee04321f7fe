import React, { useState, useEffect, useRef } from 'react';
import { Plus, RefreshCw, GitBranch, AlertCircle } from 'lucide-react';
import { useBackup } from '../../../hooks/useBackup';
import { CreateBackupDialog } from './components/CreateBackupDialog';
import { BackupList } from './components/BackupList';
import { BackupStatusBar } from './components/BackupStatusBar';

export const DatabaseBackupPage: React.FC = () => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const hasInitialized = useRef(false);

  const {
    backups,
    activeBackup,
    isLoading,
    isCreating,
    isSwitching,
    message,
    createBackup,
    refreshBackups,
    forceRefreshBackups,
    switchToBackup,
    getActiveBackup,
    clearMessage,
    formatFileSize,
    formatRelativeTime,
  } = useBackup();

  const handleCreateBackup = async (backupPath: string, backupName?: string) => {
    console.log('创建新备份');
    await createBackup(backupPath, backupName);
    // 不需要额外刷新，createBackup 内部已经更新了缓存
  };

  const handleRefresh = async () => {
    try {
      console.log('用户主动刷新备份列表');
      await forceRefreshBackups(); // 使用强制刷新，忽略缓存
    } catch (error) {
      console.error('刷新备份列表失败:', error);
    }
  };

  const handleSwitchBackup = async (backupId: number) => {
    try {
      await switchToBackup(backupId);
      // switchToBackup 函数内部已经包含了自动刷新逻辑
    } catch (error) {
      console.error('切换备份失败:', error);
    }
  };

  // 页面加载时手动获取数据
  useEffect(() => {
    // 防止 React StrictMode 的双重调用
    if (hasInitialized.current) {
      console.log('DatabaseBackupPage: 已经初始化过，跳过重复调用');
      return;
    }

    hasInitialized.current = true;
    let isMounted = true;

    const loadInitialData = async () => {
      try {
        console.log('DatabaseBackupPage: 开始加载初始数据（使用缓存机制）...');

        // 并行获取备份列表和活动备份
        // refreshBackups 会使用缓存机制，只有第一次或强制刷新时才会请求
        const [backupListResult, activeBackupResult] = await Promise.allSettled([
          refreshBackups(), // 使用缓存机制
          getActiveBackup()
        ]);

        if (isMounted) {
          console.log('DatabaseBackupPage: 初始数据加载完成');

          // 记录获取结果
          if (backupListResult.status === 'rejected') {
            console.error('获取备份列表失败:', backupListResult.reason);
          }
          if (activeBackupResult.status === 'rejected') {
            console.error('获取活动备份失败:', activeBackupResult.reason);
          }
        }
      } catch (error) {
        if (isMounted) {
          console.error('DatabaseBackupPage: 加载初始数据失败:', error);
        }
      }
    };

    loadInitialData();

    return () => {
      isMounted = false;
    };
  }, []); // 移除refreshBackups依赖，避免无限循环

  return (
    <div className="h-full bg-gray-50 flex flex-col overflow-hidden">
      {/* 标题栏 - 类似桌面应用的标题栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <GitBranch className="w-5 h-5 text-blue-600" />
            <h1 className="text-lg font-semibold text-gray-900">数据库备份管理</h1>
            <div className="h-4 w-px bg-gray-300"></div>
            <span className="text-sm text-gray-600">异常处理</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>{backups.length} 个版本</span>
            {activeBackup && (
              <>
                <span>•</span>
                <span className="text-green-600 font-medium">
                  {activeBackup.backup_name || activeBackup.backup_filename}
                </span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 工具栏 - 类似桌面应用的工具栏 */}
      <div className="bg-gray-50 border-b border-gray-200 px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 状态指示器 */}
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${activeBackup ? 'bg-green-500' : 'bg-blue-500'}`}></div>
              <span className="text-sm text-gray-700">
                {activeBackup ? '已连接到备份' : '使用初始数据库'}
              </span>
            </div>

            {/* 分隔线 */}
            <div className="h-4 w-px bg-gray-300"></div>

            {/* 快速信息 */}
            {activeBackup ? (
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>创建于 {formatRelativeTime(activeBackup.backup_timestamp)}</span>
                <span>大小 {formatFileSize(activeBackup.backup_size)}</span>
              </div>
            ) : (
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span>当前使用系统默认数据库</span>
              </div>
            )}
          </div>

          {/* 工具按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RefreshCw className={`w-4 h-4 mr-1.5 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </button>

            <button
              onClick={() => setShowCreateDialog(true)}
              disabled={isCreating}
              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 rounded shadow-sm transition-colors"
            >
              <Plus className="w-4 h-4 mr-1.5" />
              创建备份
            </button>
          </div>
        </div>
      </div>

      {/* 消息提示栏 */}
      {message && (
        <div className={`px-6 py-3 border-b flex-shrink-0 ${
          message.type === 'success'
            ? 'bg-green-50 border-green-200 text-green-800'
            : message.type === 'error'
            ? 'bg-red-50 border-red-200 text-red-800'
            : 'bg-blue-50 border-blue-200 text-blue-800'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm font-medium">{message.text}</span>
            </div>
            <button
              onClick={clearMessage}
              className="text-current opacity-70 hover:opacity-100 transition-opacity"
            >
              <span className="text-lg leading-none">×</span>
            </button>
          </div>
        </div>
      )}

      {/* 主要内容区域 - 两栏布局 80%:20% */}
      <div className="flex-1 flex min-h-0">
        {/* 主要内容区域 - 备份列表 (80%) */}
        <div className="w-[80%] bg-white flex flex-col min-h-0 flex-shrink-0">
          {/* 列表标题栏 */}
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">版本历史</h2>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>{backups.length} 个版本</span>
                {isLoading && <RefreshCw className="w-4 h-4 animate-spin" />}
              </div>
            </div>
          </div>

          {/* 备份列表内容 */}
          <div className="flex-1 p-6 overflow-y-auto">
            {isLoading && backups.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="p-4 bg-blue-50 rounded-full mb-4">
                  <RefreshCw className="w-8 h-8 text-blue-600 animate-spin" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">加载中</h3>
                <p className="text-gray-500">正在获取备份列表...</p>
              </div>
            ) : (
              <BackupList
                backups={backups}
                activeBackup={activeBackup}
                isSwitching={isSwitching}
                onSwitchBackup={handleSwitchBackup}
                formatFileSize={formatFileSize}
                formatRelativeTime={formatRelativeTime}
              />
            )}
          </div>
        </div>

        {/* 右侧面板 - 统计信息和操作指南 (20%) */}
        <div className="w-[20%] bg-gray-50 border-l border-gray-200 flex flex-col flex-shrink-0 min-w-0">
          {/* 统计信息 */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">统计信息</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">总版本数</span>
                <span className="font-medium text-gray-900">{backups.length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">活动版本</span>
                <span className="font-medium text-green-600">{activeBackup ? '1' : '0'}</span>
              </div>
              {activeBackup ? (
                <>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">当前大小</span>
                    <span className="font-medium text-gray-900">{formatFileSize(activeBackup.backup_size)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">创建时间</span>
                    <span className="font-medium text-gray-900">{formatRelativeTime(activeBackup.backup_timestamp)}</span>
                  </div>
                </>
              ) : (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">状态</span>
                  <span className="font-medium text-blue-600">使用初始数据库</span>
                </div>
              )}
            </div>
          </div>

          {/* 系统信息 */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">系统信息</h3>
            <div className="space-y-2 text-xs text-gray-600">
              <div>
                <span className="font-medium">版本控制</span>
                <p>类似 Git 的数据库版本管理</p>
              </div>
              <div>
                <span className="font-medium">数据安全</span>
                <p>支持快速备份和恢复</p>
              </div>
              <div>
                <span className="font-medium">操作提示</span>
                <p>切换备份会改变当前数据库</p>
              </div>
            </div>
          </div>

          {/* 操作指南区域 */}
          <div className="flex-1 p-4 overflow-y-auto">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">操作指南</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-gray-900 text-sm">创建备份</p>
                  <p className="text-gray-600 text-xs">点击工具栏的"创建备份"按钮</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-gray-900 text-sm">切换版本</p>
                  <p className="text-gray-600 text-xs">在版本列表中点击"切换到此版本"</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-gray-900 text-sm">查看详情</p>
                  <p className="text-gray-600 text-xs">版本卡片显示详细的备份信息</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-gray-900 text-sm">安全提示</p>
                  <p className="text-gray-600 text-xs">切换备份会改变当前数据库</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 创建备份对话框 */}
      <CreateBackupDialog
        isOpen={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onConfirm={handleCreateBackup}
        isCreating={isCreating}
      />
    </div>
  );
};