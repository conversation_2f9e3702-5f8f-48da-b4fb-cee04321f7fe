import { useState, useEffect, useCallback } from 'react';
import BackupService, { BackupData, BackupServiceState } from '../services/Backup/backupService';

/**
 * 备份管理Hook
 * 提供备份相关的状态和操作方法
 */
export const useBackup = () => {
  const [state, setState] = useState<BackupServiceState>(() =>
    BackupService.getInstance().getState()
  );

  const [message, setMessage] = useState<{
    type: 'success' | 'error' | 'info';
    text: string;
  } | null>(null);

  const backupService = BackupService.getInstance();

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: BackupServiceState) => {
      setState(newState);
    };

    const handleError = (error: string) => {
      setMessage({ type: 'error', text: error });
    };

    const handleBackupCreated = (backup: BackupData) => {
      setMessage({
        type: 'success',
        text: `备份创建成功: ${backup.backup_name || backup.backup_filename}`
      });
    };

    const handleBackupSwitched = (backup: BackupData) => {
      setMessage({
        type: 'success',
        text: `已切换到备份: ${backup.backup_name || backup.backup_filename}`
      });
    };

    // 先清理可能存在的旧监听器
    backupService.off('state-change', handleStateChange);
    backupService.off('error', handleError);
    backupService.off('backup-created', handleBackupCreated);
    backupService.off('backup-switched', handleBackupSwitched);

    // 注册新的监听器
    backupService.on('state-change', handleStateChange);
    backupService.on('error', handleError);
    backupService.on('backup-created', handleBackupCreated);
    backupService.on('backup-switched', handleBackupSwitched);

    return () => {
      backupService.off('state-change', handleStateChange);
      backupService.off('error', handleError);
      backupService.off('backup-created', handleBackupCreated);
      backupService.off('backup-switched', handleBackupSwitched);
    };
  }, []); // 移除backupService依赖，避免重复注册

  // 手动初始化服务（不自动获取数据）
  useEffect(() => {
    const initService = async () => {
      try {
        console.log('useBackup: 初始化备份服务（不自动获取数据）');
        // 只初始化基础服务，不获取任何数据
        // 数据获取由用户操作触发（如刷新按钮）
      } catch (error) {
        console.error('初始化备份服务失败:', error);
      }
    };

    initService();
  }, [backupService]);

  // 创建备份
  const createBackup = useCallback(async (backupPath: string, backupName?: string) => {
    try {
      clearMessage();
      return await backupService.createBackup(backupPath, backupName);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建备份失败';
      setMessage({ type: 'error', text: errorMessage });
      throw error;
    }
  }, [backupService]);

  // 刷新备份列表（使用缓存）
  const refreshBackups = useCallback(async () => {
    try {
      clearMessage();
      return await backupService.getBackupList();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '刷新备份列表失败';
      setMessage({ type: 'error', text: errorMessage });
      throw error;
    }
  }, [backupService]);

  // 强制刷新备份列表（忽略缓存）
  const forceRefreshBackups = useCallback(async () => {
    try {
      clearMessage();
      return await backupService.forceRefreshBackups();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '强制刷新备份列表失败';
      setMessage({ type: 'error', text: errorMessage });
      throw error;
    }
  }, [backupService]);

  // 切换备份
  const switchToBackup = useCallback(async (backupId: number) => {
    try {
      clearMessage();
      const result = await backupService.switchToBackup(backupId);

      // 备份服务内部已经处理了全局数据刷新
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '切换备份失败';
      setMessage({ type: 'error', text: errorMessage });
      throw error;
    }
  }, [backupService]);

  // 获取活动备份
  const getActiveBackup = useCallback(async () => {
    try {
      return await backupService.getActiveBackup();
    } catch (error) {
      console.error('获取活动备份失败:', error);
      return null;
    }
  }, [backupService]);

  // 清除消息
  const clearMessage = useCallback(() => {
    setMessage(null);
  }, []);

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number) => {
    return backupService.formatFileSize(bytes);
  }, [backupService]);

  // 格式化相对时间
  const formatRelativeTime = useCallback((timestamp: number) => {
    return backupService.formatRelativeTime(timestamp);
  }, [backupService]);

  return {
    // 状态
    backups: state.backups,
    activeBackup: state.activeBackup,
    isLoading: state.isLoading,
    isCreating: state.isCreating,
    isSwitching: state.isSwitching,
    error: state.error,
    message,

    // 方法
    createBackup,
    refreshBackups,
    forceRefreshBackups,
    switchToBackup,
    getActiveBackup,
    clearMessage,
    formatFileSize,
    formatRelativeTime,
  };
};