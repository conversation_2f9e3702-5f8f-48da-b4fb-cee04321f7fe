import { Selector } from '../../types/service';

/**
 * 状态管理器
 * 提供不可变状态管理和选择器模式
 */
export class StateManager<T> {
  private state: T;
  private listeners: Set<(state: T) => void> = new Set();
  private selectors: Map<Selector<T, any>, Set<(selected: any) => void>> = new Map();
  private prevSelectedValues: Map<Selector<T, any>, any> = new Map();

  /**
   * 构造函数
   * @param initialState 初始状态
   */
  constructor(initialState: T) {
    this.state = initialState;
  }

  /**
   * 获取当前状态
   * @returns 当前状态的副本
   */
  public getState(): T {
    return { ...this.state };
  }

  /**
   * 更新状态
   * @param newState 新状态或状态更新函数
   */
  public setState(newState: Partial<T> | ((prevState: T) => Partial<T>)): void {
    const prevState = this.state;
    
    // 如果newState是函数，调用它获取新状态
    const stateUpdate = typeof newState === 'function'
      ? newState(prevState)
      : newState;
    
    // 更新状态
    this.state = { ...this.state, ...stateUpdate };

    // 通知所有监听器
    this.listeners.forEach(listener => listener(this.state));

    // 检查选择器并通知相关监听器
    this.selectors.forEach((listeners, selector) => {
      const prevSelected = this.prevSelectedValues.get(selector);
      const newSelected = selector(this.state);

      // 只有当选择的值发生变化时才通知
      if (!this.shallowEqual(prevSelected, newSelected)) {
        this.prevSelectedValues.set(selector, newSelected);
        listeners.forEach(listener => listener(newSelected));
      }
    });
  }

  /**
   * 订阅状态变化
   * @param listener 监听函数
   * @returns 取消订阅函数
   */
  public subscribe(listener: (state: T) => void): () => void {
    this.listeners.add(listener);
    
    // 立即通知当前状态
    listener(this.state);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 使用选择器订阅状态的特定部分
   * @param selector 选择器函数
   * @param listener 监听函数
   * @returns 取消订阅函数
   */
  public select<R>(selector: Selector<T, R>, listener: (selected: R) => void): () => void {
    if (!this.selectors.has(selector)) {
      this.selectors.set(selector, new Set());
      this.prevSelectedValues.set(selector, selector(this.state));
    }

    const listeners = this.selectors.get(selector)!;
    listeners.add(listener);

    // 立即通知当前值
    listener(selector(this.state));

    return () => {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.selectors.delete(selector);
        this.prevSelectedValues.delete(selector);
      }
    };
  }

  /**
   * 浅比较两个对象是否相等
   * @param a 对象A
   * @param b 对象B
   * @returns 是否相等
   */
  private shallowEqual(a: any, b: any): boolean {
    if (a === b) return true;
    if (a === null || b === null) return false;
    if (typeof a !== 'object' || typeof b !== 'object') return false;

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) return false;

    for (const key of keysA) {
      if (a[key] !== b[key]) return false;
    }

    return true;
  }
}
