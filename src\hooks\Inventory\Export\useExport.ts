import { useState, useEffect, useCallback } from 'react';
import ExportService, { ExportFormat } from '../../../services/Inventory/Export/exportService';
import ExportSettingsService from '../../../services/Inventory/Export/exportSettingsService';
import { InventoryItem, FieldDefinition, DeviceCategory } from '../../../types/inventory';
import { DepartmentCategory } from '../../../services/Inventory/departmentService';

// 导出Hook返回类型
interface UseExportReturn {
  // 状态
  isExporting: boolean;
  isSending: boolean;
  progress: number;
  lastExportFileName: string;
  error?: string;
  selectedCategories: string[];
  selectedColumns: string[];
  columnOrder: string[];

  // 基础导出方法
  exportInventory: (items: InventoryItem[], fileName: string, format: ExportFormat) => Promise<Blob>;
  sendToServer: (blob: Blob, fileName: string, format: ExportFormat) => Promise<string>;
  exportAndSendToServer: (items: InventoryItem[], fileName: string, format: ExportFormat) => Promise<void>;

  // 模板导出方法
  exportImportTemplate: (columns: FieldDefinition[], fileName: string, format: ExportFormat) => Promise<Blob>;
  exportTemplateAndSendToServer: (columns: FieldDefinition[], fileName: string, format: ExportFormat) => Promise<void>;

  // 高级导出方法
  exportByCategories: (
    items: InventoryItem[],
    categories: (DeviceCategory | DepartmentCategory)[],
    selectedCategoryIds: string[],
    fileName: string,
    format: ExportFormat
  ) => Promise<Blob>;

  exportWithCustomColumns: (
    items: InventoryItem[],
    columns: FieldDefinition[],
    selectedColumnIds: string[],
    columnOrder: string[],
    fileName: string,
    format: ExportFormat
  ) => Promise<Blob>;

  exportWithSettings: (
    items: InventoryItem[],
    categories: (DeviceCategory | DepartmentCategory)[],
    columns: FieldDefinition[],
    fileName: string,
    format: ExportFormat
  ) => Promise<Blob>;

  exportWithSettingsAndSend: (
    items: InventoryItem[],
    categories: (DeviceCategory | DepartmentCategory)[],
    columns: FieldDefinition[],
    fileName: string,
    format: ExportFormat
  ) => Promise<void>;

  // 设置方法
  setSelectedCategories: (categoryIds: string[]) => void;
  setSelectedColumns: (columnIds: string[]) => void;
  setColumnOrder: (columnOrder: string[]) => void;
  initializeColumnSelection: (columns: FieldDefinition[]) => void;
  resetSettings: () => void;

  // 任务控制
  cancelTask: () => void;

  // 服务访问
  getService: () => ExportService;
  getSettingsService: () => ExportSettingsService;
}

/**
 * 导出Hook
 * 连接导出服务和UI组件
 */
export function useExport(): UseExportReturn {
  // 获取导出服务实例
  const service = ExportService.getInstance();
  const settingsService = ExportSettingsService.getInstance();

  // 本地状态
  const [state, setState] = useState({
    isExporting: service.getState().isExporting,
    isSending: service.getState().isSending,
    progress: service.getState().progress,
    lastExportFileName: service.getState().lastExportFileName,
    error: service.getState().error,
    selectedCategories: settingsService.getState().selectedCategories,
    selectedColumns: settingsService.getState().selectedColumns,
    columnOrder: settingsService.getState().columnOrder
  });

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = () => {
      setState(prevState => ({
        ...prevState,
        isExporting: service.getState().isExporting,
        isSending: service.getState().isSending,
        progress: service.getState().progress,
        lastExportFileName: service.getState().lastExportFileName,
        error: service.getState().error
      }));
    };

    // 注册状态变化监听器
    service.on('state-change', handleStateChange);

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
    };
  }, [service]);

  // 监听设置服务状态变化
  useEffect(() => {
    const handleSettingsChange = () => {
      setState(prevState => ({
        ...prevState,
        selectedCategories: settingsService.getState().selectedCategories,
        selectedColumns: settingsService.getState().selectedColumns,
        columnOrder: settingsService.getState().columnOrder
      }));
    };

    // 注册设置变化监听器
    settingsService.on('settings-changed', handleSettingsChange);

    // 清理函数
    return () => {
      settingsService.off('settings-changed', handleSettingsChange);
    };
  }, [settingsService]);

  // 导出设备台账数据
  const exportInventory = useCallback(async (
    items: InventoryItem[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> => {
    try {
      return await service.exportInventory(items, fileName, format);
    } catch (error: any) {
      console.error('导出设备台账数据失败:', error);
      throw error;
    }
  }, [service]);

  // 按分类导出设备台账数据
  const exportByCategories = useCallback(async (
    items: InventoryItem[],
    categories: (DeviceCategory | DepartmentCategory)[],
    selectedCategoryIds: string[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> => {
    try {
      return await service.exportByCategories(items, categories, selectedCategoryIds, fileName, format);
    } catch (error: any) {
      console.error('按分类导出设备台账数据失败:', error);
      throw error;
    }
  }, [service]);

  // 使用自定义列导出设备台账数据
  const exportWithCustomColumns = useCallback(async (
    items: InventoryItem[],
    columns: FieldDefinition[],
    selectedColumnIds: string[],
    columnOrder: string[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> => {
    try {
      return await service.exportWithCustomColumns(items, columns, selectedColumnIds, columnOrder, fileName, format);
    } catch (error: any) {
      console.error('使用自定义列导出设备台账数据失败:', error);
      throw error;
    }
  }, [service]);

  // 使用完整设置导出设备台账数据
  const exportWithSettings = useCallback(async (
    items: InventoryItem[],
    categories: (DeviceCategory | DepartmentCategory)[],
    columns: FieldDefinition[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> => {
    try {
      // 使用设置服务中的设置
      const selectedCategoryIds = settingsService.getState().selectedCategories;
      const selectedColumnIds = settingsService.getState().selectedColumns;
      const columnOrder = settingsService.getState().columnOrder;

      console.log('导出设置 - 选中的分类:', selectedCategoryIds);
      console.log('导出设置 - 选中的列:', selectedColumnIds);
      console.log('导出设置 - 列顺序:', columnOrder);
      console.log('导出设置 - 数据条数:', items.length);

      // 先按分类筛选数据 - 使用导出对话框中的分类选择
      // 如果没有选择任何分类，则导出所有数据
      let filteredItems: InventoryItem[];
      if (!selectedCategoryIds || selectedCategoryIds.length === 0) {
        console.log('没有选择任何分类，导出所有数据');
        filteredItems = [...items];
      } else {
        console.log('根据选中的分类筛选数据');
        filteredItems = await service.filterItemsByCategories(items, categories, selectedCategoryIds);
      }

      console.log('筛选后的数据条数:', filteredItems.length);

      // 然后使用自定义列导出
      return await service.exportWithCustomColumns(
        filteredItems,
        columns,
        selectedColumnIds,
        columnOrder,
        fileName,
        format
      );
    } catch (error: any) {
      console.error('使用完整设置导出设备台账数据失败:', error);
      throw error;
    }
  }, [service, settingsService]);

  // 发送文件到服务器
  const sendToServer = useCallback(async (
    blob: Blob,
    fileName: string,
    format: ExportFormat
  ): Promise<string> => {
    try {
      return await service.sendToServer(blob, fileName, format);
    } catch (error: any) {
      console.error('发送文件到服务器失败:', error);
      throw error;
    }
  }, [service]);

  // 通用的导出并发送方法
  const exportAndSendToServerCommon = useCallback(async <T extends any[]>(
    exportMethod: (...args: T) => Promise<Blob>,
    methodName: string,
    fileName: string,
    format: ExportFormat,
    ...args: T
  ): Promise<void> => {
    try {
      // 先导出生成文件
      const blob = await exportMethod(...args);

      // 然后发送到服务器
      await sendToServer(blob, fileName, format);
    } catch (error: any) {
      console.error(`${methodName}失败:`, error);
      throw error;
    }
  }, [sendToServer]);

  // 导出并发送到服务器
  const exportAndSendToServer = useCallback(async (
    items: InventoryItem[],
    fileName: string,
    format: ExportFormat
  ): Promise<void> => {
    return exportAndSendToServerCommon(
      exportInventory,
      '导出并发送到服务器',
      fileName,
      format,
      items,
      fileName,
      format
    );
  }, [exportAndSendToServerCommon, exportInventory]);

  // 使用完整设置导出并发送到服务器
  const exportWithSettingsAndSend = useCallback(async (
    items: InventoryItem[],
    categories: (DeviceCategory | DepartmentCategory)[],
    columns: FieldDefinition[],
    fileName: string,
    format: ExportFormat
  ): Promise<void> => {
    return exportAndSendToServerCommon(
      exportWithSettings,
      '使用完整设置导出并发送到服务器',
      fileName,
      format,
      items,
      categories,
      columns,
      fileName,
      format
    );
  }, [exportAndSendToServerCommon, exportWithSettings]);

  // 导出导入模板
  const exportImportTemplate = useCallback(async (
    columns: FieldDefinition[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> => {
    try {
      return await service.exportImportTemplate(columns, fileName, format);
    } catch (error: any) {
      console.error('导出导入模板失败:', error);
      throw error;
    }
  }, [service]);

  // 导出模板并发送到服务器
  const exportTemplateAndSendToServer = useCallback(async (
    columns: FieldDefinition[],
    fileName: string,
    format: ExportFormat
  ): Promise<void> => {
    return exportAndSendToServerCommon(
      exportImportTemplate,
      '导出模板并发送到服务器',
      fileName,
      format,
      columns,
      fileName,
      format
    );
  }, [exportAndSendToServerCommon, exportImportTemplate]);

  // 设置选中的分类
  const setSelectedCategories = useCallback((categoryIds: string[]) => {
    settingsService.setSelectedCategories(categoryIds);
  }, [settingsService]);

  // 设置选中的列
  const setSelectedColumns = useCallback((columnIds: string[]) => {
    settingsService.setSelectedColumns(columnIds);
  }, [settingsService]);

  // 设置列顺序
  const setColumnOrder = useCallback((columnOrder: string[]) => {
    settingsService.setColumnOrder(columnOrder);
  }, [settingsService]);

  // 初始化列选择
  const initializeColumnSelection = useCallback((columns: FieldDefinition[]) => {
    settingsService.initializeColumnSelection(columns);
  }, [settingsService]);

  // 重置设置
  const resetSettings = useCallback(() => {
    settingsService.resetSettings();
  }, [settingsService]);

  // 取消任务
  const cancelTask = useCallback(() => {
    service.cancelTask();
  }, [service]);

  // 获取服务实例
  const getService = useCallback(() => {
    return service;
  }, [service]);

  // 获取设置服务实例
  const getSettingsService = useCallback(() => {
    return settingsService;
  }, [settingsService]);

  // 返回Hook结果
  return {
    ...state,
    exportInventory,
    exportByCategories,
    exportWithCustomColumns,
    exportWithSettings,
    sendToServer,
    exportAndSendToServer,
    exportWithSettingsAndSend,
    exportImportTemplate,
    exportTemplateAndSendToServer,
    setSelectedCategories,
    setSelectedColumns,
    setColumnOrder,
    initializeColumnSelection,
    resetSettings,
    cancelTask,
    getService,
    getSettingsService
  };
}

export default useExport;
