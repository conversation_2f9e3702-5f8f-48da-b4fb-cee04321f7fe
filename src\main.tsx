import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import './styles/customScrollbar.css';
import './styles/focusStyles.css';
import KeyboardManager from './components/KeyboardManager';
import './utils/tabKeyFix'; // 自动初始化Tab键修复

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <KeyboardManager>
      <App />
    </KeyboardManager>
  </StrictMode>
);
