/**
 * 统一表格Hook - useBaseTable
 * 提供表格状态管理和操作方法
 */

import { useState, useCallback, useMemo, useEffect } from 'react';
import { SortingState } from '@tanstack/react-table';
import { UseTableReturn, TablePagination, TableFeatures } from '../types/table';

interface UseBaseTableOptions<T = any> {
  /** 初始数据 */
  initialData?: T[];
  /** 初始分页配置 */
  initialPagination?: Partial<TablePagination>;
  /** 功能特性配置 */
  features?: TableFeatures;
  /** 数据获取函数 */
  fetchData?: (params: {
    pageIndex: number;
    pageSize: number;
    sorting: SortingState;
    filters?: Record<string, any>;
  }) => Promise<{ data: T[]; total: number }>;
  /** 行键值获取函数 */
  rowKey?: string | ((record: T) => string);
}

/**
 * 统一表格Hook
 */
export function useBaseTable<T = any>(options: UseBaseTableOptions<T> = {}): UseTableReturn<T> {
  const {
    initialData = [],
    initialPagination = {},
    features = {},
    fetchData,
    rowKey = 'id',
  } = options;

  // 状态管理
  const [data, setData] = useState<T[]>(initialData);
  const [loading, setLoading] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  
  // 分页状态
  const [pagination, setPagination] = useState<TablePagination>({
    pageIndex: 0,
    pageSize: 10,
    total: 0,
    pageSizeOptions: [10, 20, 50, 100],
    showQuickJumper: true,
    showTotal: true,
    ...initialPagination,
  });

  // 行键值获取函数
  const getRowKey = useCallback((record: T): string => {
    if (typeof rowKey === 'string') {
      return (record as any)[rowKey];
    }
    return rowKey(record);
  }, [rowKey]);

  // 选中的行数据
  const selectedRows = useMemo(() => {
    return data.filter(record => selectedRowKeys.includes(getRowKey(record)));
  }, [data, selectedRowKeys, getRowKey]);

  // 刷新数据
  const refresh = useCallback(async () => {
    if (!fetchData) {
      return;
    }

    setLoading(true);
    try {
      const result = await fetchData({
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
        sorting,
      });
      
      setData(result.data);
      setPagination(prev => ({
        ...prev,
        total: result.total,
      }));
    } catch (error) {
      console.error('Failed to fetch table data:', error);
    } finally {
      setLoading(false);
    }
  }, [fetchData, pagination.pageIndex, pagination.pageSize, sorting]);

  // 设置页面大小
  const setPageSize = useCallback((size: number) => {
    setPagination(prev => ({
      ...prev,
      pageSize: size,
      pageIndex: 0, // 重置到第一页
    }));
  }, []);

  // 设置页码
  const setPageIndex = useCallback((index: number) => {
    setPagination(prev => ({
      ...prev,
      pageIndex: index,
    }));
  }, []);

  // 切换行选择
  const toggleRowSelection = useCallback((key: string) => {
    setSelectedRowKeys(prev => {
      const index = prev.indexOf(key);
      if (index > -1) {
        return prev.filter(k => k !== key);
      } else {
        return [...prev, key];
      }
    });
  }, []);

  // 切换全选
  const toggleAllSelection = useCallback(() => {
    if (selectedRowKeys.length === data.length) {
      // 当前全选，取消全选
      setSelectedRowKeys([]);
    } else {
      // 当前非全选，全选
      setSelectedRowKeys(data.map(getRowKey));
    }
  }, [data, selectedRowKeys.length, getRowKey]);

  // 设置排序
  const setSortingState = useCallback((newSorting: SortingState) => {
    setSorting(newSorting);
    // 如果有远程数据获取，重置到第一页
    if (fetchData) {
      setPagination(prev => ({
        ...prev,
        pageIndex: 0,
      }));
    }
  }, [fetchData]);

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelectedRowKeys([]);
  }, []);

  // 设置选中的行
  const setSelectedRows = useCallback((keys: string[]) => {
    setSelectedRowKeys(keys);
  }, []);

  // 批量操作
  const batchActions = useMemo(() => ({
    /** 删除选中的行 */
    deleteSelected: () => {
      setData(prev => prev.filter(record => !selectedRowKeys.includes(getRowKey(record))));
      setSelectedRowKeys([]);
    },
    /** 导出选中的行 */
    exportSelected: () => {
      return selectedRows;
    },
    /** 获取选中行的数量 */
    getSelectedCount: () => selectedRowKeys.length,
    /** 判断是否有选中的行 */
    hasSelected: () => selectedRowKeys.length > 0,
    /** 判断是否全选 */
    isAllSelected: () => selectedRowKeys.length === data.length && data.length > 0,
    /** 判断是否部分选中 */
    isIndeterminate: () => selectedRowKeys.length > 0 && selectedRowKeys.length < data.length,
  }), [selectedRowKeys, selectedRows, data, getRowKey]);

  // 筛选操作
  const filterActions = useMemo(() => ({
    /** 按条件筛选数据 */
    filterData: (predicate: (record: T) => boolean) => {
      const filteredData = data.filter(predicate);
      return filteredData;
    },
    /** 搜索数据 */
    searchData: (searchText: string, searchFields: string[]) => {
      if (!searchText.trim()) {
        return data;
      }
      
      const filteredData = data.filter(record => {
        return searchFields.some(field => {
          const value = (record as any)[field];
          return value && value.toString().toLowerCase().includes(searchText.toLowerCase());
        });
      });
      
      return filteredData;
    },
  }), [data]);

  return {
    // 数据状态
    data,
    loading,
    pagination,
    selection: {
      selectedRowKeys,
      selectedRows,
    },
    sorting,

    // 操作方法
    actions: {
      refresh,
      setPageSize,
      setPageIndex,
      toggleRowSelection,
      toggleAllSelection,
      setSorting: setSortingState,
      clearSelection,
      setSelectedRows,
      setData,
      ...batchActions,
      ...filterActions,
    },
  };
}

/**
 * 简化版表格Hook - 用于静态数据
 */
export function useSimpleTable<T = any>(data: T[], rowKey: string | ((record: T) => string) = 'id') {
  return useBaseTable<T>({
    initialData: data,
    rowKey,
    features: {
      sorting: true,
      selection: true,
    },
  });
}

/**
 * 远程数据表格Hook - 用于需要远程获取数据的表格
 */
export function useRemoteTable<T = any>(
  fetchData: UseBaseTableOptions<T>['fetchData'],
  options: Omit<UseBaseTableOptions<T>, 'fetchData'> = {}
) {
  const tableState = useBaseTable<T>({
    ...options,
    fetchData,
    features: {
      sorting: true,
      pagination: true,
      selection: true,
      ...options.features,
    },
  });

  // 自动加载初始数据
  useEffect(() => {
    tableState.actions.refresh();
  }, []);

  // 当分页或排序改变时自动刷新
  useEffect(() => {
    if (fetchData) {
      tableState.actions.refresh();
    }
  }, [tableState.pagination.pageIndex, tableState.pagination.pageSize, tableState.sorting]);

  return tableState;
}

export default useBaseTable;
