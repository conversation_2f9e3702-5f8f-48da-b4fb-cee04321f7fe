import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { ChevronDown, ChevronRight, Check, Server, Folder, Laptop, Database, ShieldAlert, User, Building, Building2, Usb, Calculator, Speaker, Smartphone, Printer, HardDrive, Monitor } from 'lucide-react';
import { DeviceCategory } from '../../../types/inventory';
import { DepartmentCategory } from '../../../services/Inventory/departmentService';
import { getCategoryIconConfig } from '../../../hooks/Inventory/useCategoryIcons';

// 分类选择器属性
interface CategorySelectorProps {
  categories: DeviceCategory[] | DepartmentCategory[];
  selectedCategories: string[];
  onSelectCategory: (categoryIds: string[]) => void;
  categoryMode: 'device' | 'department';
  title?: string;
  onReset?: () => void; // 添加重置函数属性
  disabled?: boolean; // 是否禁用选择器
}

/**
 * 分类选择器组件
 * 用于在导出时选择设备分类或部门分类
 */
const CategorySelector: React.FC<CategorySelectorProps> = ({
  categories,
  selectedCategories,
  onSelectCategory,
  categoryMode,
  title = categoryMode === 'device' ? '设备分类' : '部门分类',
  onReset,
  disabled = false
}) => {
  // 展开的节点状态
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // 监听分类模式变化，重置展开的节点状态
  useEffect(() => {
    // 当分类模式改变时，只展开根节点
    if (categories.length > 0) {
      const rootId = categories[0].id;
      setExpandedNodes(new Set([rootId]));
      console.log(`分类模式切换，重置展开状态，根节点ID: ${rootId}, 模式: ${categoryMode}`);
    }
  }, [categoryMode, categories]);

  // 初始化时展开根节点和选中的节点
  useEffect(() => {
    if (categories.length > 0) {
      const rootId = categories[0].id;
      const newExpandedNodes = new Set([rootId]);

      // 如果有选中的节点，找到该节点的所有父节点并展开
      if (selectedCategories.length > 0) {
        // 查找节点的所有父节点
        const findParentNodes = (nodes: DeviceCategory[] | DepartmentCategory[], targetId: string, path: string[] = []): string[] | null => {
          for (const node of nodes) {
            // 当前路径
            const currentPath = [...path, node.id];

            // 如果找到目标节点，返回路径
            if (node.id === targetId) {
              return currentPath;
            }

            // 如果有子节点，递归查找
            if (node.children && node.children.length > 0) {
              const result = findParentNodes(node.children, targetId, currentPath);
              if (result) {
                return result;
              }
            }
          }

          return null;
        };

        // 对每个选中的节点，找到其父节点路径并展开
        selectedCategories.forEach(categoryId => {
          const parentPath = findParentNodes(categories, categoryId);
          if (parentPath) {
            // 将所有父节点添加到展开节点集合中
            parentPath.forEach(id => newExpandedNodes.add(id));
          }
        });
      }

      setExpandedNodes(newExpandedNodes);
    }
  }, [categories, selectedCategories]);

  // 切换节点展开/折叠状态
  const toggleNode = useCallback((nodeId: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 获取节点及其所有子节点的ID
  const getNodeAndChildrenIds = useCallback((nodeId: string, items: DeviceCategory[] | DepartmentCategory[]): string[] => {
    let ids: string[] = [];

    // 查找节点
    const findNode = (nodes: DeviceCategory[] | DepartmentCategory[]): DeviceCategory | DepartmentCategory | null => {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = findNode(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    const node = findNode(items);
    if (!node) return ids;

    // 添加当前节点ID
    ids.push(node.id);

    // 添加所有子节点ID
    if (node.children && node.children.length > 0) {
      ids = [...ids, ...getAllCategoryIds(node.children)];
    }

    return ids;
  }, []);

  // 查找节点的辅助函数
  const findNode = useCallback((nodes: DeviceCategory[] | DepartmentCategory[], nodeId: string): DeviceCategory | DepartmentCategory | null => {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // 获取节点的所有父节点ID
  const getParentNodeIds = useCallback((nodeId: string, items: DeviceCategory[] | DepartmentCategory[]): string[] => {
    const parentIds: string[] = [];

    const findParents = (nodes: DeviceCategory[] | DepartmentCategory[], targetId: string, currentPath: string[] = []): boolean => {
      for (const node of nodes) {
        const newPath = [...currentPath, node.id];

        if (node.id === targetId) {
          parentIds.push(...currentPath);
          return true;
        }

        if (node.children && node.children.length > 0) {
          if (findParents(node.children, targetId, newPath)) {
            return true;
          }
        }
      }
      return false;
    };

    findParents(items, nodeId);
    return parentIds;
  }, []);

  // 检查节点是否被父节点选中
  const isNodeSelectedByParent = useCallback((nodeId: string): boolean => {
    const parentIds = getParentNodeIds(nodeId, categories);
    return parentIds.some(parentId => selectedCategories.includes(parentId));
  }, [getParentNodeIds, categories, selectedCategories]);

  // 维护一个排除列表，记录用户明确取消选中的节点
  const [excludedNodes, setExcludedNodes] = useState<Set<string>>(new Set());

  // 切换分类选择状态（支持独立选择，单击直接取消）
  const toggleCategorySelection = useCallback((categoryId: string) => {
    // 检查当前节点是否直接被选中
    const isDirectlySelected = selectedCategories.includes(categoryId);
    // 检查当前节点是否被父节点选中
    const isSelectedByParent = isNodeSelectedByParent(categoryId);
    // 检查当前节点是否被排除
    const isExcluded = excludedNodes.has(categoryId);

    if (isDirectlySelected) {
      // 如果节点直接被选中，取消选择该节点及其所有子节点
      const nodeAndChildrenIds = getNodeAndChildrenIds(categoryId, categories);
      const newSelectedCategories = selectedCategories.filter(id => !nodeAndChildrenIds.includes(id));
      onSelectCategory(newSelectedCategories);

      // 将该节点及其子节点添加到排除列表
      setExcludedNodes(prev => {
        const newSet = new Set(prev);
        nodeAndChildrenIds.forEach(id => newSet.add(id));
        return newSet;
      });
    } else if (isSelectedByParent && !isExcluded) {
      // 如果节点被父节点选中且未被排除，单击直接排除
      const nodeAndChildrenIds = getNodeAndChildrenIds(categoryId, categories);



      // 创建新的排除列表（包含当前要排除的节点）
      const newExcludedNodes = new Set(excludedNodes);
      nodeAndChildrenIds.forEach(id => newExcludedNodes.add(id));

      // 将该节点及其子节点添加到排除列表
      setExcludedNodes(newExcludedNodes);

      // 取消所有选中的父节点，因为它们不再包含所有子节点
      const parentIds = getParentNodeIds(categoryId, categories);
      const selectedParentIds = parentIds.filter(parentId => selectedCategories.includes(parentId));



      if (selectedParentIds.length > 0) {
        // 移除所有选中的父节点
        const newSelectedCategories = selectedCategories.filter(id => !selectedParentIds.includes(id));

        // 添加父节点的其他未被排除的子节点
        selectedParentIds.forEach(parentId => {
          const parentAndChildrenIds = getNodeAndChildrenIds(parentId, categories);
          const otherChildrenIds = parentAndChildrenIds.filter(id =>
            id !== parentId && !nodeAndChildrenIds.includes(id) && !newExcludedNodes.has(id)
          );

          newSelectedCategories.push(...otherChildrenIds);
        });


        onSelectCategory(Array.from(new Set(newSelectedCategories)));
      }
    } else if (isExcluded) {
      // 如果节点被排除，重新选中
      const nodeAndChildrenIds = getNodeAndChildrenIds(categoryId, categories);

      // 创建新的排除列表（移除当前要重新选中的节点）
      const newExcludedNodes = new Set(excludedNodes);
      nodeAndChildrenIds.forEach(id => newExcludedNodes.delete(id));

      // 从排除列表中移除
      setExcludedNodes(newExcludedNodes);

      // 直接选中该节点及其子节点
      let newSelectedCategories = Array.from(new Set([...selectedCategories, ...nodeAndChildrenIds]));

      // 检查是否应该重新选中父节点（如果父节点的所有子节点都被选中了）
      const parentIds = getParentNodeIds(categoryId, categories);
      parentIds.forEach(parentId => {
        if (!selectedCategories.includes(parentId)) {
          // 获取父节点的所有子节点
          const parentAndChildrenIds = getNodeAndChildrenIds(parentId, categories);
          const parentChildrenIds = parentAndChildrenIds.filter(id => id !== parentId);

          // 检查父节点的所有子节点是否都被选中（直接选中或未被排除）
          const allChildrenSelected = parentChildrenIds.every(childId =>
            newSelectedCategories.includes(childId) ||
            (!newExcludedNodes.has(childId) && isNodeSelectedByParent(childId))
          );

          if (allChildrenSelected) {
            // 如果所有子节点都被选中，选中父节点并移除子节点的直接选中状态
            newSelectedCategories = newSelectedCategories.filter(id => !parentChildrenIds.includes(id));
            newSelectedCategories.push(parentId);
          }
        }
      });

      onSelectCategory(Array.from(new Set(newSelectedCategories)));
    } else {
      // 如果节点未被选中，选择该节点及其所有子节点
      const nodeAndChildrenIds = getNodeAndChildrenIds(categoryId, categories);
      const newSelectedCategories = Array.from(new Set([...selectedCategories, ...nodeAndChildrenIds]));
      onSelectCategory(newSelectedCategories);

      // 从排除列表中移除（如果存在）
      setExcludedNodes(prev => {
        const newSet = new Set(prev);
        nodeAndChildrenIds.forEach(id => newSet.delete(id));
        return newSet;
      });

      // 如果选中的节点有子节点，自动展开该节点
      const node = findNode(categories, categoryId);
      if (node && node.children && node.children.length > 0) {
        setExpandedNodes(prev => {
          const newSet = new Set(prev);
          newSet.add(categoryId);
          return newSet;
        });
      }
    }
  }, [selectedCategories, onSelectCategory, getNodeAndChildrenIds, categories, findNode, setExpandedNodes, getParentNodeIds, isNodeSelectedByParent, excludedNodes]);

  // 全选/取消全选
  const toggleSelectAll = useCallback(() => {
    if (selectedCategories.length > 0) {
      // 如果已有选中项，则清空
      onSelectCategory([]);
      setExcludedNodes(new Set()); // 清空排除列表
    } else {
      // 否则全选
      const allIds = getAllCategoryIds(categories);
      onSelectCategory(allIds);
      setExcludedNodes(new Set()); // 清空排除列表
    }
  }, [categories, selectedCategories, onSelectCategory]);

  // 获取所有分类ID的辅助函数
  const getAllCategoryIds = (items: DeviceCategory[] | DepartmentCategory[]): string[] => {
    let ids: string[] = [];

    items.forEach(item => {
      ids.push(item.id);
      if (item.children && item.children.length > 0) {
        ids = [...ids, ...getAllCategoryIds(item.children)];
      }
    });

    return ids;
  };

  // 计算缩进
  const calculateIndent = (depth: number): number => {
    return 12 + depth * 16; // 基础缩进 + 每层级增加的缩进
  };

  // 获取图标样式类名
  const getIconClassName = (type: 'device' | 'department', colorClass: string): string => {
    return `w-3.5 h-3.5 mr-0.5 ${colorClass}`;
  };

  // 根据不同分类和层级选择合适的图标 - 使用新的图标配置系统
  const getIcon = (category: DeviceCategory | DepartmentCategory, depth: number) => {
    // 设备分类模式
    if (categoryMode === 'device') {
      // 确定分类层级深度
      let categoryDepth = 0;
      if (category.id === 'all') {
        categoryDepth = 0; // 根节点
      } else if (category.id.startsWith('parent-') && !category.id.includes('-', 7)) {
        categoryDepth = 1; // 一级分类
      } else if (category.id.startsWith('parent-') && category.id.includes('-', 7)) {
        categoryDepth = 2; // 二级分类
      } else {
        categoryDepth = depth + 1; // 使用传入的depth
      }

      // 获取图标配置，优先使用自定义图标
      const customIcon = (category as DeviceCategory).customIcon;
      const iconConfig = getCategoryIconConfig(
        category.name,
        category.id,
        categoryDepth,
        categories as DeviceCategory[],
        customIcon
      );

      // 图标组件映射
      const IconComponent = {
        'laptop': Laptop,
        'database': Database,
        'usb': Usb,
        'calculator': Calculator,
        'server': Server,
        'shield': ShieldAlert,
        'speaker': Speaker,
        'printer': Printer,
        'smartphone': Smartphone,
        'hardDrive': HardDrive,
        'monitor': Monitor,
        'building2': Building2,
        'folder': Folder
      }[iconConfig.icon] || Folder;

      return <IconComponent className={getIconClassName('device', iconConfig.color)} />;
    }
    // 部门分类模式
    else {
      // 根节点 - 全部部门
      if (category.id === 'all-dept') {
        return <Building2 className={getIconClassName('department', 'text-blue-600')} />;
      }

      // 判断是部门还是人员
      if (category.id.startsWith('person-') || category.id.startsWith('resp-')) {
        // 所有人员节点统一使用同一个图标
        return <User className={getIconClassName('department', 'text-purple-600')} />;
      } else if (category.id.startsWith('dept-')) {
        // 所有部门节点统一使用同一个图标
        return <Building className={getIconClassName('department', 'text-blue-600')} />;
      } else {
        // 其他未知类型的节点使用默认图标
        return <Folder className={getIconClassName('department', 'text-gray-600')} />;
      }
    }

    // 默认图标
    return <Folder className={getIconClassName(categoryMode, 'text-gray-600')} />;
  };

  // 检查父节点是否应该显示为选中状态（所有子节点都被选中且未被排除）
  const shouldParentShowAsSelected = useCallback((categoryId: string): boolean => {
    // 如果节点直接被选中，检查是否所有子节点都未被排除
    if (selectedCategories.includes(categoryId)) {
      const nodeAndChildrenIds = getNodeAndChildrenIds(categoryId, categories);
      const childrenIds = nodeAndChildrenIds.filter(id => id !== categoryId);

      // 如果没有子节点，直接返回true
      if (childrenIds.length === 0) {
        return true;
      }

      // 检查是否有任何子节点被排除
      const hasExcludedChildren = childrenIds.some(childId => excludedNodes.has(childId));
      return !hasExcludedChildren;
    }

    return false;
  }, [selectedCategories, getNodeAndChildrenIds, categories, excludedNodes]);

  // 检查节点是否被选中（包括直接选中和通过父节点选中的情况，但排除被明确排除的节点）
  const isNodeSelected = useCallback((categoryId: string): boolean => {
    // 首先检查是否被排除
    if (excludedNodes.has(categoryId)) {
      return false;
    }

    // 然后检查是否直接被选中，但需要考虑子节点的排除状态
    if (selectedCategories.includes(categoryId)) {
      return shouldParentShowAsSelected(categoryId);
    }

    // 最后检查是否被父节点选中
    return isNodeSelectedByParent(categoryId);
  }, [selectedCategories, isNodeSelectedByParent, excludedNodes, shouldParentShowAsSelected]);

  // 递归渲染分类树
  const renderTree = (items: DeviceCategory[] | DepartmentCategory[], depth = 0) => {
    return items.map(category => {
      const hasChildren = category.children && category.children.length > 0;
      const isExpanded = expandedNodes.has(category.id);
      // 检查节点是否被选中（直接选中或通过父节点选中）
      const isSelected = isNodeSelected(category.id);

      return (
        <div key={category.id} className="select-none">
          <div
            className={`flex items-center py-0.5 px-1 my-0.5 rounded cursor-pointer hover:bg-gray-100 ${
              isSelected ? 'bg-blue-50 text-blue-700' : ''
            }`}
            style={{ paddingLeft: calculateIndent(depth) }}
            onClick={() => toggleCategorySelection(category.id)}
          >
            {/* 展开/折叠图标 */}
            {hasChildren ? (
              <span
                className="inline-flex items-center justify-center text-gray-500 w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0"
                onClick={(e) => toggleNode(category.id, e)}
              >
                {isExpanded ?
                  <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4" /> :
                  <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />}
              </span>
            ) : (
              <span className="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0"></span>
            )}

            {/* 选择框 */}
            <div className={`flex items-center justify-center w-3 h-3 sm:w-4 sm:h-4 mr-1 border rounded flex-shrink-0 ${
              isSelected
                ? 'border-blue-500 bg-blue-500'
                : 'border-gray-300 bg-white'
            }`}>
              {isSelected && <Check className="w-2 h-2 sm:w-3 sm:h-3 text-white" />}
            </div>

            {/* 分类图标 */}
            <span className="flex-shrink-0">{getIcon(category, depth)}</span>

            {/* 分类名称和计数 */}
            <span className="text-xs sm:text-sm truncate flex-1 min-w-0 mx-1">{category.name}</span>
            <span className="text-xs whitespace-nowrap flex-shrink-0" style={{ color: '#60A5FA' }}>({category.count})</span>
          </div>

          {/* 子节点 */}
          {hasChildren && isExpanded && (
            <div className="ml-2">
              {renderTree(category.children, depth + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <div className={`border rounded-md h-full flex flex-col relative ${disabled ? 'opacity-70' : ''}`}>
      {/* 标题栏 - 响应式紧凑样式 */}
      <div className="bg-gray-100 px-2 py-1.5 flex items-center justify-between border-b flex-shrink-0">
        <h3 className="font-medium text-gray-700 text-xs sm:text-sm truncate flex-1 mr-2">{title}</h3>
        <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
          {/* 重置按钮 */}
          {onReset && !disabled && (
            <button
              type="button"
              className="text-xs text-gray-600 hover:text-gray-800 px-1"
              onClick={onReset}
            >
              重置
            </button>
          )}
          {/* 全选按钮 */}
          {!disabled && (
            <button
              type="button"
              className="text-xs text-blue-600 hover:text-blue-800 px-1"
              onClick={toggleSelectAll}
            >
              {/* 检查是否所有可选分类都已选中 */}
              {selectedCategories.length > 0 && selectedCategories.length >= getAllCategoryIds(categories).length
                ? '取消全选'
                : '全选'}
            </button>
          )}
        </div>
      </div>

      {/* 树状图内容 - 使用flex-1自动填充剩余高度，确保滚动容器正确工作 */}
      <div
        className={`flex-1 ${disabled ? 'pointer-events-none' : ''}`}
        style={{
          minHeight: 0,
          maxHeight: '100%',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <div
          className="absolute inset-0 overflow-y-auto p-2"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#cbd5e0 #f7fafc'
          }}
        >
          {disabled ? (
            <div className="text-center py-4 text-blue-600">
              已选择 {selectedCategories.length > 0 ? selectedCategories.length : '所有'} 条记录进行导出
            </div>
          ) : categories.length > 0 ? (
            <div className="space-y-1">
              {renderTree(categories)}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              无可用分类
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategorySelector;
