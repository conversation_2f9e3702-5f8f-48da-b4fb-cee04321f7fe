import { useCallback, useRef } from 'react';
import { useManagersWithDebug } from './useManagers';

interface FormFocusOptions {
  // 是否在验证失败时自动聚焦到第一个错误字段
  autoFocusOnError?: boolean;
  // 错误字段的选择器模式
  errorSelector?: string;
  // 是否滚动到错误字段
  scrollToError?: boolean;
}

interface FormFieldError {
  field: string;
  message: string;
  element?: HTMLElement;
}

/**
 * 表单焦点管理Hook
 * 处理表单验证错误时的焦点管理
 */
export function useFormFocus({
  autoFocusOnError = true,
  errorSelector = '[data-error="true"], .error, .is-invalid',
  scrollToError = true
}: FormFocusOptions = {}) {
  const formRef = useRef<HTMLFormElement>(null);

  // 使用统一的管理器获取
  const { getManagers } = useManagersWithDebug('FormFocus');
  
  // 聚焦到第一个错误字段
  const focusFirstError = useCallback((errors?: Record<string, string> | FormFieldError[]) => {
    if (!autoFocusOnError || !formRef.current) return;
    
    try {
      const focusManager = getFocusManager();
      let targetElement: HTMLElement | null = null;
      
      // 如果提供了错误对象，尝试根据字段名找到对应元素
      if (errors) {
        const errorFields = Array.isArray(errors) 
          ? errors.map(e => e.field)
          : Object.keys(errors);
          
        for (const fieldName of errorFields) {
          // 尝试多种选择器模式
          const selectors = [
            `[name="${fieldName}"]`,
            `#${fieldName}`,
            `[data-field="${fieldName}"]`,
            `.field-${fieldName} input`,
            `.field-${fieldName} select`,
            `.field-${fieldName} textarea`
          ];
          
          for (const selector of selectors) {
            const element = formRef.current.querySelector(selector) as HTMLElement;
            if (element && element.offsetParent !== null) { // 确保元素可见
              targetElement = element;
              break;
            }
          }
          
          if (targetElement) break;
        }
      }
      
      // 如果没有找到特定字段，查找第一个有错误标记的元素
      if (!targetElement) {
        targetElement = formRef.current.querySelector(errorSelector) as HTMLElement;
      }
      
      // 如果还是没找到，查找第一个可聚焦的表单元素
      if (!targetElement) {
        const focusableElements = focusManager.getFocusableElements(formRef.current);
        targetElement = focusableElements[0];
      }
      
      if (targetElement) {
        focusManager.setFocus(targetElement);
        
        if (scrollToError) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
        
        // 如果是输入框，选中内容以便用户重新输入
        if (targetElement instanceof HTMLInputElement && 
            (targetElement.type === 'text' || targetElement.type === 'email' || 
             targetElement.type === 'password' || targetElement.type === 'search')) {
          targetElement.select();
        }
      }
    } catch (error) {
      console.warn('表单错误焦点设置失败:', error);
    }
  }, [autoFocusOnError, errorSelector, scrollToError]);
  
  // 聚焦到特定字段
  const focusField = useCallback((fieldName: string) => {
    if (!formRef.current) return;
    
    try {
      const focusManager = getFocusManager();
      
      const selectors = [
        `[name="${fieldName}"]`,
        `#${fieldName}`,
        `[data-field="${fieldName}"]`
      ];
      
      for (const selector of selectors) {
        const element = formRef.current.querySelector(selector) as HTMLElement;
        if (element) {
          focusManager.setFocus(element);
          break;
        }
      }
    } catch (error) {
      console.warn('字段焦点设置失败:', error);
    }
  }, []);
  
  // 聚焦到表单的第一个字段
  const focusFirstField = useCallback(() => {
    if (!formRef.current) return;

    try {
      const managers = getManagers();
      if (!managers) return;

      const { focusManager } = managers;
      const focusableElements = focusManager.getFocusableElements(formRef.current);

      if (focusableElements.length > 0) {
        focusManager.setFocus(focusableElements[0]);
      }
    } catch (error) {
      console.warn('表单首字段焦点设置失败:', error);
    }
  }, [getManagers]);
  
  // 验证表单并处理焦点
  const validateAndFocus = useCallback((
    validationFn: () => Record<string, string> | FormFieldError[] | boolean,
    onValid?: () => void,
    onInvalid?: (errors: Record<string, string> | FormFieldError[]) => void
  ) => {
    const result = validationFn();
    
    if (result === true || (typeof result === 'object' && Object.keys(result).length === 0)) {
      // 验证通过
      onValid?.();
      return true;
    } else {
      // 验证失败
      const errors = result as Record<string, string> | FormFieldError[];
      focusFirstError(errors);
      onInvalid?.(errors);
      return false;
    }
  }, [focusFirstError]);
  
  return {
    formRef,
    focusFirstError,
    focusField,
    focusFirstField,
    validateAndFocus
  };
}

// JSX组件已移动到单独的文件中
// 这是一个纯TypeScript Hook文件，不包含JSX代码
