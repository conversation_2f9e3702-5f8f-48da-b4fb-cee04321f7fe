import React, { useEffect, useRef, useState } from 'react';

// 扩展Window接口以支持Electron API
declare global {
  interface Window {
    electronAPI?: {
      closeApp: () => void;
      openExternal?: (url: string) => void;
    };
  }
}

interface AnnouncementDialogProps {
  isOpen: boolean;
  announcement: string;
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
  onClose?: () => void;
  // 激活相关props
  companyName: string;
  isActivationLoading: boolean;
  isQueryingCompany: boolean;
  isActivating: boolean;
  activationError: string | null;
  onCompanyNameChange: (name: string) => void;
  onActivate: () => void;
  onClearActivationError: () => void;
}

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  url?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * 装饰性背景SVG组件
 */
const DecorativeSVG: React.FC<{ color: string; className?: string }> = ({ color, className = "" }) => (
  <div className={`absolute top-0 right-0 w-16 h-16 opacity-10 pointer-events-none ${className}`}>
    <svg viewBox="0 0 100 100" className={`w-full h-full ${color}`}>
      <circle cx="50" cy="50" r="25" fill="none" stroke="currentColor" strokeWidth="3" />
      <circle cx="50" cy="50" r="12" fill="currentColor" opacity="0.4" />
    </svg>
  </div>
);

/**
 * 自定义确认对话框组件
 */
const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title,
  message,
  url,
  onConfirm,
  onCancel
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60] p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-auto relative overflow-hidden">
        {/* 标题栏 */}
        <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <h3 className="text-lg font-bold text-gray-800">{title}</h3>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          <p className="text-gray-700 text-base leading-relaxed mb-4">{message}</p>

          {url && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4">
              <p className="text-sm text-gray-600 mb-2">链接地址：</p>
              <p className="text-blue-600 text-sm font-mono break-all bg-white p-2 rounded border">
                {url}
              </p>
            </div>
          )}
        </div>

        {/* 按钮区域 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50 flex space-x-3 justify-end">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 font-medium text-sm transition-colors duration-200"
          >
            取消
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 font-medium text-sm transition-all duration-200 shadow-sm"
          >
            打开链接
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * 执行实际的链接打开操作
 */
const performLinkOpen = (url: string) => {

  try {
    // 方法1: 使用a标签模拟点击（最安全的方法）
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    link.style.display = 'none';

    document.body.appendChild(link);

    // 尝试点击链接
    const clickEvent = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true
    });

    const success = link.dispatchEvent(clickEvent);
    document.body.removeChild(link);

    if (success) {
      console.log('链接打开成功 - 方法1');
      return;
    }
  } catch (error) {
    console.log('方法1失败:', error);
  }

  try {
    // 方法2: 使用window.open但添加更多安全参数
    const newWindow = window.open(
      url,
      '_blank',
      'noopener=yes,noreferrer=yes,toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes'
    );

    if (newWindow) {
      console.log('链接打开成功 - 方法2');
      // 立即释放引用以避免内存问题
      newWindow.opener = null;
      return;
    }
  } catch (error) {
    console.log('方法2失败:', error);
  }

  try {
    // 方法3: 在当前窗口打开（作为最后的尝试）
    // 注意：这里暂时使用alert，实际应用中可以扩展为另一个确认对话框
    if (window.confirm(`无法在新窗口打开链接，是否在当前窗口打开？\n注意：这将离开当前应用\n\n${url}`)) {
      window.location.href = url;
      return;
    }
  } catch (error) {
    console.log('方法3失败:', error);
  }

  // 所有方法都失败时，降级到剪贴板方案
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(url).then(() => {
        alert(`无法直接打开链接，已复制到剪贴板：\n${url}\n\n请粘贴到浏览器地址栏中打开`);
      }).catch(() => {
        alert(`请手动复制以下链接到浏览器中打开：\n\n${url}`);
      });
    } else {
      alert(`请手动复制以下链接到浏览器中打开：\n\n${url}`);
    }
  } catch (error) {
    alert(`请手动复制以下链接到浏览器中打开：\n\n${url}`);
  }
};

/**
 * 解析文本中的URL并转换为可点击的链接
 */
const parseTextWithLinks = (text: string, onLinkClick: (url: string) => void): React.ReactNode[] => {
  // URL正则表达式，匹配http://、https://开头的链接
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const parts = text.split(urlRegex);

  return parts.map((part, index) => {
    if (urlRegex.test(part)) {
      // 这是一个URL，创建可点击的链接
      return (
        <button
          key={index}
          onClick={() => {
            // 调用传入的链接点击处理函数
            onLinkClick(part);
          }}
          className="text-blue-600 hover:text-blue-800 underline hover:no-underline transition-colors duration-200 cursor-pointer bg-transparent border-none p-0 font-medium"
          title={`打开链接: ${part}`}
        >
          {part}
        </button>
      );
    } else {
      // 这是普通文本
      return <span key={index}>{part}</span>;
    }
  });
};

/**
 * 公告窗口组件
 * UI层：只负责渲染界面，不包含业务逻辑
 */
const AnnouncementDialog: React.FC<AnnouncementDialogProps> = ({
  isOpen,
  onClose,
  announcement,
  isLoading,
  error,
  onRetry,
  // 激活相关props
  companyName,
  isActivationLoading,
  isQueryingCompany,
  isActivating,
  activationError,
  onCompanyNameChange,
  onActivate,
  onClearActivationError
}) => {
  // 创建输入框的引用
  const companyNameInputRef = useRef<HTMLInputElement>(null);

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    url: string;
    onConfirm: () => void;
  }>({
    isOpen: false,
    url: '',
    onConfirm: () => {}
  });

  // 显示确认对话框的函数
  const showConfirmDialog = (url: string, onConfirm: () => void) => {
    setConfirmDialog({
      isOpen: true,
      url,
      onConfirm
    });
  };

  // 关闭确认对话框
  const closeConfirmDialog = () => {
    setConfirmDialog({
      isOpen: false,
      url: '',
      onConfirm: () => {}
    });
  };

  // 确认打开链接
  const handleConfirmLink = () => {
    confirmDialog.onConfirm();
    closeConfirmDialog();
  };

  // 处理链接点击
  const handleLinkClick = (url: string) => {
    showConfirmDialog(url, () => {
      performLinkOpen(url);
    });
  };

  // 当对话框打开时，自动聚焦到公司名称输入框
  useEffect(() => {
    if (isOpen && companyNameInputRef.current) {
      // 使用 setTimeout 确保 DOM 已经渲染完成
      setTimeout(() => {
        companyNameInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onActivate();
  };

  const handleCompanyNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCompanyNameChange(e.target.value);
    if (activationError) {
      onClearActivationError();
    }
  };

  const isProcessing = isActivationLoading || isQueryingCompany || isActivating;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
      <div className="bg-white border border-gray-300 rounded-lg shadow-lg w-[600px] h-[600px] relative overflow-hidden">

        <div className="relative flex flex-col h-full">

          {/* 内容区域 */}
          <div className="flex-1 flex flex-col px-4 pt-4 pb-3 space-y-3 overflow-hidden">

            {/* 系统公告部分 */}
            <div className="flex-1 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-200 rounded-lg shadow-sm relative overflow-hidden flex flex-col">
              {/* 装饰性背景图案 */}
              <div className="absolute inset-0 opacity-5 pointer-events-none">
                <div className="absolute top-4 left-4 w-16 h-16 rounded-full bg-blue-400"></div>
                <div className="absolute top-12 right-8 w-8 h-8 rounded-full bg-indigo-400"></div>
                <div className="absolute bottom-8 left-12 w-12 h-12 rounded-full bg-purple-400"></div>
                <div className="absolute bottom-4 right-4 w-6 h-6 rounded-full bg-blue-500"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full bg-gradient-to-br from-blue-300 to-indigo-300"></div>
              </div>

              {/* 公告标题 */}
              <div className="p-4 border-b border-blue-200 bg-gradient-to-r from-blue-100 to-indigo-100 flex-shrink-0 relative z-10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-sm">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-800">产品激活</h2>
                      <p className="text-sm text-gray-600 mt-0.5">欢迎使用军密安信息设备台账管理系统</p>
                    </div>
                  </div>
                  {/* 关闭应用程序按钮 */}
                  <button
                    onClick={() => {
                      // 关闭整个应用程序
                      if (window.electronAPI?.closeApp) {
                        window.electronAPI.closeApp();
                      } else {
                        // 备用方案：直接关闭窗口
                        window.close();
                      }
                    }}
                    className="w-9 h-9 bg-transparent hover:bg-red-100 rounded-lg flex items-center justify-center transition-all duration-200 group"
                    title="退出应用程序"
                  >
                    <svg className="w-5 h-5 text-red-600 group-hover:text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* 公告内容 */}
              <div className="p-6 flex-1 overflow-y-auto flex items-center justify-center min-h-[180px] relative z-10">
                {isLoading && (
                  <div className="flex flex-col items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-3 border-blue-500 border-t-transparent mb-4"></div>
                    <span className="text-gray-600 font-medium text-base">正在获取公告...</span>
                  </div>
                )}

                {error && (
                  <div className="w-full max-w-md mx-auto">
                    {/* 错误信息框 */}
                    <div className="bg-white/90 backdrop-blur-sm border-2 border-red-200/50 rounded-xl p-6 shadow-lg relative overflow-hidden">
                      {/* 装饰性背景元素 */}
                      <DecorativeSVG color="text-red-500" />

                      {/* 错误内容 */}
                      <div className="relative z-10 text-center">
                        <div className="mb-6">
                          <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
                            <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <p className="text-red-600 font-semibold mb-2 text-base">获取公告失败</p>
                          <p className="text-sm text-gray-600 mb-6">{error}</p>
                        </div>
                        <button
                          onClick={onRetry}
                          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 font-medium text-sm transition-colors duration-200 shadow-sm"
                        >
                          重新获取
                        </button>
                      </div>

                      {/* 底部装饰线 */}
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-red-400 via-rose-400 to-red-400"></div>
                    </div>
                  </div>
                )}

                {!isLoading && !error && announcement && (
                  <div className="w-full max-w-md mx-auto">
                    {/* 公告内容框 */}
                    <div className="bg-white/90 backdrop-blur-sm border-2 border-white/50 rounded-xl p-6 shadow-lg relative overflow-hidden">
                      {/* 装饰性背景元素 */}
                      <DecorativeSVG color="text-blue-600" />

                      {/* 公告文字内容 */}
                      <div className="relative z-10">
                        <div className="text-gray-800 text-base leading-relaxed whitespace-pre-wrap text-center font-medium">
                          {parseTextWithLinks(announcement, handleLinkClick)}
                        </div>
                      </div>

                      {/* 底部装饰线 */}
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500"></div>
                    </div>
                  </div>
                )}

                {!isLoading && !error && !announcement && (
                  <div className="w-full max-w-md mx-auto">
                    {/* 暂无公告信息框 */}
                    <div className="bg-white/90 backdrop-blur-sm border-2 border-gray-200/50 rounded-xl p-6 shadow-lg relative overflow-hidden">
                      {/* 装饰性背景元素 */}
                      <DecorativeSVG color="text-gray-400" />

                      {/* 内容 */}
                      <div className="relative z-10 text-center text-gray-500">
                        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <p className="font-medium text-base text-gray-600">暂无公告信息</p>
                        <p className="text-sm text-gray-500 mt-2">系统将在有重要通知时显示公告</p>
                      </div>

                      {/* 底部装饰线 */}
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-gray-300 via-slate-300 to-gray-300"></div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 系统激活部分 */}
            <div className="flex-shrink-0 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg shadow-sm relative overflow-hidden">
              {/* 装饰性背景 */}
              <div className="absolute top-0 right-0 w-24 h-24 opacity-10 pointer-events-none">
                <svg viewBox="0 0 100 100" className="w-full h-full text-green-500">
                  <circle cx="50" cy="50" r="40" fill="none" stroke="currentColor" strokeWidth="2" />
                  <circle cx="50" cy="50" r="25" fill="none" stroke="currentColor" strokeWidth="1" />
                  <circle cx="50" cy="50" r="10" fill="currentColor" />
                </svg>
              </div>

              {/* 激活表单内容 */}
              <div className="p-4 relative z-10">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="companyName" className="block text-base font-bold text-gray-700 mb-2 flex items-center">
                      <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                      </svg>
                      公司名称
                    </label>
                    <div className="relative">
                      <input
                        ref={companyNameInputRef}
                        type="text"
                        id="companyName"
                        value={companyName}
                        onChange={handleCompanyNameChange}
                        disabled={isProcessing}
                        placeholder="请输入您的公司全称"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white disabled:opacity-50 disabled:cursor-not-allowed text-gray-800 placeholder-gray-400 text-sm shadow-sm transition-all duration-200"
                        required
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {activationError && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-2">
                      <svg className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-xs text-red-700 font-medium flex-1">{activationError}</p>
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={isProcessing || !companyName.trim()}
                    className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 font-medium disabled:opacity-50 disabled:cursor-not-allowed text-sm shadow-sm transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100"
                  >
                    {isProcessing ? (
                      <span className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        <span>
                          {isQueryingCompany ? '正在验证公司信息...' :
                           isActivating ? '正在激活系统...' :
                           '处理中...'}
                        </span>
                      </span>
                    ) : (
                      <span className="flex items-center justify-center space-x-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span>激活系统</span>
                      </span>
                    )}
                  </button>

                  {/* 激活提示 */}
                  <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <svg className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="text-xs text-green-700 leading-relaxed">
                        <p className="font-medium mb-1">激活说明：</p>
                        <p>• 请确保输入的公司名称准确无误</p>
                        <p>• 激活后系统将为您的公司进行个性化配置</p>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* 确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title="打开外部链接"
        message="您即将打开一个外部链接，是否继续？"
        url={confirmDialog.url}
        onConfirm={handleConfirmLink}
        onCancel={closeConfirmDialog}
      />
    </div>
  );
};

export default AnnouncementDialog;
