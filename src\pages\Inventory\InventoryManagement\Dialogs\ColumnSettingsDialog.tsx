import React, { useState, useEffect, useCallback } from 'react';
import { X, Plus, Trash2, Check, AlertCircle, Settings } from 'lucide-react';
import useColumnSettings from '../../../../hooks/Inventory/useColumnSettings';
import CustomSelect from '../../../../components/CustomSelect';
import HierarchicalSelect from '../../../../components/HierarchicalSelect';
import ConfirmDialog from '../../../../components/ConfirmDialog';
import CustomAlert from '../../../../components/CustomAlert';
import DialogBase from '../../../../components/ui/DialogBase';
import { ExtFieldEditMode } from '../../../../types/inventory';
import { ValidatedInput } from '../../../../components/ui/FormComponents';
import { getByteLength } from '../../../../utils/fieldValidation';

// 对话框属性类型
interface ColumnSettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  // 新增：当前选中的分类信息
  currentSelectedCategory?: { parentCategory?: string; subCategory?: string };
}

/**
 * 列设置对话框组件
 * 用于添加和删除扩展字段列
 */
const ColumnSettingsDialog: React.FC<ColumnSettingsDialogProps> = ({
  isOpen,
  onClose,
  onSuccess,
  currentSelectedCategory
}) => {
  // 确认对话框状态
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogData, setConfirmDialogData] = useState<{
    subCategoryName: string;
    fieldName: string;
    fromTable: boolean;
  }>({ subCategoryName: '', fieldName: '', fromTable: false });

  // 扩展字段编辑模式状态 - 改为使用布尔值表示
  const [isEditable, setIsEditable] = useState<boolean>(true); // 可编辑
  const [isSelectOnly, setIsSelectOnly] = useState<boolean>(false); // 不可编辑（下拉选择）

  // 下拉选项状态（用于不可编辑模式）
  const [selectOptions, setSelectOptions] = useState<Array<{ code: string; value: string }>>([]);
  const [optionValue, setOptionValue] = useState<string>('');

  // 显示选项管理面板
  const [showOptionsPanel, setShowOptionsPanel] = useState<boolean>(false);

  // 当前选中的标签页：添加(add)、修改(edit)、删除(delete)
  const [activeTab, setActiveTab] = useState<'add' | 'edit' | 'delete'>('add');

  // 自定义提示框状态
  const [alertState, setAlertState] = useState<{
    isOpen: boolean;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
  }>({
    isOpen: false,
    message: '',
    type: 'info'
  });

  // 新字段名称的状态
  const [newFieldName, setNewFieldName] = useState<string>('');

  // 使用列设置Hook
  const {
    isLoading,
    error,
    success,
    subCategories,
    // 移除对 allSubCategories 的引用
    hierarchicalCategories, // 添加层级分类
    extFields,
    selectedSubCategory,
    setSelectedSubCategory,
    fieldName,
    setFieldName,
    isRequired,
    setIsRequired,
    // 删除扩展字段相关
    deleteSubCategory,
    setDeleteSubCategory,
    deleteFieldName,
    setDeleteFieldName,
    deleteExtFields,
    loadDeleteExtFields,
    // 修改扩展字段相关
    editSubCategory,
    setEditSubCategory,
    editFieldName,
    setEditFieldName,
    editExtFields,
    loadEditExtFields,
    editFieldData,
    setEditFieldData,
    // 方法
    addExtendedField,
    updateExtendedField,
    deleteExtendedField,
    loadSubCategories,
    loadExtFields,
    clearError,
    clearSuccess
  } = useColumnSettings();

  // 初始化加载
  useEffect(() => {
    if (isOpen) {
      console.log('列设置对话框打开，加载二级分类数据');
      loadSubCategories();

      // 清空选择的二级分类和字段名称
      setSelectedSubCategory('');
      setDeleteSubCategory('');
      setEditSubCategory('');
      setFieldName('');
      setDeleteFieldName('');
      setEditFieldName('');

      // 默认选中添加标签页
      setActiveTab('add');
    }
  }, [isOpen, loadSubCategories, setSelectedSubCategory, setDeleteSubCategory, setEditSubCategory, setFieldName, setDeleteFieldName, setEditFieldName]);

  // 当二级分类数据加载完成后，根据当前选中的分类信息设置初始值
  useEffect(() => {
    if (isOpen && hierarchicalCategories.length > 0) {
      // 检查当前选中的分类信息是否有效
      if (currentSelectedCategory && currentSelectedCategory.subCategory) {
        // 如果当前选中的是二级分类，则自动选择该二级分类
        console.log('当前选中的是二级分类，自动选择:', currentSelectedCategory.subCategory);

        // 查找二级分类的ID
        const subCategoryId = hierarchicalCategories.flatMap(parent =>
          parent.children || []
        ).find(child =>
          child.label === currentSelectedCategory.subCategory
        )?.value || '';

        if (subCategoryId) {
          // 设置添加、修改和删除扩展字段的二级分类
          setSelectedSubCategory(subCategoryId);
          setEditSubCategory(subCategoryId);
          setDeleteSubCategory(subCategoryId);
          console.log('自动选择二级分类:', subCategoryId);
        }
      } else {
        // 如果当前没有选中二级分类，清空选择
        console.log('当前没有选中二级分类，清空选择');
        setSelectedSubCategory('');
        setEditSubCategory('');
        setDeleteSubCategory('');
      }
    }
  }, [isOpen, hierarchicalCategories, currentSelectedCategory, setSelectedSubCategory, setEditSubCategory, setDeleteSubCategory]);

  // 当选择二级分类时加载该分类的扩展字段
  useEffect(() => {
    if (selectedSubCategory) {
      loadExtFields(selectedSubCategory);
    }
  }, [selectedSubCategory, loadExtFields]);

  // 当选择删除二级分类时加载该分类的扩展字段
  useEffect(() => {
    if (deleteSubCategory) {
      loadDeleteExtFields(deleteSubCategory);
    }
  }, [deleteSubCategory, loadDeleteExtFields]);

  // 当选择修改二级分类时加载该分类的扩展字段
  useEffect(() => {
    if (editSubCategory) {
      loadEditExtFields(editSubCategory);
    }
  }, [editSubCategory, loadEditExtFields]);

  // 当选择修改字段名称时，加载该字段的详细信息
  useEffect(() => {
    if (editSubCategory && editFieldName) {
      // 查找选中的字段
      const selectedField = editExtFields.find(field => field.key === editFieldName);
      if (selectedField) {
        // 设置字段属性
        const isEditable = selectedField.editMode === ExtFieldEditMode.Editable;
        const isSelectOnly = selectedField.editMode === ExtFieldEditMode.SelectOnly;
        const isRequired = selectedField.required;
        const options = selectedField.options || [];

        // 更新编辑字段数据
        setEditFieldData({
          isEditable,
          isSelectOnly,
          isRequired,
          options
        });

        // 如果是不可编辑模式，显示选项管理面板
        if (isSelectOnly) {
          setShowOptionsPanel(true);
        } else {
          setShowOptionsPanel(false);
        }
      }
    }
  }, [editSubCategory, editFieldName, editExtFields, setEditFieldData]);

  // 处理标签页切换
  useEffect(() => {
    // 根据当前标签页设置选项管理面板的显示状态
    if (activeTab === 'add') {
      // 添加标签页下，根据当前的编辑模式决定是否显示选项管理面板
      setShowOptionsPanel(isSelectOnly);
    } else if (activeTab === 'edit') {
      // 修改标签页下，根据选中字段的编辑模式决定是否显示选项管理面板
      setShowOptionsPanel(editFieldData.isSelectOnly && editFieldName !== '');
    } else {
      // 删除标签页下，不显示选项管理面板
      setShowOptionsPanel(false);
    }
  }, [activeTab, isSelectOnly, editFieldData.isSelectOnly, editFieldName]);

  // 显示自定义提示框
  const showAlert = useCallback((message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
    setAlertState({
      isOpen: true,
      message,
      type
    });
  }, []);

  // 处理添加扩展字段
  const handleAddExtField = useCallback(async () => {
    if (selectedSubCategory && fieldName) {
      // 确定编辑模式
      let editMode = ExtFieldEditMode.Editable; // 默认为可编辑

      if (isSelectOnly) {
        editMode = ExtFieldEditMode.SelectOnly; // 不可编辑（下拉选择）
      }

      // 准备选项数据（仅在不可编辑模式下使用）
      const options = isSelectOnly ? selectOptions : undefined;

      // 如果是不可编辑模式但没有添加选项，提示用户
      if (isSelectOnly && (!options || options.length === 0)) {
        showAlert('请至少添加一个下拉选项', 'warning');
        return;
      }

      const success = await addExtendedField(
        selectedSubCategory,
        fieldName,
        isRequired, // 必填属性保持不变
        editMode,
        options
      );

      if (success) {
        setFieldName(''); // 清空字段名称
        setIsEditable(true); // 重置为可编辑
        setIsSelectOnly(false); // 取消不可编辑
        setSelectOptions([]); // 清空选项
        setShowOptionsPanel(false); // 隐藏选项面板

        // 调用成功回调
        if (onSuccess) {
          onSuccess();
        }
      }
    }
  }, [
    selectedSubCategory,
    fieldName,
    isRequired,
    isEditable,
    isSelectOnly,
    selectOptions,
    addExtendedField,
    setFieldName,
    onSuccess
  ]);



  // 处理修改扩展字段
  const handleUpdateExtField = useCallback(async () => {
    if (editSubCategory && editFieldName) {
      // 确定编辑模式
      let editMode = ExtFieldEditMode.Editable; // 默认为可编辑

      if (editFieldData.isSelectOnly) {
        editMode = ExtFieldEditMode.SelectOnly; // 不可编辑（下拉选择）
      }

      // 准备选项数据（仅在不可编辑模式下使用）
      const options = editFieldData.isSelectOnly ? editFieldData.options : undefined;

      // 如果是不可编辑模式但没有添加选项，提示用户
      if (editFieldData.isSelectOnly && (!options || options.length === 0)) {
        showAlert('请至少添加一个下拉选项', 'warning');
        return;
      }

      // 获取新字段名（如果已填写）- 完全由用户自行输入
      const newFieldNameValue = newFieldName.trim() !== "" ? newFieldName.trim() : undefined;

      const success = await updateExtendedField(
        editSubCategory,
        editFieldName,
        editFieldData.isRequired,
        editMode,
        options,
        newFieldNameValue
      );

      if (success) {
        // 重置状态
        setEditFieldName('');
        setEditFieldData({
          isEditable: true,
          isSelectOnly: false,
          isRequired: false,
          options: []
        });
        setShowOptionsPanel(false);

        // 重置新字段名
        setNewFieldName('');

        // 调用成功回调
        if (onSuccess) {
          onSuccess();
        }
      }
    }
  }, [
    editSubCategory,
    editFieldName,
    editFieldData,
    updateExtendedField,
    setEditFieldName,
    setEditFieldData,
    onSuccess,
    showAlert
  ]);

  // 处理删除扩展字段（从下拉框选择删除）
  const handleDeleteExtFieldFromSelect = useCallback(() => {
    if (deleteSubCategory && deleteFieldName) {
      // 设置确认对话框数据
      setConfirmDialogData({
        subCategoryName: deleteSubCategory,
        fieldName: deleteFieldName,
        fromTable: false
      });
      // 显示确认对话框
      setShowConfirmDialog(true);
    }
  }, [deleteSubCategory, deleteFieldName]);

  // 确认删除扩展字段
  const confirmDeleteExtField = useCallback(async () => {
    const { subCategoryName, fieldName, fromTable } = confirmDialogData;

    try {
      // 执行删除操作
      await deleteExtendedField(subCategoryName, fieldName);

      // 如果是从下拉框删除，清空字段名称
      if (!fromTable) {
        setDeleteFieldName('');
        // 移除手动刷新代码，依赖 deleteExtendedField 中的 refreshExtFields
      }

      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('删除扩展字段失败:', error);
    } finally {
      // 关闭确认对话框
      setShowConfirmDialog(false);
    }
  }, [confirmDialogData, deleteExtendedField, setDeleteFieldName, onSuccess]);

  // 处理添加选项
  const handleAddOption = useCallback(() => {
    if (!optionValue.trim()) {
      return;
    }

    // 检查选项是否已存在
    const exists = selectOptions.some(option =>
      option.value.toLowerCase() === optionValue.trim().toLowerCase()
    );

    if (exists) {
      showAlert('该选项已存在', 'warning');
      return;
    }

    // 添加新选项
    const newOption = {
      code: optionValue.trim(),
      value: optionValue.trim()
    };

    setSelectOptions(prev => [...prev, newOption]);
    setOptionValue(''); // 清空输入
  }, [optionValue, selectOptions]);

  // 处理删除选项
  const handleDeleteOption = useCallback((index: number) => {
    setSelectOptions(prev => prev.filter((_, i) => i !== index));
  }, []);

  // 处理添加编辑字段选项
  const handleAddEditFieldOption = useCallback(() => {
    if (!optionValue.trim()) {
      return;
    }

    // 检查选项是否已存在
    const exists = editFieldData.options.some(option =>
      option.value.toLowerCase() === optionValue.trim().toLowerCase()
    );

    if (exists) {
      showAlert('该选项已存在', 'warning');
      return;
    }

    // 添加新选项
    const newOption = {
      code: optionValue.trim(),
      value: optionValue.trim()
    };

    setEditFieldData(prev => ({
      ...prev,
      options: [...prev.options, newOption]
    }));
    setOptionValue(''); // 清空输入
  }, [optionValue, editFieldData.options, setEditFieldData]);

  // 处理删除编辑字段选项
  const handleDeleteEditFieldOption = useCallback((index: number) => {
    setEditFieldData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  }, [setEditFieldData]);

  // 处理可编辑模式变更
  const handleEditableChange = useCallback((checked: boolean) => {
    if (checked) {
      // 如果勾选可编辑，取消勾选不可编辑
      setIsEditable(true);
      setIsSelectOnly(false);
      setShowOptionsPanel(false);
      // 清空选项
      setSelectOptions([]);
    } else {
      // 如果取消勾选可编辑，默认勾选不可编辑
      setIsEditable(false);
      setIsSelectOnly(true);
      setShowOptionsPanel(true);
    }
  }, []);

  // 处理不可编辑模式变更
  const handleSelectOnlyChange = useCallback((checked: boolean) => {
    if (checked) {
      // 如果勾选不可编辑，取消勾选可编辑
      setIsSelectOnly(true);
      setIsEditable(false);
      setShowOptionsPanel(true);
    } else {
      // 如果取消勾选不可编辑，默认勾选可编辑
      setIsSelectOnly(false);
      setIsEditable(true);
      setShowOptionsPanel(false);
      // 清空选项
      setSelectOptions([]);
    }
  }, []);

  // 处理编辑字段可编辑模式变更
  const handleEditFieldEditableChange = useCallback((checked: boolean) => {
    if (checked) {
      // 如果勾选可编辑，取消勾选不可编辑
      setEditFieldData(prev => ({
        ...prev,
        isEditable: true,
        isSelectOnly: false
      }));
      setShowOptionsPanel(false);
    } else {
      // 如果取消勾选可编辑，默认勾选不可编辑
      setEditFieldData(prev => ({
        ...prev,
        isEditable: false,
        isSelectOnly: true
      }));
      setShowOptionsPanel(true);
    }
  }, [setEditFieldData]);

  // 处理编辑字段不可编辑模式变更
  const handleEditFieldSelectOnlyChange = useCallback((checked: boolean) => {
    if (checked) {
      // 如果勾选不可编辑，取消勾选可编辑
      setEditFieldData(prev => ({
        ...prev,
        isEditable: false,
        isSelectOnly: true
      }));
      setShowOptionsPanel(true);
    } else {
      // 如果取消勾选不可编辑，默认勾选可编辑
      setEditFieldData(prev => ({
        ...prev,
        isEditable: true,
        isSelectOnly: false,
        options: [] // 清空选项
      }));
      setShowOptionsPanel(false);
    }
  }, [setEditFieldData]);

  // 处理编辑字段必填状态变更
  const handleEditFieldRequiredChange = useCallback((checked: boolean) => {
    setEditFieldData(prev => ({
      ...prev,
      isRequired: checked
    }));
  }, [setEditFieldData]);

  // 创建一个包装的关闭处理函数，在关闭对话框时清除成功和错误提示
  const handleClose = useCallback(() => {
    // 清除成功和错误提示，确保下次打开对话框时不会显示之前的提示
    clearSuccess();
    clearError();
    // 重置状态
    setIsEditable(true);
    setIsSelectOnly(false);
    setSelectOptions([]);
    setShowOptionsPanel(false);
    // 重置修改扩展字段相关状态
    setEditSubCategory('');
    setEditFieldName('');
    setEditFieldData({
      isEditable: true,
      isSelectOnly: false,
      isRequired: false,
      options: []
    });
    // 重置标签页状态
    setActiveTab('add');
    // 调用父组件的onClose函数
    onClose();
  }, [onClose, clearSuccess, clearError]);

  // ESC键处理已由统一的焦点管理系统处理，无需重复实现

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleClose}
      width="100%"
      maxWidth="42rem"
      maxHeight="95vh"
      animation="fade"
      closeOnOverlayClick={false}
    >
      <div className="flex flex-col h-full max-h-[95vh]">
        {/* 对话框标题 - 固定高度 */}
        <div className="flex-shrink-0 flex justify-between items-center px-3 sm:px-4 py-2.5 border-b">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800">列设置</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none flex-shrink-0"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 对话框内容 - 可滚动区域 */}
        <div className="flex-1 overflow-y-auto min-h-0 px-3 sm:px-4 py-3 sm:py-4">{/* 响应式内边距 */}

          {/* 错误提示 */}
          {error && (
            <div className="mb-3 sm:mb-4 p-2 sm:p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
              <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-red-700">{error}</p>
              </div>
              <button
                onClick={clearError}
                className="ml-2 text-xs text-red-600 hover:text-red-800 px-1 sm:px-2 py-1 border border-red-200 rounded hover:bg-red-100"
              >
                关闭
              </button>
            </div>
          )}

          {/* 成功提示 */}
          {success && (
            <div className="mb-3 sm:mb-4 p-2 sm:p-3 bg-green-50 border border-green-200 rounded-md flex items-start">
              <Check className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-xs sm:text-sm text-green-700">{success}</p>
                <button
                  onClick={clearSuccess}
                  className="text-xs text-green-600 hover:text-green-800 mt-1"
                >
                  关闭
                </button>
              </div>
            </div>
          )}

          {/* 标签页切换 - 响应式优化 */}
          <div className="flex border-b mb-3 sm:mb-4">
            <button
              type="button"
              onClick={() => setActiveTab('add')}
              className={`px-2 sm:px-4 py-2 font-medium text-xs sm:text-sm ${
                activeTab === 'add'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              添加扩展字段
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('edit')}
              className={`px-2 sm:px-4 py-2 font-medium text-xs sm:text-sm ${
                activeTab === 'edit'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              修改扩展字段
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('delete')}
              className={`px-2 sm:px-4 py-2 font-medium text-xs sm:text-sm ${
                activeTab === 'delete'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              删除扩展字段
            </button>
          </div>

          {/* 添加扩展字段表单 */}
          {activeTab === 'add' && (
          <div className="mb-3 sm:mb-4 border rounded-md p-3 sm:p-4 bg-gray-50">
            <h3 className="text-sm sm:text-base font-medium text-gray-800 mb-2 sm:mb-3 border-b pb-2">添加扩展字段列</h3>
            <div>
              <div className="grid grid-cols-12 gap-2 sm:gap-3">
                {/* 二级分类选择 - 使用层级下拉框 */}
                <div className="col-span-12 sm:col-span-4">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                    选择二级分类 <span className="text-red-500">*</span>
                  </label>
                  <HierarchicalSelect
                    options={hierarchicalCategories}
                    value={selectedSubCategory}
                    onChange={setSelectedSubCategory}
                    placeholder="请选择二级分类"
                    required
                  />
                </div>

                {/* 字段名称输入 - 自定义样式以匹配HierarchicalSelect */}
                <div className="col-span-12 sm:col-span-8">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                    字段名称 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={fieldName}
                      onChange={(e) => setFieldName(e.target.value)}
                      placeholder="请输入字段名称"
                      className="w-full h-[38px] px-3 pr-16 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs font-mono text-gray-400">
                      {getByteLength(fieldName)}/108
                    </div>
                  </div>
                </div>

                {/* 编辑模式选择 - 改为勾选框 */}
                <div className="col-span-12 mt-1 sm:mt-2">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                    字段属性
                  </label>
                  <div className="flex flex-wrap gap-3 sm:gap-4">
                    {/* 可编辑 */}
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isEditable}
                        onChange={(e) => handleEditableChange(e.target.checked)}
                        className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 rounded"
                        style={{ outline: 'none' }}
                        id="editable-checkbox"
                      />
                      <label htmlFor="editable-checkbox" className="ml-1 sm:ml-2 text-xs sm:text-sm text-gray-700 whitespace-nowrap">
                        可编辑
                      </label>
                    </div>

                    {/* 不可编辑（下拉选择） */}
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isSelectOnly}
                        onChange={(e) => handleSelectOnlyChange(e.target.checked)}
                        className="h-4 w-4 text-blue-600 rounded"
                        style={{ outline: 'none' }}
                        id="select-only-checkbox"
                      />
                      <label htmlFor="select-only-checkbox" className="ml-2 text-sm text-gray-700 whitespace-nowrap">
                        不可编辑（下拉选择）
                      </label>
                    </div>

                    {/* 必填字段 */}
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isRequired}
                        onChange={(e) => setIsRequired(e.target.checked)}
                        className="h-4 w-4 text-blue-600 rounded"
                        style={{ outline: 'none' }}
                        id="required-checkbox"
                      />
                      <label htmlFor="required-checkbox" className="ml-2 text-sm text-gray-700 whitespace-nowrap">
                        必填字段
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* 选项管理面板（仅在不可编辑模式下显示） */}
              {showOptionsPanel && (
                <div className="mt-4 border-t pt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">下拉选项管理</h4>
                  <div className="flex items-center mb-2">
                    <div className="w-2/5">
                      <ValidatedInput
                        label=""
                        value={optionValue}
                        onChange={setOptionValue}
                        placeholder="请输入选项值"
                        required={false}
                        maxByteLength={108}
                        showCounter={true}
                        className="mb-0"
                      />
                    </div>
                    <div className="ml-2">
                      <button
                        type="button"
                        onClick={handleAddOption}
                        disabled={!optionValue.trim()}
                        className={`flex items-center justify-center h-[38px] px-6 py-3 rounded-md text-base font-medium ${
                          !optionValue.trim()
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-green-600 text-white hover:bg-green-700'
                        }`}
                        style={{ outline: 'none', whiteSpace: 'nowrap' }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        添加选项
                      </button>
                    </div>
                  </div>

                  {/* 选项列表 */}
                  {selectOptions.length > 0 ? (
                    <div className="max-h-32 overflow-y-auto border rounded-md">
                      <table className="w-full text-sm">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="px-2 py-1 text-left">选项值</th>
                            <th className="px-2 py-1 w-16 text-center">操作</th>
                          </tr>
                        </thead>
                        <tbody>
                          {selectOptions.map((option, index) => (
                            <tr key={index} className="border-t">
                              <td className="px-2 py-1">{option.value}</td>
                              <td className="px-2 py-1 text-center">
                                <button
                                  type="button"
                                  onClick={() => handleDeleteOption(index)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 italic">
                      请添加至少一个选项
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          )}

          {/* 修改扩展字段表单 */}
          {activeTab === 'edit' && (
          <div className="mb-4 border rounded-md p-4 bg-gray-50">
            <h3 className="text-base font-medium text-gray-800 mb-3 border-b pb-2">修改扩展字段列</h3>
            <div>
              <div className="grid grid-cols-12 gap-3">
                {/* 二级分类选择 - 使用层级下拉框 */}
                <div className="col-span-12 sm:col-span-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择二级分类 <span className="text-red-500">*</span>
                  </label>
                  <HierarchicalSelect
                    options={hierarchicalCategories}
                    value={editSubCategory}
                    onChange={setEditSubCategory}
                    placeholder="请选择二级分类"
                    required
                  />
                </div>

                {/* 字段名称选择 */}
                <div className="col-span-12 sm:col-span-8">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择字段名称 <span className="text-red-500">*</span>
                  </label>
                  <CustomSelect
                    options={editExtFields.map(field => ({
                      id: field.key,
                      value: field.key,
                      label: field.title
                    }))}
                    value={editFieldName}
                    onChange={setEditFieldName}
                    placeholder="请选择字段名称"
                    required
                    disabled={!editSubCategory || editExtFields.length === 0}
                    direction="bottom" // 强制向下展开
                  />
                </div>

                {/* 新字段名称 */}
                {editFieldName && (
                  <div className="col-span-12 sm:col-span-6 mt-2">
                    <ValidatedInput
                      label="新字段名称"
                      value={newFieldName}
                      onChange={setNewFieldName}
                      placeholder="请输入新字段名称(留空则不修改)"
                      required={false}
                      maxByteLength={108}
                      showCounter={true}
                    />
                  </div>
                )}

                {/* 编辑模式选择 - 改为勾选框 */}
                {editFieldName && (
                  <div className="col-span-12 mt-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      字段属性
                    </label>
                    <div className="flex flex-wrap gap-4">
                      {/* 可编辑 */}
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editFieldData.isEditable}
                          onChange={(e) => handleEditFieldEditableChange(e.target.checked)}
                          className="h-4 w-4 text-blue-600 rounded"
                          style={{ outline: 'none' }}
                          id="edit-editable-checkbox"
                        />
                        <label htmlFor="edit-editable-checkbox" className="ml-2 text-sm text-gray-700 whitespace-nowrap">
                          可编辑
                        </label>
                      </div>

                      {/* 不可编辑（下拉选择） */}
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editFieldData.isSelectOnly}
                          onChange={(e) => handleEditFieldSelectOnlyChange(e.target.checked)}
                          className="h-4 w-4 text-blue-600 rounded"
                          style={{ outline: 'none' }}
                          id="edit-select-only-checkbox"
                        />
                        <label htmlFor="edit-select-only-checkbox" className="ml-2 text-sm text-gray-700 whitespace-nowrap">
                          不可编辑（下拉选择）
                        </label>
                      </div>

                      {/* 必填字段 */}
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={editFieldData.isRequired}
                          onChange={(e) => handleEditFieldRequiredChange(e.target.checked)}
                          className="h-4 w-4 text-blue-600 rounded"
                          style={{ outline: 'none' }}
                          id="edit-required-checkbox"
                        />
                        <label htmlFor="edit-required-checkbox" className="ml-2 text-sm text-gray-700 whitespace-nowrap">
                          必填字段
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 选项管理面板（仅在不可编辑模式下显示） */}
              {editFieldName && editFieldData.isSelectOnly && (
                <div className="mt-4 border-t pt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">下拉选项管理</h4>
                  <div className="flex items-center mb-2">
                    <div className="w-2/5">
                      <ValidatedInput
                        label=""
                        value={optionValue}
                        onChange={setOptionValue}
                        placeholder="请输入选项值"
                        required={false}
                        maxByteLength={108}
                        showCounter={true}
                        className="mb-0"
                      />
                    </div>
                    <div className="ml-2">
                      <button
                        type="button"
                        onClick={handleAddEditFieldOption}
                        disabled={!optionValue.trim()}
                        className={`flex items-center justify-center h-[38px] px-6 py-3 rounded-md text-base font-medium ${
                          !optionValue.trim()
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-green-600 text-white hover:bg-green-700'
                        }`}
                        style={{ outline: 'none', whiteSpace: 'nowrap' }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        添加选项
                      </button>
                    </div>
                  </div>

                  {/* 选项列表 */}
                  {editFieldData.options.length > 0 ? (
                    <div className="max-h-32 overflow-y-auto border rounded-md">
                      <table className="w-full text-sm">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="px-2 py-1 text-left">选项值</th>
                            <th className="px-2 py-1 w-16 text-center">操作</th>
                          </tr>
                        </thead>
                        <tbody>
                          {editFieldData.options.map((option, index) => (
                            <tr key={index} className="border-t">
                              <td className="px-2 py-1">{option.value}</td>
                              <td className="px-2 py-1 text-center">
                                <button
                                  type="button"
                                  onClick={() => handleDeleteEditFieldOption(index)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 italic">
                      请添加至少一个选项
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          )}

          {/* 删除扩展字段表单 */}
          {activeTab === 'delete' && (
          <div className="mb-4 border rounded-md p-4 bg-gray-50">
            <h3 className="text-base font-medium text-gray-800 mb-3 border-b pb-2">删除扩展字段列</h3>
            <div>
              <div className="grid grid-cols-12 gap-3">
                {/* 二级分类选择 - 使用层级下拉框 */}
                <div className="col-span-12 sm:col-span-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择二级分类 <span className="text-red-500">*</span>
                  </label>
                  <HierarchicalSelect
                    options={hierarchicalCategories}
                    value={deleteSubCategory}
                    onChange={setDeleteSubCategory}
                    placeholder="请选择二级分类"
                    required
                  />
                </div>

                {/* 字段名称选择 */}
                <div className="col-span-12 sm:col-span-8">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择字段名称 <span className="text-red-500">*</span>
                  </label>
                  <CustomSelect
                    options={deleteExtFields.map(field => ({
                      id: field.key,
                      value: field.key,
                      label: field.title
                    }))}
                    value={deleteFieldName}
                    onChange={setDeleteFieldName}
                    placeholder="请选择字段名称"
                    required
                    disabled={!deleteSubCategory || deleteExtFields.length === 0}
                    direction="bottom" // 强制向下展开
                  />
                </div>
              </div>
            </div>
          </div>
          )}
        </div>

        {/* 对话框底部按钮 - 固定在底部，与导出菜单保持一致 */}
        <div className="flex-shrink-0 px-3 sm:px-4 py-2.5 border-t bg-white flex justify-end space-x-3">
          {/* 取消按钮 */}
          <button
            onClick={handleClose}
            className="px-6 py-3 border border-gray-300 rounded text-base font-medium text-gray-700 hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          {/* 添加字段按钮 - 仅在添加标签页显示 */}
          {activeTab === 'add' && (
            <button
              onClick={handleAddExtField}
              disabled={isLoading || !selectedSubCategory || !fieldName}
              className={`px-6 py-3 rounded text-base font-medium flex items-center transition-colors ${
                isLoading || !selectedSubCategory || !fieldName
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              <Plus className="h-4 w-4 mr-2" />
              添加字段
            </button>
          )}

          {/* 修改字段按钮 - 仅在修改标签页显示 */}
          {activeTab === 'edit' && (
            <button
              onClick={handleUpdateExtField}
              disabled={isLoading || !editSubCategory || !editFieldName}
              className={`px-6 py-3 rounded text-base font-medium flex items-center transition-colors ${
                isLoading || !editSubCategory || !editFieldName
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-yellow-600 text-white hover:bg-yellow-700'
              }`}
            >
              <Settings className="h-4 w-4 mr-2" />
              修改字段
            </button>
          )}

          {/* 删除字段按钮 - 仅在删除标签页显示 */}
          {activeTab === 'delete' && (
            <button
              onClick={handleDeleteExtFieldFromSelect}
              disabled={isLoading || !deleteSubCategory || !deleteFieldName}
              className={`px-6 py-3 rounded text-base font-medium flex items-center transition-colors ${
                isLoading || !deleteSubCategory || !deleteFieldName
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-red-600 text-white hover:bg-red-700'
              }`}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除字段
            </button>
          )}
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={confirmDeleteExtField}
        title="确认删除"
        message={`确定要删除扩展字段"${confirmDialogData.fieldName}"吗？此操作不可撤销。`}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />

      {/* 自定义提示框 */}
      <CustomAlert
        isOpen={alertState.isOpen}
        onClose={() => setAlertState(prev => ({ ...prev, isOpen: false }))}
        message={alertState.message}
        type={alertState.type}
        confirmText="确定"
      />
    </DialogBase>
  );
};

export default ColumnSettingsDialog;
