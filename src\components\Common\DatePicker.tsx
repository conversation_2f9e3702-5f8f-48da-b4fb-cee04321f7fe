import React, { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';

interface DatePickerProps {
  value?: string;
  onChange: (date: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: boolean;
  showTime?: boolean; // 是否显示时间选择
  showSeconds?: boolean; // 是否显示秒选择（需要showTime为true）
}

/**
 * 自定义日期时间选择器组件
 * 采用台账管理页面的样式，兼容m108版本内核浏览器
 */
const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  placeholder = '请选择日期',
  disabled = false,
  className = '',
  error = false,
  showTime = false,
  showSeconds = false
}) => {
  // 初始化当前日期（避免重复创建）
  const now = new Date();

  const [isOpen, setIsOpen] = useState(false);
  const [displayValue, setDisplayValue] = useState('');
  const [year, setYear] = useState(now.getFullYear());
  const [month, setMonth] = useState(now.getMonth());
  const [selectedTime, setSelectedTime] = useState(() => {
    if (value && showTime) {
      const date = new Date(value);
      return {
        hours: date.getHours(),
        minutes: date.getMinutes(),
        seconds: showSeconds ? date.getSeconds() : 0
      };
    }
    return { hours: 9, minutes: 0, seconds: 0 };
  });
  const inputRef = useRef<HTMLInputElement>(null);
  const calendarRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const [isPositioned, setIsPositioned] = useState(false);

  // 解析日期字符串为日期对象（优化版）
  const parseDate = (dateStr: string): Date | null => {
    if (!dateStr) return null;
    try {
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    } catch {
      return null;
    }
  };

  // 统一的日期格式化函数（支持秒级精度）
  const formatDate = useCallback((date: Date, time?: { hours: number; minutes: number; seconds?: number }, forDisplay = false): string => {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    if (forDisplay) {
      const baseStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      if (showTime && time) {
        let timeStr = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
        if (showSeconds && time.seconds !== undefined) {
          timeStr += `:${String(time.seconds).padStart(2, '0')}`;
        }
        return `${baseStr} ${timeStr}`;
      }
      return baseStr;
    }

    const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    if (showTime && time) {
      let timeStr = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
      if (showSeconds && time.seconds !== undefined) {
        timeStr += `:${String(time.seconds).padStart(2, '0')}`;
      }
      return `${dateStr}T${timeStr}`;
    }
    return dateStr;
  }, [showTime, showSeconds]);

  // 初始化和更新状态（支持秒级精度）
  useEffect(() => {
    const date = parseDate(value || '');
    if (date) {
      setYear(date.getFullYear());
      setMonth(date.getMonth());

      if (showTime && value?.includes('T')) {
        const [, timePart] = value.split('T');
        const timeParts = timePart.split(':').map(Number);
        const hours = timeParts[0] || 9;
        const minutes = timeParts[1] || 0;
        const seconds = showSeconds ? (timeParts[2] || 0) : 0;

        const timeObj = { hours, minutes, seconds };
        setSelectedTime(timeObj);
        setDisplayValue(formatDate(date, timeObj, true));
      } else {
        setDisplayValue(formatDate(date, undefined, true));
      }
    } else {
      setDisplayValue('');
    }
  }, [value, showTime, showSeconds, formatDate]);

  // 计算下拉菜单位置（防止闪现）
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setIsPositioned(false); // 重置定位状态

      // 使用requestAnimationFrame确保DOM更新后再计算位置
      requestAnimationFrame(() => {
        if (!inputRef.current) return;

        const rect = inputRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;
        const calendarHeight = showTime ? 300 : 280;

        let top = rect.bottom + window.scrollY + 2;

        // 如果下方空间不足且上方空间更充足，则向上展开
        if (spaceBelow < calendarHeight && spaceAbove > spaceBelow && spaceAbove > calendarHeight) {
          top = rect.top + window.scrollY - calendarHeight - 2;
        }

        setDropdownPosition({
          top: top,
          left: rect.left + window.scrollX,
          width: showTime ? 'auto' : 280
        });

        setIsPositioned(true); // 标记已定位
      });
    } else {
      setIsPositioned(false);
    }
  }, [isOpen, showTime]);

  // 统一的事件处理（修复滚动问题）
  useEffect(() => {
    if (!isOpen) return;

    const closeDropdown = () => setIsOpen(false);

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (inputRef.current?.contains(target) || calendarRef.current?.contains(target)) return;
      closeDropdown();
    };

    const handleScroll = (event: Event) => {
      // 只有当滚动事件不是来自日历内部时才关闭
      const target = event.target as Node;
      if (calendarRef.current?.contains(target)) return;
      closeDropdown();
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', closeDropdown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', closeDropdown);
    };
  }, [isOpen]);

  // 生成日历网格（优化版）
  const calendarGrid = (() => {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDay = new Date(year, month, 1).getDay();
    const grid: (number | null)[] = Array(firstDay).fill(null);

    for (let i = 1; i <= daysInMonth; i++) {
      grid.push(i);
    }

    return grid;
  })();

  // 处理日期选择（优化版）
  const handleDateSelect = (day: number) => {
    const selectedDate = new Date(year, month, day);
    const time = showTime ? selectedTime : undefined;
    const dateStr = formatDate(selectedDate, time);

    onChange(dateStr);
    setDisplayValue(formatDate(selectedDate, time, true));

    if (!showTime) setIsOpen(false);
  };

  // 处理日期导航（优化版）
  const handleMonthChange = (increment: number) => {
    const newDate = new Date(year, month + increment, 1);
    setYear(newDate.getFullYear());
    setMonth(newDate.getMonth());
  };

  const handleYearChange = (increment: number) => {
    setYear(prev => prev + increment);
  };

  // 更新时间（支持秒级精度）
  const updateTime = (hours: number, minutes: number, seconds?: number) => {
    const newTime = {
      hours,
      minutes,
      seconds: showSeconds ? (seconds !== undefined ? seconds : selectedTime.seconds) : 0
    };
    setSelectedTime(newTime);

    const currentDate = parseDate(value || '');
    if (currentDate) {
      const dateStr = formatDate(currentDate, newTime);
      onChange(dateStr);
      setDisplayValue(formatDate(currentDate, newTime, true));
    }
  };

  // 操作按钮处理（支持秒级精度）
  const handleClear = () => {
    onChange('');
    setDisplayValue('');
    setSelectedTime({ hours: 9, minutes: 0, seconds: 0 });
    setIsOpen(false);
  };

  const handleToday = () => {
    const time = showTime ? {
      hours: now.getHours(),
      minutes: now.getMinutes(),
      seconds: showSeconds ? now.getSeconds() : 0
    } : undefined;
    const dateStr = formatDate(now, time);

    onChange(dateStr);
    setDisplayValue(formatDate(now, time, true));

    if (showTime) {
      setSelectedTime(time!);
    } else {
      setIsOpen(false);
    }
  };

  // 辅助函数（优化版）
  const handleInputClick = () => !disabled && setIsOpen(!isOpen);
  const confirmSelection = () => setIsOpen(false);

  const selectedDay = (() => {
    const date = parseDate(value || '');
    return date?.getFullYear() === year && date?.getMonth() === month ? date.getDate() : null;
  })();

  // 常量定义
  const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
  const weekdayNames = ['日', '一', '二', '三', '四', '五', '六'];

  // 时间选择器组件
  const TimeSelector = ({ type, max, selected, onSelect }: {
    type: string;
    max: number;
    selected: number;
    onSelect: (value: number) => void;
  }) => (
    <div className="flex-1 border-r border-gray-200 last:border-r-0">
      <div className="text-xs text-gray-600 py-1 text-center bg-gray-100">{type}</div>
      <div className="max-h-60 overflow-y-auto">
        {Array.from({ length: max }, (_, i) => (
          <div
            key={i}
            className={`px-2 py-1 text-center cursor-pointer transition-colors ${
              selected === i
                ? 'bg-blue-500 text-white font-medium'
                : 'text-gray-700 hover:bg-blue-50'
            }`}
            onClick={() => onSelect(i)}
          >
            {String(i).padStart(2, '0')}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="relative">
      <div
        ref={inputRef}
        className={`w-full px-3 py-2 border rounded-md cursor-pointer transition-colors flex justify-between items-center text-sm
          ${disabled
            ? 'bg-gray-100 border-gray-200 cursor-not-allowed'
            : error
              ? 'border-red-300 bg-red-50'
              : isOpen
                ? 'border-blue-500 ring-1 ring-blue-500'
                : 'border-gray-300 hover:border-gray-400'
          }
          ${className}`}
        onClick={handleInputClick}
        style={{
          backgroundColor: disabled ? '#f3f4f6' : error ? '#fef2f2' : 'white'
        }}
      >
        <span className={!displayValue ? 'text-gray-400' : 'text-gray-700'}>
          {displayValue || placeholder}
        </span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 text-gray-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>

      {isOpen && !disabled && createPortal(
        <div
          ref={calendarRef}
          className="fixed z-[9999] bg-white border border-gray-300 shadow-lg rounded-md text-sm"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: showTime ? 'auto' : `${dropdownPosition.width}px`,
            opacity: isPositioned ? 1 : 0,
            transition: isPositioned ? 'opacity 0.1s ease-in' : 'none'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className={`flex ${showTime ? 'flex-row' : 'flex-col'}`}>
            {/* 日历部分 */}
            <div className={`${showTime ? 'flex-1' : 'w-full'} p-2.5`}>
              {/* 日历头部 - 年月选择 */}
              <div className="flex justify-between items-center mb-1.5">
                <button
                  type="button"
                  onClick={() => handleYearChange(-1)}
                  className="p-0.5 text-gray-600 hover:text-gray-900 text-sm"
                >
                  &lt;&lt;
                </button>
                <button
                  type="button"
                  onClick={() => handleMonthChange(-1)}
                  className="p-0.5 text-gray-600 hover:text-gray-900 text-sm"
                >
                  &lt;
                </button>
                <span className="font-medium text-sm">
                  {year}年 {monthNames[month]}
                </span>
                <button
                  type="button"
                  onClick={() => handleMonthChange(1)}
                  className="p-0.5 text-gray-600 hover:text-gray-900 text-sm"
                >
                  &gt;
                </button>
                <button
                  type="button"
                  onClick={() => handleYearChange(1)}
                  className="p-0.5 text-gray-600 hover:text-gray-900 text-sm"
                >
                  &gt;&gt;
                </button>
              </div>

              {/* 星期标题 */}
              <div className="grid grid-cols-7 gap-0.5 mb-1">
                {weekdayNames.map((day, index) => (
                  <div
                    key={index}
                    className="text-center text-xs font-medium text-gray-700 py-0.5"
                  >
                    {day}
                  </div>
                ))}
              </div>

              {/* 日期网格 */}
              <div className="grid grid-cols-7 gap-0.5 mb-1.5">
                {calendarGrid.map((day, index) => (
                  <div
                    key={index}
                    className={`
                      text-center py-0.5 text-xs w-6 h-6 flex items-center justify-center
                      ${day === null ? 'text-gray-300' : 'cursor-pointer hover:bg-blue-50'}
                      ${day !== null && day === selectedDay ? 'bg-blue-500 text-white hover:bg-blue-600' : ''}
                      ${day !== null ? 'rounded' : ''}
                    `}
                    onClick={() => day !== null && handleDateSelect(day)}
                  >
                    {day}
                  </div>
                ))}
              </div>

              {/* 底部按钮 */}
              <div className="flex justify-between pt-1.5 border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleClear}
                  className="text-xs text-gray-600 hover:text-gray-800 transition-colors"
                >
                  清除
                </button>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={handleToday}
                    className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    今天
                  </button>
                  {showTime && (
                    <button
                      type="button"
                      onClick={confirmSelection}
                      className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      确定
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* 时间选择器 */}
            {showTime && (
              <div className={`${showSeconds ? 'w-48' : 'w-32'} border-l border-gray-200 bg-gray-50 flex`}>
                <TimeSelector
                  type="小时"
                  max={24}
                  selected={selectedTime.hours}
                  onSelect={(hours) => updateTime(hours, selectedTime.minutes, selectedTime.seconds)}
                />
                <TimeSelector
                  type="分钟"
                  max={60}
                  selected={selectedTime.minutes}
                  onSelect={(minutes) => updateTime(selectedTime.hours, minutes, selectedTime.seconds)}
                />
                {showSeconds && (
                  <TimeSelector
                    type="秒"
                    max={60}
                    selected={selectedTime.seconds}
                    onSelect={(seconds) => updateTime(selectedTime.hours, selectedTime.minutes, seconds)}
                  />
                )}
              </div>
            )}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default DatePicker;
