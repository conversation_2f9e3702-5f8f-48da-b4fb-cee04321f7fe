# 网络字段智能识别和专用输入组件

## 功能概述
为设备台账添加/编辑表单的扩展字段实现智能字段类型识别，当字段名称包含特定关键词时，自动使用对应的专用输入组件，提供更好的用户体验和数据输入准确性。

## 智能识别规则

### 字段识别逻辑
系统通过检查扩展字段的`title`或`key`属性来识别字段类型：

```typescript
// IP地址字段识别（不区分大小写）
if (field.title.toLowerCase().includes('ip') || field.key.toLowerCase().includes('ip')) {
  // 使用IP地址专用组件
}

// MAC地址字段识别（不区分大小写）
if (field.title.toLowerCase().includes('mac') || field.key.toLowerCase().includes('mac')) {
  // 使用MAC地址专用组件
}

// 硬盘序列号字段识别
if (field.title === '硬盘序列号' || field.key === 'hard_drive_serial' || field.title.includes('硬盘序列号')) {
  // 使用硬盘序列号专用组件
}
```

### 识别优先级
1. **硬盘序列号** - 精确匹配和包含匹配
2. **IP地址** - 包含"ip"关键词（不区分大小写）
3. **MAC地址** - 包含"mac"关键词（不区分大小写）
4. **默认处理** - 使用标准的文本/选择/日期组件

## IP地址输入组件 (IPAddressField)

### 核心特性
- **分段输入**：4个独立输入框，格式为 `xxx.xxx.xxx.xxx`
- **智能限制**：每段自动限制0-255范围，达到限制后无法继续输入
- **自动跳转**：输入3位数字或数值超过25时自动跳转到下一段
- **键盘导航**：支持方向键、Tab键、点号(.)导航
- **粘贴解析**：支持完整IP地址粘贴时自动解析到各段

### 界面设计
```
IP地址 *
┌────┐   ┌────┐   ┌────┐   ┌────┐
│192 │ . │168 │ . │ 1  │ . │ 1  │
└────┘   └────┘   └────┘   └────┘
```

### 交互行为
1. **输入限制**：只允许数字输入，自动过滤非数字字符
2. **范围验证**：超过255的输入自动调整为255
3. **自动跳转**：
   - 输入3位数字时自动跳转
   - 输入数值>25时自动跳转（如输入26会立即跳转）
   - 按点号(.)时跳转到下一段
4. **键盘导航**：
   - 右方向键：在光标位于末尾时跳转到下一段
   - 左方向键：在光标位于开头时跳转到上一段
   - 退格键：在当前段为空时跳转到上一段

### 粘贴功能
- **完整IP识别**：粘贴"***********"时自动分配到各段
- **部分内容处理**：粘贴纯数字时在当前段处理
- **格式验证**：自动验证每段是否在0-255范围内

## MAC地址输入组件 (MACAddressField)

### 核心特性
- **分段输入**：6个独立输入框，格式为 `XX:XX:XX:XX:XX:XX`
- **十六进制限制**：每段只允许2位十六进制字符
- **自动大写**：输入时自动转换为大写字母
- **自动跳转**：输入2位字符后自动跳转到下一段
- **多格式粘贴**：支持冒号、短横线、点号分隔或无分隔符的MAC地址

### 界面设计
```
MAC地址 *
┌────┐   ┌────┐   ┌────┐   ┌────┐   ┌────┐   ┌────┐
│ AA │ : │ BB │ : │ CC │ : │ DD │ : │ EE │ : │ FF │
└────┘   └────┘   └────┘   └────┘   └────┘   └────┘
```

### 交互行为
1. **字符限制**：只允许0-9和A-F的十六进制字符
2. **自动转换**：小写字母自动转换为大写
3. **自动跳转**：
   - 输入2位字符时自动跳转到下一段
   - 按冒号(:)或短横线(-)时跳转到下一段
4. **键盘导航**：
   - 右方向键：在光标位于末尾时跳转到下一段
   - 左方向键：在光标位于开头时跳转到上一段
   - 退格键：在当前段为空时跳转到上一段

### 粘贴功能
- **多格式支持**：
  - `AA:BB:CC:DD:EE:FF` (冒号分隔)
  - `AA-BB-CC-DD-EE-FF` (短横线分隔)
  - `AA.BB.CC.DD.EE.FF` (点号分隔)
  - `AABBCCDDEEFF` (无分隔符)
- **智能解析**：自动提取十六进制字符并分配到各段
- **部分粘贴**：支持从当前位置开始的部分MAC地址粘贴

## 技术实现

### 文件结构
```
src/components/ui/
├── IPAddressField.tsx        # IP地址专用输入组件
├── MACAddressField.tsx       # MAC地址专用输入组件
└── HardDriveSerialField.tsx  # 硬盘序列号专用输入组件

src/pages/Inventory/InventoryManagement/Dialogs/
└── InventoryForm.tsx         # 集成智能识别逻辑

src/example/
└── NetworkFieldsDemo.tsx     # 演示页面
```

### 集成方式
在`InventoryForm.tsx`的`ExtField`组件中添加识别逻辑：

```typescript
// 在现有的硬盘序列号处理之后添加
if (field.title.toLowerCase().includes('ip') || field.key.toLowerCase().includes('ip')) {
  return <IPAddressField ... />;
}

if (field.title.toLowerCase().includes('mac') || field.key.toLowerCase().includes('mac')) {
  return <MACAddressField ... />;
}
```

### 数据格式
- **IP地址**：存储为标准点分十进制格式 `***********`
- **MAC地址**：存储为冒号分隔的大写格式 `AA:BB:CC:DD:EE:FF`
- **字节计数**：所有组件都支持512字节限制和实时计数显示

## 用户体验设计

### 视觉一致性
- **标签样式**：与其他扩展字段保持一致的加粗标签
- **输入框样式**：统一的边框、聚焦效果和过渡动画
- **错误显示**：统一的错误信息样式和位置
- **字节计数器**：右侧显示当前字节数/512的格式

### 交互体验
- **无提示干扰**：不显示冗余的格式提示信息
- **智能占位符**：使用简洁的示例格式作为占位符
- **键盘友好**：完整支持键盘操作，无需鼠标
- **粘贴智能**：支持多种格式的粘贴和自动解析

### 响应式设计
- **分段布局**：在小屏幕上保持良好的显示效果
- **触摸友好**：输入框大小适合触摸操作
- **字体选择**：MAC地址使用等宽字体确保对齐

## 兼容性和扩展

### 浏览器兼容性
- **现代浏览器**：完全支持所有功能
- **m108内核**：经过测试，确保在目标环境中正常工作
- **键盘事件**：使用标准键盘事件，兼容性良好

### 扩展性设计
- **模块化组件**：每个组件独立，便于维护和扩展
- **统一接口**：所有专用组件使用相同的props接口
- **识别规则**：可以轻松添加新的字段类型识别规则

### 未来扩展可能
- **端口号字段**：识别"port"关键词，提供1-65535范围限制
- **URL字段**：识别"url"关键词，提供URL格式验证
- **邮箱字段**：识别"email"关键词，提供邮箱格式验证
- **电话号码字段**：识别"phone"关键词，提供电话号码格式化

## 测试验证

### 功能测试要点
1. **字段识别**：验证不同关键词的正确识别
2. **输入限制**：验证格式限制和字符过滤
3. **键盘导航**：验证各种键盘操作的正确性
4. **粘贴功能**：验证多种格式的粘贴解析
5. **数据同步**：验证与外部状态的正确同步

### 用户体验测试
1. **输入流畅性**：验证输入过程的流畅性
2. **错误处理**：验证错误状态的显示和恢复
3. **视觉一致性**：验证与其他字段的视觉统一性
4. **响应式表现**：验证在不同屏幕尺寸下的表现

## 界面简化优化

### 优化内容
根据用户反馈，移除了IP地址和MAC地址输入组件右侧的字节计数器显示，使界面更加简洁。

### 修改详情
- **移除字节计数器**：不再显示"xxx/512"格式的字节使用情况
- **简化布局**：输入框布局更加紧凑，没有额外的空间占用
- **清理代码**：移除了相关的导入和未使用的变量

### 最终效果
**IP地址输入**：
```
IP地址 *
┌────┐   ┌────┐   ┌────┐   ┌────┐
│192 │ . │168 │ . │ 1  │ . │ 1  │
└────┘   └────┘   └────┘   └────┘
```

**MAC地址输入**：
```
MAC地址 *
┌────┐   ┌────┐   ┌────┐   ┌────┐   ┌────┐   ┌────┐
│ AA │ : │ BB │ : │ CC │ : │ DD │ : │ EE │ : │ FF │
└────┘   └────┘   └────┘   └────┘   └────┘   └────┘
```

### 改进效果
- ✅ 界面更加简洁，没有冗余信息
- ✅ 输入框布局更紧凑，视觉效果更好
- ✅ 与其他扩展字段的风格更加统一
- ✅ 减少了用户的视觉干扰，专注于输入本身
