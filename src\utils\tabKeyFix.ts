/**
 * 简化的Tab键修复工具
 * 专门解决m108 miniblink环境中Tab键打出空格的问题
 */

let isInitialized = false;
let debugMode = false;

// 启用调试模式
export function enableTabDebug() {
  debugMode = true;
  console.log('🔧 Tab键调试模式已启用');
}

// 禁用调试模式
export function disableTabDebug() {
  debugMode = false;
  console.log('🔧 Tab键调试模式已禁用');
}

// 调试日志
function debugLog(message: string, ...args: any[]) {
  if (debugMode) {
    console.log(`🎹 ${message}`, ...args);
  }
}

// 检测Tab键的多种方式
function isTabKey(event: KeyboardEvent): boolean {
  return event.key === 'Tab' || 
         event.keyCode === 9 || 
         event.which === 9 || 
         event.code === 'Tab';
}

// 获取所有可聚焦元素（使用焦点管理器的统一方法）
function getFocusableElements(): HTMLElement[] {
  try {
    // 尝试使用焦点管理器的统一方法
    const { getFocusManager } = require('../hooks/base/useFocusManager');
    const focusManager = getFocusManager();
    return focusManager.getFocusableElements(document.body);
  } catch (error) {
    // 降级处理：使用本地实现
    debugLog('无法使用焦点管理器，使用降级实现', error);

    const selector = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(',');

    const elements = Array.from(document.querySelectorAll(selector)) as HTMLElement[];

    return elements.filter(element => {
      const rect = element.getBoundingClientRect();
      const style = window.getComputedStyle(element);
      return (
        rect.width > 0 &&
        rect.height > 0 &&
        style.visibility !== 'hidden' &&
        style.display !== 'none' &&
        !element.hasAttribute('hidden') &&
        (element.getAttribute('aria-hidden') !== 'true')
      );
    });
  }
}

// 手动处理Tab键导航（简化版，优先使用焦点管理器）
function handleTabNavigation(event: KeyboardEvent) {
  debugLog('处理Tab键导航', { shiftKey: event.shiftKey });

  try {
    // 优先使用焦点管理器的Tab键导航
    const { getFocusManager } = require('../hooks/base/useFocusManager');
    const focusManager = getFocusManager();
    focusManager.handleTabNavigation(event, document.body);
    debugLog('使用焦点管理器处理Tab键导航');
    return;
  } catch (error) {
    debugLog('无法使用焦点管理器，使用降级Tab键导航', error);
  }

  // 降级处理：简化的Tab键导航
  const focusableElements = getFocusableElements();
  debugLog(`找到 ${focusableElements.length} 个可聚焦元素`);

  if (focusableElements.length === 0) {
    debugLog('没有可聚焦元素');
    return;
  }

  const activeElement = document.activeElement as HTMLElement;
  const currentIndex = focusableElements.indexOf(activeElement);

  let nextIndex: number;
  if (event.shiftKey) {
    nextIndex = currentIndex <= 0 ? focusableElements.length - 1 : currentIndex - 1;
  } else {
    nextIndex = currentIndex >= focusableElements.length - 1 ? 0 : currentIndex + 1;
  }

  const nextElement = focusableElements[nextIndex];
  if (nextElement) {
    try {
      nextElement.focus();
      debugLog('Tab导航成功', nextElement);
    } catch (error) {
      debugLog('Tab导航失败', error);
    }
  }
}

// Tab键事件处理器
function handleKeyDown(event: KeyboardEvent) {
  if (isTabKey(event)) {
    debugLog('Tab键被检测到', {
      key: event.key,
      keyCode: event.keyCode,
      which: event.which,
      target: event.target,
      defaultPrevented: event.defaultPrevented
    });

    // 如果事件已经被其他处理器阻止，不再处理
    if (event.defaultPrevented) {
      debugLog('Tab键事件已被其他处理器处理，跳过');
      return;
    }

    // 检查是否在输入框中，如果是则不处理
    const target = event.target as HTMLElement;
    if (target && (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.isContentEditable
    )) {
      debugLog('Tab键在输入元素中，跳过处理');
      return;
    }

    // 只阻止默认行为，不阻止事件传播
    event.preventDefault();

    debugLog('Tab键默认行为已阻止');

    // 手动处理Tab键导航
    handleTabNavigation(event);

    return false;
  }
  // 对于非Tab键，不做任何处理，让其他处理器正常工作
}

// keypress事件处理器（防止字符输出）
function handleKeyPress(event: KeyboardEvent) {
  if (isTabKey(event)) {
    debugLog('keypress阶段Tab键拦截');
    // 只阻止默认行为，不阻止事件传播
    event.preventDefault();
    return false;
  }
  // 对于非Tab键，不做任何处理
}

// 初始化Tab键修复
export function initTabKeyFix() {
  if (isInitialized) {
    debugLog('Tab键修复已经初始化');
    return;
  }

  debugLog('初始化Tab键修复');

  // 使用更低优先级的事件监听，让键盘管理器先处理
  // 延迟注册，确保键盘管理器已经初始化
  setTimeout(() => {
    document.addEventListener('keydown', handleKeyDown, false);
    document.addEventListener('keypress', handleKeyPress, false);
    debugLog('Tab键修复事件监听器已注册');
  }, 100);
  
  // 添加CSS样式防止Tab字符显示
  const style = document.createElement('style');
  style.id = 'tab-key-fix-styles';
  style.textContent = `
    /* 防止Tab键产生可见字符 */
    * {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    
    input, textarea, [contenteditable] {
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
    }
    
    /* 确保Tab字符不可见 */
    *::before, *::after {
      content: none !important;
    }
    
    /* 调试模式下的焦点指示 */
    .tab-debug-focus {
      outline: 2px solid #ff0000 !important;
      outline-offset: 2px !important;
    }
  `;
  
  document.head.appendChild(style);
  
  isInitialized = true;
  debugLog('Tab键修复初始化完成');
  
  // 添加全局测试方法
  (window as any).testTabKey = () => {
    debugLog('测试Tab键功能');
    const focusableElements = getFocusableElements();
    debugLog(`找到 ${focusableElements.length} 个可聚焦元素`, focusableElements);
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
      debugLog('聚焦到第一个元素', focusableElements[0]);
    }
  };
  
  (window as any).enableTabDebug = enableTabDebug;
  (window as any).disableTabDebug = disableTabDebug;
  
  debugLog('全局测试方法已添加: window.testTabKey(), window.enableTabDebug(), window.disableTabDebug()');
}

// 清理Tab键修复
export function cleanupTabKeyFix() {
  if (!isInitialized) {
    return;
  }
  
  debugLog('清理Tab键修复');
  
  document.removeEventListener('keydown', handleKeyDown, false);
  document.removeEventListener('keypress', handleKeyPress, false);
  
  const style = document.getElementById('tab-key-fix-styles');
  if (style) {
    document.head.removeChild(style);
  }
  
  delete (window as any).testTabKey;
  delete (window as any).enableTabDebug;
  delete (window as any).disableTabDebug;
  
  isInitialized = false;
  debugLog('Tab键修复清理完成');
}

// 自动初始化（在开发环境中）
if (typeof window !== 'undefined') {
  // 延迟初始化，确保DOM加载完成
  setTimeout(() => {
    initTabKeyFix();
    
    // 在开发环境或URL包含debug参数时启用调试
    if (process.env.NODE_ENV === 'development' || 
        window.location.search.includes('debug=tab')) {
      enableTabDebug();
    }
  }, 100);
}
