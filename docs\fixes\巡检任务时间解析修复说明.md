# 巡检任务时间解析修复说明

## 问题描述

在巡检任务修改页面中，时间安排部分的开始时间和结束时间显示为"1970-01-20"，并且解析出来的时间与表格显示的时间相差8小时，这是因为时间戳解析不正确和时区处理问题导致的。

## 问题原因

在`TaskEditDialog.tsx`组件中，`formatDateForPicker`函数直接使用时间戳创建Date对象，但没有正确处理时间戳的单位：

```typescript
// 原有问题代码
const formatDateForPicker = (timestamp: number | undefined): string => {
  if (!timestamp) return '';
  const date = new Date(timestamp);  // 没有判断是秒级还是毫秒级
  return date.toISOString().slice(0, 19).replace('T', ' ');
};
```

当后端返回的是秒级时间戳（如：1753786813）时，JavaScript的Date构造函数会将其当作毫秒级时间戳处理，导致日期计算错误，显示为1970年的日期。

另外，原代码使用`toISOString()`方法返回UTC时间，而用户需要的是本地时间（东八区），导致时间相差8小时。

## 修复方案

参考项目中已有的`formatInspectionDateTime`函数的逻辑，在`formatDateForPicker`函数中添加时间戳单位判断，并确保输出格式与DatePicker组件期望的格式一致：

```typescript
// 修复后的代码
const formatDateForPicker = (timestamp: number | undefined): string => {
  if (!timestamp || timestamp <= 0) return '';

  // 判断是秒级还是毫秒级时间戳
  // 如果时间戳小于等于10位数，认为是秒级时间戳
  const milliseconds = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;

  try {
    const date = new Date(milliseconds);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '';
    }

    // 使用本地时间格式化，避免时区问题
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // DatePicker组件期望的格式是 "YYYY-MM-DDTHH:mm:ss"（用T分隔，本地时间）
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.warn('日期格式化失败:', error);
    return '';
  }
};
```

## 修复内容

### 1. 时间戳单位判断
- 添加了时间戳长度检查
- 10位及以下认为是秒级时间戳，需要乘以1000转换为毫秒级
- 超过10位认为是毫秒级时间戳，直接使用

### 2. 时区处理
- 使用本地时间方法（`getFullYear()`, `getMonth()`, `getDate()`等）替代`toISOString()`
- 避免UTC时间转换，确保显示的是用户本地时区的时间
- 解决了东八区时间相差8小时的问题

### 3. 错误处理
- 添加了时间戳有效性检查（`timestamp <= 0`）
- 添加了Date对象有效性检查（`isNaN(date.getTime())`）
- 添加了try-catch错误捕获和日志记录

### 3. 格式兼容性
- 确保输出格式与DatePicker组件期望的`"YYYY-MM-DDTHH:mm:ss"`格式一致
- DatePicker组件通过检查`value?.includes('T')`来识别时间部分

### 4. 一致性保证
- 与项目中`formatInspectionDateTime`函数保持相同的时间戳处理逻辑
- 确保整个项目中时间戳处理的一致性
- 保证修改对话框中的时间与表格显示的时间完全一致

## 测试验证

### 测试用例
1. **秒级时间戳测试**：1753786813 → 应显示为 "2025-07-29 19:00:13"
2. **毫秒级时间戳测试**：1753786813000 → 应显示为 "2025-07-29 19:00:13"
3. **无效时间戳测试**：0, undefined, null → 应显示为空字符串
4. **异常时间戳测试**：负数、非数字 → 应显示为空字符串

### 验证方法
1. 打开巡检任务页面
2. 点击任意任务的"修改任务"按钮
3. 查看时间安排部分的开始时间和结束时间
4. 确认显示正确的日期时间，而不是1970年的日期

## 影响范围

### 直接影响
- 巡检任务修改对话框中的时间显示

### 间接影响
- 提高了用户体验，避免了时间显示错误的困惑
- 保证了数据的正确性和一致性

## 相关文件

- `src/components/Inspection/TaskEditDialog.tsx` - 主要修复文件
- `src/utils/formatUtils.ts` - 参考的时间格式化工具函数

## 注意事项

1. 此修复遵循了项目中已有的时间戳处理标准
2. 保持了与其他时间显示功能的一致性
3. 添加了完善的错误处理，提高了代码的健壮性
4. 不影响其他功能的正常运行

## 后续建议

1. 建议在项目中统一时间戳处理逻辑，创建通用的时间戳转换工具函数
2. 考虑在API接口文档中明确时间戳的单位规范
3. 在类似的时间处理场景中应用相同的修复模式
