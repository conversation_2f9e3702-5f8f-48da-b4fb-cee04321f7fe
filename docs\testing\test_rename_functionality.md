# 树状图修改功能测试说明

## 修改内容

已成功修改树状图的修改节点功能，现在根据分类层级显示不同的文本：

### 1. 一级分类（设备分类）
- 右键菜单显示：**修改分类**
- 对话框标题：**修改分类**
- 输入框标签：**分类名称**
- 输入框占位符：**请输入分类名称**

### 2. 二级分类（设备类型）
- 右键菜单显示：**修改类型**
- 对话框标题：**修改类型**
- 输入框标签：**类型名称**
- 输入框占位符：**请输入类型名称**

### 3. 部门节点
- 右键菜单显示：**修改名称**（保持原有）
- 对话框标题：**修改名称**
- 输入框标签：**无标签**（已隐藏标签文字和*号）
- 输入框占位符：**请输入分类名称**

### 4. 人员节点
- 右键菜单显示：**修改名称**（保持原有）
- 对话框标题：**修改名称**
- 输入框标签：**分类名称**
- 输入框占位符：**请输入分类名称**

## 实现逻辑

添加了 `getRenameText` 函数来判断节点类型：

```typescript
const getRenameText = (categoryId: string, categoryMode: string) => {
  if (categoryMode === 'device') {
    if (categoryId.startsWith('parent-') && !categoryId.includes('-', 'parent-'.length)) {
      return '修改分类'; // 一级分类
    } else if (categoryId.startsWith('parent-') && categoryId.includes('-', 'parent-'.length)) {
      return '修改类型'; // 二级分类
    }
  }
  return '修改名称'; // 默认情况（部门、人员等）
};
```

## 测试步骤

1. 打开设备分类树
2. 右键点击一级分类节点，确认菜单显示"修改分类"
3. 点击"修改分类"，确认对话框标题为"修改分类"，输入框标签为"分类名称"
4. 右键点击二级分类节点，确认菜单显示"修改类型"
5. 点击"修改类型"，确认对话框标题为"修改类型"，输入框标签为"类型名称"
6. 切换到部门分类树，确认部门节点仍显示"修改名称"
7. 右键点击部门节点，点击"修改名称"，确认对话框中没有显示标签文字和*号

## 修改的文件

- `src/pages/Inventory/InventoryManagement/CategoryTree.tsx`
  - 添加了 `getRenameText` 辅助函数
  - 修改了4个右键菜单位置的文本显示
  - 修改了对话框标题
  - 修改了输入框标签和占位符文本
  - 为部门节点隐藏了标签文字和必填*号

## 最新修改（部门节点标签隐藏）

针对部门分类树中部门节点的修改名称对话框，已实现：
- 删除了"分类名称"标签文字
- 隐藏了必填的*号
- 保持输入框功能正常

判断条件：`categoryMode === 'department' && renameDialog.categoryId.startsWith('dept-')`

所有修改都已完成并通过编译测试。
