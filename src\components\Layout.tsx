import React, { useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Settings, Minus, Square, X, Database, Activity, AlertTriangle, ClipboardCheck } from 'lucide-react';
import KeepAliveOutlet from './KeepAliveOutlet';
import StatusBar from './ui/StatusBar';
import logoImage from '../assets/jma.png';
// import SkipLinks from './ui/SkipLinks'; // 已禁用跳过链接功能


import '../styles/focusStyles.css';

/**
 * 原生响应数据接口
 */
interface NativeResponse {
  success?: boolean;
  message?: string;
  data?: unknown;
}

/**
 * 扩展Window接口以包含原生方法
 */
declare global {
  interface Window {
    mbQuery?: (msgType: number, message: string, callback: (msgType: number, response: NativeResponse) => void) => void;
  }
}

// 定义消息类型
const MSG_TYPE = {
  MINIMIZE: 0x0001,
  MAXIMIZE: 0x0002,
  CLOSE: 0x0003
};

// 扩展CSSProperties类型
declare module 'react' {
  interface CSSProperties {
    WebkitAppRegion?: 'drag' | 'no-drag';
  }
}

const Layout = () => {
  const location = useLocation();
  const [isMaximized, setIsMaximized] = React.useState(false);





  // 添加全局样式
  useEffect(() => {
    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      .draggable-header {
        -webkit-app-region: drag;
        cursor: move;
      }
      .draggable-header a,
      .draggable-header button,
      .draggable-header [style*="WebkitAppRegion: no-drag"],
      .draggable-header *[style*="WebkitAppRegion: no-drag"] {
        -webkit-app-region: no-drag;
        cursor: default;
      }
      .draggable-header a {
        cursor: pointer;
      }

      /* 调试模式 - 在开发环境中可视化拖拽区域 */
      ${process.env.NODE_ENV === 'development' ? `
        .draggable-header [style*="WebkitAppRegion: drag"] {
          background-color: rgba(0, 255, 0, 0.1) !important;
        }
        .draggable-header [style*="WebkitAppRegion: no-drag"] {
          background-color: rgba(255, 0, 0, 0.1) !important;
        }
      ` : ''}
    `;
    // 添加到文档头部
    document.head.appendChild(styleElement);

    // 清理函数
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);





  const navigation = [
    { name: '台账管理', path: '/inventory', icon: Database },
    { name: '巡检管理', path: '/data-sync', icon: ClipboardCheck },
    { name: '异常处理', path: '/exceptions', icon: AlertTriangle },
    { name: '日志管理', path: '/logs', icon: Activity },
  ];

  // 回调函数
  const onNativeResponse = (customMsg: number, response: NativeResponse) => {
    console.log("收到Native响应:", response);
    switch(customMsg) {
      case MSG_TYPE.MINIMIZE:
        console.log("窗口已最小化");
        break;
      case MSG_TYPE.MAXIMIZE:
        console.log("窗口已最大化/还原");
        setIsMaximized(!isMaximized);
        document.documentElement.classList.toggle('maximize');
        break;
      case MSG_TYPE.CLOSE:
        console.log("窗口即将关闭");
        break;
    }
  };

  const handleMinimize = () => {
    if (window.mbQuery) {
      window.mbQuery(MSG_TYPE.MINIMIZE, "minimize", onNativeResponse);
    }
  };

  const handleMaximize = () => {
    if (window.mbQuery) {
      window.mbQuery(MSG_TYPE.MAXIMIZE, "maximize", onNativeResponse);
    }
  };

  const handleClose = () => {
    if (window.mbQuery) {
      window.mbQuery(MSG_TYPE.CLOSE, "close", onNativeResponse);
    }
  };

  // 处理双击导航栏最大化/还原窗口
  const handleDoubleClick = (e: React.MouseEvent) => {
    // 确保双击事件不是来自导航按钮或窗口控制按钮区域
    if ((e.target as HTMLElement).closest('[style*="WebkitAppRegion: no-drag"]') === null) {
      handleMaximize();
    }
  };

  // 检查当前路径是否匹配导航项
  const isActiveRoute = (path: string) => {
    if (path === '/') {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };



  return (
    <div className="min-h-screen bg-gray-50 flex flex-col h-full">
      {/* 跳转链接功能已禁用 */}
      {/* <SkipLinks
        links={[
          { href: '#main-content', label: '跳转到主要内容' },
          { href: '#navigation', label: '跳转到导航菜单' }
        ]}
      /> */}

      <header
        id="navigation"
        className="bg-gradient-to-r from-gray-50 to-gray-100 shadow-lg border-b border-gray-300/60 sticky top-0 z-50 flex-none draggable-header"
        style={{ WebkitAppRegion: 'drag' }}
        onDoubleClick={handleDoubleClick}
      >
        <div className="flex items-center justify-between px-4 h-16" style={{ WebkitAppRegion: 'drag' }}>
          {/* Left section with logo and title */}
          <div className="flex items-center space-x-3 flex-shrink-0">
            <img src={logoImage} alt="Logo" className="h-7 w-7" style={{ WebkitAppRegion: 'no-drag' }} />
            <span className="text-lg font-bold text-gray-800 whitespace-nowrap tracking-tight leading-none"
                  style={{
                    WebkitAppRegion: 'drag',
                    textShadow: '0 1px 2px rgba(0,0,0,0.1)',
                    letterSpacing: '0.01em'
                  }}>
              军密安信息设备台账管理系统 v1.0
            </span>
          </div>

          {/* Navigation - Centered */}
          <nav className="flex-1 flex justify-center items-center ml-6 mr-4" style={{ WebkitAppRegion: 'drag' }}>
            <div className="flex w-full justify-center space-x-1" style={{ WebkitAppRegion: 'drag' }}>
              {navigation.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  style={{ WebkitAppRegion: 'no-drag' }}
                  className={`flex-1 max-w-[120px] mx-1 sm:mx-2 py-2 rounded-md transition-all duration-200 flex flex-col items-center justify-center ${
                    isActiveRoute(item.path)
                      ? 'text-blue-700 bg-white shadow-md border border-blue-200/50'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-white/70 hover:shadow-sm'
                  }`}
                >
                  {item.icon && <item.icon className="h-6 w-6 mb-1.5" />}
                  <span className="text-sm font-medium">{item.name}</span>
                </Link>
              ))}
            </div>
          </nav>

          {/* Window Controls - Right */}
          <div className="flex items-center justify-end">
            {/* 功能按钮组 */}
            <div className="flex items-center space-x-4 mr-6">
              <div style={{ WebkitAppRegion: 'no-drag' }}>
                <Settings className="h-5 w-5 text-gray-600 cursor-pointer hover:text-gray-800 transition-colors duration-200" />
              </div>
            </div>

            {/* 窗口控制按钮组 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={handleMinimize}
                className="window-control-btn"
                style={{ WebkitAppRegion: 'no-drag' }}
                title="最小化"
              >
                <Minus className="h-4 w-4 text-gray-600" />
              </button>
              <button
                onClick={handleMaximize}
                className="window-control-btn"
                style={{ WebkitAppRegion: 'no-drag' }}
                title={isMaximized ? "还原" : "最大化"}
              >
                <Square className="h-4 w-4 text-gray-600" />
              </button>
              <button
                onClick={handleClose}
                className="window-control-btn close"
                style={{ WebkitAppRegion: 'no-drag' }}
                title="关闭"
              >
                <X className="h-4 w-4 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <main
        id="main-content"
        className="flex-1 p-4 md:p-6 space-y-4 overflow-auto min-h-0 focus:outline-none focus-ring"
        tabIndex={-1}
        role="main"
        aria-label="主要内容"
      >
        <KeepAliveOutlet />
      </main>

      {/* 添加状态栏 */}
      <StatusBar />
    </div>
  );
};

export default React.memo(Layout);