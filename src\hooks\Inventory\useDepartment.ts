import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useService } from '../base/useService';
import DepartmentService from '../../services/Inventory/departmentService';
import InventoryService from '../../services/Inventory/inventoryService';
import {
  DepartmentCategory,
  DepartmentServiceState,
  DepartmentServiceEvents
} from '../../services/Inventory/department';
import { DepartmentUtils } from '../../services/Inventory/department/utils';
import { InventoryItem } from '../../types/inventory';
import { logError } from '../../utils/errorHandler';


// 重新导出类型，这样UI层可以从 Hook 导入，而不是直接从服务层导入
export type { DepartmentCategory };



// 返回类型定义
interface UseDepartmentReturn {
  // 状态
  departmentCategories: DepartmentCategory[];
  currentDepartmentCategory: string;
  isLoading: boolean;
  error?: string;

  // 过滤后的设备列表
  filteredItems: InventoryItem[];

  // 移动确认对话框状态
  showMoveConfirmDialog: boolean;
  moveConfirmDraggedNode: { id: string; name: string; type: 'department' | 'person' } | null;
  moveConfirmTargetNode: { id: string; name: string } | null;

  // 方法
  generateDepartmentTree: (inventoryList: InventoryItem[]) => void;
  filterByDepartment: (categoryId: string) => void;
  addDepartmentOrPerson: (parentId: string, name: string, itemType: 'department' | 'person', alias?: string, mobileNumber?: string, positionSecurityLevel?: number) => Promise<void>;
  addDepartment: (parentId: string, departmentName: string) => Promise<void>;
  addPerson: (departmentId: string, personName: string, alias?: string, mobileNumber?: string, positionSecurityLevel?: number) => Promise<void>;
  renameDepartmentOrPerson: (nodeId: string, newName: string) => Promise<void>;
  deleteDepartmentOrPerson: (nodeId: string) => Promise<void>;
  updateDepartmentPosition: (departmentId: string, newParentId: string) => Promise<void>;
  updatePersonPosition: (personId: string, newDepartmentId: string) => Promise<void>;

  // 拖拽相关方法（仅支持人员拖拽，部门拖拽已禁用）
  handleDrop: (draggedId: string, targetId: string) => Promise<void>;
  confirmMove: () => Promise<void>;
  cancelMove: () => void;
  canDragNode: (nodeId: string) => boolean; // 只允许人员节点拖拽
  canDropNode: (draggedId: string, targetId: string) => boolean; // 只允许人员拖拽到部门

  // 事件监听
  onTreeLoaded: (callback: (categories: DepartmentCategory[]) => void) => () => void;
  onTreeUpdated: (callback: (categories: DepartmentCategory[]) => void) => () => void;
  onDepartmentAdded: (callback: (data: { parentId: string, name: string }) => void) => () => void;
  onPersonAdded: (callback: (data: { departmentId: string, name: string, alias?: string }) => void) => () => void;
  onNodeRenamed: (callback: (data: { nodeId: string, newName: string }) => void) => () => void;
  onNodeDeleted: (callback: (data: { nodeId: string }) => void) => () => void;
}

/**
 * 部门分类Hook
 * 提供部门树的加载、过滤和操作功能
 * @param inventoryList 设备列表（用于筛选，不用于计数）
 */
export function useDepartment(inventoryList: InventoryItem[] = []): UseDepartmentReturn {
  // 使用基础钩子获取服务实例和状态，移除inventoryList依赖避免循环
  const [state, service] = useService<DepartmentServiceState, DepartmentServiceEvents, DepartmentService>(
    () => DepartmentService.getInstance(),
    []
  );

  // 获取InventoryService实例以获取完整的总表数据
  const inventoryService = useMemo(() => InventoryService.getInstance(), []);



  // 过滤后的设备列表
  const [filteredItems, setFilteredItems] = useState<InventoryItem[]>([...inventoryList]);

  // 移动确认对话框状态
  const [showMoveConfirmDialog, setShowMoveConfirmDialog] = useState<boolean>(false);
  const [moveConfirmDraggedNode, setMoveConfirmDraggedNode] = useState<{ id: string; name: string; type: 'department' | 'person' } | null>(null);
  const [moveConfirmTargetNode, setMoveConfirmTargetNode] = useState<{ id: string; name: string } | null>(null);

  // 使用 useRef 跟踪上一次的值，避免无限循环
  const prevInventoryListRef = useRef<InventoryItem[]>([]);
  const prevDepartmentCategoriesRef = useRef<DepartmentCategory[]>([]);
  const updateCountsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 使用 useRef 存储当前的 inventoryList，确保使用完整的设备列表
  const currentInventoryListRef = useRef<InventoryItem[]>([]);

  // 更新引用，确保使用完整的设备列表
  useEffect(() => {
    const fullInventoryList = inventoryService.getState().inventoryList;
    currentInventoryListRef.current = fullInventoryList;
    console.log(`useDepartment: 更新设备列表引用，数量: ${fullInventoryList.length}`);
  }, [inventoryList, inventoryService]);

  // 查找节点的辅助函数
  const findNodeById = useCallback((categories: DepartmentCategory[], nodeId: string): DepartmentCategory | null => {
    for (const category of categories) {
      if (category.id === nodeId) {
        return category;
      }
      if (category.children?.length) {
        const found = findNodeById(category.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // 当组件初始化时，等待部门树加载完成（简化版本，减少重复检查）
  useEffect(() => {
    // 创建一个标志来跟踪组件是否已卸载
    let isMounted = true;

    // 简化的部门树加载检查
    const initializeDepartmentTree = () => {
      try {
        // 如果部门树已经加载，更新引用值
        if (state.departmentCategories.length > 0) {
          console.log('部门树已加载，初始化引用值');

          // 获取完整的总表数据用于引用值初始化
          const fullInventoryList = inventoryService.getState().inventoryList;

          // 更新引用值，避免重复更新，使用总表数据
          prevInventoryListRef.current = [...fullInventoryList];
          prevDepartmentCategoriesRef.current = [...state.departmentCategories];

          console.log(`部门树初始化完成，总表数据长度: ${fullInventoryList.length}`);
          return;
        }

        // 如果部门树尚未加载，等待InventoryService统一管理
        console.log('部门树尚未加载，等待InventoryService统一管理');
      } catch (error) {
        if (isMounted) {
          logError(error, 'useDepartment.initializeDepartmentTree');
        }
      }
    };

    // 延迟执行，避免与其他初始化逻辑冲突
    const timer = setTimeout(initializeDepartmentTree, 200);

    // 清理函数
    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, [state.departmentCategories.length]);

  // 当设备列表变化时，更新部门树计数（优化版本，减少冗余更新）
  useEffect(() => {
    // 创建一个标志来跟踪组件是否已卸载
    let isMounted = true;

    // 更新部门树计数的函数
    const updateCounts = () => {
      // 严格检查：部门树必须已加载
      if (state.departmentCategories.length > 0) {
        // 获取完整的总表数据用于计数
        const fullInventoryList = inventoryService.getState().inventoryList;

        // 检查总表数据是否真正变化（长度或内容）
        const inventoryChanged =
          prevInventoryListRef.current.length !== fullInventoryList.length ||
          JSON.stringify(prevInventoryListRef.current.map(item => item.id)) !==
          JSON.stringify(fullInventoryList.map(item => item.id)); // 只比较ID，提高性能

        // 检查部门树是否真正变化
        const departmentsChanged =
          prevDepartmentCategoriesRef.current.length !== state.departmentCategories.length ||
          JSON.stringify(prevDepartmentCategoriesRef.current.map((item: DepartmentCategory) => item.id)) !==
          JSON.stringify(state.departmentCategories.map((item: DepartmentCategory) => item.id)); // 只比较ID，提高性能

        // 只有当真正变化时才更新计数
        if (inventoryChanged || departmentsChanged) {
          console.log(`数据变化检测：总表数据变化=${inventoryChanged}, 部门树变化=${departmentsChanged}`);
          console.log(`总表数据长度: ${fullInventoryList.length}, 传入数据长度: ${currentInventoryListRef.current.length}`);

          // 清除之前的定时器（如果有）
          if (updateCountsTimeoutRef.current) {
            clearTimeout(updateCountsTimeoutRef.current);
          }

          // 使用定时器延迟更新，避免频繁更新
          updateCountsTimeoutRef.current = setTimeout(() => {
            if (!isMounted) return;

            try {
              console.log('执行部门树计数更新，使用总表数据...');
              // 使用完整的总表数据进行计数，而不是传入的可能经过筛选的数据
              const updated = service.updateDepartmentTreeCounts(fullInventoryList);

              // 只有当成功更新时才更新引用值
              if (updated && isMounted) {
                prevInventoryListRef.current = [...fullInventoryList]; // 使用总表数据更新引用
                prevDepartmentCategoriesRef.current = [...state.departmentCategories];
                console.log('部门树计数更新完成（基于总表数据）');
              }
            } catch (error) {
              if (isMounted) {
                logError(error, 'useDepartment.updateDepartmentTreeCounts');
              }
            }
          }, 300); // 300ms 延迟，避免频繁更新
        } else {
          console.log('数据无变化，跳过部门树计数更新');
        }
      } else {
        console.log('部门树尚未加载，跳过计数更新');
      }
    };

    // 执行更新
    updateCounts();

    // 清理函数
    return () => {
      isMounted = false;
      if (updateCountsTimeoutRef.current) {
        clearTimeout(updateCountsTimeoutRef.current);
      }
    };
  }, [state.departmentCategories, service]);

  // 监听部门树首次加载完成，触发一次性计数更新
  useEffect(() => {
    // 只有当部门树从无到有时才触发
    if (state.departmentCategories.length > 0 && prevDepartmentCategoriesRef.current.length === 0) {
      console.log('部门树首次加载完成，触发一次性计数更新');

      // 延迟执行，确保部门树已完全加载
      const timer = setTimeout(() => {
        try {
          // 获取完整的总表数据用于计数
          const fullInventoryList = inventoryService.getState().inventoryList;
          console.log(`部门树首次加载，使用总表数据进行计数，数据长度: ${fullInventoryList.length}`);

          // 立即更新计数，不使用防抖，使用总表数据
          const updated = service.updateDepartmentTreeCounts(fullInventoryList);
          if (updated) {
            console.log('部门树首次加载计数更新完成（基于总表数据）');
            // 更新引用值
            prevDepartmentCategoriesRef.current = [...state.departmentCategories];
            prevInventoryListRef.current = [...fullInventoryList]; // 使用总表数据更新引用
          }
        } catch (error) {
          logError(error, 'useDepartment.firstTimeCountUpdate');
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [state.departmentCategories.length, service, inventoryService]);

  // 加载部门树
  const generateDepartmentTree = useCallback(async (_items: InventoryItem[]) => {
    try {
      // 不再主动加载部门树，等待 InventoryService 统一管理
      console.log('generateDepartmentTree 被调用，但不执行加载，等待 InventoryService 统一管理');
    } catch (error) {
      logError(error, 'useDepartment.generateDepartmentTree');
    }
  }, [service]);

  // 按部门筛选
  const filterByDepartment = useCallback((categoryId: string) => {
    try {
      // 如果部门树尚未加载，跳过筛选
      if (state.departmentCategories.length === 0) {
        console.log('部门树尚未加载，跳过筛选操作');
        setFilteredItems([]);
        return;
      }

      // 检查节点是否仍然存在（避免删除操作后的竞态条件）
      const nodeExists = DepartmentUtils.findNodeById(state.departmentCategories, categoryId);
      if (!nodeExists) {
        console.log(`useDepartment.filterByDepartment 节点 ${categoryId} 已不存在，跳过筛选`);
        // 节点已被删除，清空筛选结果
        setFilteredItems([]);
        return;
      }

      console.log(`useDepartment.filterByDepartment 开始筛选，categoryId: ${categoryId}`);
      console.log(`输入设备列表长度: ${currentInventoryListRef.current.length}`);

      const filtered = service.filterByDepartment(
        currentInventoryListRef.current,
        state.departmentCategories,
        categoryId
      );

      console.log(`useDepartment.filterByDepartment 筛选结果长度: ${filtered.length}`);
      console.log(`筛选结果设备ID列表:`, filtered.map(item => item.id));

      setFilteredItems(filtered);

      // 移除手动触发计数更新，统一由useEffect监听机制处理
      // 这样可以避免重复的计数更新调用
    } catch (error) {
      logError(error, 'useDepartment.filterByDepartment');
      setFilteredItems([]);
    }
  }, [state.departmentCategories, service]);

  // 当当前分类变化时，重新筛选设备列表
  useEffect(() => {
    filterByDepartment(state.currentCategory);
  }, [state.currentCategory, filterByDepartment]);

  // 添加addDepartmentOrPerson方法到hooks中
  const addDepartmentOrPerson = useCallback(async (parentId: string, name: string, itemType: 'department' | 'person', alias?: string, mobileNumber?: string, positionSecurityLevel?: number) => {
    try {
      if (itemType === 'department') {
        await service.addDepartment(parentId, name);
      } else {
        await service.addPerson(parentId, name, alias, mobileNumber, positionSecurityLevel);
      }
    } catch (error) {
      logError(error, 'useDepartment.addDepartmentOrPerson');
      throw error;
    }
  }, [service]);

  // 添加addDepartment方法到hooks中
  const addDepartment = useCallback(async (parentId: string, departmentName: string) => {
    try {
      await service.addDepartment(parentId, departmentName);
    } catch (error) {
      logError(error, 'useDepartment.addDepartment');
      throw error;
    }
  }, [service]);

  // 添加addPerson方法到hooks中
  const addPerson = useCallback(async (departmentId: string, personName: string, alias?: string, mobileNumber?: string, positionSecurityLevel?: number) => {
    try {
      await service.addPerson(departmentId, personName, alias, mobileNumber, positionSecurityLevel);
    } catch (error) {
      logError(error, 'useDepartment.addPerson');
      throw error;
    }
  }, [service]);

  // 添加renameDepartmentOrPerson方法到hooks中
  const renameDepartmentOrPerson = useCallback(async (nodeId: string, newName: string) => {
    try {
      await service.renameDepartmentOrPerson(nodeId, newName);
    } catch (error) {
      logError(error, 'useDepartment.renameDepartmentOrPerson');
      throw error;
    }
  }, [service]);

  // 添加deleteDepartmentOrPerson方法到hooks中
  const deleteDepartmentOrPerson = useCallback(async (nodeId: string) => {
    try {
      await service.deleteDepartmentOrPerson(nodeId);
    } catch (error) {
      logError(error, 'useDepartment.deleteDepartmentOrPerson');
      throw error;
    }
  }, [service]);

  // 事件监听方法
  const onTreeLoaded = useCallback((callback: (categories: DepartmentCategory[]) => void) => {
    service.on('tree-loaded', callback);
    return () => service.off('tree-loaded', callback);
  }, [service]);

  const onTreeUpdated = useCallback((callback: (categories: DepartmentCategory[]) => void) => {
    service.on('tree-updated', callback);
    return () => service.off('tree-updated', callback);
  }, [service]);

  const onDepartmentAdded = useCallback((callback: (data: { parentId: string, name: string }) => void) => {
    service.on('department-added', callback);
    return () => service.off('department-added', callback);
  }, [service]);

  const onPersonAdded = useCallback((callback: (data: { departmentId: string, name: string, alias?: string }) => void) => {
    service.on('person-added', callback);
    return () => service.off('person-added', callback);
  }, [service]);

  const onNodeRenamed = useCallback((callback: (data: { nodeId: string, newName: string }) => void) => {
    service.on('node-renamed', callback);
    return () => service.off('node-renamed', callback);
  }, [service]);

  const onNodeDeleted = useCallback((callback: (data: { nodeId: string }) => void) => {
    service.on('node-deleted', callback);
    return () => service.off('node-deleted', callback);
  }, [service]);

  // 添加更新部门位置的方法
  const updateDepartmentPosition = useCallback(async (departmentId: string, newParentId: string) => {
    try {
      await service.updateDepartmentPosition(departmentId, newParentId);
    } catch (error) {
      logError(error, 'useDepartment.updateDepartmentPosition');
      throw error;
    }
  }, [service]);

  // 添加更新人员位置的方法
  const updatePersonPosition = useCallback(async (personId: string, newDepartmentId: string) => {
    try {
      await service.updatePersonPosition(personId, newDepartmentId);
    } catch (error) {
      logError(error, 'useDepartment.updatePersonPosition');
      throw error;
    }
  }, [service]);

  // 存储待确认的移动操作
  const pendingMoveRef = useRef<{
    draggedId: string;
    targetId: string;
  } | null>(null);

  // 处理拖拽放置的方法 - 显示确认对话框
  const handleDrop = useCallback(async (draggedId: string, targetId: string) => {
    try {
      // 只支持人员拖拽
      if (!draggedId.startsWith('person-')) {
        throw new Error(`只支持人员拖拽，不支持的节点类型: ${draggedId}`);
      }

      // 查找拖拽节点和目标节点
      const draggedNode = findNodeById(state.departmentCategories, draggedId);
      const targetNode = findNodeById(state.departmentCategories, targetId);

      if (!draggedNode || !targetNode) {
        throw new Error('找不到拖拽节点或目标节点');
      }

      // 存储待确认的移动操作
      pendingMoveRef.current = {
        draggedId,
        targetId
      };

      // 设置确认对话框数据
      setMoveConfirmDraggedNode({
        id: draggedId,
        name: draggedNode.name,
        type: 'person' // 现在只支持人员拖拽
      });
      setMoveConfirmTargetNode({
        id: targetId,
        name: targetNode.name
      });

      // 显示确认对话框
      setShowMoveConfirmDialog(true);
    } catch (error) {
      logError(error, 'useDepartment.handleDrop');
      throw error;
    }
  }, [state.departmentCategories, findNodeById]);

  // 确认移动操作
  const confirmMove = useCallback(async () => {
    try {
      if (!pendingMoveRef.current) {
        throw new Error('没有待确认的移动操作');
      }

      const { draggedId, targetId } = pendingMoveRef.current;

      // 只支持人员移动
      if (!draggedId.startsWith('person-')) {
        throw new Error('只支持人员移动操作');
      }

      // 更新人员位置
      await updatePersonPosition(draggedId, targetId);

      // 清除待确认的移动操作
      pendingMoveRef.current = null;
      // 关闭确认对话框
      setShowMoveConfirmDialog(false);
    } catch (error) {
      logError(error, 'useDepartment.confirmMove');
      throw error;
    }
  }, [updatePersonPosition]);

  // 取消移动操作
  const cancelMove = useCallback(() => {
    // 清除待确认的移动操作
    pendingMoveRef.current = null;
    // 关闭确认对话框
    setShowMoveConfirmDialog(false);
  }, []);

  // 判断节点是否可拖拽
  const canDragNode = useCallback((nodeId: string) => {
    // 根节点不可拖拽
    if (nodeId === 'all-dept') {
      return false;
    }

    // 取消部门拖拽功能 - 所有部门节点都不可拖拽
    if (nodeId.startsWith('dept-')) {
      return false;
    }

    // 只允许人员节点拖拽
    if (nodeId.startsWith('person-')) {
      return true;
    }

    // 其他节点不可拖拽
    return false;
  }, [state.departmentCategories]);

  // 判断节点是否可放置
  const canDropNode = useCallback((draggedId: string, targetId: string) => {
    // 不能拖拽到自己
    if (draggedId === targetId) {
      return false;
    }

    // 只允许人员节点拖拽，所以被拖拽的必须是人员节点
    if (!draggedId.startsWith('person-')) {
      return false;
    }

    // 人员节点不能放置到根节点
    if (targetId === 'all-dept') {
      return false;
    }

    // 人员节点不能放置到其他人员节点下
    if (targetId.startsWith('person-')) {
      return false;
    }

    // 人员节点只能放置到部门节点下
    if (targetId.startsWith('dept-')) {
      return true;
    }

    return false;
  }, [state.departmentCategories]);



  // 查找节点的父节点
  const findParentNode = useCallback((categories: DepartmentCategory[], nodeId: string, parent: DepartmentCategory | null = null): DepartmentCategory | null => {
    for (const category of categories) {
      if (category.id === nodeId) {
        return parent;
      }
      if (category.children?.length) {
        const found = findParentNode(category.children, nodeId, category);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // 检查一个节点是否是另一个节点的子节点
  const isChildOf = useCallback((categories: DepartmentCategory[], childId: string, parentId: string): boolean => {
    const parentNode = findNodeById(categories, parentId);
    if (!parentNode || !parentNode.children) {
      return false;
    }

    for (const child of parentNode.children) {
      if (child.id === childId) {
        return true;
      }
      if (child.children && isChildOf(categories, childId, child.id)) {
        return true;
      }
    }

    return false;
  }, [findNodeById]);

  return {
    departmentCategories: state.departmentCategories,
    currentDepartmentCategory: state.currentCategory,
    isLoading: state.isLoading,
    error: state.error,
    filteredItems,

    // 移动确认对话框状态
    showMoveConfirmDialog,
    moveConfirmDraggedNode,
    moveConfirmTargetNode,

    // 方法
    generateDepartmentTree,
    filterByDepartment,
    addDepartmentOrPerson,
    addDepartment,
    addPerson,
    renameDepartmentOrPerson,
    deleteDepartmentOrPerson,
    updateDepartmentPosition,
    updatePersonPosition,

    // 拖拽相关方法
    handleDrop,
    confirmMove,
    cancelMove,
    canDragNode,
    canDropNode,

    // 事件监听
    onTreeLoaded,
    onTreeUpdated,
    onDepartmentAdded,
    onPersonAdded,
    onNodeRenamed,
    onNodeDeleted
  };
}