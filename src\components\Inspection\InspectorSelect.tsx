import React, { useState, useEffect, useCallback } from 'react';
import { ChevronDown, User, Users, RefreshCw } from 'lucide-react';
import useInspectionAccount from '../../hooks/Inspection/useInspectionAccount';

/**
 * 责任人选择组件属性接口
 */
interface InspectorSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  required?: boolean;
  error?: boolean; // 新增：外部错误状态
}

/**
 * 责任人选项接口
 */
interface InspectorOption {
  value: string;
  label: string;
  personName: string;
  personAlias: string;
}

/**
 * 责任人选择组件
 * 从账户管理中智能获取责任人列表
 */
const InspectorSelect: React.FC<InspectorSelectProps> = ({
  value,
  onChange,
  placeholder = '请选择责任人',
  disabled = false,
  className = '',
  required = false,
  error = false
}) => {
  // 使用账户管理Hook
  const { getAccounts, accounts, isLoading } = useInspectionAccount();

  // 本地状态
  const [isOpen, setIsOpen] = useState(false);
  const [inspectorOptions, setInspectorOptions] = useState<InspectorOption[]>([]);
  const [isLoadingInspectors, setIsLoadingInspectors] = useState(false);

  /**
   * 从账户数据构建责任人选项
   */
  const buildInspectorOptions = useCallback((accountList: typeof accounts) => {
    const options: InspectorOption[] = [];
    const seen = new Set<string>();

    accountList.forEach(account => {
      if (account.inspector && !seen.has(account.inspector)) {
        seen.add(account.inspector);
        
        options.push({
          value: account.inspector,
          label: account.inspector,
          personName: account.person_name || '',
          personAlias: account.person_alias || ''
        });
      }
    });

    // 按姓名排序
    options.sort((a, b) => a.label.localeCompare(b.label, 'zh-CN'));
    
    return options;
  }, []);

  /**
   * 加载责任人选项
   */
  const loadInspectorOptions = useCallback(async () => {
    try {
      setIsLoadingInspectors(true);
      
      // 获取账户数据（使用智能缓存）
      await getAccounts();
      
      // 构建选项
      const options = buildInspectorOptions(accounts);
      setInspectorOptions(options);
      
      console.log('责任人选项加载完成:', options);
    } catch (error) {
      console.error('加载责任人选项失败:', error);
      setInspectorOptions([]);
    } finally {
      setIsLoadingInspectors(false);
    }
  }, [getAccounts, accounts, buildInspectorOptions]);

  /**
   * 初始化时加载数据
   */
  useEffect(() => {
    loadInspectorOptions();
  }, [loadInspectorOptions]);

  /**
   * 当账户数据变化时更新选项
   */
  useEffect(() => {
    if (accounts.length > 0) {
      const options = buildInspectorOptions(accounts);
      setInspectorOptions(options);
    }
  }, [accounts, buildInspectorOptions]);

  /**
   * 处理选项选择
   */
  const handleSelect = (option: InspectorOption) => {
    onChange(option.value);
    setIsOpen(false);
  };

  /**
   * 处理手动刷新
   */
  const handleRefresh = async (e: React.MouseEvent) => {
    e.stopPropagation();
    await loadInspectorOptions();
  };

  /**
   * 获取当前选中的选项
   */
  const selectedOption = inspectorOptions.find(option => option.value === value);

  /**
   * 点击外部关闭下拉框
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.inspector-select-container')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  return (
    <div className={`inspector-select-container relative ${className}`}>
      {/* 选择框 */}
      <div
        className={`
          w-full px-3 py-2 border rounded-lg cursor-pointer transition-colors
          ${disabled
            ? 'bg-gray-100 border-gray-200 cursor-not-allowed'
            : error
              ? 'border-red-300 bg-red-50'
              : isOpen
                ? 'border-blue-500 ring-1 ring-blue-500'
                : 'border-gray-300 hover:border-gray-400'
          }
        `}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0">
            <User className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
            <span className={`truncate ${selectedOption ? 'text-gray-900' : 'text-gray-500'}`}>
              {selectedOption ? selectedOption.label : placeholder}
            </span>
          </div>
          
          <div className="flex items-center ml-2">
            {/* 刷新按钮 */}
            {!disabled && (
              <button
                type="button"
                onClick={handleRefresh}
                disabled={isLoadingInspectors || isLoading}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors mr-1"
                title="刷新责任人列表"
              >
                <RefreshCw className={`h-3 w-3 ${(isLoadingInspectors || isLoading) ? 'animate-spin' : ''}`} />
              </button>
            )}
            
            {/* 下拉箭头 */}
            <ChevronDown 
              className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
            />
          </div>
        </div>
      </div>

      {/* 下拉选项 */}
      {isOpen && !disabled && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto">
          {isLoadingInspectors || isLoading ? (
            <div className="px-3 py-2 text-center text-gray-500">
              <RefreshCw className="h-4 w-4 animate-spin inline mr-2" />
              加载中...
            </div>
          ) : inspectorOptions.length > 0 ? (
            <>
              {/* 选项列表 */}
              {inspectorOptions.map((option) => (
                <div
                  key={option.value}
                  className={`
                    px-3 py-2 cursor-pointer transition-colors flex items-center
                    ${option.value === value 
                      ? 'bg-blue-50 text-blue-700' 
                      : 'hover:bg-gray-50'
                    }
                  `}
                  onClick={() => handleSelect(option)}
                >
                  <User className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                  <span className="truncate">{option.label}</span>
                  {option.value === value && (
                    <div className="ml-auto text-blue-600">
                      ✓
                    </div>
                  )}
                </div>
              ))}
            </>
          ) : (
            <div className="px-3 py-2 text-center text-gray-500">
              <Users className="h-4 w-4 inline mr-2" />
              暂无责任人数据
              <div className="text-xs text-gray-400 mt-1">
                请先在账户管理中添加巡检账户
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InspectorSelect;
