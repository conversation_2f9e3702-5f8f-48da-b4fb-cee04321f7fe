import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    chunkSizeWarningLimit: 1000, // 增加警告阈值到1000kb
    rollupOptions: {
      output: {
        manualChunks: {
          // 将React相关库打包到一个chunk
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          // 将UI组件库打包到一个chunk
          'ui-vendor': ['antd', '@ant-design/icons'],
          // 将图表和数据处理库打包到一个chunk
          'data-vendor': ['recharts', 'xlsx', 'exceljs', 'docx'],
          // 将PDF和图像处理库打包到一个chunk
          'pdf-vendor': ['jspdf', 'jspdf-autotable', 'html2canvas'],
        }
      }
    }
  }
});
