/**
 * 通用格式化工具函数
 * 统一处理各种数据格式化需求，避免代码重复
 */

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param precision 小数位数，默认2位
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number, precision: number = 2): string {
  // 处理无效值
  if (bytes == null || isNaN(bytes) || bytes < 0) {
    return '未知大小';
  }

  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  // 确保不超出数组范围
  const sizeIndex = Math.min(i, sizes.length - 1);
  const value = bytes / Math.pow(k, sizeIndex);

  return `${value.toFixed(precision)} ${sizes[sizeIndex]}`;
}

/**
 * 格式化存储大小（兼容旧版本接口）
 * @param bytes 字节数
 * @returns 格式化后的存储大小字符串
 */
export function formatStorageSize(bytes: number): string {
  return formatFileSize(bytes, 2);
}

/**
 * 将毫秒转换为可读的时间格式
 * @param ms 毫秒数
 * @returns 格式化后的时间字符串
 */
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}毫秒`;
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}秒`;
  } else if (ms < 3600000) {
    return `${(ms / 60000).toFixed(1)}分钟`;
  } else {
    return `${(ms / 3600000).toFixed(1)}小时`;
  }
}

/**
 * 格式化相对时间
 * @param timestamp 时间戳（毫秒）
 * @returns 相对时间字符串
 */
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    const minutes = Math.floor(diff / 60000);
    return `${minutes}分钟前`;
  } else if (diff < 86400000) { // 24小时内
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  } else if (diff < 2592000000) { // 30天内
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  } else {
    // 超过30天显示具体日期
    return new Date(timestamp).toLocaleDateString('zh-CN');
  }
}

/**
 * 格式化巡检时间戳为用户友好的日期时间格式
 * @param timestamp 时间戳（秒级或毫秒级）
 * @returns 格式化后的日期时间字符串 (YYYY-MM-DD HH:mm:ss)
 */
export function formatInspectionDateTime(timestamp: number): string {
  if (!timestamp || timestamp <= 0) {
    return '未知时间';
  }

  // 判断是秒级还是毫秒级时间戳
  // 如果时间戳小于10位数，认为是秒级时间戳
  const milliseconds = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;

  try {
    const date = new Date(milliseconds);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '无效时间';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.warn('巡检时间格式化失败:', error);
    return '时间格式错误';
  }
}

/**
 * 格式化百分比
 * @param value 数值（0-100）
 * @param precision 小数位数，默认1位
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, precision: number = 1): string {
  if (value == null || isNaN(value)) {
    return '0%';
  }
  return `${value.toFixed(precision)}%`;
}

/**
 * 格式化数字，添加千分位分隔符
 * @param num 数字
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number): string {
  if (num == null || isNaN(num)) {
    return '0';
  }
  return num.toLocaleString('zh-CN');
}

/**
 * 估算字符串显示宽度（考虑中文字符）
 * @param str 字符串
 * @returns 估算宽度
 */
export function getStringWidth(str: string): number {
  if (!str) return 0;

  // 中文字符计为2个单位宽度，其他字符计为1个单位宽度
  let width = 0;
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    width += (charCode > 127 || charCode === 94) ? 2 : 1;
  }

  return width;
}

/**
 * 截断字符串并添加省略号
 * @param str 原字符串
 * @param maxLength 最大长度
 * @param suffix 后缀，默认为'...'
 * @returns 截断后的字符串
 */
export function truncateString(str: string, maxLength: number, suffix: string = '...'): string {
  if (!str || str.length <= maxLength) {
    return str;
  }
  return str.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * 解析存储容量字符串为字节数
 * @param storage 存储容量字符串，如 "1.5 GB"
 * @returns 字节数
 */
export function parseStorageToBytes(storage: string): number {
  if (!storage) return 0;
  
  const value = parseFloat(storage);
  if (isNaN(value)) return 0;
  
  const upperStorage = storage.toUpperCase();
  
  if (upperStorage.includes('TB')) {
    return value * 1024 * 1024 * 1024 * 1024;
  } else if (upperStorage.includes('GB')) {
    return value * 1024 * 1024 * 1024;
  } else if (upperStorage.includes('MB')) {
    return value * 1024 * 1024;
  } else if (upperStorage.includes('KB')) {
    return value * 1024;
  } else {
    return value; // 假设是字节
  }
}

/**
 * 解析存储容量字符串为GB数值（兼容旧版本）
 * @param storage 存储容量字符串
 * @returns GB数值
 */
export function parseStorageToGB(storage: string): number {
  const bytes = parseStorageToBytes(storage);
  return bytes / (1024 * 1024 * 1024);
}
