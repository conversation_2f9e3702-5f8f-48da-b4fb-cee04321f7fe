import { useState, useCallback } from 'react';

interface DialogState<T = any> {
  isOpen: boolean;
  isLoading: boolean;
  data?: T;
}

interface UseDialogStateOptions<T = any> {
  // 初始数据
  initialData?: T;
  // 对话框打开时的回调
  onOpen?: (data?: T) => void;
  // 对话框关闭时的回调
  onClose?: () => void;
}

/**
 * 对话框状态Hook
 * 提供对话框的基本状态管理，简化对话框组件的创建
 */
export function useDialogState<T = any>({
  initialData,
  onOpen,
  onClose
}: UseDialogStateOptions<T> = {}) {
  // 对话框状态
  const [state, setState] = useState<DialogState<T>>({
    isOpen: false,
    isLoading: false,
    data: initialData
  });

  // 打开对话框
  const open = useCallback((data?: T) => {
    setState(prev => ({
      ...prev,
      isOpen: true,
      data: data !== undefined ? data : prev.data
    }));

    // 调用打开回调
    if (onOpen) {
      onOpen(data);
    }
  }, [onOpen]);

  // 关闭对话框
  const close = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false
    }));

    // 调用关闭回调
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  // 设置加载状态
  const setLoading = useCallback((isLoading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading
    }));
  }, []);

  // 设置数据
  const setData = useCallback((data: T) => {
    setState(prev => ({
      ...prev,
      data
    }));
  }, []);

  // 重置状态
  const reset = useCallback(() => {
    setState({
      isOpen: false,
      isLoading: false,
      data: initialData
    });
  }, [initialData]);

  return {
    // 状态
    isOpen: state.isOpen,
    isLoading: state.isLoading,
    data: state.data,

    // 方法
    open,
    close,
    setLoading,
    setData,
    reset
  };
}
