import React, { useCallback, useEffect, useRef, useState } from 'react';
import { DeviceCategory } from '../../../types/inventory';
import { DepartmentCategory } from '../../../hooks/Inventory/useCategoryTree';
import useCategoryTree from '../../../hooks/Inventory/useCategoryTree';
import { useCustomDrag } from '../../../hooks/base/useCustomDrag';
import DepartmentService from '../../../services/Inventory/departmentService';
import ConfirmDialog from '../../../components/ui/ConfirmDialog';
import ExistingPersonAssociationDialog from '../../../components/ExistingPersonAssociationDialog';
import CustomAlert from '../../../components/CustomAlert';
import {
  getValidationTypeByCategory,
  validateField
} from '../../../utils/fieldValidation';
import { ValidatedInput } from '../../../components/ui/FormComponents';
import { getCategoryIconConfig, CATEGORY_ICON_CONFIG, IconType, ICON_DESCRIPTIONS } from '../../../hooks/Inventory/useCategoryIcons';
import { getIconComponent, ICON_NAME_MAP } from '../../../utils/iconMapping';
import IconSelector from '../../../components/IconSelector';
import { CategoryIconService } from '../../../services/Inventory/categoryIconService';
import InventoryService from '../../../services/Inventory/inventoryService';
import * as LucideIcons from 'lucide-react';
import {
  ChevronDown,
  ChevronRight,
  Folder,
  Edit,
  Trash2,
  AlertTriangle,
  Plus,
  FileDown,
  Building2,
  User,
  Cpu,
  Building,
  MapPin
} from 'lucide-react';

interface CategoryTreeProps {
  categories: DeviceCategory[] | DepartmentCategory[];
  currentCategory: string;
  onSelectCategory: (categoryId: string) => void;
  originalCategories?: DeviceCategory[] | DepartmentCategory[]; // 用于比较是否是过滤后的结果
  isFiltered?: boolean; // 是否处于搜索过滤状态
  categoryMode?: 'device' | 'department'; // 分类模式：设备或部门
  onRenameCategory?: (categoryId: string, categoryName: string) => Promise<void>; // 重命名回调
  onDeleteCategory?: (categoryId: string) => Promise<void>; // 删除回调
  onAddInventory?: (categoryId?: string) => void; // 添加台账回调，可以传递分类ID
  onExportCategory?: (categoryId: string, categoryName: string) => void; // 导出分类回调
  // 新增的设备父类和子类操作回调
  onUpdateParentCategory?: (curParentCategoryName: string, newParentCategoryName: string) => Promise<void>; // 更新设备父类
  onDeleteParentCategory?: (parentCategoryName: string) => Promise<void>; // 删除设备父类
  onUpdateSubCategory?: (curSubCategoryName: string, newSubCategoryName: string) => Promise<void>; // 更新设备子类
  onDeleteSubCategory?: (subCategoryName: string) => Promise<void>; // 删除设备子类
  // 拖拽相关属性
  draggable?: boolean; // 是否支持拖拽
  onDrop?: (draggedId: string, targetId: string) => void; // 拖拽放置回调
  canDrag?: (nodeId: string) => boolean; // 判断节点是否可拖拽
  canDrop?: (draggedId: string, targetId: string) => boolean; // 判断节点是否可放置
  // 更改部门回调
  onChangeDepartment?: (personId: string, personName: string) => void; // 更改人员所属部门回调
  // 编辑人员信息回调
  onEditPerson?: (personId: string) => void; // 编辑人员详细信息回调
}

// 计算缩进距离
// 根据分类模式和层级深度计算缩进距离
// 部门树使用递减缩进策略，设备树保持原样
const calculateIndent = (depth: number, mode: 'device' | 'department'): string => {
  // 设备树使用原始缩进策略
  if (mode === 'device') {
    return `${depth * 16 + 8}px`;
  }

  // 部门树使用递减缩进策略
  // 基础缩进
  const baseIndent = 8;

  // 递减缩进策略
  let totalIndent = baseIndent;

  for (let i = 0; i < depth; i++) {
    // 根据层级深度决定缩进增量
    if (i === 0) {
      totalIndent += 12; // 第一级缩进12px
    } else if (i === 1) {
      totalIndent += 10; // 第二级缩进10px
    } else if (i === 2) {
      totalIndent += 8;  // 第三级缩进8px
    } else {
      totalIndent += 6;  // 更深层级缩进6px
    }
  }

  return `${totalIndent}px`;
};

// 根据分类模式返回图标类名
const getIconClassName = (mode: 'device' | 'department', colorClass: string): string => {
  // 设备树使用原始图标尺寸，部门树使用较小的图标尺寸
  return mode === 'department'
    ? `w-3.5 h-3.5 mr-0.5 ${colorClass}`
    : `w-4 h-4 mr-1 ${colorClass}`;
};

// 自定义拖拽功能，不再使用全局拖拽状态

const CategoryTree: React.FC<CategoryTreeProps> = ({
  categories,
  currentCategory,
  onSelectCategory,
  isFiltered = false,
  categoryMode = 'device',
  onRenameCategory,
  onDeleteCategory,
  onAddInventory,
  onExportCategory,
  onUpdateParentCategory,
  onDeleteParentCategory,
  onUpdateSubCategory,
  onDeleteSubCategory,
  draggable = false,
  onDrop,
  canDrag,
  canDrop,
  onChangeDepartment,
  onEditPerson
}) => {
  // 检查一个节点是否是另一个节点的子节点
  const isChildOf = (potentialChild: DepartmentCategory, potentialParent: DepartmentCategory): boolean => {
    // 如果潜在的子节点是根节点，则它不是任何节点的子节点
    if (potentialChild.id === 'all-dept') {
      return false;
    }

    // 如果潜在的父节点没有子节点，则返回false
    if (!potentialParent.children) {
      return false;
    }

    // 检查直接子节点
    for (const child of potentialParent.children) {
      if (child.id === potentialChild.id) {
        return true;
      }

      // 递归检查子节点的子节点
      if (child.children && isChildOf(potentialChild, child)) {
        return true;
      }
    }

    return false;
  };

  // 查找节点
  const findNodeById = (nodes: (DeviceCategory | DepartmentCategory)[], id: string): DepartmentCategory | null => {
    for (const node of nodes) {
      if (node.id === id) {
        return node as DepartmentCategory;
      }

      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) {
          return found;
        }
      }
    }

    return null;
  };

  // 检查是否可以放置
  const checkCanDrop = (draggedId: string, targetId: string): boolean => {
    // 不允许拖拽到自己
    if (draggedId === targetId) {
      return false;
    }

    // 查找拖拽节点和目标节点
    const draggedNode = findNodeById(categories, draggedId);
    const targetNode = findNodeById(categories, targetId);

    if (!draggedNode || !targetNode) {
      return false;
    }

    // 不允许拖拽到自己的子节点（避免循环引用）
    if (isChildOf(targetNode, draggedNode)) {
      return false;
    }

    // 使用自定义的canDrop函数
    return canDrop?.(draggedId, targetId) ?? false;
  };

  // 使用自定义拖拽Hook
  const {
    dragState,
    hoverTargetId,
    canDropOnTarget,
    dragHandlers,
    resetDragState
  } = useCustomDrag({
    enabled: draggable && categoryMode === 'department',
    canDrag: canDrag,
    canDrop: checkCanDrop,
    onDrop: (targetId, draggedId) => {
      onDrop?.(draggedId, targetId);
    },
    requireLongPress: true, // 启用长按拖动
    longPressDelay: 500, // 设置长按时间为500毫秒
    dragImageStyle: {
      position: 'absolute',
      pointerEvents: 'none',
      zIndex: '9999',
      opacity: '0.7',
      backgroundColor: '#f0f9ff',
      border: '1px solid #93c5fd',
      borderRadius: '4px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      padding: '4px',
      maxWidth: '300px',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis'
    },
    dropTargetClassName: 'bg-blue-100 border border-blue-300 rounded'
  });

  // 组件卸载时清理拖拽状态
  useEffect(() => {
    return () => {
      resetDragState();
    };
  }, [resetDragState]);

  // 树状图容器引用
  const treeContainerRef = useRef<HTMLDivElement>(null);

  // 确保树状图容器保持焦点的辅助函数
  const ensureTreeFocus = useCallback(() => {
    setTimeout(() => {
      if (treeContainerRef.current) {
        treeContainerRef.current.focus();
      }
    }, 0);
  }, []);

  // 组件挂载后自动聚焦，以便接收键盘事件
  useEffect(() => {
    ensureTreeFocus();
  }, [ensureTreeFocus]);

  // 当分类模式切换时，重新聚焦树容器
  useEffect(() => {
    // 延迟聚焦，确保DOM更新完成
    const timer = setTimeout(() => {
      ensureTreeFocus();
    }, 100);

    return () => clearTimeout(timer);
  }, [categoryMode, ensureTreeFocus]);

  // 监听现有人员关联事件
  useEffect(() => {
    if (categoryMode !== 'department') return;

    const departmentService = DepartmentService.getInstance();

    const handleExistingPersonAssociated = (eventData: {
      personName: string;
      alias?: string;
      departmentId: string;
      message?: string;
    }) => {
      console.log('收到现有人员关联事件:', eventData);

      // 查找部门名称
      let departmentName = '';
      const findDepartmentName = (categories: DepartmentCategory[], deptId: string): string => {
        for (const category of categories) {
          if (category.id === deptId) {
            return category.name;
          }
          if (category.children) {
            const found = findDepartmentName(category.children, deptId);
            if (found) return found;
          }
        }
        return '';
      };

      departmentName = findDepartmentName(categories as DepartmentCategory[], eventData.departmentId);

      // 显示现有人员关联对话框
      setExistingPersonDialog({
        isOpen: true,
        personName: eventData.personName,
        alias: eventData.alias || '',
        departmentName
      });
    };

    // 添加事件监听器
    departmentService.on('existing-person-associated', handleExistingPersonAssociated);

    // 清理函数
    return () => {
      departmentService.off('existing-person-associated', handleExistingPersonAssociated);
    };
  }, [categoryMode, categories]);
  // 使用自定义Hook获取状态和方法
  const {
    // 状态
    expandedNodes,
    contextMenu,
    renameDialog,
    renameValue,
    isRenaming,
    deleteDialog,
    isDeleting,
    inlineEdit,
    inlineEditInputRef,
    renameConfirmDialog,
    alertState,

    // 方法
    setRenameValue,
    closeAlert,
    toggleNode,
    handleContextMenu,
    handleRenameClick,
    executeRename,
    handleDeleteClick,
    confirmDelete,
    closeRenameDialog,
    closeDeleteDialog,
    closeContextMenu,
    startInlineEdit,
    updateInlineEditValue,
    handleInlineEditComplete,
    handleInlineEditKeyDown,
    confirmRename,
    closeRenameConfirmDialog
  } = useCategoryTree({
    categories,
    currentCategory,
    isFiltered,
    categoryMode,
    onRenameCategory,
    onDeleteCategory,
    onUpdateParentCategory,
    onDeleteParentCategory,
    onUpdateSubCategory,
    onDeleteSubCategory,
    onSelectCategory
  });

  // 菜单导航状态
  const [menuNavigation, setMenuNavigation] = useState({
    isNavigatingMenu: false,
    selectedMenuItemIndex: -1
  });

  // 验证状态
  const [renameError, setRenameError] = useState<string | null>(null);

  // 图标选择相关状态
  const [selectedIcon, setSelectedIcon] = useState<IconType | undefined>();
  const [showIconSelector, setShowIconSelector] = useState(false);

  // 获取分类图标服务实例
  const categoryIconService = CategoryIconService.getInstance();

  // 获取当前分类的图标（包括默认图标和自定义图标）
  const getCurrentCategoryIcon = useCallback((categoryId: string): IconType | undefined => {
    if (categoryMode !== 'device') return undefined;

    // 首先尝试获取自定义图标
    const customIcon = categoryIconService.getIcon(categoryId);
    if (customIcon) {
      return customIcon as IconType;
    }

    // 如果没有自定义图标，获取当前分类的默认图标

    // 递归查找分类（包括子分类）
    const findCategoryRecursive = (cats: any[], targetId: string): DeviceCategory | undefined => {
      for (const cat of cats) {
        if (cat.id === targetId) {
          return cat;
        }
        if (cat.children && cat.children.length > 0) {
          const found = findCategoryRecursive(cat.children, targetId);
          if (found) return found;
        }
      }
      return undefined;
    };

    const currentCategory = findCategoryRecursive(categories, categoryId) as DeviceCategory;

    if (currentCategory) {
      // 如果分类有 customIcon 属性（来自后端或默认配置）
      if (currentCategory.customIcon) {
        return currentCategory.customIcon as IconType;
      }

      // 使用图标配置系统获取默认图标
      let categoryDepth = 0;
      if (categoryId === 'all') {
        categoryDepth = 0;
      } else if (categoryId.startsWith('parent-') && !categoryId.includes('-', 7)) {
        categoryDepth = 1;
      } else if (categoryId.startsWith('parent-') && categoryId.includes('-', 7)) {
        categoryDepth = 2;
      }

      const iconConfig = getCategoryIconConfig(
        currentCategory.name,
        currentCategory.id,
        categoryDepth,
        categories as DeviceCategory[],
        undefined // 不传入自定义图标，获取默认图标
      );

      return iconConfig.icon as IconType;
    }

    return undefined;
  }, [categoryMode, categoryIconService, categories]);



  // 自定义重命名处理函数
  const handleCustomRenameClick = useCallback(() => {
    // 调用原始的重命名处理函数
    handleRenameClick();

    // 获取当前分类的图标并设置到状态中
    const currentIcon = getCurrentCategoryIcon(contextMenu.categoryId);
    setSelectedIcon(currentIcon);
    setShowIconSelector(false); // 确保图标选择器是关闭的
  }, [handleRenameClick, getCurrentCategoryIcon, contextMenu.categoryId]);

  // 自定义执行重命名函数
  const executeCustomRename = useCallback(async () => {
    try {
      // 先执行原始的重命名操作
      await executeRename();

      // 如果重命名成功且是设备分类模式，保存图标并更新UI
      if (categoryMode === 'device' && selectedIcon) {
        const categoryId = renameDialog.categoryId;
        categoryIconService.setIcon(categoryId, selectedIcon);
        console.log(`已保存分类 ${categoryId} 的图标: ${selectedIcon}`);

        // 直接更新当前分类树中的节点图标
        try {
          const inventoryService = InventoryService.getInstance();
          const currentState = inventoryService.getState();

          // 深拷贝分类树数据
          const updatedCategories = JSON.parse(JSON.stringify(currentState.deviceCategories));

          // 查找并更新目标节点的图标
          const updateNodeIcon = (nodes: any[]): boolean => {
            for (const node of nodes) {
              if (node.id === categoryId) {
                node.customIcon = selectedIcon;
                console.log(`已更新节点 ${categoryId} 的图标为: ${selectedIcon}`);
                return true;
              }
              if (node.children && updateNodeIcon(node.children)) {
                return true;
              }
            }
            return false;
          };

          // 更新节点图标
          updateNodeIcon(updatedCategories);

          // 更新状态
          inventoryService['stateService'].updateState({ deviceCategories: updatedCategories });
          inventoryService.forceUpdate();

          console.log('节点图标已更新并刷新UI');
        } catch (refreshError) {
          console.error('更新节点图标失败:', refreshError);
        }
      }
    } catch (error) {
      console.error('重命名失败:', error);
      // 错误处理由原始函数处理
    }
  }, [executeRename, categoryMode, selectedIcon, renameDialog.categoryId, categoryIconService]);

  // 自定义展开节点函数，包含关闭右键菜单的逻辑
  const handleToggleNode = useCallback((id: string, e: React.MouseEvent) => {
    // 如果右键菜单可见，关闭它
    if (contextMenu.visible) {
      closeContextMenu();
    }

    // 调用原始的 toggleNode 函数
    toggleNode(id, e);
  }, [contextMenu.visible, closeContextMenu, toggleNode]);

  // 现有人员关联对话框状态
  const [existingPersonDialog, setExistingPersonDialog] = useState({
    isOpen: false,
    personName: '',
    alias: '',
    departmentName: ''
  });

  // 获取重命名操作的文本
  const getRenameText = (categoryId: string, categoryMode: string) => {
    if (categoryMode === 'device') {
      if (categoryId.startsWith('parent-') && !categoryId.includes('-', 'parent-'.length)) {
        return '修改分类'; // 一级分类
      } else if (categoryId.startsWith('parent-') && categoryId.includes('-', 'parent-'.length)) {
        return '修改类型'; // 二级分类
      }
    }
    return '修改名称'; // 默认情况（部门、人员等）
  };

  // 验证重命名输入
  const validateRenameInput = (value: string, categoryId: string) => {
    const validationType = getValidationTypeByCategory(categoryId, categoryMode);
    const validation = validateField(validationType, value);

    setRenameError(validation.isValid ? null : validation.error || '验证失败');
    return validation.isValid;
  };

  // 双击检测状态 - 防止双击时执行单击
  const pendingClickRef = useRef<(() => void) | null>(null);

  // 处理节点单击
  const handleNodeClick = useCallback((category: DeviceCategory | DepartmentCategory, e: React.MouseEvent) => {
    e.stopPropagation();

    // 如果右键菜单可见，关闭它
    if (contextMenu.visible) {
      closeContextMenu();
    }

    // 将单击操作放入下一个事件循环，给双击事件机会取消它
    pendingClickRef.current = () => {
      onSelectCategory(category.id);
      ensureTreeFocus();
      pendingClickRef.current = null;
    };

    // 使用 requestAnimationFrame 延迟执行，确保双击事件能够取消单击
    requestAnimationFrame(() => {
      if (pendingClickRef.current) {
        pendingClickRef.current();
      }
    });
  }, [onSelectCategory, ensureTreeFocus, contextMenu.visible, closeContextMenu]);

  // 处理节点双击
  const handleNodeDoubleClick = useCallback((category: DeviceCategory | DepartmentCategory, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault(); // 防止双击选中文本

    // 取消待执行的单击操作
    pendingClickRef.current = null;

    // 立即执行双击逻辑
    startInlineEdit(category, e);
  }, [startInlineEdit]);

  // 人员悬停提示状态
  const [personTooltip, setPersonTooltip] = useState<{
    visible: boolean;
    personId: string;
    x: number;
    y: number;
    personInfo: any;
    isLoading: boolean;
  }>({
    visible: false,
    personId: '',
    x: 0,
    y: 0,
    personInfo: null,
    isLoading: false
  });

  // 当右键菜单关闭时，重置菜单导航状态
  useEffect(() => {
    if (!contextMenu.visible) {
      setMenuNavigation({
        isNavigatingMenu: false,
        selectedMenuItemIndex: -1
      });
    }
  }, [contextMenu.visible]);

  // 处理人员悬停
  const handlePersonMouseEnter = function(e: React.MouseEvent, personId: string) {
    const rect = e.currentTarget.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算提示框的预估宽度和高度
    const tooltipWidth = 320;
    const tooltipHeight = 200;

    let x = rect.right + 10;
    let y = rect.top + rect.height / 2;

    // 如果右侧空间不够，显示在左侧
    if (x + tooltipWidth > viewportWidth - 20) {
      x = rect.left - tooltipWidth - 10;
    }

    // 如果左侧空间也不够，显示在右侧但调整位置
    if (x < 20) {
      x = rect.right + 10;
      if (x + tooltipWidth > viewportWidth - 20) {
        x = viewportWidth - tooltipWidth - 20;
      }
    }

    // 垂直方向居中，但确保不超出视口
    if (y - tooltipHeight / 2 < 20) {
      y = 20 + tooltipHeight / 2;
    } else if (y + tooltipHeight / 2 > viewportHeight - 20) {
      y = viewportHeight - tooltipHeight / 2 - 20;
    }

    // 显示加载状态
    setPersonTooltip({
      visible: true,
      personId: personId,
      x: x,
      y: y,
      personInfo: null,
      isLoading: true
    });

    // 获取人员信息 - 使用缓存机制
    // 从新的ID格式中提取原始人员ID: person-1-dept-2 -> 1
    let numericId: number;

    // 首先尝试从categories中找到对应的节点，获取originalPersonId
    const findPersonNode = (nodes: any[], targetId: string): any => {
      for (const node of nodes) {
        if (node.id === targetId) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = findPersonNode(node.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const personNode = findPersonNode(categories, personId);
    if (personNode && personNode.originalPersonId) {
      numericId = personNode.originalPersonId;
    } else {
      // 回退到旧的解析方式（兼容性）
      const match = personId.match(/^person-(\d+)/);
      if (match) {
        numericId = parseInt(match[1], 10);
      } else {
        numericId = NaN;
      }
    }

    console.log('开始获取人员信息:', { personId: personId, numericId: numericId });

    if (isNaN(numericId)) {
      console.warn('无效的人员ID:', personId);
      setPersonTooltip(function(prev) {
        return {
          visible: prev.visible,
          personId: prev.personId,
          x: prev.x,
          y: prev.y,
          personInfo: null,
          isLoading: false
        };
      });
      return;
    }

    // 使用静态导入的DepartmentService的缓存机制
    try {
      const departmentService = DepartmentService.getInstance();

      // 使用缓存机制获取人员信息
      departmentService.getPersonInfo(numericId).then(function(person) {
        if (person) {
          console.log('查找到的人员信息:', person);
          setPersonTooltip(function(prev) {
            return {
              visible: prev.visible,
              personId: prev.personId,
              x: prev.x,
              y: prev.y,
              personInfo: person,
              isLoading: false
            };
          });
        } else {
          console.warn('未找到对应ID的人员信息:', numericId);
          setPersonTooltip(function(prev) {
            return {
              visible: prev.visible,
              personId: prev.personId,
              x: prev.x,
              y: prev.y,
              personInfo: null,
              isLoading: false
            };
          });
        }
      }).catch(function(error: any) {
        console.error('获取人员信息失败:', error);
        setPersonTooltip(function(prev) {
          return {
            visible: prev.visible,
            personId: prev.personId,
            x: prev.x,
            y: prev.y,
            personInfo: null,
            isLoading: false
          };
        });
      });
    } catch (error) {
      console.error('获取人员信息失败:', error);
      setPersonTooltip(function(prev) {
        return {
          visible: prev.visible,
          personId: prev.personId,
          x: prev.x,
          y: prev.y,
          personInfo: null,
          isLoading: false
        };
      });
    }
  };

  // 处理人员悬停离开
  const handlePersonMouseLeave = function() {
    setPersonTooltip({
      visible: false,
      personId: '',
      x: 0,
      y: 0,
      personInfo: null,
      isLoading: false
    });
  };

  // 获取岗位密级显示文本
  const getSecurityLevelText = function(level: number): string {
    switch (level) {
      case 0: return '非涉密人员';
      case 1: return '一般涉密人员';
      case 2: return '重要涉密人员';
      case 3: return '核心涉密人员';
      default: return '未知';
    }
  };

  // 获取岗位密级颜色
  const getSecurityLevelColor = function(level: number): string {
    switch (level) {
      case 0: return 'text-gray-600';
      case 1: return 'text-blue-600';
      case 2: return 'text-orange-600';
      case 3: return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // 获取设备图标映射 - 使用新的图标配置系统
  const getDeviceIcon = (category: DeviceCategory | DepartmentCategory, depth: number) => {
    // 确定分类层级深度
    let categoryDepth = 0;
    if (category.id === 'all') {
      categoryDepth = 0; // 根节点
    } else if (category.id.startsWith('parent-') && !category.id.includes('-', 7)) {
      categoryDepth = 1; // 一级分类
    } else if (category.id.startsWith('parent-') && category.id.includes('-', 7)) {
      categoryDepth = 2; // 二级分类
    } else {
      categoryDepth = depth + 1; // 使用传入的depth
    }

    // 获取图标配置，优先使用自定义图标
    const customIcon = (category as DeviceCategory).customIcon;
    const iconConfig = getCategoryIconConfig(
      category.name,
      category.id,
      categoryDepth,
      categories as DeviceCategory[],
      customIcon
    );

    // 使用共享的图标映射
    const iconName = ICON_NAME_MAP[iconConfig.icon as keyof typeof ICON_NAME_MAP];
    const IconComponent = iconName ? (LucideIcons as any)[iconName] : Folder;

    // 处理新添加的分类
    if (category.id.startsWith('new-') || (category as DeviceCategory).isNew) {
      return <Cpu className={getIconClassName('device', 'text-gray-700')} />;
    }

    return <IconComponent className={getIconClassName('device', iconConfig.color)} />;
  };

  // 获取部门图标
  const getDepartmentIcon = (category: DeviceCategory | DepartmentCategory) => {
    // 根节点 - 全部部门
    if (category.id === 'all-dept') {
      return <Building2 className="w-3.5 h-3.5 mr-0.5 text-blue-600" />;
    }

    // 判断是部门还是人员
    if (category.id.startsWith('person-') || category.id.startsWith('resp-')) {
      // 所有人员节点统一使用同一个图标
      return <User className="w-3.5 h-3.5 mr-0.5 text-purple-600" />;
    } else if (category.id.startsWith('dept-')) {
      // 所有部门节点使用位置图标，更符合部门概念
      return <MapPin className="w-3.5 h-3.5 mr-0.5 text-blue-600" />;
    }

    // 其他未知类型的节点使用默认图标
    return <Folder className="w-3.5 h-3.5 mr-0.5 text-gray-600" />;
  };

  // 渲染内联编辑输入框
  const renderInlineEdit = (category: DeviceCategory | DepartmentCategory) => {
    if (inlineEdit.isEditing && inlineEdit.nodeId === category.id) {
      return (
        <div className="absolute inset-0 z-10">
          <input
            ref={inlineEditInputRef}
            type="text"
            className="w-full h-full px-1 py-0 border border-blue-500 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
            value={inlineEdit.editValue}
            onChange={(e) => updateInlineEditValue(e.target.value)}
            onKeyDown={handleInlineEditKeyDown}
            onBlur={handleInlineEditComplete}
            onClick={(e) => e.stopPropagation()}
            autoFocus
          />
        </div>
      );
    }
    return null;
  };

  // 渲染节点内容
  const renderNodeContent = (category: DeviceCategory | DepartmentCategory, hasChildren: boolean | undefined, isExpanded: boolean, depth: number) => {
    return (
      <>
        {hasChildren ? (
          <span
            className={`inline-flex items-center justify-center text-gray-500 ${categoryMode === 'department' ? 'w-3 h-3 mr-0.5' : 'w-4 h-4 mr-1'}`}
            onClick={(e) => handleToggleNode(category.id, e)}
          >
            {isExpanded ?
              <ChevronDown className={categoryMode === 'department' ? 'w-3 h-3' : 'w-4 h-4'} /> :
              <ChevronRight className={categoryMode === 'department' ? 'w-3 h-3' : 'w-4 h-4'} />}
          </span>
        ) : (
          <span className={categoryMode === 'department' ? 'w-3 h-3 mr-0.5' : 'w-4 h-4 mr-1'}></span>
        )}

        {categoryMode === 'device'
          ? getDeviceIcon(category, depth)
          : getDepartmentIcon(category)}

        <div className="flex-1 relative min-w-0">
          {renderInlineEdit(category)}
          <div className={`flex w-full ${inlineEdit.isEditing && inlineEdit.nodeId === category.id ? 'invisible' : 'visible'}`}>
            <span className="truncate">
              {category.name}
            </span>
            <span className="ml-auto text-xs whitespace-nowrap category-count" style={{ color: '#60A5FA' }}>({category.count})</span>
          </div>
        </div>
      </>
    );
  };

  // 递归渲染分类树
  const renderTree = (items: DeviceCategory[] | DepartmentCategory[], depth = 0) => {
    return items.map(category => {
      const hasChildren = category.children && category.children.length > 0;
      const isExpanded = expandedNodes.has(category.id);
      const isSelected = currentCategory === category.id;
      const isDraggable = draggable && categoryMode === 'department';
      const isDragging = dragState.isDragging && dragState.draggedId === category.id;
      const isDropTarget = hoverTargetId === category.id && canDropOnTarget;

      return (
        <div key={category.id} className="select-none">
          {isDraggable ? (
            <div
              className={`${isDropTarget ? 'bg-blue-100 border border-blue-300 rounded' : ''}`}
              onMouseDown={(e) => dragHandlers.onMouseDown(e, category.id, category)}
              onMouseEnter={(e) => dragHandlers.onMouseEnter(e, category.id)}
              onMouseLeave={(e) => dragHandlers.onMouseLeave(e, category.id)}
            >
              <div
                className={`flex items-center ${categoryMode === 'department' ? 'py-0.5' : 'py-1'} px-1 my-0.5 rounded-sm cursor-pointer hover:bg-blue-50 ${
                  isSelected ? 'bg-blue-100 text-blue-700 font-medium' : ''
                } ${isDragging ? 'opacity-50' : ''}`}
                style={{ paddingLeft: calculateIndent(depth, categoryMode) }}
                onClick={(e) => handleNodeClick(category, e)}
                onDoubleClick={(e) => handleNodeDoubleClick(category, e)}
                onContextMenu={(e) => handleContextMenu(e, category)}
                onMouseEnter={categoryMode === 'department' && (category.id.startsWith('person-') || category.originalPersonId) ? function(e) { return handlePersonMouseEnter(e, category.id); } : undefined}
                onMouseLeave={categoryMode === 'department' && (category.id.startsWith('person-') || category.originalPersonId) ? handlePersonMouseLeave : undefined}
                data-node-id={category.id} // 添加节点ID属性
              >
                {renderNodeContent(category, hasChildren, isExpanded, depth)}
              </div>
            </div>
          ) : (
            <div
              className={`flex items-center ${categoryMode === 'department' ? 'py-0.5' : 'py-1'} px-1 my-0.5 rounded-sm cursor-pointer hover:bg-blue-50 ${
                isSelected ? 'bg-blue-100 text-blue-700 font-medium' : ''
              }`}
              style={{ paddingLeft: calculateIndent(depth, categoryMode) }}
              onClick={(e) => handleNodeClick(category, e)}
              onDoubleClick={(e) => handleNodeDoubleClick(category, e)}
              onContextMenu={(e) => handleContextMenu(e, category)}
              onMouseEnter={categoryMode === 'department' && (category.id.startsWith('person-') || category.originalPersonId) ? function(e) { return handlePersonMouseEnter(e, category.id); } : undefined}
              onMouseLeave={categoryMode === 'department' && (category.id.startsWith('person-') || category.originalPersonId) ? handlePersonMouseLeave : undefined}
              data-node-id={category.id} // 添加节点ID属性
            >
              {renderNodeContent(category, hasChildren, isExpanded, depth)}
            </div>
          )}

          {hasChildren && isExpanded && (
            <div className={categoryMode === 'department' ? 'ml-1' : 'ml-2'}>
              {renderTree(category.children || [], depth + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  // 更新错误提示显示
  const renderNoResultsMessage = () => {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-gray-500">
        <AlertTriangle className="w-10 h-10 mb-2 text-gray-400" />
        <p className="text-center">
          {categoryMode === 'device'
            ? '未找到匹配的设备分类'
            : '未找到匹配的部门分类'
          }
        </p>
      </div>
    );
  };

  // 创建一个扁平化的节点列表，用于键盘导航
  const createFlattenedNodeList = () => {
    const flattenedNodes: { id: string; isVisible: boolean }[] = [];

    const flattenNodes = (nodes: DeviceCategory[] | DepartmentCategory[], isParentExpanded = true) => {
      nodes.forEach(node => {
        // 节点是否可见取决于其父节点是否展开
        const isVisible = isParentExpanded;
        flattenedNodes.push({ id: node.id, isVisible });

        // 如果节点有子节点且已展开，则递归处理子节点
        if (node.children && node.children.length > 0 && expandedNodes.has(node.id)) {
          flattenNodes(node.children, isVisible && expandedNodes.has(node.id));
        }
      });
    };

    flattenNodes(categories, true);
    return flattenedNodes;
  };

  // 使用已有的findNodeById函数，但需要调整返回类型

  // 查找父节点的辅助函数
  const findParentNode = (nodes: DeviceCategory[] | DepartmentCategory[], childId: string, parentNode: DeviceCategory | DepartmentCategory | null = null): DeviceCategory | DepartmentCategory | null => {
    for (const node of nodes) {
      if (node.id === childId) {
        return parentNode;
      }
      if (node.children && node.children.length > 0) {
        const found = findParentNode(node.children, childId, node);
        if (found) return found;
      }
    }
    return null;
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 调试信息：记录键盘事件
    if (process.env.NODE_ENV === 'development') {
      console.log(`🎹 CategoryTree键盘事件: ${e.key}, 分类模式: ${categoryMode}, 当前分类: ${currentCategory}`);
    }

    // 如果重命名对话框、内联编辑或其他需要键盘输入的UI元素处于活动状态，不处理键盘事件
    if (
      renameDialog.visible ||
      inlineEdit.isEditing ||
      (e.target as HTMLElement).tagName === 'INPUT' ||
      (e.target as HTMLElement).tagName === 'TEXTAREA' ||
      (e.target as HTMLElement).isContentEditable
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🎹 键盘事件被忽略：输入元素处于活动状态');
      }
      return;
    }

    // 阻止默认行为（滚动）
    e.preventDefault();

    // 如果右键菜单已打开，处理菜单导航
    if (contextMenu.visible) {
      handleMenuKeyNavigation(e);
      return;
    }

    const flattenedNodes = createFlattenedNodeList();
    const visibleNodes = flattenedNodes.filter(node => node.isVisible);

    // 找到当前选中节点的索引
    let currentIndex = visibleNodes.findIndex(node => node.id === currentCategory);

    // 如果没有找到当前选中节点（例如，第一次使用键盘导航），则默认选择第一个节点
    if (currentIndex === -1 && visibleNodes.length > 0) {
      currentIndex = 0;
      const firstNodeId = visibleNodes[0].id;
      onSelectCategory(firstNodeId);
      return;
    }

    // 处理上下方向键
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      // 计算下一个要选择的节点索引
      let nextIndex = currentIndex;
      if (e.key === 'ArrowUp') {
        // 向上移动，选择上一个节点
        nextIndex = currentIndex > 0 ? currentIndex - 1 : 0;
      } else if (e.key === 'ArrowDown') {
        // 向下移动，选择下一个节点
        nextIndex = currentIndex < visibleNodes.length - 1 ? currentIndex + 1 : visibleNodes.length - 1;
      }

      // 如果索引有变化，选择新节点
      if (nextIndex !== currentIndex && nextIndex >= 0 && nextIndex < visibleNodes.length) {
        const nextNodeId = visibleNodes[nextIndex].id;
        onSelectCategory(nextNodeId);

        // 确保选中的节点在视图中可见
        setTimeout(() => {
          const element = document.querySelector(`[data-node-id="${nextNodeId}"]`);
          if (element) {
            element.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
          }

          // 确保树状图容器保持焦点
          ensureTreeFocus();
        }, 0);
      }
    }
    // 处理左右方向键
    else if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
      const currentNode = findNodeById(categories, currentCategory) as DeviceCategory | DepartmentCategory;
      if (!currentNode) return;

      const hasChildren = currentNode.children && currentNode.children.length > 0;
      const isExpanded = expandedNodes.has(currentCategory);

      if (e.key === 'ArrowLeft') {
        if (isExpanded && hasChildren) {
          // 如果当前节点已展开，则收起
          const mockEvent = { stopPropagation: () => {} } as React.MouseEvent;
          toggleNode(currentCategory, mockEvent);
        } else {
          // 如果当前节点已收起或没有子节点，则选择父节点
          const parentNode = findParentNode(categories, currentCategory);
          if (parentNode && parentNode.id !== 'all' && parentNode.id !== 'all-dept') {
            onSelectCategory(parentNode.id);

            // 确保选中的节点在视图中可见
            setTimeout(() => {
              const element = document.querySelector(`[data-node-id="${parentNode.id}"]`);
              if (element) {
                element.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
              }

              // 确保树状图容器保持焦点
              ensureTreeFocus();
            }, 0);
          }
        }
      } else if (e.key === 'ArrowRight') {
        if (!isExpanded && hasChildren) {
          // 如果当前节点未展开且有子节点，则展开
          const mockEvent = { stopPropagation: () => {} } as React.MouseEvent;
          toggleNode(currentCategory, mockEvent);
        } else if (isExpanded && hasChildren) {
          // 如果当前节点已展开且有子节点，则选择第一个子节点
          const firstChild = currentNode.children[0];
          if (firstChild) {
            onSelectCategory(firstChild.id);

            // 确保选中的节点在视图中可见
            setTimeout(() => {
              const element = document.querySelector(`[data-node-id="${firstChild.id}"]`);
              if (element) {
                element.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
              }

              // 确保树状图容器保持焦点
              ensureTreeFocus();
            }, 0);
          }
        }
      }
    }
    // 处理回车键
    else if (e.key === 'Enter') {
      // 回车键相当于鼠标右键点击，打开上下文菜单
      const currentNode = findNodeById(categories, currentCategory) as DeviceCategory | DepartmentCategory;
      if (!currentNode) return;

      // 获取当前节点的DOM元素位置，用于定位上下文菜单
      const element = document.querySelector(`[data-node-id="${currentCategory}"]`);
      if (element) {
        // 获取元素的位置信息
        const rect = element.getBoundingClientRect();

        // 创建一个模拟的鼠标事件，用于传递给handleContextMenu
        const mockEvent = {
          preventDefault: () => {},
          stopPropagation: () => {},
          clientX: rect.left + rect.width / 2, // 水平居中
          clientY: rect.top + rect.height / 2  // 垂直居中
        } as React.MouseEvent;

        // 调用handleContextMenu函数，打开上下文菜单
        handleContextMenu(mockEvent, currentNode);

        // 设置菜单导航状态，准备进入菜单导航
        setMenuNavigation({
          isNavigatingMenu: false, // 初始状态为false，等待右方向键进入
          selectedMenuItemIndex: -1
        });

        // 确保树状图容器保持焦点
        ensureTreeFocus();
      }
    }
  };

  // 处理菜单键盘导航
  const handleMenuKeyNavigation = (e: React.KeyboardEvent) => {
    // 阻止默认行为和事件传播，防止全局键盘管理器干扰
    e.preventDefault();
    e.stopPropagation();

    // 获取菜单项元素 - 使用更可靠的选择器
    let menuItems = document.querySelectorAll('[data-menu-item]');

    // 如果没有找到，尝试直接查找菜单项
    if (!menuItems.length) {
      menuItems = document.querySelectorAll('.flex.items-center.cursor-pointer');
    }

    if (!menuItems.length) return;

    // 处理左方向键 - 退出菜单
    if (e.key === 'ArrowLeft') {
      // 更新菜单导航状态
      setMenuNavigation({
        isNavigatingMenu: false,
        selectedMenuItemIndex: -1
      });

      // 关闭右键菜单 - 通过模拟点击文档来关闭菜单
      document.body.click();

      // 确保树状图容器保持焦点
      ensureTreeFocus();

      return;
    }

    // 处理右方向键 - 进入菜单
    if (e.key === 'ArrowRight' && !menuNavigation.isNavigatingMenu) {
      setMenuNavigation({
        isNavigatingMenu: true,
        selectedMenuItemIndex: 0
      });

      // 高亮第一个菜单项
      (menuItems[0] as HTMLElement).classList.add('bg-blue-100');
      return;
    }

    // 如果已经在导航菜单中
    if (menuNavigation.isNavigatingMenu) {
      // 处理上下方向键 - 在菜单项之间导航
      if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        // 移除当前选中项的高亮
        if (menuNavigation.selectedMenuItemIndex >= 0 && menuNavigation.selectedMenuItemIndex < menuItems.length) {
          (menuItems[menuNavigation.selectedMenuItemIndex] as HTMLElement).classList.remove('bg-blue-100');
        }

        // 计算新的选中项索引
        let newIndex = menuNavigation.selectedMenuItemIndex;
        if (e.key === 'ArrowUp') {
          newIndex = newIndex > 0 ? newIndex - 1 : menuItems.length - 1;
        } else {
          newIndex = newIndex < menuItems.length - 1 ? newIndex + 1 : 0;
        }

        // 更新状态并高亮新选中项
        setMenuNavigation({
          isNavigatingMenu: true,
          selectedMenuItemIndex: newIndex
        });

        (menuItems[newIndex] as HTMLElement).classList.add('bg-blue-100');
      }

      // 处理回车键 - 选择菜单项
      else if (e.key === 'Enter') {
        // 阻止默认行为和事件传播，防止全局键盘管理器干扰
        e.preventDefault();
        e.stopPropagation();

        if (menuNavigation.selectedMenuItemIndex >= 0 && menuNavigation.selectedMenuItemIndex < menuItems.length) {
          // 模拟点击选中的菜单项
          (menuItems[menuNavigation.selectedMenuItemIndex] as HTMLElement).click();
          console.log('菜单项被选择:', menuNavigation.selectedMenuItemIndex);
        }
      }
    }
  };

  return (
    <div
      ref={treeContainerRef}
      className="overflow-auto h-full relative outline-none"
      tabIndex={0} // 使div可以接收键盘焦点
      onKeyDown={handleKeyDown} // 添加键盘事件处理
    >
      <div className="p-2">
        {categories.length > 0 ? (
          renderTree(categories)
        ) : (
          renderNoResultsMessage()
        )}
      </div>

      {/* 右键菜单 - 修改为固定定位，相对于视口 */}
      {contextMenu.visible && (
        <div
          className="fixed bg-white shadow-lg rounded-md border border-gray-200 py-1 z-50 context-menu"
          data-context-menu="true"
          style={{
            top: `${contextMenu.y}px`,
            left: `${contextMenu.x}px`,
            // 确保菜单不会超出视口
            maxWidth: 'calc(100vw - 20px)',
            maxHeight: 'calc(100vh - 20px)'
          }}
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
          onContextMenu={(e) => e.stopPropagation()}
          ref={(el) => {
            // 为所有菜单项添加data-menu-item属性
            if (el) {
              const menuItems = el.querySelectorAll('.flex.items-center.cursor-pointer');
              menuItems.forEach(item => {
                item.setAttribute('data-menu-item', '');
              });
            }
          }}
        >
          {/* 设备分类根节点菜单 */}
          {categoryMode === 'device' && contextMenu.categoryId === 'all' ? (
            <>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加分类回调（对应左侧树状图上方的添加分类按钮）
                  // 传递根节点ID
                  onAddInventory && onAddInventory('all');
                }}
                data-menu-item
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>设备分类</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用导出分类回调
                  onExportCategory && onExportCategory(contextMenu.categoryId, contextMenu.categoryName);
                }}
              >
                <FileDown className="w-4 h-4 mr-2 text-blue-600" />
                <span>导出全部</span>
              </div>
            </>
          ) : categoryMode === 'device' && contextMenu.categoryId.startsWith('parent-') && !contextMenu.categoryId.includes('-', 'parent-'.length) ? (
            // 设备一级分类菜单
            <>
              <div
                className="flex items-center px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加类型回调（对应左侧树状图上方的添加类型按钮）
                  // 传递一级分类ID
                  onAddInventory && onAddInventory(contextMenu.categoryId);
                }}
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>设备类型</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用导出分类回调
                  onExportCategory && onExportCategory(contextMenu.categoryId, contextMenu.categoryName);
                }}
              >
                <FileDown className="w-4 h-4 mr-2 text-blue-600" />
                <span>导出分类</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={categoryMode === 'device' ? handleCustomRenameClick : handleRenameClick}
              >
                <Edit className="w-4 h-4 mr-2 text-blue-600" />
                <span>{getRenameText(contextMenu.categoryId, categoryMode)}</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer text-red-600"
                onClick={handleDeleteClick}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                <span>删除</span>
              </div>
            </>
          ) : categoryMode === 'device' ? (
            // 设备二级分类菜单
            <>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加台账回调
                  // 传递二级分类ID
                  onAddInventory && onAddInventory(contextMenu.categoryId);
                }}
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>添加台账</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用导出分类回调
                  onExportCategory && onExportCategory(contextMenu.categoryId, contextMenu.categoryName);
                }}
              >
                <FileDown className="w-4 h-4 mr-2 text-blue-600" />
                <span>导出分类</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={categoryMode === 'device' ? handleCustomRenameClick : handleRenameClick}
              >
                <Edit className="w-4 h-4 mr-2 text-blue-600" />
                <span>{getRenameText(contextMenu.categoryId, categoryMode)}</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer text-red-600"
                onClick={handleDeleteClick}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                <span>删除</span>
              </div>
            </>
          ) : categoryMode === 'department' && contextMenu.categoryId === 'all-dept' ? (
            // 部门树根节点菜单
            <>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加部门回调
                  onAddInventory && onAddInventory('all-dept-add-department');
                }}
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>添加部门</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用导出分类回调
                  onExportCategory && onExportCategory(contextMenu.categoryId, contextMenu.categoryName);
                }}
              >
                <FileDown className="w-4 h-4 mr-2 text-blue-600" />
                <span>导出全部</span>
              </div>
            </>
          ) : categoryMode === 'department' && contextMenu.categoryId.startsWith('dept-') ? (
            // 部门节点菜单
            <>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加部门回调
                  onAddInventory && onAddInventory('dept-add-department-' + contextMenu.categoryId);
                }}
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>添加部门</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加人员回调
                  onAddInventory && onAddInventory('dept-add-person-' + contextMenu.categoryId);
                }}
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>添加人员</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用导出分类回调
                  onExportCategory && onExportCategory(contextMenu.categoryId, contextMenu.categoryName);
                }}
              >
                <FileDown className="w-4 h-4 mr-2 text-blue-600" />
                <span>导出分类</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={categoryMode === 'device' ? handleCustomRenameClick : handleRenameClick}
              >
                <Edit className="w-4 h-4 mr-2 text-blue-600" />
                <span>{getRenameText(contextMenu.categoryId, categoryMode)}</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer text-red-600"
                onClick={handleDeleteClick}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                <span>删除</span>
              </div>
            </>
          ) : categoryMode === 'department' && contextMenu.categoryId.startsWith('person-') ? (
            // 人员节点菜单
            <>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加台账回调
                  onAddInventory && onAddInventory(contextMenu.categoryId);
                }}
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>添加台账</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用编辑人员信息回调
                  onEditPerson && onEditPerson(contextMenu.categoryId);
                  // 关闭右键菜单 - 通过点击文档来关闭
                  document.body.click();
                }}
              >
                <Edit className="w-4 h-4 mr-2 text-blue-600" />
                <span>编辑信息</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用导出分类回调
                  onExportCategory && onExportCategory(contextMenu.categoryId, contextMenu.categoryName);
                }}
              >
                <FileDown className="w-4 h-4 mr-2 text-blue-600" />
                <span>导出分类</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer text-red-600"
                onClick={handleDeleteClick}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                <span>删除</span>
              </div>
            </>
          ) : (
            // 其他类型节点的菜单保持不变
            <>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用添加台账回调
                  onAddInventory && onAddInventory(contextMenu.categoryId);
                }}
              >
                <Plus className="w-4 h-4 mr-2 text-green-600" />
                <span>添加台账</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  // 调用导出分类回调
                  onExportCategory && onExportCategory(contextMenu.categoryId, contextMenu.categoryName);
                }}
              >
                <FileDown className="w-4 h-4 mr-2 text-blue-600" />
                <span>导出分类</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer"
                onClick={categoryMode === 'device' ? handleCustomRenameClick : handleRenameClick}
              >
                <Edit className="w-4 h-4 mr-2 text-blue-600" />
                <span>{getRenameText(contextMenu.categoryId, categoryMode)}</span>
              </div>
              <div
                className="flex items-center px-4 py-1.5 hover:bg-gray-100 cursor-pointer text-red-600"
                onClick={handleDeleteClick}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                <span>删除</span>
              </div>
            </>
          )}
        </div>
      )}

      {/* 重命名对话框 */}
      {renameDialog.visible && (
        <div
          className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
          onClick={(e) => e.stopPropagation()}
        >
          <div
            className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 dialog-container"
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                if (showIconSelector) {
                  setShowIconSelector(false);
                } else {
                  closeRenameDialog();
                }
                e.preventDefault();
                e.stopPropagation();
              }
            }}
          >
            {/* 对话框标题 */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-3">
                {showIconSelector && (
                  <button
                    onClick={() => setShowIconSelector(false)}
                    className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                    title="返回"
                  >
                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                )}
                <h2 className="text-xl font-semibold">
                  {showIconSelector ? '选择图标' : getRenameText(renameDialog.categoryId, categoryMode)}
                </h2>
              </div>
              <button
                onClick={closeRenameDialog}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                title="关闭"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 内容区域 */}
            {showIconSelector ? (
              // 图标选择器 - 无重复标题
              <div className="space-y-4">
                <IconSelector
                  selectedIcon={selectedIcon}
                  onSelectIcon={(icon) => {
                    setSelectedIcon(icon);
                    setShowIconSelector(false);
                  }}
                  onClose={() => setShowIconSelector(false)}
                />
              </div>
            ) : (
              <div>
                {/* 名称输入区域 - 主要操作，放在最前面 */}
                <div className="mb-5">
                  {/* 部门节点不显示标签 */}
                  {!(categoryMode === 'department' && renameDialog.categoryId.startsWith('dept-')) && (
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {categoryMode === 'device' && renameDialog.categoryId.startsWith('parent-') && !renameDialog.categoryId.includes('-', 'parent-'.length)
                        ? '分类名称'
                        : categoryMode === 'device' && renameDialog.categoryId.startsWith('parent-') && renameDialog.categoryId.includes('-', 'parent-'.length)
                        ? '类型名称'
                        : '分类名称'} <span className="text-red-500">*</span>
                    </label>
                  )}
                  <div
                    onKeyDown={(e) => {
                      // 如果按下回车键，执行重命名
                      if (e.key === 'Enter' && !isRenaming && renameValue.trim() && !renameError) {
                        executeCustomRename();
                      }

                      // 如果按下ESC键，关闭对话框
                      if (e.key === 'Escape') {
                        closeRenameDialog();
                      }
                    }}
                  >
                    <ValidatedInput
                      label=""
                      value={renameValue}
                      onChange={(value) => {
                        setRenameValue(value);
                        validateRenameInput(value, renameDialog.categoryId);
                      }}
                      error={renameError || undefined}
                      placeholder={categoryMode === 'device' && renameDialog.categoryId.startsWith('parent-') && !renameDialog.categoryId.includes('-', 'parent-'.length)
                        ? '请输入分类名称'
                        : categoryMode === 'device' && renameDialog.categoryId.startsWith('parent-') && renameDialog.categoryId.includes('-', 'parent-'.length)
                        ? '请输入类型名称'
                        : '请输入分类名称'}
                      required
                      disabled={isRenaming}
                      maxByteLength={108}
                      showCounter={true}
                      autoFocus={true}
                    />
                  </div>
                </div>

                {/* 图标选择区域 - 与添加分类样式保持一致 */}
                {categoryMode === 'device' && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      分类图标 <span className="text-gray-500">(可选)</span>
                    </label>
                    <div className="flex items-center space-x-3">
                      <button
                        type="button"
                        onClick={() => setShowIconSelector(true)}
                        className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        disabled={isRenaming}
                      >
                        {selectedIcon ? (
                          <>
                            {getIconComponent(selectedIcon, "w-5 h-5")}
                            <span className="text-sm text-gray-700">
                              {ICON_DESCRIPTIONS[selectedIcon]}
                            </span>
                          </>
                        ) : (
                          <>
                            <Folder className="w-5 h-5 text-gray-400" />
                            <span className="text-sm text-gray-500">选择图标</span>
                          </>
                        )}
                      </button>

                      {selectedIcon && (
                        <button
                          type="button"
                          onClick={() => setSelectedIcon(undefined)}
                          className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                          disabled={isRenaming}
                        >
                          清除
                        </button>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      选择一个图标来代表此分类，如果不选择将使用默认图标
                    </p>
                  </div>
                )}

                {/* 按钮区域 */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                  <button
                    onClick={closeRenameDialog}
                    className="px-6 py-2.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200 font-medium"
                    disabled={isRenaming}
                  >
                    取消
                  </button>
                  <button
                    onClick={executeCustomRename}
                    className="px-6 py-2.5 bg-blue-600 rounded-md text-white hover:bg-blue-700 disabled:bg-gray-400 transition-colors duration-200 font-medium shadow-sm"
                    disabled={isRenaming || !renameValue.trim() || !!renameError}
                  >
                    {isRenaming ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>处理中...</span>
                      </div>
                    ) : (
                      '确认'
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {deleteDialog.visible && (
        <div
          className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
          onClick={(e) => e.stopPropagation()}
        >
          <div
            className="bg-white rounded-lg shadow-xl w-full max-w-md p-6"
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                closeDeleteDialog();
                e.preventDefault();
                e.stopPropagation();
              }
            }}
            tabIndex={-1}
          >
            <h2 className="text-xl font-semibold mb-4">确认删除</h2>
            <div className="mb-4">
              <p className="text-gray-700">
                您确定要删除 <span className="text-red-600 font-semibold">{deleteDialog.categoryName}</span> 吗？
              </p>
              {/* <p className="text-red-600 text-sm mt-2">
                此操作不可逆，删除后数据将无法恢复。
              </p> */}
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={closeDeleteDialog}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                disabled={isDeleting}
              >
                取消
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 rounded-md text-white hover:bg-red-700"
                disabled={isDeleting}
              >
                {isDeleting ? '删除中...' : '确认删除'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 人员悬停提示框 */}
      {personTooltip.visible && (
        <div
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4"
          style={{
            left: personTooltip.x + 'px',
            top: personTooltip.y + 'px',
            transform: 'translateY(-50%)',
            pointerEvents: 'none',
            minWidth: '256px',
            maxWidth: '320px'
          }}
        >
          {personTooltip.isLoading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : personTooltip.personInfo ? (
            <div className="space-y-3">
              {/* 标题 */}
              <div className="border-b border-gray-200 pb-2">
                <h3 className="text-lg font-semibold text-gray-800">人员信息</h3>
              </div>

              {/* 基本信息 */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <span className="text-sm font-bold text-gray-600 w-16">姓名:</span>
                  <span className="text-sm text-gray-800">{personTooltip.personInfo.user_name}</span>
                </div>

                {personTooltip.personInfo.alias && (
                  <div className="flex items-center">
                    <span className="text-sm font-bold text-gray-600 w-16">备注:</span>
                    <span className="text-sm text-gray-800">{personTooltip.personInfo.alias}</span>
                  </div>
                )}

                {/* 显示所有部门信息 */}
                <div className="flex items-start">
                  <span className="text-sm font-bold text-gray-600 w-16 mt-0.5">部门:</span>
                  <div className="flex-1">
                    {personTooltip.personInfo.departments && personTooltip.personInfo.departments.length > 0 ? (
                      <div className="space-y-1">
                        {personTooltip.personInfo.departments.map((dept: any, index: number) => (
                          <div key={dept.id} className="flex items-center">
                            <span className="text-sm text-gray-800">
                              {dept.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span className="text-sm text-gray-800">{personTooltip.personInfo.primary_department_name || '未分配'}</span>
                    )}
                  </div>
                </div>

                {personTooltip.personInfo.position_security_level !== undefined && (
                  <div className="flex items-center">
                    <span className="text-sm font-bold text-gray-600 w-16">密级:</span>
                    <span className={'text-sm font-medium ' + getSecurityLevelColor(personTooltip.personInfo.position_security_level)}>
                      {personTooltip.personInfo.position_security_level_text || getSecurityLevelText(personTooltip.personInfo.position_security_level)}
                    </span>
                  </div>
                )}

                                {personTooltip.personInfo.mobile_number && (
                  <div className="flex items-center">
                    <span className="text-sm font-bold text-gray-600 w-16">联系方式:</span>
                    <span className="text-sm text-gray-800">{personTooltip.personInfo.mobile_number}</span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <span className="text-gray-600">暂无人员信息</span>
            </div>
          )}
        </div>
      )}

      {/* 重命名确认对话框 */}
      <ConfirmDialog
        isOpen={renameConfirmDialog.isOpen}
        onClose={closeRenameConfirmDialog}
        onConfirm={confirmRename}
        title={renameConfirmDialog.title}
        message={renameConfirmDialog.message}
        confirmText="确认"
        cancelText="取消"
        type="info"
      />

      {/* 现有人员关联提示对话框 */}
      <ExistingPersonAssociationDialog
        isOpen={existingPersonDialog.isOpen}
        onClose={() => setExistingPersonDialog(prev => ({ ...prev, isOpen: false }))}
        personName={existingPersonDialog.personName}
        alias={existingPersonDialog.alias}
        departmentName={existingPersonDialog.departmentName}
      />

      {/* 自定义错误提示框 */}
      <CustomAlert
        isOpen={alertState.isOpen}
        onClose={closeAlert}
        title="操作失败"
        message={alertState.message}
        type={alertState.type}
        confirmText="确定"
      />
    </div>
  );
};

export default CategoryTree;
