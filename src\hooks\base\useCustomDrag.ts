import { useState, useRef, useEffect, useCallback } from 'react';

// 拖拽状态接口
export interface DragState {
  isDragging: boolean;
  draggedElement: HTMLElement | null;
  draggedId: string;
  draggedData: any;
  dragImage: HTMLElement | null;
  initialMouseOffset: { x: number; y: number };
  currentMousePosition: { x: number; y: number };
}

// 拖拽选项接口
export interface CustomDragOptions {
  // 是否启用拖拽
  enabled?: boolean;
  // 拖拽开始回调
  onDragStart?: (id: string, data: any, event: MouseEvent) => void;
  // 拖拽中回调
  onDrag?: (id: string, data: any, event: MouseEvent) => void;
  // 拖拽结束回调
  onDragEnd?: (id: string, data: any, event: MouseEvent) => void;
  // 拖拽进入目标回调
  onDragEnter?: (targetId: string, draggedId: string, event: MouseEvent) => void;
  // 拖拽离开目标回调
  onDragLeave?: (targetId: string, draggedId: string, event: MouseEvent) => void;
  // 放置回调
  onDrop?: (targetId: string, draggedId: string, event: MouseEvent) => void;
  // 判断是否可拖拽
  canDrag?: (id: string) => boolean;
  // 判断是否可放置
  canDrop?: (draggedId: string, targetId: string) => boolean;
  // 拖拽图像样式
  dragImageStyle?: Partial<CSSStyleDeclaration>;
  // 拖拽时的透明度
  dragOpacity?: number;
  // 拖拽时的光标样式
  dragCursor?: string;
  // 可放置时的样式类名
  dropTargetClassName?: string;
  // 不可放置时的样式类名
  noDropTargetClassName?: string;
  // 是否需要长按才能拖拽
  requireLongPress?: boolean;
  // 长按触发时间（毫秒）
  longPressDelay?: number;
}

// 拖拽Hook返回值接口
export interface UseCustomDragReturn {
  // 拖拽状态
  dragState: DragState;
  // 当前悬停的目标ID
  hoverTargetId: string | null;
  // 是否可放置
  canDropOnTarget: boolean;
  // 拖拽处理器
  dragHandlers: {
    // 开始拖拽
    onMouseDown: (e: React.MouseEvent<HTMLElement>, id: string, data?: any) => void;
    // 目标鼠标进入
    onMouseEnter: (e: React.MouseEvent<HTMLElement>, id: string) => void;
    // 目标鼠标离开
    onMouseLeave: (e: React.MouseEvent<HTMLElement>, id: string) => void;
  };
  // 重置拖拽状态
  resetDragState: () => void;
}

/**
 * 自定义拖拽Hook
 * 提供兼容性更好的拖拽功能
 * @param options 拖拽选项
 */
export function useCustomDrag(options: CustomDragOptions = {}): UseCustomDragReturn {
  // 默认选项
  const defaultOptions: CustomDragOptions = {
    enabled: true,
    dragOpacity: 0.7,
    dragCursor: 'move',
    dropTargetClassName: 'custom-drag-over',
    noDropTargetClassName: 'custom-drag-no-drop',
    requireLongPress: false, // 默认不需要长按
    longPressDelay: 500, // 默认长按时间为500毫秒
    dragImageStyle: {
      position: 'absolute',
      pointerEvents: 'none',
      zIndex: '9999',
      opacity: '0.7',
      backgroundColor: '#f0f9ff',
      border: '1px solid #93c5fd',
      borderRadius: '4px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      padding: '4px',
      maxWidth: '300px',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis'
    }
  };

  // 合并选项
  const mergedOptions = { ...defaultOptions, ...options };

  // 拖拽状态
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedElement: null,
    draggedId: '',
    draggedData: null,
    dragImage: null,
    initialMouseOffset: { x: 0, y: 0 },
    currentMousePosition: { x: 0, y: 0 }
  });

  // 悬停目标状态
  const [hoverTargetId, setHoverTargetId] = useState<string | null>(null);
  const [canDropOnTarget, setCanDropOnTarget] = useState<boolean>(false);

  // 引用存储当前拖拽状态，用于事件处理器中访问最新状态
  const dragStateRef = useRef<DragState>(dragState);
  const hoverTargetIdRef = useRef<string | null>(hoverTargetId);

  // 长按相关状态
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const longPressDataRef = useRef<{
    element: HTMLElement;
    id: string;
    data: any;
    event: React.MouseEvent<HTMLElement>;
  } | null>(null);

  // 更新引用
  useEffect(() => {
    dragStateRef.current = dragState;
    hoverTargetIdRef.current = hoverTargetId;
  }, [dragState, hoverTargetId]);

  // 创建拖拽图像
  const createDragImage = useCallback((element: HTMLElement): HTMLElement => {
    // 检查是否有拖拽数据
    const draggedData = element.getAttribute('data-drag-data');
    let draggedTitle = '';

    // 尝试解析拖拽数据
    if (draggedData) {
      try {
        const data = JSON.parse(draggedData);
        draggedTitle = data.title || '';
      } catch (e) {
        // 解析失败，使用元素内容
        draggedTitle = element.textContent || '';
      }
    } else {
      // 没有拖拽数据，使用元素内容
      draggedTitle = element.textContent || '';
    }

    // 创建新的拖拽图像元素
    const clone = document.createElement('div');

    // 设置内容
    clone.textContent = draggedTitle;

    // 应用样式
    Object.assign(clone.style, mergedOptions.dragImageStyle);

    // 设置初始位置（屏幕外，避免闪烁）
    clone.style.top = '-1000px';
    clone.style.left = '-1000px';

    // 添加到文档
    document.body.appendChild(clone);

    return clone;
  }, [mergedOptions.dragImageStyle]);

  // 更新拖拽图像位置
  const updateDragImagePosition = useCallback((x: number, y: number) => {
    if (dragStateRef.current.dragImage) {
      // 计算偏移，使拖拽图像位于鼠标指针下方
      const offsetX = 15; // 鼠标右侧偏移
      const offsetY = 15; // 鼠标下方偏移

      dragStateRef.current.dragImage.style.left = `${x + offsetX}px`;
      dragStateRef.current.dragImage.style.top = `${y + offsetY}px`;
    }
  }, []);

  // 处理长按事件
  const handleLongPress = useCallback((element: HTMLElement, id: string, data: any, event: React.MouseEvent<HTMLElement>) => {
    // 检查是否可拖拽
    if (mergedOptions.canDrag && !mergedOptions.canDrag(id)) return;

    // 记录初始鼠标位置
    const initialMouseOffset = {
      x: event.clientX,
      y: event.clientY
    };

    // 创建拖拽图像
    const dragImage = createDragImage(element);

    // 更新拖拽状态
    setDragState({
      isDragging: true,
      draggedElement: element,
      draggedId: id,
      draggedData: data || null,
      dragImage,
      initialMouseOffset,
      currentMousePosition: initialMouseOffset
    });

    // 添加拖拽样式
    element.style.opacity = mergedOptions.dragOpacity?.toString() || '0.7';

    // 调用拖拽开始回调
    mergedOptions.onDragStart?.(id, data, event.nativeEvent);

    // 清除长按计时器引用
    longPressTimerRef.current = null;
    longPressDataRef.current = null;
  }, [mergedOptions, createDragImage]);

  // 清除长按计时器
  const clearLongPressTimer = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
    longPressDataRef.current = null;
  }, []);

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLElement>, id: string, data?: any) => {
    // 如果禁用拖拽或右键点击，则不处理
    if (!mergedOptions.enabled || e.button !== 0) return;

    // 获取目标元素
    const element = e.currentTarget;

    // 清除任何现有的长按计时器
    clearLongPressTimer();

    // 如果需要长按才能拖拽
    if (mergedOptions.requireLongPress) {
      // 保存长按数据
      longPressDataRef.current = {
        element,
        id,
        data,
        event: e
      };

      // 设置长按计时器
      longPressTimerRef.current = setTimeout(() => {
        // 确保长按数据存在
        if (longPressDataRef.current) {
          const { element, id, data, event } = longPressDataRef.current;
          handleLongPress(element, id, data, event);
        }
      }, mergedOptions.longPressDelay);

      // 阻止默认行为和冒泡
      e.preventDefault();
      e.stopPropagation();
      return;
    }

    // 不需要长按，直接开始拖拽
    // 检查是否可拖拽
    if (mergedOptions.canDrag && !mergedOptions.canDrag(id)) return;

    // 记录初始鼠标位置
    const initialMouseOffset = {
      x: e.clientX,
      y: e.clientY
    };

    // 创建拖拽图像
    const dragImage = createDragImage(element);

    // 更新拖拽状态
    setDragState({
      isDragging: true,
      draggedElement: element,
      draggedId: id,
      draggedData: data || null,
      dragImage,
      initialMouseOffset,
      currentMousePosition: initialMouseOffset
    });

    // 添加拖拽样式
    element.style.opacity = mergedOptions.dragOpacity?.toString() || '0.7';

    // 调用拖拽开始回调
    mergedOptions.onDragStart?.(id, data, e.nativeEvent);

    // 阻止默认行为和冒泡
    e.preventDefault();
    e.stopPropagation();
  }, [mergedOptions, createDragImage, clearLongPressTimer, handleLongPress]);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((e: MouseEvent) => {
    // 如果有长按计时器但鼠标移动超过阈值，则取消长按
    if (longPressTimerRef.current && longPressDataRef.current) {
      const { event } = longPressDataRef.current;
      const moveThreshold = 5; // 5像素的移动阈值

      // 计算移动距离
      const dx = Math.abs(e.clientX - event.clientX);
      const dy = Math.abs(e.clientY - event.clientY);

      // 如果移动超过阈值，取消长按
      if (dx > moveThreshold || dy > moveThreshold) {
        clearLongPressTimer();
      }
    }

    // 如果没有拖拽，则不处理
    if (!dragStateRef.current.isDragging) return;

    // 更新当前鼠标位置
    setDragState(prev => ({
      ...prev,
      currentMousePosition: { x: e.clientX, y: e.clientY }
    }));

    // 更新拖拽图像位置
    updateDragImagePosition(e.clientX, e.clientY);

    // 调用拖拽中回调
    mergedOptions.onDrag?.(
      dragStateRef.current.draggedId,
      dragStateRef.current.draggedData,
      e
    );

    // 阻止默认行为
    e.preventDefault();
  }, [mergedOptions, updateDragImagePosition, clearLongPressTimer]);

  // 处理鼠标释放事件
  const handleMouseUp = useCallback((e: MouseEvent) => {
    // 清除长按计时器
    clearLongPressTimer();

    // 如果没有拖拽，则不处理
    if (!dragStateRef.current.isDragging) return;

    // 获取拖拽元素
    const { draggedElement, draggedId, draggedData, dragImage } = dragStateRef.current;

    // 恢复拖拽元素样式
    if (draggedElement) {
      draggedElement.style.opacity = '1';
    }

    // 移除拖拽图像
    if (dragImage && document.body.contains(dragImage)) {
      document.body.removeChild(dragImage);
    }

    // 如果有悬停目标且可放置，则执行放置操作
    if (
      hoverTargetIdRef.current &&
      (!mergedOptions.canDrop || mergedOptions.canDrop(draggedId, hoverTargetIdRef.current))
    ) {
      // 调用放置回调
      mergedOptions.onDrop?.(hoverTargetIdRef.current, draggedId, e);
    }

    // 调用拖拽结束回调
    mergedOptions.onDragEnd?.(draggedId, draggedData, e);

    // 重置拖拽状态
    setDragState({
      isDragging: false,
      draggedElement: null,
      draggedId: '',
      draggedData: null,
      dragImage: null,
      initialMouseOffset: { x: 0, y: 0 },
      currentMousePosition: { x: 0, y: 0 }
    });

    // 重置悬停目标
    setHoverTargetId(null);
    setCanDropOnTarget(false);

    // 阻止默认行为
    e.preventDefault();
  }, [mergedOptions, clearLongPressTimer]);

  // 处理鼠标进入目标事件
  const handleMouseEnter = useCallback((e: React.MouseEvent<HTMLElement>, id: string) => {
    // 如果没有拖拽，则不处理
    if (!dragStateRef.current.isDragging) return;

    // 如果进入的是拖拽元素自身，则不处理
    if (id === dragStateRef.current.draggedId) return;

    // 检查是否可放置
    const canDrop = !mergedOptions.canDrop || mergedOptions.canDrop(dragStateRef.current.draggedId, id);

    // 更新悬停目标
    setHoverTargetId(id);
    setCanDropOnTarget(canDrop);

    // 调用拖拽进入回调
    mergedOptions.onDragEnter?.(id, dragStateRef.current.draggedId, e.nativeEvent);

    // 阻止默认行为和冒泡
    e.preventDefault();
    e.stopPropagation();
  }, [mergedOptions]);

  // 处理鼠标离开目标事件
  const handleMouseLeave = useCallback((e: React.MouseEvent<HTMLElement>, id: string) => {
    // 如果没有拖拽，则不处理
    if (!dragStateRef.current.isDragging) return;

    // 如果当前悬停目标是离开的元素，则重置悬停目标
    if (hoverTargetIdRef.current === id) {
      setHoverTargetId(null);
      setCanDropOnTarget(false);

      // 调用拖拽离开回调
      mergedOptions.onDragLeave?.(id, dragStateRef.current.draggedId, e.nativeEvent);
    }

    // 阻止默认行为和冒泡
    e.preventDefault();
    e.stopPropagation();
  }, [mergedOptions]);

  // 重置拖拽状态
  const resetDragState = useCallback(() => {
    // 清除长按计时器
    clearLongPressTimer();

    // 获取拖拽元素和图像
    const { draggedElement, dragImage } = dragStateRef.current;

    // 恢复拖拽元素样式
    if (draggedElement) {
      draggedElement.style.opacity = '1';
    }

    // 移除拖拽图像
    if (dragImage && document.body.contains(dragImage)) {
      document.body.removeChild(dragImage);
    }

    // 重置拖拽状态
    setDragState({
      isDragging: false,
      draggedElement: null,
      draggedId: '',
      draggedData: null,
      dragImage: null,
      initialMouseOffset: { x: 0, y: 0 },
      currentMousePosition: { x: 0, y: 0 }
    });

    // 重置悬停目标
    setHoverTargetId(null);
    setCanDropOnTarget(false);
  }, [clearLongPressTimer]);

  // 添加全局事件监听
  useEffect(() => {
    // 添加全局鼠标移动和释放事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // 清理函数
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // 确保清理拖拽图像
      const { dragImage } = dragStateRef.current;
      if (dragImage && document.body.contains(dragImage)) {
        document.body.removeChild(dragImage);
      }
    };
  }, [handleMouseMove, handleMouseUp]);

  // 返回拖拽状态和处理器
  return {
    dragState,
    hoverTargetId,
    canDropOnTarget,
    dragHandlers: {
      onMouseDown: handleMouseDown,
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave
    },
    resetDragState
  };
}
