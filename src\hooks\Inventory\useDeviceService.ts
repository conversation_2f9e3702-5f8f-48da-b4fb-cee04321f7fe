import { useState, useEffect, useCallback } from 'react';
import DeviceService from '../../services/Inventory/deviceService';
import ExtFieldService from '../../services/Inventory/extFieldService';
import { InventoryItem } from '../../types/inventory';

/**
 * 设备服务Hook - 提供添加设备功能
 */
export default function useDeviceService() {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<Error | null>(null);
  const [taskId, setTaskId] = useState<string | null>(null);

  // 获取设备服务实例
  const deviceService = DeviceService.getInstance();

  // 初始化事件监听
  useEffect(() => {
    const handleTaskStart = (id: string) => {
      setTaskId(id);
      setIsLoading(true);
      setProgress(0);
      setError(null);
    };

    const handleTaskProgress = (data: { taskId: string; progress: number }) => {
      setProgress(data.progress);
    };

    const handleTaskComplete = () => {
      setIsLoading(false);
      setProgress(100);
    };

    const handleTaskError = (err: Error) => {
      setIsLoading(false);
      setError(err);
    };

    // 不再定义分类树相关的事件处理函数，避免重复监听
    // 这些事件已经在useInventory.ts中处理

    // 只注册任务相关事件监听
    deviceService.on('task-start', handleTaskStart);
    deviceService.on('task-progress', handleTaskProgress);
    deviceService.on('task-complete', handleTaskComplete);
    deviceService.on('task-error', handleTaskError);

    // 不再注册分类树相关事件监听，避免重复监听
    // 这些事件已经在useInventory.ts中处理

    // 清理函数
    return () => {
      // 只清理任务相关事件监听
      deviceService.off('task-start', handleTaskStart);
      deviceService.off('task-progress', handleTaskProgress);
      deviceService.off('task-complete', handleTaskComplete);
      deviceService.off('task-error', handleTaskError);

      // 不再清理分类树相关事件监听，因为没有注册它们
    };
  }, [deviceService]);

  /**
   * 添加设备
   */
  const addDevice = useCallback(async (deviceData: {
    deviceType: string;        // 设备分类（一级分类）
    deviceName: string;        // 设备名称（二级分类）
    deviceModel: string;       // 设备型号
    deviceManufacturer: string;// 设备厂商
    securityLevel: string;     // 密级
    purpose: string;           // 用途
    location: string;          // 放置位置
    activationTime: number;    // 启用时间（时间戳）
    usage: string;             // 使用情况
    rfidCode: string;          // RFID码
    personalName: string;      // 责任人姓名
    ext1?: string;             // 扩展字段1
    ext2?: string;             // 扩展字段2
    ext3?: string;             // 扩展字段3
  }) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.addDevice(deviceData);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);



  /**
   * 添加分类（一级分类）
   */
  const addDeviceType = useCallback(async (
    typeName: string,
    customIcon?: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      // 传递typeName和customIcon
      const result = await deviceService.addDeviceType(typeName, customIcon);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  /**
   * 添加类型（二级分类）
   */
  const addDeviceSubtype = useCallback(async (
    parentCategoryName: string,
    subtypeName: string,
    customIcon?: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.addDeviceSubtype(parentCategoryName, subtypeName, customIcon);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  /**
   * 从前端库存项目格式转换为后端设备数据格式
   */
  const convertToDeviceData = useCallback((item: InventoryItem) => {
    return deviceService.convertInventoryItemToDeviceData(item);
  }, [deviceService]);

  /**
   * 使用前端库存项目直接添加设备
   */
  const addInventoryItem = useCallback(async (item: InventoryItem) => {
    const deviceData = convertToDeviceData(item);
    return await addDevice(deviceData);
  }, [addDevice, convertToDeviceData]);



  /**
   * 使用前端库存项目直接添加设备（支持扩展字段）
   */
  const addInventoryItemWithExt = useCallback(async (item: InventoryItem, extFields: Record<string, any> = {}) => {
    // 使用扩展字段服务处理扩展字段数据
    const extFieldService = ExtFieldService.getInstance();
    const processedExtFields = extFieldService.convertExtFieldsToBackendFormat(extFields);

    // 使用设备服务的转换方法
    const deviceData = deviceService.convertInventoryItemToDeviceDataExt(item, processedExtFields);
    return await addDevice(deviceData);
  }, [addDevice, deviceService]);

  /**
   * 重命名设备分类
   */
  const renameCategory = useCallback(async (
    categoryId: string,
    newName: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.renameCategory(categoryId, newName);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  /**
   * 删除设备分类
   */
  const deleteCategory = useCallback(async (
    categoryId: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.deleteCategory(categoryId);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  /**
   * 更新设备父类（一级分类）
   */
  const updateParentCategory = useCallback(async (
    curParentCategoryName: string,
    newParentCategoryName: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.updateParentCategory(curParentCategoryName, newParentCategoryName);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  /**
   * 删除设备父类（一级分类）
   */
  const deleteParentCategory = useCallback(async (
    parentCategoryName: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.deleteParentCategory(parentCategoryName);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  /**
   * 更新设备子类（二级分类）
   */
  const updateSubCategory = useCallback(async (
    curSubCategoryName: string,
    newSubCategoryName: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.updateSubCategory(curSubCategoryName, newSubCategoryName);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  /**
   * 删除设备子类（二级分类）
   */
  const deleteSubCategory = useCallback(async (
    subCategoryName: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await deviceService.deleteSubCategory(subCategoryName);

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [deviceService]);

  return {
    isLoading,
    progress,
    error,
    taskId,
    addDevice,
    addInventoryItem,
    addInventoryItemWithExt,
    convertToDeviceData,
    addDeviceType,
    addDeviceSubtype,
    renameCategory,
    deleteCategory,
    // 新增的方法
    updateParentCategory,
    deleteParentCategory,
    updateSubCategory,
    deleteSubCategory,
    // 设备服务实例
    deviceService
  };
}