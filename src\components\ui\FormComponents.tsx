import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { getByteLength } from '../../utils/fieldValidation';

/**
 * 通用长度计算工具函数
 */
const useLengthCalculation = (value: string, maxByteLength?: number, maxCharLength?: number) => {
  const getCurrentLength = () => {
    if (maxByteLength) {
      return getByteLength(value);
    }
    if (maxCharLength) {
      return value.length;
    }
    return 0;
  };

  const getMaxLength = () => {
    return maxByteLength || maxCharLength || 0;
  };

  const currentLength = getCurrentLength();
  const maxLength = getMaxLength();
  const isOverLimit = maxLength > 0 && currentLength > maxLength;

  return { currentLength, maxLength, isOverLimit };
};

/**
 * 表单字段错误提示组件
 */
interface FieldErrorProps {
  error?: string;
  fieldName: string;
  className?: string;
}

export const FieldError: React.FC<FieldErrorProps> = ({
  error,
  fieldName,
  className = ''
}) => {
  if (!error) return null;
  
  return (
    <div
      id={`${fieldName}-error`}
      className={`text-sm text-red-600 mt-1 ${className}`}
      role="alert"
      aria-live="polite"
    >
      {error}
    </div>
  );
};

/**
 * 预设配置类型
 */
type InputPreset = 'name' | 'alias' | 'mobile' | 'category' | 'custom';

/**
 * 预设配置映射
 */
const INPUT_PRESETS: Record<InputPreset, Partial<ValidatedInputProps>> = {
  name: {
    maxByteLength: 108,
    showCounter: true,
    required: true
  },
  alias: {
    maxByteLength: 108,
    showCounter: true,
    required: false
  },
  mobile: {
    maxCharLength: 11,
    showCounter: true,
    formatHint: "只能包含数字和-",
    type: 'tel' as const,
    required: false
  },
  category: {
    maxByteLength: 108,
    showCounter: true,
    required: true
  },
  custom: {}
};

/**
 * 验证输入框组件
 * 提供统一的输入框样式和验证反馈
 */
interface ValidatedInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  maxByteLength?: number;
  maxCharLength?: number;
  formatHint?: string;
  className?: string;
  type?: 'text' | 'password' | 'email' | 'tel';
  showCounter?: boolean;
  preset?: InputPreset;
  autoFocus?: boolean; // 是否自动聚焦
}

/**
 * 验证下拉输入框组件
 * 支持字节限制和计数器显示，计数器放在下拉按钮前面
 */
interface ValidatedComboboxProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: Array<{ code: string; value: string }>;
  error?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  maxByteLength?: number;
  maxCharLength?: number;
  className?: string;
  showCounter?: boolean;
  preset?: InputPreset;
}

export const ValidatedCombobox: React.FC<ValidatedComboboxProps> = (props) => {
  // 应用预设配置
  const presetConfig = props.preset ? INPUT_PRESETS[props.preset] : {};
  const finalProps = { ...presetConfig, ...props };

  const {
    label,
    value,
    onChange,
    options,
    error,
    placeholder,
    required = false,
    disabled = false,
    maxByteLength,
    maxCharLength,
    className = '',
    showCounter = true
  } = finalProps;

  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  const inputId = `combobox-${label.replace(/\s+/g, '-').toLowerCase()}`;

  // 使用通用长度计算工具
  const { currentLength, maxLength, isOverLimit } = useLengthCalculation(inputValue, maxByteLength, maxCharLength);

  // 更新输入值
  useEffect(() => {
    const selectedOption = options.find(opt => opt.code === value);
    const displayValue = selectedOption ? selectedOption.value : (value || '');
    setInputValue(displayValue);
  }, [value, options]);

  // 过滤选项
  useEffect(() => {
    if (isOpen && inputValue) {
      const filtered = options.filter(option =>
        option.value.toLowerCase().includes(inputValue.toLowerCase())
      );
      setFilteredOptions(filtered);
    } else {
      setFilteredOptions(options);
    }
  }, [inputValue, isOpen, options]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    const matchingOption = options.find(opt => opt.value === newValue);
    const newCode = matchingOption ? matchingOption.code : newValue;
    onChange(newCode);

    if (!isOpen) {
      setIsOpen(true);
    }
  };

  const selectOption = (option: { code: string; value: string }) => {
    setInputValue(option.value);
    setIsOpen(false);
    onChange(option.code);
  };

  // 计算下拉菜单位置
  useEffect(() => {
    if (isOpen && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  }, [isOpen]);

  // 处理点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isOutsideInput = inputRef.current && !inputRef.current.contains(event.target as Node);
      const isOutsideDropdown = dropdownRef.current && !dropdownRef.current.contains(event.target as Node);

      if (isOutsideInput && isOutsideDropdown) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={className}>
      <label htmlFor={inputId} className="block text-sm font-bold text-gray-700 mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
        {!required && <span className="text-xs text-gray-500 ml-2">(可选)</span>}
      </label>
      <div className="relative">
        <input
          ref={inputRef}
          id={inputId}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          disabled={disabled}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
            error || isOverLimit
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          } ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''} ${
            showCounter && maxLength > 0 ? 'pr-20' : 'pr-10'
          }`}
          aria-invalid={!!(error || isOverLimit)}
          aria-describedby={error ? `${inputId}-error` : undefined}
        />

        {/* 字符/字节计数器 - 放在下拉按钮前面 */}
        {showCounter && maxLength > 0 && (
          <div className={`absolute right-10 top-1/2 transform -translate-y-1/2 text-xs font-mono ${
            isOverLimit ? 'text-red-500' : currentLength > maxLength * 0.8 ? 'text-orange-500' : 'text-gray-400'
          }`}>
            {currentLength}/{maxLength}
          </div>
        )}

        {/* 下拉按钮 */}
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          disabled={disabled}
        >
          <svg
            className={`h-4 w-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {/* 下拉选项列表 */}
      {isOpen && !disabled && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[9000] max-h-60 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }}
        >
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option) => (
              <div
                key={option.code}
                className="px-3 py-2 cursor-pointer hover:bg-blue-50 text-sm"
                onClick={() => selectOption(option)}
              >
                {option.value}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-gray-500 text-sm">无匹配选项</div>
          )}
        </div>,
        document.body
      )}

      {/* 显示超限错误或其他错误 */}
      <FieldError
        error={isOverLimit ? `超出限制，当前${currentLength}${maxByteLength ? '字节' : '字符'}，最大${maxLength}${maxByteLength ? '字节' : '字符'}` : error}
        fieldName={inputId}
      />
    </div>
  );
};

export const ValidatedInput: React.FC<ValidatedInputProps> = (props) => {
  // 应用预设配置
  const presetConfig = props.preset ? INPUT_PRESETS[props.preset] : {};
  const finalProps = { ...presetConfig, ...props };

  const {
    label,
    value,
    onChange,
    error,
    placeholder,
    required = false,
    disabled = false,
    maxByteLength,
    maxCharLength,
    formatHint,
    className = '',
    type = 'text',
    showCounter = true,
    autoFocus = false
  } = finalProps;
  const inputId = `input-${(label || 'field').replace(/\s+/g, '-').toLowerCase()}`;

  // 使用通用长度计算工具
  const { currentLength, maxLength, isOverLimit } = useLengthCalculation(value, maxByteLength, maxCharLength);

  // 构建格式提示的placeholder
  const buildPlaceholder = () => {
    if (formatHint && placeholder) {
      return `${placeholder} (${formatHint})`;
    }
    if (formatHint) {
      return formatHint;
    }
    return placeholder;
  };

  return (
    <div className={className}>
      {label && (
        <label htmlFor={inputId} className="block text-sm font-bold text-gray-700 mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
          {!required && <span className="text-xs text-gray-500 ml-2">(可选)</span>}
        </label>
      )}
      <div className="relative">
        <input
          id={inputId}
          type={type}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={buildPlaceholder()}
          disabled={disabled}
          autoFocus={autoFocus}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
            error || isOverLimit
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          } ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''} ${
            showCounter && maxLength > 0 ? 'pr-16' : ''
          }`}
          aria-invalid={!!(error || isOverLimit)}
          aria-describedby={error ? `${inputId}-error` : undefined}
        />

        {/* 字符/字节计数器 */}
        {showCounter && maxLength > 0 && (
          <div className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-xs font-mono ${
            isOverLimit ? 'text-red-500' : currentLength > maxLength * 0.8 ? 'text-orange-500' : 'text-gray-400'
          }`}>
            {currentLength}/{maxLength}
          </div>
        )}
      </div>

      {/* 显示超限错误或其他错误 */}
      <FieldError
        error={isOverLimit ? `超出限制，当前${currentLength}${maxByteLength ? '字节' : '字符'}，最大${maxLength}${maxByteLength ? '字节' : '字符'}` : error}
        fieldName={inputId}
      />
    </div>
  );
};

/**
 * 表单字段包装组件
 * 提供统一的字段布局和错误处理
 */
interface FormFieldProps {
  children: React.ReactNode;
  label: string;
  fieldName: string;
  error?: string;
  required?: boolean;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  children,
  label,
  fieldName,
  error,
  required = false,
  className = ''
}) => {
  return (
    <div className={`form-field ${className}`} data-field={fieldName}>
      <label
        htmlFor={fieldName}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {label}
        {required && <span className="text-red-500 ml-1" aria-label="必填">*</span>}
      </label>
      
      <div className="relative">
        {children}
        {error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg
              className="h-5 w-5 text-red-500"
              fill="currentColor"
              viewBox="0 0 20 20"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>
      
      <FieldError error={error} fieldName={fieldName} />
    </div>
  );
};
