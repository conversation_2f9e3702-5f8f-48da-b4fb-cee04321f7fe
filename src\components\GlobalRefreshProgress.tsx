import React, { useState, useEffect } from 'react';
import { RefreshCw, CheckCircle, AlertCircle, Database } from 'lucide-react';
import GlobalDataRefreshService from '../services/globalDataRefreshService';

interface GlobalRefreshProgressProps {
  className?: string;
}

/**
 * 全局数据刷新进度组件
 * 显示切换备份时的数据刷新进度
 */
export const GlobalRefreshProgress: React.FC<GlobalRefreshProgressProps> = ({
  className = ''
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const globalRefreshService = GlobalDataRefreshService.getInstance();

    // 监听刷新开始
    const handleRefreshStarted = () => {
      console.log('全局刷新开始');
      setIsRefreshing(true);
      setProgress(0);
      setCurrentStep('准备刷新');
      setIsVisible(true);
    };

    // 监听刷新进度
    const handleRefreshProgress = ({ step, progress: newProgress }: { step: string; progress: number }) => {
      console.log(`刷新进度: ${step} - ${newProgress}%`);
      setCurrentStep(step);
      setProgress(newProgress);
    };

    // 监听刷新完成
    const handleRefreshCompleted = () => {
      console.log('全局刷新完成');
      setIsRefreshing(false);
      setProgress(100);
      setCurrentStep('刷新完成');

      // 3秒后隐藏进度条
      setTimeout(() => {
        setIsVisible(false);
      }, 3000);
    };

    // 监听刷新失败
    const handleRefreshFailed = ({ error, step }: { error: string; step: string }) => {
      console.error(`刷新失败: ${step} - ${error}`);
      setIsRefreshing(false);
      setCurrentStep(`${step} 失败`);

      // 5秒后隐藏进度条
      setTimeout(() => {
        setIsVisible(false);
      }, 5000);
    };

    // 订阅事件
    globalRefreshService.on('refresh-started', handleRefreshStarted);
    globalRefreshService.on('refresh-progress', handleRefreshProgress);
    globalRefreshService.on('refresh-completed', handleRefreshCompleted);
    globalRefreshService.on('refresh-failed', handleRefreshFailed);

    // 检查当前状态
    const currentProgress = globalRefreshService.getRefreshProgress();
    if (currentProgress.isRefreshing) {
      setIsRefreshing(true);
      setProgress(currentProgress.progress);
      setCurrentStep(currentProgress.currentStep);
      setIsVisible(true);
    }

    return () => {
      globalRefreshService.off('refresh-started', handleRefreshStarted);
      globalRefreshService.off('refresh-progress', handleRefreshProgress);
      globalRefreshService.off('refresh-completed', handleRefreshCompleted);
      globalRefreshService.off('refresh-failed', handleRefreshFailed);
    };
  }, []);

  if (!isVisible) {
    return null;
  }

  return (
    <>
      {/* 遮罩层 - 阻止用户操作 */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-[9998] flex items-center justify-center">
        {/* 进度对话框 */}
        <div className="bg-white rounded-xl shadow-2xl border border-gray-200 p-8 min-w-96 max-w-md mx-4">
          {/* 标题栏 */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <Database className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">数据刷新中</h3>
            <p className="text-gray-600">
              正在重新加载所有数据，请稍候...
            </p>
          </div>

          {/* 进度条 */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-700">刷新进度</span>
              <span className="text-sm font-semibold text-blue-600">{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out shadow-sm"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* 当前步骤 */}
          <div className="flex items-center justify-center space-x-3 mb-6">
            {isRefreshing ? (
              <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
            ) : progress === 100 ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-red-600" />
            )}
            <span className="text-gray-700 font-medium">{currentStep}</span>
          </div>

          {/* 详细信息 */}
          {isRefreshing && (
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                </div>
                <div>
                  <p className="text-sm text-blue-900 font-medium mb-1">正在刷新的模块：</p>
                  <ul className="text-xs text-blue-700 space-y-1">
                    <li>• 台账管理数据</li>
                    <li>• 部门组织架构</li>
                    <li>• 扩展字段配置</li>
                    <li>• 全局系统配置</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {progress === 100 && !isRefreshing && (
            <div className="bg-green-50 rounded-lg p-4 border border-green-200">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                <div>
                  <p className="text-sm text-green-900 font-medium">刷新完成！</p>
                  <p className="text-xs text-green-700 mt-1">
                    所有数据已更新，系统已准备就绪
                  </p>
                </div>
              </div>
            </div>
          )}

          {!isRefreshing && progress < 100 && (
            <div className="bg-red-50 rounded-lg p-4 border border-red-200">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
                <div>
                  <p className="text-sm text-red-900 font-medium">刷新遇到问题</p>
                  <p className="text-xs text-red-700 mt-1">
                    部分数据可能未能正确加载
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 底部提示 */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              {isRefreshing ? '请勿关闭窗口或进行其他操作' : '即将自动关闭...'}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};
