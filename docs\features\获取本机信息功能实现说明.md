# 获取本机信息功能实现说明

## 功能概述

在台账管理页面的添加设备台账菜单中，新增了"获取本机信息"按钮功能，可以自动获取当前计算机的硬件和系统信息，并自动填充到添加设备台账的表单中。

## 实现的文件

### 1. 修改的文件

#### `src/pages/Inventory/InventoryManagement/Dialogs/AddInventoryDialog.tsx`
- 添加了TaskManager导入
- 新增获取本机信息相关状态：`isCollectingInfo`、`collectInfoError`、`formUpdateFunction`
- 实现了`handleCollectSystemInfo`函数，调用SystemInfoDLL.dll的CollectInventoryInfo函数
- 修改了按钮区域布局，添加了"获取本机信息"按钮
- 添加了表单数据更新机制

#### `src/pages/Inventory/InventoryManagement/Dialogs/InventoryForm.tsx`
- 新增`onFormDataUpdate`属性接口
- 实现了`updateFormData`函数用于更新表单数据
- 添加了useEffect来暴露updateFormData函数给父组件

## DLL调用规范

### DLL文件
- **DLL名称**: `SystemInfoDLL.dll`
- **导出函数**: `CollectInventoryInfo`
- **传参**: `{}`（空对象）

### API返回数据格式
```json
{
  "data": {
    "computerName": "LAPTOP-N103OECJ",
    "computerType": "便携机",
    "diskSerialNumber": "S67MNF3T817395",
    "installDate": "2025-03-27 01:30:59",
    "ipAddresses": "**************",
    "macAddress": "C0:35:32:E4:CF:52",
    "model": "Lenovo 83LQ",
    "version": "Windows 11"
  },
  "message": "台账信息收集完成",
  "status": "success",
  "timestamp": 1753865286953
}
```

### 字段映射说明
| 后端字段 | 中文含义 | 前端映射 |
|---------|---------|---------|
| computerName | 计算机名称 | 名称字段 |
| model | 型号 | 型号字段 |
| computerType | 计算机类型（台式机/便携机） | 设备类型字段（智能识别） |
| version | 操作系统 | 操作系统扩展字段 |
| installDate | 操作系统安装日期 | 安装日期扩展字段（时间选择器） |
| diskSerialNumber | 硬盘序列号 | 硬盘序列号扩展字段（多个用;分隔） |
| macAddress | MAC地址 | MAC地址扩展字段 |
| ipAddresses | IP地址（逗号分隔） | IP地址扩展字段 |

## 功能特性

### 1. 智能字段映射
- **基础字段映射**：
  - 计算机名称 → 名称
  - 型号 → 型号
  - 计算机类型 → 设备类型（智能识别台式机/便携机）

- **扩展字段映射**：
  - 操作系统 → 查找包含"操作系统"的扩展字段
  - 安装日期 → 查找包含"安装日期"的扩展字段，自动转换为时间选择器格式
  - 硬盘序列号 → 查找包含"硬盘序列号"的扩展字段
  - MAC地址 → 查找包含"mac"的扩展字段
  - IP地址 → 查找包含"ip"的扩展字段

### 2. 设备类型智能识别
- 当`computerType`为"台式机"时，自动选择台式机类型
- 当`computerType`为"便携机"时，自动选择便携机类型
- 如果识别的类型不是这两种，不修改设备类型字段

### 3. 时间格式处理
- 操作系统安装日期自动转换为DatePicker期望的格式：`YYYY-MM-DDTHH:mm:ss`
- 支持时间选择器的秒级精度显示

### 4. 多硬盘序列号支持
- 支持多个硬盘序列号，使用分号(;)分隔
- 直接填充到硬盘序列号扩展字段

## UI界面设计

### 1. 按钮布局
- **左侧**：获取本机信息按钮
- **右侧**：取消和保存按钮
- 使用`justify-between`布局，保持界面平衡

### 2. 按钮状态
- **正常状态**：显示"获取本机信息"文字和文档图标
- **加载状态**：显示旋转动画和"获取中..."文字
- **禁用状态**：在正在获取信息或表单提交时禁用

### 3. 错误提示
- 在按钮旁边显示错误信息
- 使用红色文字提示获取失败的原因

## 技术实现

### 1. DLL调用流程
```typescript
// 1. 获取TaskManager实例
const taskManager = TaskManager.getInstance();

// 2. 提交任务
const taskId = await taskManager.submitTask('SystemInfoDLL', 'CollectInventoryInfo', {});

// 3. 等待结果
const result = await taskManager.waitForTaskResult(taskId);

// 4. 解析数据并填充表单
if (result && result.data) {
  // 处理字段映射和数据填充
}
```

### 2. 表单数据更新机制
- 通过`onFormDataUpdate`回调传递更新函数给父组件
- 父组件保存更新函数引用，在获取到系统信息后调用
- 支持基础字段和扩展字段的同时更新

### 3. 字段查找算法
- 使用模糊匹配查找对应的扩展字段
- 支持中文字段名和英文key的匹配
- 优先匹配完整字段名，其次匹配关键词

## 错误处理

### 1. DLL调用错误
- 网络连接失败
- DLL文件不存在
- 函数调用超时

### 2. 数据解析错误
- 返回数据格式不正确
- 必要字段缺失
- 日期格式转换失败

### 3. 表单更新错误
- 表单组件未初始化
- 字段映射失败
- 数据类型不匹配

## 使用方法

1. 打开台账管理页面
2. 点击"添加设备台账"按钮
3. 在弹出的对话框中，点击左下角的"获取本机信息"按钮
4. 等待系统信息获取完成
5. 检查自动填充的字段信息
6. 手动补充或修改其他必要信息
7. 点击"保存"完成设备台账添加

## 注意事项

1. **权限要求**：需要系统具有读取硬件信息的权限
2. **网络环境**：需要WebSocket连接正常工作
3. **扩展字段**：只有在设备类型对应的扩展字段存在时才会自动填充
4. **数据覆盖**：获取本机信息会覆盖已填写的对应字段
5. **错误恢复**：获取失败时不会影响手动填写功能

## 后续优化建议

1. **缓存机制**：对获取到的系统信息进行短期缓存
2. **字段配置**：允许用户自定义字段映射关系
3. **批量获取**：支持批量获取多台设备的信息
4. **信息验证**：对获取到的信息进行有效性验证
5. **历史记录**：保存获取信息的历史记录供参考
