import { AppError } from '../services/base/baseService';

/**
 * 处理服务错误
 * 将各种错误类型转换为标准的AppError
 * @param error 原始错误
 * @returns 标准化的AppError
 */
export function handleServiceError(error: unknown): AppError {
  if (error instanceof AppError) {
    return error;
  }
  
  if (error instanceof Error) {
    return new AppError(error.message, 'SERVICE_ERROR', error);
  }
  
  return new AppError(
    typeof error === 'string' ? error : '发生未知错误',
    'UNKNOWN_ERROR',
    error
  );
}

/**
 * 清理技术性ID和术语
 * @param message 原始消息
 * @returns 清理后的消息
 */
function cleanTechnicalTerms(message: string): string {
  return message
    // 清理部门相关的技术性ID
    .replace(/dept-\d+/gi, '部门')
    .replace(/all-dept/gi, '公司')
    .replace(/person-\d+/gi, '人员')
    // 清理其他技术术语
    .replace(/\bdept\b/gi, '部门')
    .replace(/\bdepartment\b/gi, '部门')
    .replace(/\bperson\b/gi, '人员')
    .replace(/\buser\b/gi, '用户')
    // 清理多余的空格和标点
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * 本地化错误消息
 * 将技术性的错误消息转换为用户友好的中文消息
 * @param message 原始错误消息
 * @returns 本地化的错误消息
 */
export function localizeErrorMessage(message: string): string {
  if (!message) return '发生未知错误';

  // 首先清理技术性术语
  const cleanedMessage = cleanTechnicalTerms(message);
  const lowerMessage = cleanedMessage.toLowerCase();

  // 处理包含技术性ID的错误消息
  if (lowerMessage.includes('dept-') || lowerMessage.includes('all-dept')) {
    if (lowerMessage.includes('not found') || lowerMessage.includes('未找到')) {
      return '未找到指定的部门';
    }
    if (lowerMessage.includes('duplicate') || lowerMessage.includes('重复')) {
      return '部门名称重复，请使用不同的名称';
    }
    if (lowerMessage.includes('invalid') || lowerMessage.includes('无效')) {
      return '部门信息无效，请检查输入内容';
    }
    if (lowerMessage.includes('permission') || lowerMessage.includes('权限')) {
      return '没有操作该部门的权限';
    }
    return '部门操作失败，请稍后重试';
  }

  // 部门相关错误
  if (lowerMessage.includes('dept') || lowerMessage.includes('department')) {
    if (lowerMessage.includes('not found') || lowerMessage.includes('未找到')) {
      return '未找到指定的部门';
    }
    if (lowerMessage.includes('duplicate') || lowerMessage.includes('重复')) {
      return '部门名称重复，请使用不同的名称';
    }
    if (lowerMessage.includes('invalid') || lowerMessage.includes('无效')) {
      return '部门信息无效，请检查输入内容';
    }
    if (lowerMessage.includes('permission') || lowerMessage.includes('权限')) {
      return '没有操作该部门的权限';
    }
    return '部门操作失败，请稍后重试';
  }

  // 人员相关错误
  if (lowerMessage.includes('person') || lowerMessage.includes('user') || lowerMessage.includes('人员')) {
    if (lowerMessage.includes('not found') || lowerMessage.includes('未找到')) {
      return '未找到指定的人员';
    }
    if (lowerMessage.includes('duplicate') || lowerMessage.includes('重复')) {
      return '人员信息重复';
    }
    if (lowerMessage.includes('permission') || lowerMessage.includes('权限')) {
      return '没有操作该人员的权限';
    }
    return '人员操作失败，请稍后重试';
  }

  // 网络相关错误
  if (lowerMessage.includes('network') || lowerMessage.includes('connection') || lowerMessage.includes('timeout')) {
    return '网络连接失败，请检查网络连接后重试';
  }

  // 权限相关错误
  if (lowerMessage.includes('permission') || lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
    return '权限不足，无法执行此操作';
  }

  // 数据库相关错误
  if (lowerMessage.includes('database') || lowerMessage.includes('sql') || lowerMessage.includes('db')) {
    return '数据库操作失败，请稍后重试';
  }

  // 验证相关错误
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid') || lowerMessage.includes('format')) {
    return '输入数据格式不正确，请检查后重新输入';
  }

  // 如果包含常见的技术术语，提供通用的友好消息
  if (lowerMessage.includes('error') || lowerMessage.includes('failed') || lowerMessage.includes('exception')) {
    return '操作失败，请稍后重试';
  }

  // 如果消息很短且可能是技术代码，提供通用消息
  if (message.length < 10 && /^[a-zA-Z_]+$/.test(message)) {
    return '操作失败，请稍后重试';
  }

  // 返回原始消息（如果已经是中文或用户友好的消息）
  return message;
}

/**
 * 格式化错误消息
 * 根据错误代码生成用户友好的错误消息
 * @param error AppError对象
 * @returns 格式化的错误消息
 */
export function formatErrorMessage(error: AppError): string {
  // 首先尝试本地化错误消息
  const localizedMessage = localizeErrorMessage(error.message);

  // 根据错误代码返回不同的错误消息
  switch (error.code) {
    case 'NETWORK_ERROR':
      return '网络连接错误，请检查您的网络连接';
    case 'TASK_ERROR':
      return `任务执行失败: ${localizedMessage}`;
    case 'SERVICE_ERROR':
      return `服务错误: ${localizedMessage}`;
    case 'VALIDATION_ERROR':
      return `数据验证失败: ${localizedMessage}`;
    case 'BUSINESS_ERROR':
      return localizedMessage; // 业务错误直接使用本地化后的消息
    default:
      return localizedMessage || '发生未知错误';
  }
}

/**
 * 记录错误
 * 将错误信息记录到控制台或发送到错误跟踪服务
 * @param error 错误对象
 * @param context 错误上下文
 */
export function logError(error: unknown, context: string = ''): void {
  const appError = handleServiceError(error);
  
  console.error(
    `[ERROR] ${context ? `[${context}] ` : ''}${appError.code}: ${appError.message}`,
    appError.details || ''
  );
  
  // 这里可以添加将错误发送到错误跟踪服务的逻辑
}

/**
 * 创建特定类型的错误
 * @param message 错误消息
 * @param details 错误详情
 * @returns 网络错误
 */
export function createNetworkError(message: string, details?: any): AppError {
  return new AppError(message, 'NETWORK_ERROR', details);
}

/**
 * 创建验证错误
 * @param message 错误消息
 * @param details 错误详情
 * @returns 验证错误
 */
export function createValidationError(message: string, details?: any): AppError {
  return new AppError(message, 'VALIDATION_ERROR', details);
}

/**
 * 创建任务错误
 * @param message 错误消息
 * @param details 错误详情
 * @returns 任务错误
 */
export function createTaskError(message: string, details?: any): AppError {
  return new AppError(message, 'TASK_ERROR', details);
}

/**
 * 处理任务管理器相关的错误
 * @param taskType 任务类型
 * @param error 错误对象
 * @returns 标准化的Error对象
 */
export function handleTaskError(taskType: string, error: unknown): Error {
  const errorMessage = error instanceof Error ? error.message : `${taskType}任务执行失败`;
  console.error(`${taskType}任务失败:`, error);
  return new Error(errorMessage);
}

/**
 * 处理RFID相关操作的错误
 * @param operation 操作名称
 * @param error 错误对象
 * @returns 格式化的错误消息
 */
export function handleRfidError(operation: string, error: unknown): string {
  const errorMessage = error instanceof Error ? error.message : `${operation}失败`;
  console.error(`${operation}失败:`, error);
  return errorMessage;
}
