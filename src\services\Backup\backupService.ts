import { BaseService, BaseServiceState, BaseServiceEvents } from '../base/baseService';
import GlobalDataRefreshService from '../globalDataRefreshService';
import { formatFileSize, formatRelativeTime } from '../../utils/formatUtils';

/**
 * 备份数据接口
 */
export interface BackupData {
  backup_id: number;
  backup_path: string;
  backup_name: string;        // 备份备注
  backup_timestamp: number;
  backup_time_str: string;
  backup_size: number;
  backup_filename: string;
  backup_directory?: string;
  md5_hash?: string;
  is_active?: number;
}

/**
 * API响应接口
 */
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
}

/**
 * 备份服务状态接口
 */
export interface BackupServiceState extends BaseServiceState {
  backups: BackupData[];
  activeBackup: BackupData | null;
  isCreating: boolean;
  isSwitching: boolean;
  isDataCached: boolean; // 标记数据是否已缓存
}

/**
 * 备份服务事件接口
 */
export interface BackupServiceEvents extends BaseServiceEvents<BackupServiceState> {
  'backup-created': BackupData;
  'backup-switched': BackupData;
  'backups-loaded': BackupData[];
  'active-backup-loaded': BackupData | null;
}

/**
 * 数据库备份服务类
 * 提供数据库备份管理功能，类似git checkout
 */
class BackupService extends BaseService<BackupServiceState, BackupServiceEvents> {
  private static instance: BackupService;
  private requestQueue: Promise<any> = Promise.resolve();
  private activeRequests: Set<string> = new Set(); // 跟踪活跃的请求
  private lastRequestTime: Map<string, number> = new Map(); // 跟踪最后请求时间

  /**
   * 获取备份服务实例
   */
  public static getInstance(): BackupService {
    if (!BackupService.instance) {
      BackupService.instance = new BackupService();
    }
    return BackupService.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      backups: [],
      activeBackup: null,
      isCreating: false,
      isSwitching: false,
      isDataCached: false,
    });
  }

  /**
   * 创建数据库备份
   * @param backupPath 备份路径
   * @param backupName 备份备注（可选）
   */
  public async createBackup(backupPath: string, backupName?: string): Promise<BackupData> {
    try {
      this.updateState({ isCreating: true });

      const params = {
        action: 'backup_database',
        action_params: {
          backup_path: backupPath,
          backup_name: backupName || '',
        },
      };

      const result = await this.submitTask<ApiResponse<BackupData>>('DbFun', params);

      if (!result.success) {
        throw new Error(result.error?.message || '创建备份失败');
      }

      const backupData = result.data!;

      // 更新备份列表，保持缓存状态
      const updatedBackups = [...this.state.backups, backupData];
      this.updateState({
        backups: updatedBackups,
        isCreating: false,
        isDataCached: true // 确保缓存状态保持有效
      });

      this.emitter.emit('backup-created', backupData);
      return backupData;
    } catch (error) {
      this.updateState({ isCreating: false });
      throw this.handleError(error);
    }
  }

  /**
   * 队列化执行请求，确保不并发
   */
  private async queueRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    this.requestQueue = this.requestQueue.then(async () => {
      try {
        return await requestFn();
      } catch (error) {
        throw error;
      }
    });
    return this.requestQueue;
  }

  /**
   * 获取备份列表
   * @param forceRefresh 是否强制刷新，忽略缓存
   */
  public async getBackupList(forceRefresh: boolean = false): Promise<BackupData[]> {
    const requestKey = 'getBackupList';
    const now = Date.now();
    const lastTime = this.lastRequestTime.get(requestKey) || 0;

    // 如果数据已缓存且不是强制刷新，直接返回缓存数据
    if (this.state.isDataCached && !forceRefresh) {
      console.log('备份列表已缓存，返回缓存数据');
      return this.state.backups;
    }

    // 防止短时间内的重复请求（500ms内）
    if (now - lastTime < 500) {
      console.log('短时间内重复请求，返回缓存数据');
      return this.state.backups;
    }

    // 添加调用栈追踪（仅在开发模式）
    if (process.env.NODE_ENV === 'development') {
      const stack = new Error().stack;
      console.log('getBackupList 被调用，调用栈:', stack?.split('\n').slice(0, 5).join('\n'));
    }

    // 防止重复请求
    if (this.activeRequests.has(requestKey)) {
      console.log('备份列表请求已在进行中，跳过重复请求');
      // 等待当前请求完成
      while (this.activeRequests.has(requestKey)) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.state.backups;
    }

    // 记录请求时间
    this.lastRequestTime.set(requestKey, now);

    return this.queueRequest(async () => {
      // 再次检查，防止竞态条件
      if (this.activeRequests.has(requestKey)) {
        console.log('队列中发现重复请求，返回当前状态');
        return this.state.backups;
      }

      this.activeRequests.add(requestKey);

      try {
        console.log('开始获取备份列表...');
        const params = {
          action: 'get_backup_list',
          action_params: {},
        };

        const result = await this.submitTask<ApiResponse<BackupData[]>>('DbFun', params);

        if (!result.success) {
          throw new Error(result.error?.message || '获取备份列表失败');
        }

        const backups = result.data || [];
        this.updateState({
          backups,
          isDataCached: true // 标记数据已缓存
        });

        this.emitter.emit('backups-loaded', backups);
        console.log('备份列表获取完成，数据已缓存');
        return backups;
      } catch (error) {
        throw this.handleError(error);
      } finally {
        this.activeRequests.delete(requestKey);
      }
    });
  }

  /**
   * 切换到指定备份
   * @param backupId 备份ID
   */
  public async switchToBackup(backupId: number): Promise<BackupData> {
    try {
      this.updateState({ isSwitching: true });

      const params = {
        action: 'switch_to_backup',
        action_params: {
          backup_id: backupId,
        },
      };

      const result = await this.submitTask<ApiResponse<BackupData>>('DbFun', params);

      if (!result.success) {
        throw new Error(result.error?.message || '切换备份失败');
      }

      const backupData = result.data!;

      // 更新活动备份和备份列表中的is_active状态
      const updatedBackups = this.state.backups.map(backup => ({
        ...backup,
        is_active: backup.backup_id === backupId ? 1 : 0,
      }));

      this.updateState({
        activeBackup: backupData,
        backups: updatedBackups,
        isSwitching: false
      });

      this.emitter.emit('backup-switched', backupData);

      // 切换备份成功后，触发全局数据刷新
      try {
        console.log('备份切换成功，开始全局数据刷新...');
        const globalRefreshService = GlobalDataRefreshService.getInstance();

        // 异步执行全局刷新，不阻塞当前操作
        globalRefreshService.refreshAllData().catch(error => {
          console.error('全局数据刷新失败:', error);
        });

        console.log('全局数据刷新已启动');
      } catch (error) {
        console.error('启动全局数据刷新失败:', error);
      }

      return backupData;
    } catch (error) {
      this.updateState({ isSwitching: false });
      throw this.handleError(error);
    }
  }

  /**
   * 获取当前活动备份
   */
  public async getActiveBackup(): Promise<BackupData | null> {
    const requestKey = 'getActiveBackup';
    const now = Date.now();
    const lastTime = this.lastRequestTime.get(requestKey) || 0;

    // 防止短时间内的重复请求（500ms内）
    if (now - lastTime < 500) {
      console.log('短时间内重复请求活动备份，返回缓存数据');
      return this.state.activeBackup;
    }

    // 添加调用栈追踪（仅在开发模式）
    if (process.env.NODE_ENV === 'development') {
      const stack = new Error().stack;
      console.log('getActiveBackup 被调用，调用栈:', stack?.split('\n').slice(0, 5).join('\n'));
    }

    // 防止重复请求
    if (this.activeRequests.has(requestKey)) {
      console.log('活动备份请求已在进行中，跳过重复请求');
      // 等待当前请求完成
      while (this.activeRequests.has(requestKey)) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.state.activeBackup;
    }

    // 记录请求时间
    this.lastRequestTime.set(requestKey, now);

    return this.queueRequest(async () => {
      // 再次检查，防止竞态条件
      if (this.activeRequests.has(requestKey)) {
        console.log('队列中发现重复的活动备份请求，返回当前状态');
        return this.state.activeBackup;
      }

      this.activeRequests.add(requestKey);

      try {
        console.log('开始获取活动备份...');
        const params = {
          action: 'get_active_backup',
          action_params: {},
        };

        const result = await this.submitTask<ApiResponse<BackupData>>('DbFun', params);

        if (!result.success) {
          // 如果未找到活动备份，返回null
          if (result.error?.code === 'E4004') {
            this.updateState({ activeBackup: null });
            this.emitter.emit('active-backup-loaded', null);
            console.log('未找到活动备份');
            return null;
          }
          throw new Error(result.error?.message || '获取活动备份失败');
        }

        const activeBackup = result.data!;
        this.updateState({ activeBackup });

        this.emitter.emit('active-backup-loaded', activeBackup);
        console.log('活动备份获取完成');
        return activeBackup;
      } catch (error) {
        throw this.handleError(error);
      } finally {
        this.activeRequests.delete(requestKey);
      }
    });
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   */
  public formatFileSize(bytes: number): string {
    // 使用通用的格式化工具
    return formatFileSize(bytes);
  }

  /**
   * 格式化时间为相对时间
   * @param timestamp 时间戳（秒级）
   */
  public formatRelativeTime(timestamp: number): string {
    // 处理无效值
    if (timestamp == null || isNaN(timestamp) || timestamp <= 0) {
      return '未知时间';
    }

    // 转换为毫秒级时间戳并使用统一的格式化函数
    return formatRelativeTime(timestamp * 1000);
  }

  /**
   * 清除缓存，强制下次获取时重新请求数据
   */
  public clearCache(): void {
    console.log('清除备份数据缓存');
    this.updateState({ isDataCached: false });
  }

  /**
   * 强制刷新备份列表（忽略缓存）
   */
  public async forceRefreshBackups(): Promise<BackupData[]> {
    console.log('强制刷新备份列表');
    return this.getBackupList(true);
  }

  /**
   * 初始化服务（不自动加载数据）
   */
  public async initialize(): Promise<void> {
    await super.initialize();

    // 不再在初始化时自动加载数据
    // 数据加载由页面组件或用户操作触发
    console.log('BackupService 初始化完成，数据加载由页面控制');
  }
}

export default BackupService;