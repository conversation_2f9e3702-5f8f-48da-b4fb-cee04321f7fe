/**
 * 任务信息接口
 * 描述任务的状态和结果
 */
export interface TaskInfo {
  id: string;
  status: 'waiting' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  result?: any;
  error?: string;
  createdAt: number;
  attempts: number;
  maxAttempts: number;
  taskType?: string;
  params?: any;
}

/**
 * 任务参数接口
 * 用于提交任务时的参数
 */
export interface TaskParams {
  dllName: string;
  funcName: string;
  params: any;
  priority?: number;
}

/**
 * 服务响应接口
 * 描述服务响应的通用格式
 */
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * 分页参数接口
 * 用于分页请求
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
}

/**
 * 分页结果接口
 * 描述分页结果的通用格式
 */
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 排序参数接口
 * 用于排序请求
 */
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

/**
 * 过滤参数接口
 * 用于过滤请求
 */
export interface FilterParams {
  field: string;
  value: any;
  operator?: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith';
}

/**
 * 查询参数接口
 * 组合分页、排序和过滤参数
 */
export interface QueryParams {
  pagination?: PaginationParams;
  sort?: SortParams[];
  filters?: FilterParams[];
  search?: string;
}

/**
 * 选择器函数类型
 * 用于从状态中选择特定数据
 */
export type Selector<T, R> = (state: T) => R;

/**
 * 服务操作结果接口
 * 描述服务操作的结果
 */
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
