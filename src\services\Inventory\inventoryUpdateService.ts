import TaskManager, { TaskInfo } from '../../utils/taskManager';
import WebSocketManager from '../../utils/websocket';
import { InventoryItem } from '../../types/inventory';
import ExtFieldService from './extFieldService';
import mitt from 'mitt';
import { convertToUnixTimestamp } from '../../utils/fieldUtils';

// 定义更新服务事件类型
type UpdateServiceEvents = {
  'device-updated': { id: string; updates: Partial<InventoryItem>; result: any };
};

class InventoryUpdateService {
  private static instance: InventoryUpdateService;
  private ws: WebSocketManager;
  private task: TaskManager;
  private dllName = 'AccountTableDll';
  private isUpdatingItem = false; // 更新操作标志位
  private emitter = mitt<UpdateServiceEvents>(); // 事件发射器

  private constructor() {
    this.ws = WebSocketManager.getInstance();
    this.task = TaskManager.getInstance();
  }



  public static getInstance(): InventoryUpdateService {
    if (!InventoryUpdateService.instance) {
      InventoryUpdateService.instance = new InventoryUpdateService();
    }
    return InventoryUpdateService.instance;
  }

  /**
   * 订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on<K extends keyof UpdateServiceEvents>(event: K, callback: (data: UpdateServiceEvents[K]) => void): void {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off<K extends keyof UpdateServiceEvents>(event: K, callback: (data: UpdateServiceEvents[K]) => void): void {
    this.emitter.off(event, callback as any);
  }

  /**
   * 提交任务并等待结果
   */
  private async submitAndWaitTask<T>(funcName: string, params: any = {}, retryCount: number = 0): Promise<T> {
    // 最大重试次数
    const MAX_RETRIES = 2;
    // 超时时间（毫秒），增加到60秒
    const TIMEOUT = 60000;

    // 确保WebSocket已连接
    if (!this.ws.isConnected()) {
      try {
        console.log('尝试连接WebSocket...');
        await this.ws.connect();
      } catch (error) {
        console.error('WebSocket连接失败:', error);
        throw new Error('WebSocket连接失败');
      }
    }

    try {
      console.log(`准备提交任务: ${funcName}, 重试次数: ${retryCount}`);
      const taskId = await this.task.submitTask(this.dllName, funcName, params);
      console.log(`提交任务成功: ${funcName}, taskId: ${taskId}, 参数:`, JSON.stringify(params));

      return new Promise((resolve, reject) => {
        // 设置超时
        const timeoutId = setTimeout(async () => {
          this.task.offTaskUpdate(taskId, handleTaskUpdate);
          console.warn(`任务超时: ${funcName}, taskId: ${taskId}, 超时时间: ${TIMEOUT}ms`);

          // 如果未达到最大重试次数，则重试
          if (retryCount < MAX_RETRIES) {
            console.log(`准备重试任务: ${funcName}, 当前重试次数: ${retryCount + 1}/${MAX_RETRIES}`);
            try {
              // 重试任务
              const result = await this.submitAndWaitTask<T>(funcName, params, retryCount + 1);
              resolve(result);
            } catch (retryError) {
              console.error(`重试任务失败: ${funcName}, 错误:`, retryError);
              reject(retryError);
            }
          } else {
            console.error(`任务超时且达到最大重试次数: ${funcName}, taskId: ${taskId}`);
            reject(new Error(`任务超时: ${funcName}, taskId: ${taskId}, 已重试${retryCount}次`));
          }
        }, TIMEOUT);

        const handleTaskUpdate = (taskInfo: TaskInfo) => {
          console.log(`任务更新: ${taskId}, status: ${taskInfo.status}, progress: ${taskInfo.progress}`);

          if (taskInfo.result) {
            console.log(`任务结果: ${taskId}, 结果类型: ${typeof taskInfo.result}`);
          }

          if (taskInfo.status === 'completed') {
            clearTimeout(timeoutId);
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            if (taskInfo.result) {
              // 检查结果中的success字段
              let resultData = taskInfo.result;

              // 如果结果是字符串，尝试解析为JSON
              if (typeof resultData === 'string') {
                try {
                  resultData = JSON.parse(resultData);
                } catch (parseError) {
                  console.warn(`解析任务结果失败: ${funcName}, taskId: ${taskId}`, parseError);
                }
              }

              // 检查业务逻辑是否成功
              if (resultData && typeof resultData === 'object' && resultData.success === false) {
                const errorMessage = resultData.error_message || resultData.message || '操作失败';
                console.error(`任务业务逻辑失败: ${funcName}, taskId: ${taskId}, 错误: ${errorMessage}`);
                reject(new Error(errorMessage));
                return;
              }

              console.log(`任务完成: ${funcName}, taskId: ${taskId}`);
              resolve(taskInfo.result as T);
            } else {
              console.error(`任务完成但结果为空: ${funcName}, taskId: ${taskId}`);
              reject(new Error('任务返回结果为空'));
            }
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            clearTimeout(timeoutId);
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            const errorMsg = taskInfo.error || '任务失败';
            console.error(`任务失败: ${funcName}, taskId: ${taskId}, 错误:`, errorMsg);
            reject(new Error(errorMsg));
          }
        };

        this.task.onTaskUpdate(taskId, handleTaskUpdate);
      });
    } catch (error) {
      console.error(`提交任务失败: ${funcName}, 错误:`, error);
      throw error;
    }
  }

  /**
   * 更新设备信息
   * @param id 设备ID
   * @param updates 要更新的字段
   * @returns 更新后的设备信息
   */
  public async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<void> {
    // 防止重复更新
    if (this.isUpdatingItem) {
      console.log('正在更新设备中，跳过重复更新请求');
      throw new Error('操作进行中，请稍后再试');
    }

    this.isUpdatingItem = true;

    try {
      // 直接使用数字ID（现在ID已经是数字格式）
      const deviceId = parseInt(id);

      // 准备更新参数
      const actionParams: any = {
        "id": deviceId
      };

      // 添加需要更新的字段
      if (updates.location !== undefined) actionParams.location = updates.location;
      if (updates.name !== undefined) actionParams.device_name_brand = updates.name;
      if (updates.model !== undefined) actionParams.model = updates.model;

      // 处理部门字段 - 使用新的department_path字段（可选字段）
      if (updates.department !== undefined && updates.department !== null && updates.department.trim() !== '') {
        // 暂时直接使用部门名称作为路径
        // TODO: 后续可以实现完整的部门路径解析逻辑
        actionParams.department_path = updates.department.trim();
        console.log(`设备部门更新为: "${updates.department.trim()}"`);
      } else if (updates.department !== undefined) {
        console.log('部门字段为空，不传递department_path参数');
      }

      // 处理责任人字段（可选字段）
      if (updates.responsible !== undefined && updates.responsible !== null && updates.responsible.trim() !== '') {
        const responsibleTrimmed = updates.responsible.trim();
        // 分离责任人姓名和备注
        const responsibleName = responsibleTrimmed.split(' (')[0].trim();

        if (responsibleName) {
          actionParams.responsible_person_name = responsibleName;
          console.log(`设备责任人更新为: "${responsibleName}"`);

          // 如果有备注，提取备注
          if (responsibleTrimmed.includes(' (')) {
            const aliasMatch = responsibleTrimmed.match(/\(([^)]+)\)/);
            if (aliasMatch && aliasMatch[1]) {
              actionParams.responsible_person_alias = aliasMatch[1].trim();
              console.log(`责任人备注: "${aliasMatch[1].trim()}"`);
            }
          }
        }
      } else if (updates.responsible !== undefined) {
        console.log('责任人字段为空，不传递responsible_person_name参数');
      }
      if (updates.purpose !== undefined) actionParams.purpose = updates.purpose;
      if (updates.status !== undefined) actionParams.usage_status = updates.status;
      if (updates.securityLevel !== undefined) actionParams.confidentiality_level = updates.securityLevel;
      if (updates.securityCode !== undefined) actionParams.confidentiality_code = updates.securityCode;
      if (updates.isNetworked !== undefined) {
        // 将字符串“是”“否”转换为布尔值
        if (typeof updates.isNetworked === 'string') {
          actionParams.is_networked = updates.isNetworked === '是';
        } else {
          actionParams.is_networked = !!updates.isNetworked;
        }
      }
      if (updates.serialNumber !== undefined) actionParams.serial_number_asset_number = updates.serialNumber;
      if (updates.ipAddress !== undefined) actionParams.ip_address = updates.ipAddress;
      if (updates.macAddress !== undefined) actionParams.mac_address = updates.macAddress;
      if (updates.operatingSystem !== undefined) actionParams.operating_system = updates.operatingSystem;
      if (updates.securityRfid !== undefined) actionParams.security_code_rfid = updates.securityRfid;
      if (updates.parentCategory !== undefined) actionParams.parent_category_name = updates.parentCategory;
      if (updates.type !== undefined) actionParams.sub_category_name = updates.type;

      // 处理时间字段
      if (updates.startTime !== undefined) {
        actionParams.activation_timestamp = convertToUnixTimestamp(updates.startTime);
      }

      if (updates.purchaseTime !== undefined) {
        actionParams.purchase_timestamp = convertToUnixTimestamp(updates.purchaseTime);
      }

      // 如果有扩展字段，使用扩展字段服务处理后添加到参数中
      if (updates.extendedFields) {
        const extFieldService = ExtFieldService.getInstance();
        const processedExtFields = extFieldService.convertExtFieldsToBackendFormat(updates.extendedFields);
        actionParams.extended_fields = processedExtFields;
      }

      const params = {
        "action": "update_device",
        "action_params": actionParams
      };

      console.log('开始更新设备:', JSON.stringify(params));

      // 调用后端API
      const result = await this.submitAndWaitTask('DbFun', params);
      console.log('更新设备成功');

      // 触发设备更新事件，包含更新信息
      // 注意：原始数据将由调用方提供，避免循环依赖
      this.emitter.emit('device-updated', {
        id,
        updates,
        result
      });

    } catch (error: any) {
      console.error('更新设备失败:', error.message);
      throw error;
    } finally {
      this.isUpdatingItem = false; // 完成后重置标志位
    }
  }
}

export default InventoryUpdateService;