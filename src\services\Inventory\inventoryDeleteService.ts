import TaskManager, { TaskInfo } from '../../utils/taskManager';
import WebSocketManager from '../../utils/websocket';
import { InventoryItem } from '../../types/inventory';

class InventoryDeleteService {
  private static instance: InventoryDeleteService;
  private ws: WebSocketManager;
  private task: TaskManager;
  private dllName = 'AccountTableDll';
  private isDeletingItems = false; // 删除操作标志位

  private constructor() {
    this.ws = WebSocketManager.getInstance();
    this.task = TaskManager.getInstance();
  }

  public static getInstance(): InventoryDeleteService {
    if (!InventoryDeleteService.instance) {
      InventoryDeleteService.instance = new InventoryDeleteService();
    }
    return InventoryDeleteService.instance;
  }

  /**
   * 提交任务并等待结果
   */
  private async submitAndWaitTask<T>(funcName: string, params: any = {}, retryCount: number = 0): Promise<T> {
    // 最大重试次数
    const MAX_RETRIES = 2;
    // 超时时间（毫秒），增加到60秒
    const TIMEOUT = 60000;

    // 确保WebSocket已连接
    if (!this.ws.isConnected()) {
      try {
        console.log('尝试连接WebSocket...');
        await this.ws.connect();
      } catch (error) {
        console.error('WebSocket连接失败:', error);
        throw new Error('WebSocket连接失败');
      }
    }

    try {
      console.log(`准备提交任务: ${funcName}, 重试次数: ${retryCount}`);
      const taskId = await this.task.submitTask(this.dllName, funcName, params);
      console.log(`提交任务成功: ${funcName}, taskId: ${taskId}, 参数:`, JSON.stringify(params));

      return new Promise((resolve, reject) => {
        // 设置超时
        const timeoutId = setTimeout(async () => {
          this.task.offTaskUpdate(taskId, handleTaskUpdate);
          console.warn(`任务超时: ${funcName}, taskId: ${taskId}, 超时时间: ${TIMEOUT}ms`);

          // 如果未达到最大重试次数，则重试
          if (retryCount < MAX_RETRIES) {
            console.log(`准备重试任务: ${funcName}, 当前重试次数: ${retryCount + 1}/${MAX_RETRIES}`);
            try {
              // 重试任务
              const result = await this.submitAndWaitTask<T>(funcName, params, retryCount + 1);
              resolve(result);
            } catch (retryError) {
              console.error(`重试任务失败: ${funcName}, 错误:`, retryError);
              reject(retryError);
            }
          } else {
            console.error(`任务超时且达到最大重试次数: ${funcName}, taskId: ${taskId}`);
            reject(new Error(`任务超时: ${funcName}, taskId: ${taskId}, 已重试${retryCount}次`));
          }
        }, TIMEOUT);

        const handleTaskUpdate = (taskInfo: TaskInfo) => {
          console.log(`任务更新: ${taskId}, status: ${taskInfo.status}, progress: ${taskInfo.progress}`);

          if (taskInfo.result) {
            console.log(`任务结果: ${taskId}, 结果类型: ${typeof taskInfo.result}`);
          }

          if (taskInfo.status === 'completed') {
            clearTimeout(timeoutId);
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            if (taskInfo.result) {
              // 检查结果中的success字段
              let resultData = taskInfo.result;

              // 如果结果是字符串，尝试解析为JSON
              if (typeof resultData === 'string') {
                try {
                  resultData = JSON.parse(resultData);
                } catch (parseError) {
                  console.warn(`解析任务结果失败: ${funcName}, taskId: ${taskId}`, parseError);
                }
              }

              // 检查业务逻辑是否成功
              if (resultData && typeof resultData === 'object' && resultData.success === false) {
                const errorMessage = resultData.error_message || resultData.message || '操作失败';
                console.error(`任务业务逻辑失败: ${funcName}, taskId: ${taskId}, 错误: ${errorMessage}`);
                reject(new Error(errorMessage));
                return;
              }

              console.log(`任务完成: ${funcName}, taskId: ${taskId}`);
              resolve(taskInfo.result as T);
            } else {
              console.error(`任务完成但结果为空: ${funcName}, taskId: ${taskId}`);
              reject(new Error('任务返回结果为空'));
            }
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            clearTimeout(timeoutId);
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            const errorMsg = taskInfo.error || '任务失败';
            console.error(`任务失败: ${funcName}, taskId: ${taskId}, 错误:`, errorMsg);
            reject(new Error(errorMsg));
          }
        };

        this.task.onTaskUpdate(taskId, handleTaskUpdate);
      });
    } catch (error) {
      console.error(`提交任务失败: ${funcName}, 错误:`, error);
      throw error;
    }
  }

  // 删除设备
  public async deleteInventoryItems(ids: string[]): Promise<void> {
    // 防止重复删除
    if (this.isDeletingItems) {
      console.log('正在删除设备中，跳过重复删除请求');
      throw new Error('操作进行中，请稍后再试');
    }

    this.isDeletingItems = true;

    try {
      // 对每个要删除的设备调用后端API
      for (const id of ids) {
        // 直接使用数字ID（现在ID已经是数字格式）
        const deviceId = parseInt(id);

        // 准备删除设备的参数，按照新的API格式
        const params = {
          "action": "delete_device",
          "action_params": {
            "id": deviceId
          }
        };

        console.log(`开始删除设备 ${id}:`, JSON.stringify(params));

        try {
          // 调用后端API
          await this.submitAndWaitTask('DbFun', params);
          console.log(`删除设备 ${id} 成功`);
        } catch (apiError) {
          console.error(`删除设备 ${id} 失败:`, apiError);
          throw apiError;
        }
      }

      console.log('所有设备删除成功');
    } catch (error: any) {
      console.error('删除设备失败:', error.message);
      throw error;
    } finally {
      this.isDeletingItems = false; // 完成后重置标志位
    }
  }
}

export default InventoryDeleteService;