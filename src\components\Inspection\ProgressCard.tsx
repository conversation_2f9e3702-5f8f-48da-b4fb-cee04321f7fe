import React from 'react';

/**
 * 进度卡片组件的属性接口
 */
interface ProgressCardProps {
  isVisible: boolean;
  progress: number;
  title: string;
  className?: string;
}

/**
 * 进度卡片组件
 * 提供统一的进度显示，消除重复代码
 */
const ProgressCard: React.FC<ProgressCardProps> = ({
  isVisible,
  progress,
  title,
  className = ''
}) => {
  if (!isVisible) {
    return null;
  }

  return (
    <div className={`mb-4 border border-gray-200 rounded-md bg-white shadow-sm ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">{title}</span>
          <span className="text-sm text-gray-500">{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default ProgressCard;
