import { BaseService, BaseServiceEvents, BaseServiceState } from '../base/baseService';
import { ExtFieldDefinition, ExtFieldEditMode } from '../../types/inventory';
import ServiceRegistry from '../serviceRegistry';
import WebSocketManager from '../../utils/websocket';

import { isTimeField, convertTimeToISO, convertToUnixTimestamp } from '../../utils/fieldUtils';

/**
 * 扩展字段服务状态接口
 */
export interface ExtFieldServiceState extends BaseServiceState {
  // 分类对应的扩展字段定义映射
  categoryExtFields: Record<string, ExtFieldDefinition[]>;
  // 当前选中分类的扩展字段定义（用于表格显示）
  currentExtFields: ExtFieldDefinition[];
  // 当前表单使用的扩展字段定义（用于添加/修改台账对话框）
  formExtFields: ExtFieldDefinition[];
  // 全部设备总表的扩展字段缓存（用于解决总表扩展字段消失问题）
  allDevicesExtFields: ExtFieldDefinition[];
  // 所有分类的扩展字段定义
  allExtFields: Array<{
    parent_category_id: number;
    parent_category_name: string;
    sub_category_id: number;
    sub_category_name: string;
    fields: Array<{
      id: number;
      field_name: string;
      field_type: number;
      field_type_text: string;
      is_required: boolean | number;
      sort_order: number;
      edit_mode?: string; // 编辑模式
      is_editable?: number; // 新增：是否可编辑 1-可编辑 0-不可编辑(下拉选择)
      options?: Array<{ // 选项（用于select类型或不可编辑模式）
        id: number;
        is_default: number;
        value: string;
      }>;
    }>;
  }>;
}

/**
 * 扩展字段服务事件接口
 */
export interface ExtFieldServiceEvents extends BaseServiceEvents<ExtFieldServiceState> {
  'ext-fields-loaded': ExtFieldDefinition[];
  'current-ext-fields-changed': ExtFieldDefinition[];
  'form-ext-fields-changed': ExtFieldDefinition[];
  'all-ext-fields-loaded': ExtFieldServiceState['allExtFields'];
  'ext-fields-changed': boolean; // 扩展字段变化事件，用于通知UI更新
}

/**
 * 扩展字段服务
 * 负责管理设备分类的扩展字段定义和数据
 */
class ExtFieldService extends BaseService<ExtFieldServiceState, ExtFieldServiceEvents> {
  private static instance: ExtFieldService;
  private isLoadingExtFields = false;

  /**
   * 构造函数
   */
  constructor() {
    super('AccountTableDll', {
      isLoading: false,
      categoryExtFields: {},
      currentExtFields: [],
      formExtFields: [],
      allDevicesExtFields: [],
      allExtFields: []
    });
  }



  /**
   * 智能确定字段类型
   * @param apiField 后端扩展字段
   * @returns 前端字段类型
   */
  private determineFieldType(apiField: ExtFieldServiceState['allExtFields'][0]['fields'][0]): string {
    // 如果后端明确指定了类型，优先使用后端类型
    if (apiField.field_type_text) {
      // 如果后端类型是date，直接返回
      if (apiField.field_type_text === 'date') {
        return 'date';
      }
      // 如果后端类型是select，直接返回
      if (apiField.field_type_text === 'select') {
        return 'select';
      }
    }

    // 智能检测：如果字段名称包含时间相关关键词，自动设置为date类型
    if (isTimeField(apiField.field_name)) {
      console.log(`检测到时间字段: ${apiField.field_name}，自动设置为date类型`);
      return 'date';
    }

    // 如果有选项数据，设置为select类型
    if (apiField.options && apiField.options.length > 0) {
      return 'select';
    }

    // 默认返回后端指定的类型，如果没有则返回text
    return apiField.field_type_text || 'text';
  }

  /**
   * 将后端扩展字段格式转换为前端格式
   * @param apiField 后端扩展字段
   * @returns 前端扩展字段定义
   */
  private convertToExtFieldDefinition(apiField: ExtFieldServiceState['allExtFields'][0]['fields'][0]): ExtFieldDefinition {
    // 确定字段是否必填 - 支持布尔值和数字值（0/1）
    const determinedRequired = apiField.is_required === true || apiField.is_required === 1;

    // 调试日志：检查is_required字段的值
    console.log(`扩展字段 ${apiField.field_name} 的 is_required 原始值:`, apiField.is_required, '转换后:', determinedRequired);

    // 智能确定字段类型
    const determinedType = this.determineFieldType(apiField);

    // 确定编辑模式 - 优先使用is_editable属性
    let determinedEditMode: ExtFieldEditMode;

    // 新API使用is_editable: 1表示可编辑，0表示不可编辑(下拉选择)
    if (apiField.is_editable !== undefined) {
      determinedEditMode = apiField.is_editable === 1
        ? ExtFieldEditMode.Editable
        : ExtFieldEditMode.SelectOnly;
    }
    // 如果没有is_editable，回退到旧的判断逻辑
    else if (determinedType === 'select' || (apiField.options && apiField.options.length > 0)) {
      determinedEditMode = ExtFieldEditMode.SelectOnly;
    }
    // 兼容处理来自API的旧 'edit_mode' 字符串
    else if (apiField.edit_mode) {
      if (apiField.edit_mode === ExtFieldEditMode.SelectOnly) {
        determinedEditMode = ExtFieldEditMode.SelectOnly;
      } else if (apiField.edit_mode === ExtFieldEditMode.Editable) {
        determinedEditMode = ExtFieldEditMode.Editable;
      } else if (apiField.edit_mode === ExtFieldEditMode.Required) {
        // 旧的 'Required' 模式处理：保持字段为可编辑或下拉选择
        determinedEditMode = (determinedType === 'select' || (apiField.options && apiField.options.length > 0))
          ? ExtFieldEditMode.SelectOnly
          : ExtFieldEditMode.Editable;
      } else {
        // 默认为可编辑
        determinedEditMode = ExtFieldEditMode.Editable;
      }
    }
    // 默认为可编辑
    else {
      determinedEditMode = ExtFieldEditMode.Editable;
    }

    // 将API的选项结构 (id, value, is_default) 映射到前端的选项结构 (code, value)
    const frontendOptions = apiField.options?.map(opt => ({
      code: opt.id.toString(), // 使用API选项的 'id' 作为前端的 'code'
      value: opt.value
    }));

    return {
      key: apiField.field_name,
      title: apiField.field_name,
      type: determinedType, // 使用智能确定的类型
      required: determinedRequired,
      editMode: determinedEditMode,
      options: frontendOptions // 使用映射后的选项
    };
  }

  /**
   * 生成缓存键
   * @param parentCategory 一级分类名称
   * @param subCategory 二级分类名称
   * @returns 缓存键
   */
  private generateCacheKey(parentCategory?: string, subCategory?: string): string {
    if (!parentCategory) return 'all';
    if (subCategory) {
      return `${parentCategory.toLowerCase()}::${subCategory.toLowerCase()}`;
    }
    return parentCategory.toLowerCase();
  }

  /**
   * 获取单例实例
   * @returns ExtFieldService实例
   */
  public static getInstance(): ExtFieldService {
    if (!ExtFieldService.instance) {
      ExtFieldService.instance = new ExtFieldService();
    }
    return ExtFieldService.instance;
  }

  /**
   * 获取指定分类的扩展字段定义
   * @param categoryType 设备一级分类
   * @param categoryName 设备二级分类
   * @returns 扩展字段定义数组
   */
  public async getCategoryExtFields(categoryType: string, categoryName: string): Promise<ExtFieldDefinition[]> {
    try {
      console.log(`获取分类扩展字段: ${categoryType}::${categoryName}`);

      // 构建缓存键
      const cacheKey = this.generateCacheKey(categoryType, categoryName);

      // 如果已经有缓存的扩展字段定义，直接返回
      if (this.state.categoryExtFields[cacheKey]) {
        console.log(`使用缓存的扩展字段定义: ${cacheKey}`);
        return this.state.categoryExtFields[cacheKey];
      }

      // 如果没有加载所有扩展字段，先加载
      if (this.isEmptyArray(this.state.allExtFields, '先加载所有扩展字段')) {
        await this.getAllExtFields();
      }

      // 从已加载的所有扩展字段中查找匹配的分类
      const matchingCategory = this.state.allExtFields.find(item => {
        const parentMatch = item.parent_category_name.toLowerCase() === categoryType.toLowerCase();
        const subMatch = item.sub_category_name.toLowerCase() === categoryName.toLowerCase();
        return parentMatch && subMatch;
      });

      // 如果找到匹配的分类且有字段
      if (matchingCategory && matchingCategory.fields && matchingCategory.fields.length > 0) {
        console.log(`找到匹配的分类: ${categoryType}::${categoryName}`);

        // 转换为前端格式
        const extFields: ExtFieldDefinition[] = matchingCategory.fields.map(field =>
          this.convertToExtFieldDefinition(field)
        );

        // 更新缓存
        const categoryExtFields = {
          ...this.state.categoryExtFields,
          [cacheKey]: extFields
        };

        this.updateState({ categoryExtFields });
        return extFields;
      } else {
        console.log(`未找到匹配的分类或字段: ${categoryType}::${categoryName}`);

        // 缓存空结果，避免重复请求
        const categoryExtFields = {
          ...this.state.categoryExtFields,
          [cacheKey]: []
        };

        this.updateState({ categoryExtFields });
        return [];
      }
    } catch (error) {
      console.error('获取分类扩展字段失败:', error);
      this.emitter.emit('error', '获取分类扩展字段失败');
      return [];
    }
  }

  /**
   * 设置当前选中分类的扩展字段
   * @param extFields 扩展字段定义数组
   * @param isAllDevices 是否是全部设备总表
   */
  public setCurrentExtFields(extFields: ExtFieldDefinition[], isAllDevices: boolean = false): void {
    // 如果新的扩展字段与当前的相同，则不更新
    if (this.areExtFieldsEqual(this.state.currentExtFields, extFields)) {
      console.log('扩展字段未变化，跳过更新');
      return;
    }

    console.log('设置当前扩展字段:', extFields, isAllDevices ? '(全部设备总表)' : '');

    // 如果是全部设备总表，同时更新全部设备总表的扩展字段缓存
    if (isAllDevices && extFields.length > 0) {
      console.log('更新全部设备总表的扩展字段缓存:', extFields.length, '个字段');
      this.updateState({
        currentExtFields: extFields,
        allDevicesExtFields: [...extFields]
      });
    } else {
      // 更新状态
      this.updateState({
        currentExtFields: extFields
      });
    }

    // 使用防抖延迟触发事件通知
    this._debounceTimer = this.debounce(this._debounceTimer, () => {
      this.emitter.emit('current-ext-fields-changed', extFields);
      this._debounceTimer = null;
    });
  }

  /**
   * 恢复全部设备总表的扩展字段
   * 当在全部设备总表中进行列设置操作后，扩展字段可能会消失，此时调用此方法恢复
   */
  public restoreAllDevicesExtFields(): void {
    // 如果没有全部设备总表的扩展字段缓存，则不恢复
    if (this.isEmptyArray(this.state.allDevicesExtFields, '没有全部设备总表的扩展字段缓存，跳过恢复')) {
      return;
    }

    // 如果当前扩展字段与全部设备总表的扩展字段相同，则不恢复
    if (this.areExtFieldsEqual(this.state.currentExtFields, this.state.allDevicesExtFields)) {
      console.log('当前扩展字段与全部设备总表的扩展字段相同，跳过恢复');
      return;
    }

    console.log('恢复全部设备总表的扩展字段:', this.state.allDevicesExtFields.length, '个字段');

    // 更新状态
    this.updateState({
      currentExtFields: [...this.state.allDevicesExtFields]
    });

    // 使用防抖延迟触发事件通知
    this._debounceTimer = this.debounce(this._debounceTimer, () => {
      this.emitter.emit('current-ext-fields-changed', this.state.allDevicesExtFields);
      this._debounceTimer = null;
    });
  }

  // 防抖定时器
  private _debounceTimer: number | null = null;

  /**
   * 通用防抖方法
   * @param timer 定时器引用
   * @param callback 回调函数
   * @param delay 延迟时间（毫秒）
   * @returns 新的定时器ID
   */
  private debounce(timer: number | null, callback: () => void, delay: number = 50): number {
    if (timer) {
      clearTimeout(timer);
    }
    return window.setTimeout(callback, delay);
  }

  /**
   * 检查数组是否为空
   * @param arr 要检查的数组
   * @param logMessage 如果为空时的日志消息
   * @returns 是否为空
   */
  private isEmptyArray(arr: any[], logMessage: string): boolean {
    if (arr.length === 0) {
      console.log(logMessage);
      return true;
    }
    return false;
  }

  /**
   * 比较两个扩展字段数组是否相等
   * @param fields1 第一个扩展字段数组
   * @param fields2 第二个扩展字段数组
   * @returns 是否相等
   */
  public areExtFieldsEqual(fields1: ExtFieldDefinition[], fields2: ExtFieldDefinition[]): boolean {
    if (fields1.length !== fields2.length) {
      return false;
    }

    // 创建字段键的集合进行比较
    const keys1 = new Set(fields1.map(f => f.key));
    const keys2 = new Set(fields2.map(f => f.key));

    // 检查两个集合是否相等
    if (keys1.size !== keys2.size) {
      return false;
    }

    for (const key of keys1) {
      if (!keys2.has(key)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 清除当前扩展字段
   */
  public clearCurrentExtFields(): void {
    // 如果当前扩展字段已经为空，则不更新
    if (this.isEmptyArray(this.state.currentExtFields, '当前扩展字段已经为空，跳过清除')) {
      return;
    }

    console.log('清除当前扩展字段');

    this.updateState({
      currentExtFields: []
    });

    // 使用防抖延迟触发事件通知
    this._debounceTimer = this.debounce(this._debounceTimer, () => {
      this.emitter.emit('current-ext-fields-changed', []);
      this._debounceTimer = null;
    });
  }

  /**
   * 设置表单扩展字段
   * @param extFields 扩展字段定义数组
   */
  public setFormExtFields(extFields: ExtFieldDefinition[]): void {
    // 如果新的扩展字段与当前的相同，则不更新
    if (this.areExtFieldsEqual(this.state.formExtFields, extFields)) {
      console.log('表单扩展字段未变化，跳过更新');
      return;
    }

    console.log('设置表单扩展字段:', extFields);

    // 更新状态
    this.updateState({
      formExtFields: extFields
    });

    // 使用防抖延迟触发事件通知
    this._formDebounceTimer = this.debounce(this._formDebounceTimer, () => {
      this.emitter.emit('form-ext-fields-changed', extFields);
      this._formDebounceTimer = null;
    });
  }

  // 表单防抖定时器
  private _formDebounceTimer: number | null = null;

  /**
   * 清除表单扩展字段
   */
  public clearFormExtFields(): void {
    // 如果表单扩展字段已经为空，则不更新
    if (this.isEmptyArray(this.state.formExtFields, '表单扩展字段已经为空，跳过清除')) {
      return;
    }

    console.log('清除表单扩展字段');

    this.updateState({
      formExtFields: []
    });

    // 使用防抖延迟触发事件通知
    this._formDebounceTimer = this.debounce(this._formDebounceTimer, () => {
      this.emitter.emit('form-ext-fields-changed', []);
      this._formDebounceTimer = null;
    });
  }

  /**
   * 清除扩展字段缓存
   * 当设备分类发生变化时调用
   */
  public clearExtFieldsCache(): void {
    this.updateState({
      categoryExtFields: {}
    });
  }

  /**
   * 直接更新所有扩展字段定义
   * @param allExtFields 所有扩展字段定义
   */
  public updateAllExtFields(allExtFields: ExtFieldServiceState['allExtFields']): void {
    console.log('更新所有扩展字段定义:', allExtFields.length, '个分类');

    // 更新状态
    this.updateState({
      allExtFields
    });

    // 同时更新分类扩展字段缓存
    const categoryExtFields = { ...this.state.categoryExtFields };

    // 1. 先更新二级分类缓存
    // 遍历所有分类的扩展字段定义，并转换为前端格式
    allExtFields.forEach((item: ExtFieldServiceState['allExtFields'][0]) => {
      if (item.fields && item.fields.length > 0) {
        const cacheKey = this.generateCacheKey(item.parent_category_name, item.sub_category_name);

        // 转换为前端格式
        const extFields: ExtFieldDefinition[] = item.fields.map(field =>
          this.convertToExtFieldDefinition(field)
        );

        categoryExtFields[cacheKey] = extFields;
        console.log(`更新分类 ${cacheKey} 的扩展字段: ${extFields.length} 个字段`);
      }
    });

    // 2. 更新一级分类缓存
    // 收集一级分类及其扩展字段
    const parentCategoryFieldsMap = new Map<string, Map<string, ExtFieldDefinition>>();

    // 遍历所有分类，收集一级分类的扩展字段
    allExtFields.forEach(category => {
      const parentName = category.parent_category_name;

      // 初始化一级分类的扩展字段集合
      if (!parentCategoryFieldsMap.has(parentName)) {
        parentCategoryFieldsMap.set(parentName, new Map<string, ExtFieldDefinition>());
      }

      // 处理扩展字段
      if (category.fields && category.fields.length > 0) {
        category.fields.forEach(field => {
          const fieldName = field.field_name;
          const fieldMap = parentCategoryFieldsMap.get(parentName);

          if (fieldMap && !fieldMap.has(fieldName)) {
            fieldMap.set(fieldName, this.convertToExtFieldDefinition(field));
          }
        });
      }
    });

    // 更新一级分类缓存
    parentCategoryFieldsMap.forEach((fieldMap, parentName) => {
      const parentCacheKey = this.generateCacheKey(parentName);
      const extFields = Array.from(fieldMap.values());
      categoryExtFields[parentCacheKey] = extFields;
      console.log(`更新一级分类 ${parentName} 的扩展字段缓存: ${extFields.length} 个字段`);
    });

    // 3. 更新总表缓存
    // 收集所有唯一的扩展字段
    const allFieldsSet = new Set<string>();
    const allFieldsMap = new Map<string, ExtFieldDefinition>();

    // 遍历所有分类，收集所有唯一的扩展字段
    allExtFields.forEach(category => {
      if (category.fields && category.fields.length > 0) {
        category.fields.forEach(field => {
          const fieldName = field.field_name;
          if (!allFieldsSet.has(fieldName)) {
            allFieldsSet.add(fieldName);
            allFieldsMap.set(fieldName, this.convertToExtFieldDefinition(field));
          }
        });
      }
    });

    // 更新总表缓存
    const allCacheKey = this.generateCacheKey();
    const allExtFieldsList = Array.from(allFieldsMap.values());
    categoryExtFields[allCacheKey] = allExtFieldsList;

    // 同时更新allDevicesExtFields
    this.updateState({
      categoryExtFields,
      allDevicesExtFields: [...allExtFieldsList]
    });

    console.log(`更新总表扩展字段缓存: ${allExtFieldsList.length} 个字段`);

    // 触发所有扩展字段加载事件
    this.emitter.emit('all-ext-fields-loaded', allExtFields);
  }

  /**
   * 获取所有分类的扩展字段定义
   * 在初始化时调用，用于获取所有二级分类的扩展字段定义
   * @param forceRefresh 是否强制刷新，忽略缓存
   * @returns 所有分类的扩展字段定义
   */
  public async getAllExtFields(forceRefresh: boolean = false): Promise<ExtFieldServiceState['allExtFields']> {
    try {
      // 如果已经有缓存的扩展字段定义且不强制刷新，直接返回
      if (this.state.allExtFields.length > 0 && !forceRefresh) {
        console.log('使用缓存的所有扩展字段定义，共', this.state.allExtFields.length, '个分类');
        return this.state.allExtFields;
      }

      console.log(forceRefresh ? '强制刷新所有扩展字段定义' : '获取所有扩展字段定义');

      // 检查WebSocket连接状态
      const ws = WebSocketManager.getInstance();
      if (!ws.isConnected()) {
        console.log('WebSocket未连接，等待连接后再获取扩展字段');
        return [];
      }

      console.log('开始获取所有扩展字段');

      // 构建请求参数
      const params = {
        action: "get_all_extended_fields"
      };

      // 提交任务并等待结果
      const result = await this.submitTask<any>('DbFun', params);

      // 如果后端没有返回扩展字段定义，则返回空数组
      if (!result || !result.data) {
        console.log('后端返回的数据为空');
        return [];
      }

      const allExtFields = result.data;
      console.log(`获取到 ${allExtFields.length} 个分类的扩展字段`);

      // 直接使用updateAllExtFields方法更新所有扩展字段定义
      // 注意：updateAllExtFields已经会触发'all-ext-fields-loaded'事件
      this.updateAllExtFields(allExtFields);

      // 移除重复的事件触发，避免多次UI更新

      // 扩展字段已更新，将通过事件通知UI重新生成表格字段

      return allExtFields;
    } catch (error) {
      console.error('获取所有扩展字段失败:', error);
      this.emitter.emit('error', '获取所有扩展字段失败');
      return [];
    }
  }

  /**
   * 将扩展字段数据转换为后端格式
   * @param extFields 扩展字段值
   * @returns 后端格式的扩展字段数据（空值字段传参为空字符串）
   */
  public convertExtFieldsToBackendFormat(extFields: Record<string, any> = {}): Record<string, any> {
    // 处理所有扩展字段，包括空值字段
    const processedExtFields: Record<string, any> = {};

    // 获取当前表单使用的扩展字段定义
    const formExtFieldDefinitions = this.state.formExtFields;

    Object.entries(extFields).forEach(([key, value]) => {
      // 判断值是否为空
      const isEmpty =
        value === undefined ||
        value === null ||
        (typeof value === 'string' && value.trim() === '');

      // 查找当前字段的定义
      const fieldDefinition = formExtFieldDefinitions.find(field => field.key === key);

      if (isEmpty) {
        // 空值扩展字段传参为空字符串
        processedExtFields[key] = '';
      } else {
        // 如果是不可编辑的下拉选择字段且有选项定义，则转换ID为实际值
        if (fieldDefinition &&
            fieldDefinition.editMode === ExtFieldEditMode.SelectOnly &&
            fieldDefinition.options &&
            fieldDefinition.options.length > 0) {

          // 查找选中选项的值
          const selectedOption = fieldDefinition.options.find(opt => opt.code === value);

          if (selectedOption) {
            // 使用选项的值（而不是ID/code）
            processedExtFields[key] = selectedOption.value;
          } else {
            // 如果没找到匹配的选项，仍使用原值
            processedExtFields[key] = value;
          }
        }
        // 如果是时间字段，转换为Unix时间戳
        else if (fieldDefinition &&
                 (fieldDefinition.type === 'date' ||
                  isTimeField(fieldDefinition.title) ||
                  isTimeField(fieldDefinition.key))) {

          // 使用统一的Unix时间戳转换工具函数
          processedExtFields[key] = convertToUnixTimestamp(value);
        } else {
          // 其他类型的字段直接使用原值
          processedExtFields[key] = value;
        }
      }
    });

    return processedExtFields;
  }

  /**
   * 验证扩展字段值是否符合定义要求
   * @param extFields 扩展字段值
   * @param extFieldDefinitions 扩展字段定义
   * @returns 验证结果，包含错误信息
   */
  public validateExtFields(
    extFields: Record<string, any> = {},
    extFieldDefinitions: ExtFieldDefinition[] = []
  ): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // 验证必填字段
    extFieldDefinitions.forEach(field => {
      if (field.required) {
        const value = extFields[field.key];
        const isEmpty =
          value === undefined ||
          value === null ||
          (typeof value === 'string' && value.trim() === '');

        if (isEmpty) {
          errors[field.key] = `${field.title}不能为空`;
        }
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * 获取要显示的扩展字段
   * 根据当前分类情况获取要显示的扩展字段
   * @param currentCategory 当前选中的分类信息
   * @returns 要显示的扩展字段数组
   */
  public getExtFieldsToShow(currentCategory?: { parentCategory?: string; subCategory?: string }): ExtFieldDefinition[] {
    try {
      const allExtFields = this.state.allExtFields;

      // 如果没有扩展字段，返回空数组
      if (this.isEmptyArray(allExtFields, '没有扩展字段定义，返回空数组')) {
        return [];
      }

      // 生成缓存键
      const cacheKey = this.generateCacheKey(
        currentCategory?.parentCategory,
        currentCategory?.subCategory
      );

      // 尝试从缓存中获取
      if (this.state.categoryExtFields[cacheKey]) {
        console.log(`使用缓存的扩展字段: ${cacheKey}`);
        return this.state.categoryExtFields[cacheKey];
      }

      let result: ExtFieldDefinition[] = [];

      // 如果指定了二级分类，只显示该分类的扩展字段
      if (currentCategory?.parentCategory && currentCategory?.subCategory) {
        console.log(`查找特定分类的扩展字段: ${currentCategory.parentCategory}::${currentCategory.subCategory}`);

        // 使用不区分大小写的比较
        const matchingCategory = allExtFields.find((item: any) => {
          const parentMatch = item.parent_category_name.toLowerCase() === currentCategory.parentCategory?.toLowerCase();
          const subMatch = item.sub_category_name.toLowerCase() === currentCategory.subCategory?.toLowerCase();
          return parentMatch && subMatch;
        });

        if (matchingCategory && matchingCategory.fields && matchingCategory.fields.length > 0) {
          console.log(`找到匹配的分类，有 ${matchingCategory.fields.length} 个扩展字段`);

          // 转换为前端格式
          result = matchingCategory.fields.map(field => this.convertToExtFieldDefinition(field));
        }
      }
      // 如果只指定了一级分类，不显示扩展字段（只有选择二级分类时才显示扩展字段）
      else if (currentCategory?.parentCategory && !currentCategory?.subCategory) {
        console.log(`选择一级分类 ${currentCategory.parentCategory}，不显示扩展字段（需要选择二级分类才显示）`);
        return [];
      }
      // 如果没有指定分类，显示所有扩展字段
      else {
        console.log('显示所有分类的扩展字段');

        // 先尝试从总表缓存中获取
        const allCacheKey = this.generateCacheKey();
        if (this.state.categoryExtFields[allCacheKey]) {
          console.log(`使用总表缓存的扩展字段: ${allCacheKey}, 字段数量: ${this.state.categoryExtFields[allCacheKey].length}`);
          return this.state.categoryExtFields[allCacheKey];
        }

        // 如果缓存中没有，则从allExtFields中收集
        // 但是只收集那些有实际子分类存在的扩展字段
        const fieldNameSet = new Set<string>();
        const fieldMap = new Map<string, ExtFieldDefinition>();

        // 获取当前的设备分类树，检查哪些子分类实际存在
        const inventoryService = ServiceRegistry.getInstance()
          .getOrCreate('inventoryService', () => require('./inventoryService').default.getInstance());
        const deviceCategories = inventoryService.getState().deviceCategories;

        // 构建实际存在的子分类集合
        const existingSubCategories = new Set<string>();
        const flattenCategories = (categories: any[]): void => {
          categories.forEach(category => {
            if (category.children && category.children.length > 0) {
              category.children.forEach((subCategory: any) => {
                existingSubCategories.add(subCategory.name);
              });
              flattenCategories(category.children);
            }
          });
        };
        flattenCategories(deviceCategories);

        console.log('实际存在的子分类:', Array.from(existingSubCategories));

        allExtFields.forEach((category: any) => {
          // 只处理那些有实际子分类存在的扩展字段
          if (category.fields && category.fields.length > 0 &&
              existingSubCategories.has(category.sub_category_name)) {
            console.log(`包含子分类 "${category.sub_category_name}" 的扩展字段到总表`);

            category.fields.forEach((field: any) => {
              if (!fieldNameSet.has(field.field_name)) {
                fieldNameSet.add(field.field_name);
                const extField = this.convertToExtFieldDefinition(field);
                fieldMap.set(field.field_name, extField);
                result.push(extField);
              }
            });
          } else if (category.fields && category.fields.length > 0) {
            console.log(`跳过子分类 "${category.sub_category_name}" 的扩展字段（子分类不存在）`);
          }
        });

        // 更新总表缓存
        const categoryExtFields = {
          ...this.state.categoryExtFields,
          [allCacheKey]: result
        };

        // 同时更新allDevicesExtFields
        this.updateState({
          categoryExtFields,
          allDevicesExtFields: [...result]
        });

        console.log(`更新总表扩展字段缓存，共 ${result.length} 个字段`);
      }

      console.log(`找到 ${result.length} 个扩展字段`);

      // 更新缓存
      if (!this.state.categoryExtFields[cacheKey]) {
        const categoryExtFields = {
          ...this.state.categoryExtFields,
          [cacheKey]: result
        };
        this.updateState({ categoryExtFields });
      }

      return result;
    } catch (error) {
      console.error('获取要显示的扩展字段失败:', error);
      return [];
    }
  }

  /**
   * 在前端缓存中添加扩展字段
   * @param parentCategoryName 一级分类名称
   * @param subCategoryName 二级分类名称
   * @param fieldName 字段名称
   * @param isRequired 是否必填
   * @param editMode 编辑模式
   * @param fieldType 字段类型
   * @param options 选项数据（用于select类型或不可编辑模式）
   * @returns 是否添加成功
   */
  public addExtFieldToCache(
    parentCategoryName: string,
    subCategoryName: string,
    fieldName: string,
    isRequired: boolean = false,
    editMode: ExtFieldEditMode = ExtFieldEditMode.Editable,
    fieldType: string = "text",
    options?: Array<{ code: string; value: string }>
  ): boolean {
    try {
      console.log(`在前端缓存中添加扩展字段: ${parentCategoryName}::${subCategoryName} - ${fieldName}`);

      // 复制当前的allExtFields数组
      const allExtFields = [...this.state.allExtFields];

      // 查找对应的分类
      const categoryIndex = allExtFields.findIndex(
        cat => cat.parent_category_name.toLowerCase() === parentCategoryName.toLowerCase() &&
               cat.sub_category_name.toLowerCase() === subCategoryName.toLowerCase()
      );

      if (categoryIndex === -1) {
        console.error(`未找到对应的分类: ${parentCategoryName}::${subCategoryName}`);
        return false;
      }

      // 检查字段是否已存在
      const category = allExtFields[categoryIndex];
      const existingFieldIndex = category.fields.findIndex(
        field => field.field_name.toLowerCase() === fieldName.toLowerCase()
      );

      if (existingFieldIndex !== -1) {
        console.log(`字段已存在: ${fieldName}`);
        return false;
      }

      // 创建新字段 - 适配新的API规范
      const newField: any = {
        id: Date.now(), // 使用时间戳作为临时ID
        field_name: fieldName,
        field_type: fieldType === "text" ? 0 : 1, // 0=文本类型, 1=下拉类型
        field_type_text: fieldType,
        is_required: isRequired,
        sort_order: category.fields.length + 1,
        edit_mode: editMode, // 保留旧的编辑模式属性，以向后兼容
        is_editable: editMode === ExtFieldEditMode.SelectOnly ? 0 : 1 // 新增: 是否可编辑 1-可编辑 0-不可编辑(下拉选择)
      };

      // 如果有选项数据，添加到字段中
      if (options && options.length > 0) {
        newField.options = options;
      }

      // 添加字段到分类
      category.fields.push(newField);

      // 更新allExtFields
      this.updateAllExtFields(allExtFields);

      // 创建新的扩展字段定义
      const newExtField = this.convertToExtFieldDefinition(newField);

      // 准备更新缓存
      let categoryExtFields = { ...this.state.categoryExtFields };

      // 1. 更新二级分类缓存
      const subCacheKey = this.generateCacheKey(parentCategoryName, subCategoryName);
      const subExtFields = category.fields.map(field => this.convertToExtFieldDefinition(field));
      categoryExtFields[subCacheKey] = subExtFields;

      // 2. 更新一级分类缓存
      const parentCacheKey = this.generateCacheKey(parentCategoryName);
      let parentExtFields = categoryExtFields[parentCacheKey] || [];

      // 检查字段是否已存在于一级分类缓存中
      const existingParentField = parentExtFields.find(field => field.key === fieldName);
      if (!existingParentField) {
        // 添加新字段到一级分类缓存
        parentExtFields = [...parentExtFields, newExtField];
        categoryExtFields[parentCacheKey] = parentExtFields;
        console.log(`更新一级分类 ${parentCategoryName} 的扩展字段缓存，添加字段: ${fieldName}`);
      }

      // 3. 更新总表缓存
      const allCacheKey = this.generateCacheKey();
      let allExtFieldsCache = categoryExtFields[allCacheKey] || [];

      // 检查字段是否已存在于总表缓存中
      const existingAllField = allExtFieldsCache.find(field => field.key === fieldName);
      if (!existingAllField) {
        // 添加新字段到总表缓存
        allExtFieldsCache = [...allExtFieldsCache, newExtField];
        categoryExtFields[allCacheKey] = allExtFieldsCache;

        // 同时更新allDevicesExtFields
        this.updateState({
          allDevicesExtFields: [...allExtFieldsCache]
        });

        console.log(`更新总表扩展字段缓存，添加字段: ${fieldName}`);
      }

      // 更新缓存状态
      this.updateState({ categoryExtFields });

      // 触发扩展字段变化事件
      this.emitter.emit('ext-fields-changed', true);

      console.log(`成功在前端缓存中添加扩展字段: ${fieldName}`);
      return true;
    } catch (error) {
      console.error('在前端缓存中添加扩展字段失败:', error);
      return false;
    }
  }

  /**
   * 更新总表和一级分类的扩展字段缓存
   * 从所有分类中收集唯一的扩展字段，并更新总表和一级分类缓存
   */
  public updateAllTableExtFields(): void {
    try {
      console.log('更新总表和一级分类的扩展字段缓存');

      // 从所有分类中收集唯一的扩展字段（用于总表）
      const allFieldsSet = new Set<string>();
      const allFieldsMap = new Map<string, ExtFieldDefinition>();

      // 收集一级分类及其二级分类的映射
      const parentCategoryMap = new Map<string, Set<string>>();

      // 一级分类的扩展字段映射
      const parentCategoryFieldsMap = new Map<string, Map<string, ExtFieldDefinition>>();

      // 遍历所有分类的扩展字段
      this.state.allExtFields.forEach(category => {
        const parentName = category.parent_category_name;
        const subName = category.sub_category_name;

        // 记录一级分类和二级分类的关系
        if (!parentCategoryMap.has(parentName)) {
          parentCategoryMap.set(parentName, new Set<string>());
        }
        parentCategoryMap.get(parentName)?.add(subName);

        // 初始化一级分类的扩展字段映射
        if (!parentCategoryFieldsMap.has(parentName)) {
          parentCategoryFieldsMap.set(parentName, new Map<string, ExtFieldDefinition>());
        }

        // 处理扩展字段
        if (category.fields && category.fields.length > 0) {
          category.fields.forEach(field => {
            // 更新总表的扩展字段集合
            if (!allFieldsSet.has(field.field_name)) {
              allFieldsSet.add(field.field_name);
              allFieldsMap.set(field.field_name, this.convertToExtFieldDefinition(field));
            }

            // 更新一级分类的扩展字段集合
            const parentFieldsMap = parentCategoryFieldsMap.get(parentName);
            if (parentFieldsMap && !parentFieldsMap.has(field.field_name)) {
              parentFieldsMap.set(field.field_name, this.convertToExtFieldDefinition(field));
            }
          });
        }
      });

      // 转换为数组（总表）
      const allExtFields = Array.from(allFieldsMap.values());

      // 更新总表缓存
      const allCacheKey = this.generateCacheKey(); // 不传参数，返回'all'

      let categoryExtFields = {
        ...this.state.categoryExtFields,
        [allCacheKey]: allExtFields
      };

      // 更新一级分类的缓存
      parentCategoryMap.forEach((subCategories, parentName) => {
        const parentFieldsMap = parentCategoryFieldsMap.get(parentName);
        if (parentFieldsMap) {
          const parentExtFields = Array.from(parentFieldsMap.values());
          const parentCacheKey = this.generateCacheKey(parentName);

          categoryExtFields = {
            ...categoryExtFields,
            [parentCacheKey]: parentExtFields
          };

          console.log(`更新一级分类 ${parentName} 的扩展字段缓存，共 ${parentExtFields.length} 个字段`);
        }
      });

      // 同时更新allDevicesExtFields，确保总表视图下的扩展字段正确
      this.updateState({
        categoryExtFields,
        allDevicesExtFields: [...allExtFields]
      });

      console.log(`更新总表扩展字段缓存完成，共 ${allExtFields.length} 个字段`);
    } catch (error) {
      console.error('更新总表和一级分类扩展字段缓存失败:', error);
    }
  }

  /**
   * 在前端缓存中删除扩展字段
   * @param parentCategoryName 一级分类名称
   * @param subCategoryName 二级分类名称
   * @param fieldName 字段名称
   * @returns 是否删除成功
   */
  public deleteExtFieldFromCache(
    parentCategoryName: string,
    subCategoryName: string,
    fieldName: string
  ): boolean {
    try {
      console.log(`在前端缓存中删除扩展字段: ${parentCategoryName}::${subCategoryName} - ${fieldName}`);

      // 复制当前的allExtFields数组
      const allExtFields = [...this.state.allExtFields];

      // 查找对应的分类
      const categoryIndex = allExtFields.findIndex(
        cat => cat.parent_category_name.toLowerCase() === parentCategoryName.toLowerCase() &&
               cat.sub_category_name.toLowerCase() === subCategoryName.toLowerCase()
      );

      if (categoryIndex === -1) {
        console.error(`未找到对应的分类: ${parentCategoryName}::${subCategoryName}`);
        return false;
      }

      // 查找字段
      const category = allExtFields[categoryIndex];
      const fieldIndex = category.fields.findIndex(
        field => field.field_name.toLowerCase() === fieldName.toLowerCase()
      );

      if (fieldIndex === -1) {
        console.error(`未找到对应的字段: ${fieldName}`);
        return false;
      }

      // 删除字段
      category.fields.splice(fieldIndex, 1);

      // 更新allExtFields
      this.updateAllExtFields(allExtFields);

      // 准备更新缓存
      let categoryExtFields = { ...this.state.categoryExtFields };

      // 1. 更新二级分类缓存
      const subCacheKey = this.generateCacheKey(parentCategoryName, subCategoryName);
      const subExtFields = category.fields.map(field => this.convertToExtFieldDefinition(field));
      categoryExtFields[subCacheKey] = subExtFields;

      // 2. 检查该字段是否还存在于其他同一级分类下的二级分类中
      const fieldStillExistsInOtherSubCategories = this.checkFieldExistsInOtherSubCategories(
        parentCategoryName,
        subCategoryName,
        fieldName,
        allExtFields
      );

      // 3. 更新一级分类缓存
      if (!fieldStillExistsInOtherSubCategories) {
        const parentCacheKey = this.generateCacheKey(parentCategoryName);
        let parentExtFields = categoryExtFields[parentCacheKey] || [];

        // 从一级分类缓存中移除字段
        parentExtFields = parentExtFields.filter(field => field.key !== fieldName);
        categoryExtFields[parentCacheKey] = parentExtFields;
        console.log(`更新一级分类 ${parentCategoryName} 的扩展字段缓存，移除字段: ${fieldName}`);
      } else {
        console.log(`字段 ${fieldName} 仍存在于 ${parentCategoryName} 的其他二级分类中，保留在一级分类缓存中`);
      }

      // 4. 检查该字段是否还存在于任何分类中
      const fieldStillExistsAnywhere = this.checkFieldExistsAnywhere(fieldName, allExtFields);

      // 5. 更新总表缓存
      if (!fieldStillExistsAnywhere) {
        const allCacheKey = this.generateCacheKey();
        let allExtFieldsCache = categoryExtFields[allCacheKey] || [];

        // 从总表缓存中移除字段
        allExtFieldsCache = allExtFieldsCache.filter(field => field.key !== fieldName);
        categoryExtFields[allCacheKey] = allExtFieldsCache;

        // 同时更新allDevicesExtFields
        this.updateState({
          allDevicesExtFields: [...allExtFieldsCache]
        });

        console.log(`更新总表扩展字段缓存，移除字段: ${fieldName}`);
      } else {
        console.log(`字段 ${fieldName} 仍存在于其他分类中，保留在总表缓存中`);
      }

      // 更新缓存状态
      this.updateState({ categoryExtFields });

      // 触发扩展字段变化事件
      this.emitter.emit('ext-fields-changed', true);

      console.log(`成功在前端缓存中删除扩展字段: ${fieldName}`);
      return true;
    } catch (error) {
      console.error('在前端缓存中删除扩展字段失败:', error);
      return false;
    }
  }

  /**
   * 检查字段是否存在于同一级分类的其他二级分类中
   * @param parentCategoryName 一级分类名称
   * @param currentSubCategoryName 当前二级分类名称
   * @param fieldName 字段名称
   * @param allExtFields 所有扩展字段数据
   * @returns 是否存在
   */
  private checkFieldExistsInOtherSubCategories(
    parentCategoryName: string,
    currentSubCategoryName: string,
    fieldName: string,
    allExtFields: ExtFieldServiceState['allExtFields']
  ): boolean {
    // 查找同一级分类下的其他二级分类
    const otherSubCategories = allExtFields.filter(
      cat => cat.parent_category_name.toLowerCase() === parentCategoryName.toLowerCase() &&
             cat.sub_category_name.toLowerCase() !== currentSubCategoryName.toLowerCase()
    );

    // 检查字段是否存在于这些二级分类中
    return otherSubCategories.some(category =>
      category.fields.some(field => field.field_name.toLowerCase() === fieldName.toLowerCase())
    );
  }

  /**
   * 检查字段是否存在于任何分类中
   * @param fieldName 字段名称
   * @param allExtFields 所有扩展字段数据
   * @returns 是否存在
   */
  private checkFieldExistsAnywhere(
    fieldName: string,
    allExtFields: ExtFieldServiceState['allExtFields']
  ): boolean {
    return allExtFields.some(category =>
      category.fields.some(field => field.field_name.toLowerCase() === fieldName.toLowerCase())
    );
  }

  /**
   * 安全地获取需要从总表中移除的字段列表
   * @param fieldNames 要检查的字段名称列表
   * @param excludeSubCategory 要排除的二级分类名称
   * @param excludeParentCategory 要排除的一级分类名称
   * @returns 需要从总表中移除的字段列表
   */
  private getFieldsToRemoveFromTotal(
    fieldNames: string[],
    excludeSubCategory: string,
    excludeParentCategory: string
  ): string[] {
    const fieldsToRemove: string[] = [];

    for (const fieldName of fieldNames) {
      // 检查字段是否存在于其他分类中（排除当前要删除的分类）
      const existsElsewhere = this.state.allExtFields.some(category => {
        // 排除当前要删除的二级分类
        if (category.parent_category_name.toLowerCase() === excludeParentCategory.toLowerCase() &&
            category.sub_category_name.toLowerCase() === excludeSubCategory.toLowerCase()) {
          return false;
        }

        // 检查字段是否存在于其他分类中
        return category.fields.some(field => field.field_name.toLowerCase() === fieldName.toLowerCase());
      });

      // 如果字段不存在于其他分类中，需要从总表中移除
      if (!existsElsewhere) {
        fieldsToRemove.push(fieldName);
      }
    }

    return fieldsToRemove;
  }

  /**
   * 安全地获取需要从父级分类中移除的字段列表
   * @param fieldNames 要检查的字段名称列表
   * @param excludeSubCategory 要排除的二级分类名称
   * @param parentCategory 父级分类名称
   * @returns 需要从父级分类中移除的字段列表
   */
  private getFieldsToRemoveFromParent(
    fieldNames: string[],
    excludeSubCategory: string,
    parentCategory: string
  ): string[] {
    const fieldsToRemove: string[] = [];

    for (const fieldName of fieldNames) {
      // 检查字段是否存在于同一父级分类的其他二级分类中
      const existsInSiblingCategories = this.state.allExtFields.some(category => {
        // 只检查同一父级分类下的其他二级分类
        if (category.parent_category_name.toLowerCase() === parentCategory.toLowerCase() &&
            category.sub_category_name.toLowerCase() !== excludeSubCategory.toLowerCase()) {
          return category.fields.some(field => field.field_name.toLowerCase() === fieldName.toLowerCase());
        }
        return false;
      });

      // 如果字段不存在于同级其他分类中，需要从父级分类中移除
      if (!existsInSiblingCategories) {
        fieldsToRemove.push(fieldName);
      }
    }

    return fieldsToRemove;
  }

  /**
   * 安全地查找已删除二级分类的父级分类（改进版本）
   * @param subCategoryName 二级分类名称
   * @returns 父级分类名称，如果找不到则返回null
   */
  private findParentCategoryForDeletedSubCategory(subCategoryName: string): string | null {
    try {
      console.log(`查找二级分类 ${subCategoryName} 的父级分类`);

      // 方法1：从 allExtFields 中查找（最可靠）
      for (const category of this.state.allExtFields) {
        if (category.sub_category_name.toLowerCase() === subCategoryName.toLowerCase()) {
          console.log(`从 allExtFields 中找到父级分类: ${category.parent_category_name}`);
          return category.parent_category_name;
        }
      }

      // 方法2：从缓存键中安全地查找
      const matchingKeys: string[] = [];
      for (const key in this.state.categoryExtFields) {
        if (key.includes('::')) {
          const parts = key.split('::');
          if (parts.length === 2 && parts[1].toLowerCase() === subCategoryName.toLowerCase()) {
            matchingKeys.push(key);
            console.log(`从缓存键中找到匹配项: ${key}`);
          }
        }
      }

      // 如果找到匹配的缓存键，返回第一个的父级分类
      if (matchingKeys.length > 0) {
        const parentCategory = matchingKeys[0].split('::')[0];
        console.log(`从缓存键中确定父级分类: ${parentCategory}`);
        return parentCategory;
      }

      // 方法3：尝试从常见的设备分类中推断
      const commonDeviceCategories = ['安全产品类', '网络设备类', '服务器类', '存储设备类', '终端设备类'];
      for (const parentCategory of commonDeviceCategories) {
        const cacheKey = `${parentCategory.toLowerCase()}::${subCategoryName.toLowerCase()}`;
        if (this.state.categoryExtFields[cacheKey]) {
          console.log(`从常见分类中推断出父级分类: ${parentCategory}`);
          return parentCategory;
        }
      }

      console.warn(`无法找到二级分类 ${subCategoryName} 的父级分类`);
      return null;
    } catch (error) {
      console.error(`查找父级分类时出错:`, error);
      return null;
    }
  }

  /**
   * 缓存修复工具：清理所有损坏的缓存状态
   * @returns 修复结果
   */
  public repairExtFieldsCache(): { success: boolean; message: string; details: string[] } {
    const details: string[] = [];

    try {
      console.log('开始修复扩展字段缓存...');

      // 1. 清理所有缓存
      this.clearExtFieldsCache();
      details.push('已清理所有扩展字段缓存');

      // 2. 重建缓存
      const allExtFields = [...this.state.allExtFields];
      this.updateAllExtFields(allExtFields);
      details.push('已重建 allExtFields 缓存');

      // 3. 重建分类缓存
      let categoryExtFields: Record<string, ExtFieldDefinition[]> = {};

      // 重建总表缓存
      const allCacheKey = this.generateCacheKey();
      const allFields = this.getAllUniqueFields(allExtFields);
      categoryExtFields[allCacheKey] = allFields;
      details.push(`已重建总表缓存，包含 ${allFields.length} 个字段`);

      // 重建一级分类缓存
      const parentCategories = this.getUniqueParentCategories(allExtFields);
      for (const parentCategory of parentCategories) {
        const parentCacheKey = this.generateCacheKey(parentCategory);
        const parentFields = this.getFieldsForParentCategory(allExtFields, parentCategory);
        categoryExtFields[parentCacheKey] = parentFields;
        details.push(`已重建一级分类 ${parentCategory} 的缓存，包含 ${parentFields.length} 个字段`);
      }

      // 重建二级分类缓存
      for (const category of allExtFields) {
        const subCacheKey = this.generateCacheKey(category.parent_category_name, category.sub_category_name);
        const subFields = category.fields.map(field => this.convertToExtFieldDefinition(field));
        categoryExtFields[subCacheKey] = subFields;
        details.push(`已重建二级分类 ${category.parent_category_name}::${category.sub_category_name} 的缓存`);
      }

      // 4. 更新状态
      this.updateState({
        categoryExtFields,
        allDevicesExtFields: [...allFields]
      });

      console.log('扩展字段缓存修复完成');
      return {
        success: true,
        message: '扩展字段缓存修复成功',
        details
      };

    } catch (error) {
      console.error('修复扩展字段缓存失败:', error);
      details.push(`修复失败: ${error.message}`);
      return {
        success: false,
        message: '扩展字段缓存修复失败',
        details
      };
    }
  }

  /**
   * 获取所有唯一字段
   */
  private getAllUniqueFields(allExtFields: ExtFieldServiceState['allExtFields']): ExtFieldDefinition[] {
    const uniqueFields = new Map<string, ExtFieldDefinition>();

    for (const category of allExtFields) {
      for (const field of category.fields) {
        const fieldDef = this.convertToExtFieldDefinition(field);
        uniqueFields.set(field.field_name.toLowerCase(), fieldDef);
      }
    }

    return Array.from(uniqueFields.values());
  }

  /**
   * 获取所有唯一的一级分类
   */
  private getUniqueParentCategories(allExtFields: ExtFieldServiceState['allExtFields']): string[] {
    const parentCategories = new Set<string>();

    for (const category of allExtFields) {
      parentCategories.add(category.parent_category_name);
    }

    return Array.from(parentCategories);
  }

  /**
   * 获取指定一级分类的所有字段
   */
  private getFieldsForParentCategory(
    allExtFields: ExtFieldServiceState['allExtFields'],
    parentCategory: string
  ): ExtFieldDefinition[] {
    const uniqueFields = new Map<string, ExtFieldDefinition>();

    for (const category of allExtFields) {
      if (category.parent_category_name.toLowerCase() === parentCategory.toLowerCase()) {
        for (const field of category.fields) {
          const fieldDef = this.convertToExtFieldDefinition(field);
          uniqueFields.set(field.field_name.toLowerCase(), fieldDef);
        }
      }
    }

    return Array.from(uniqueFields.values());
  }

  /**
   * 清理已删除二级分类的缓存（改进版本）
   * @param subCategoryName 二级分类名称
   * @param parentCategoryName 父级分类名称
   */
  private cleanupCacheForDeletedSubCategory(subCategoryName: string, parentCategoryName: string): void {
    try {
      console.log(`清理已删除二级分类 ${subCategoryName} 的缓存`);

      // 复制当前的缓存
      let categoryExtFields = { ...this.state.categoryExtFields };

      // 1. 清理二级分类缓存
      const subCacheKey = this.generateCacheKey(parentCategoryName, subCategoryName);
      if (categoryExtFields[subCacheKey]) {
        // 获取该二级分类的扩展字段，用于后续处理
        const subExtFields = [...categoryExtFields[subCacheKey]];
        const fieldNames = subExtFields.map(field => field.key);

        // 删除该二级分类的缓存
        delete categoryExtFields[subCacheKey];
        console.log(`删除二级分类 ${subCategoryName} 的缓存`);

        // 如果没有扩展字段，直接返回
        if (fieldNames.length === 0) {
          console.log(`二级分类 ${subCategoryName} 没有扩展字段，无需进一步清理`);
          this.updateState({ categoryExtFields });
          return;
        }

        console.log(`二级分类 ${subCategoryName} 有 ${fieldNames.length} 个扩展字段需要清理`);

        // 2. 安全地检查这些字段是否还存在于其他二级分类中
        // 使用 allExtFields 数据源而不是缓存键遍历
        const fieldsToRemoveFromAll = this.getFieldsToRemoveFromTotal(fieldNames, subCategoryName, parentCategoryName);
        const fieldsToRemoveFromParent = this.getFieldsToRemoveFromParent(fieldNames, subCategoryName, parentCategoryName);



        // 3. 更新父级分类缓存
        if (fieldsToRemoveFromParent.length > 0) {
          const parentCacheKey = this.generateCacheKey(parentCategoryName);
          let parentExtFields = categoryExtFields[parentCacheKey] || [];

          // 从父级分类缓存中移除字段
          parentExtFields = parentExtFields.filter(field => !fieldsToRemoveFromParent.includes(field.key));
          categoryExtFields[parentCacheKey] = parentExtFields;

          console.log(`从父级分类 ${parentCategoryName} 的缓存中移除 ${fieldsToRemoveFromParent.length} 个字段`);
        }

        // 4. 更新总表缓存
        if (fieldsToRemoveFromAll.length > 0) {
          const allCacheKey = this.generateCacheKey();
          let allExtFieldsCache = categoryExtFields[allCacheKey] || [];

          // 从总表缓存中移除字段
          allExtFieldsCache = allExtFieldsCache.filter(field => !fieldsToRemoveFromAll.includes(field.key));
          categoryExtFields[allCacheKey] = allExtFieldsCache;

          // 同时更新allDevicesExtFields
          this.updateState({
            categoryExtFields,
            allDevicesExtFields: [...allExtFieldsCache]
          });

          console.log(`从总表缓存中移除 ${fieldsToRemoveFromAll.length} 个字段`);
        } else {
          // 即使没有字段需要从总表移除，也需要更新缓存状态
          this.updateState({ categoryExtFields });
        }
      } else {
        console.log(`未找到二级分类 ${subCategoryName} 的缓存，无需清理`);
        // 仍然更新缓存状态以确保一致性
        this.updateState({ categoryExtFields });
      }

      console.log(`成功清理已删除二级分类 ${subCategoryName} 的缓存`);
      return true; // 返回成功标志
    } catch (error) {
      console.error(`清理已删除二级分类 ${subCategoryName} 的缓存失败:`, error);
      // 出错时也更新缓存状态，确保不会留下不一致的状态
      this.updateState({ categoryExtFields: { ...this.state.categoryExtFields } });
      return false; // 返回失败标志
    }
  }

  /**
   * 删除指定二级分类的所有扩展字段（改进版本）
   * @param subCategoryName 二级分类名称
   * @returns 是否删除成功
   */
  public deleteSubCategoryExtFields(subCategoryName: string): boolean {
    try {
      console.log(`删除二级分类的所有扩展字段: ${subCategoryName}`);

      // 复制当前的allExtFields数组
      const allExtFields = [...this.state.allExtFields];

      // 查找对应的分类
      const categoryIndex = allExtFields.findIndex(
        cat => cat.sub_category_name.toLowerCase() === subCategoryName.toLowerCase()
      );

      // 先尝试从 allExtFields 中获取父级分类信息
      let parentCategoryName: string | null = null;
      if (categoryIndex !== -1) {
        parentCategoryName = allExtFields[categoryIndex].parent_category_name;
      } else {
        // 如果在 allExtFields 中找不到，尝试从缓存中查找
        parentCategoryName = this.findParentCategoryForDeletedSubCategory(subCategoryName);
      }

      // 如果找不到父级分类信息，无法进行清理
      if (!parentCategoryName) {
        console.warn(`无法找到二级分类 ${subCategoryName} 的父级分类，跳过缓存清理`);
        return false;
      }

      // 如果找不到对应的二级分类，可能是因为该分类已经被删除
      // 在这种情况下，我们仍然需要清理缓存中可能存在的该分类的扩展字段
      if (categoryIndex === -1) {
        console.log(`未找到对应的二级分类: ${subCategoryName}，可能已被删除，尝试清理缓存`);
        console.log(`使用父级分类 ${parentCategoryName} 进行缓存清理`);

        // 清理缓存
        const cleanupSuccess = this.cleanupCacheForDeletedSubCategory(subCategoryName, parentCategoryName);

        // 触发扩展字段变化事件
        this.emitter.emit('ext-fields-changed', true);

        return cleanupSuccess;
      }

      // 获取该二级分类的信息
      const category = allExtFields[categoryIndex];

      // 如果该分类没有扩展字段，直接返回成功
      if (!category.fields || category.fields.length === 0) {
        console.log(`二级分类 ${subCategoryName} 没有扩展字段，无需删除`);
        return true;
      }

      // 保存该分类的所有字段名称，用于后续处理
      const fieldNames = category.fields.map(field => field.field_name);
      console.log(`二级分类 ${subCategoryName} 有 ${fieldNames.length} 个扩展字段需要删除`);

      // 保存字段名称，用于日志输出
      const fieldNameList = fieldNames.join(', ');
      console.log(`准备删除二级分类 ${subCategoryName} 的扩展字段: ${fieldNameList}`);

      // 智能字段清理：检查字段是否被其他分类使用
      const { fieldsToDelete, sharedFields } = this.analyzeFieldUsage(fieldNames, subCategoryName, allExtFields);

      if (sharedFields.length > 0) {
        console.log(`检测到 ${sharedFields.length} 个字段被其他分类共享，将保留在总表中:`, sharedFields.join(', '));
      }

      if (fieldsToDelete.length > 0) {
        console.log(`将删除 ${fieldsToDelete.length} 个独有字段:`, fieldsToDelete.join(', '));
      }

      // 清空该分类的字段
      category.fields = [];

      // 更新allExtFields - 这会自动更新所有相关缓存
      this.updateAllExtFields(allExtFields);

      // 智能更新总表缓存：只删除不被其他分类使用的字段
      this.smartUpdateTotalTableCache(fieldsToDelete, sharedFields);

      // 触发扩展字段变化事件
      this.emitter.emit('ext-fields-changed', true);

      console.log(`成功删除二级分类 ${subCategoryName} 的所有扩展字段`);
      return true;
    } catch (error) {
      console.error('删除二级分类的所有扩展字段失败:', error);
      return false;
    }
  }

  /**
   * 分析字段使用情况，确定哪些字段可以安全删除
   * @param fieldNames 要分析的字段名称列表
   * @param excludeSubCategory 要排除的子分类名称
   * @param allExtFields 所有扩展字段数据
   * @returns 分析结果：可删除的字段和共享字段
   */
  private analyzeFieldUsage(
    fieldNames: string[],
    excludeSubCategory: string,
    allExtFields: any[]
  ): { fieldsToDelete: string[]; sharedFields: string[] } {
    const fieldsToDelete: string[] = [];
    const sharedFields: string[] = [];

    // 为每个字段检查是否被其他分类使用
    fieldNames.forEach(fieldName => {
      let isShared = false;

      // 检查所有其他分类是否使用了这个字段
      for (const category of allExtFields) {
        // 跳过当前要删除的分类
        if (category.sub_category_name === excludeSubCategory) {
          continue;
        }

        // 检查该分类是否有这个字段
        if (category.fields && category.fields.some((field: any) => field.field_name === fieldName)) {
          isShared = true;
          break;
        }
      }

      if (isShared) {
        sharedFields.push(fieldName);
      } else {
        fieldsToDelete.push(fieldName);
      }
    });

    return { fieldsToDelete, sharedFields };
  }

  /**
   * 智能更新总表缓存，只删除不被其他分类使用的字段
   * @param fieldsToDelete 可以安全删除的字段列表
   * @param sharedFields 被其他分类共享的字段列表
   */
  private smartUpdateTotalTableCache(fieldsToDelete: string[], sharedFields: string[]): void {
    try {
      const allCacheKey = this.generateCacheKey();
      const allExtFieldsCache = this.state.categoryExtFields[allCacheKey] || [];

      // 只删除不被其他分类使用的字段
      const updatedAllExtFieldsCache = allExtFieldsCache.filter(field =>
        !fieldsToDelete.includes(field.key)
      );

      // 更新缓存
      const categoryExtFields = { ...this.state.categoryExtFields };
      categoryExtFields[allCacheKey] = updatedAllExtFieldsCache;

      this.updateState({
        categoryExtFields,
        allDevicesExtFields: [...updatedAllExtFieldsCache]
      });

      // 输出详细的清理结果
      if (fieldsToDelete.length > 0) {
        console.log(`✅ 成功删除 ${fieldsToDelete.length} 个独有字段: ${fieldsToDelete.join(', ')}`);
      }

      if (sharedFields.length > 0) {
        console.log(`ℹ️ 保留 ${sharedFields.length} 个共享字段: ${sharedFields.join(', ')}`);
      }

      console.log(`📊 总表缓存更新完成，当前有 ${updatedAllExtFieldsCache.length} 个字段`);

    } catch (error) {
      console.error('智能更新总表缓存失败:', error);
    }
  }

  /**
   * 获取扩展字段的列键名
   * @param fieldName 扩展字段名称
   * @returns 列键名
   */
  public getExtFieldColumnKey(fieldName: string): string {
    return `ext_${fieldName}`;
  }

  /**
   * 初始化扩展字段的列可见性
   * @param columnVisibilityService 列可见性服务
   * @param extFields 扩展字段定义数组
   * @param defaultVisible 默认是否可见
   */
  public initExtFieldsColumnVisibility(
    columnVisibilityService: any,
    extFields: ExtFieldDefinition[],
    defaultVisible: boolean = true
  ): void {
    if (!extFields || extFields.length === 0) {
      return;
    }

    // 获取所有扩展字段的列键名
    const extFieldKeys = extFields.map(field => this.getExtFieldColumnKey(field.key));

    // 批量初始化列可见性
    columnVisibilityService.initMultipleExtFieldColumnVisibility(extFieldKeys, defaultVisible);
  }
}

// 注册服务
ServiceRegistry.getInstance().registerFactory('extFieldService', () => ExtFieldService.getInstance());

export default ExtFieldService;
