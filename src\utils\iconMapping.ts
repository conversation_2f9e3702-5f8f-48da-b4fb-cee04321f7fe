/**
 * 图标映射工具
 * 提供统一的图标名称到组件的映射
 */

import React from 'react';
import * as LucideIcons from 'lucide-react';
import { IconType } from '../hooks/Inventory/useCategoryIcons';

// 图标名称到Lucide组件名称的映射
export const ICON_NAME_MAP: Record<IconType, string> = {
  server: 'Server',
  folder: 'Folder',
  folderOpen: 'FolderOpen',
  monitor: 'Monitor',
  database: 'Database',
  shield: 'ShieldAlert',
  printer: 'Printer',
  smartphone: 'Smartphone',
  hardDrive: 'HardDrive',
  laptop: 'Laptop',
  building2: 'Building2',
  building: 'Building',
  users: 'Users',
  user: 'User',
  usb: 'Usb',
  calculator: 'Calculator',
  speaker: 'Speaker',
  cpu: 'Cpu',
  briefcase: 'Briefcase',
  wrench: 'Wrench',
  hammer: 'Hammer',
  settings: 'Settings',
  cog: 'Cog',
  wifi: 'Wifi',
  radio: 'Radio',
  bluetooth: 'Bluetooth',
  signal: 'Signal',
  battery: 'Battery',
  zap: 'Zap',
  power: 'Power',
  plug: 'Plug',
  archive: 'Archive',
  save: 'Save',
  download: 'Download',
  upload: 'Upload',
  lock: 'Lock',
  key: 'Key',
  shieldAlert: 'Shield',
  eye: 'Eye',
  eyeOff: 'EyeOff',
  tv: 'Tv',
  camera: 'Camera',
  video: 'Video',
  image: 'Image',
  globe: 'Globe',
  link: 'Link',
  gamepad2: 'Gamepad2',
  headphones: 'Headphones',
  mic: 'Mic',
  volume2: 'Volume2',
  clock: 'Clock',
  timer: 'Timer',
  thermometer: 'Thermometer',
  activity: 'Activity',
  // 新增设备相关图标映射
  router: 'Router',
  tablet: 'Tablet',
  watch: 'Watch',
  mousePointer: 'MousePointer',
  keyboard: 'Keyboard',
  scanLine: 'ScanLine',
  fingerprint: 'Fingerprint',
  lightbulb: 'Lightbulb',
  fan: 'Fan',
  car: 'Car',
  truck: 'Truck',
  plane: 'Plane',
  ship: 'Ship',
  bike: 'Bike',
  fuel: 'Fuel',
  gauge: 'Gauge',
  compass: 'Compass',
  mapPin: 'MapPin',
  navigation: 'Navigation',
  satellite: 'Satellite',
  rss: 'Rss',
  cast: 'Cast',
  monitorSpeaker: 'MonitorSpeaker',
  disc: 'Disc',
  memoryStick: 'MemoryStick',
  circuitBoard: 'CircuitBoard',
  batteryCharging: 'BatteryCharging',
  batteryLow: 'BatteryLow',
  powerOff: 'PowerOff',
  plugZap: 'PlugZap',
  cable: 'Cable',
  wifiOff: 'WifiOff'
};

/**
 * 根据图标类型获取对应的React组件
 * @param iconType 图标类型
 * @param className CSS类名
 * @returns React组件或null
 */
export const getIconComponent = (iconType: IconType | undefined, className?: string) => {
  if (!iconType) return null;
  
  const iconName = ICON_NAME_MAP[iconType];
  const IconComponent = iconName ? (LucideIcons as any)[iconName] : null;
  
  if (!IconComponent) return null;
  
  return React.createElement(IconComponent, { className });
};

/**
 * 检查图标类型是否有效
 * @param iconType 图标类型
 * @returns 是否有效
 */
export const isValidIconType = (iconType: string): iconType is IconType => {
  return iconType in ICON_NAME_MAP;
};

/**
 * 获取所有支持的图标类型
 * @returns 图标类型数组
 */
export const getAllIconTypes = (): IconType[] => {
  return Object.keys(ICON_NAME_MAP) as IconType[];
};
