import React, { useState } from 'react';
import { formatInspectionDateTime } from '../utils/formatUtils';

interface InspectionTaskInfo {
  task_name: string;               // 巡检任务名称
  inspection_start_date: number;   // 巡检开始时间戳
}

interface InspectionTooltipProps {
  inspectionTaskInfo: InspectionTaskInfo;  // 巡检任务详细信息
  children: React.ReactNode;
  className?: string;
}

/**
 * 巡检状态悬浮提示组件
 * 当鼠标悬停在巡检状态异常的设备上时显示巡检任务详细信息
 */
const InspectionTooltip: React.FC<InspectionTooltipProps> = ({
  inspectionTaskInfo,
  children,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  // 处理鼠标进入
  const handleMouseEnter = (e: React.MouseEvent) => {

    // 使用鼠标位置进行定位，这样更准确
    const mouseX = e.clientX;
    const mouseY = e.clientY;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算提示框的预估宽度和高度
    const tooltipWidth = 280;
    const tooltipHeight = 120;
    const offset = 10;

    // 默认显示在鼠标右侧
    let x = mouseX + offset;
    let y = mouseY - tooltipHeight / 2; // 垂直居中

    // 如果右侧空间不够，显示在左侧
    if (x + tooltipWidth > viewportWidth - 20) {
      x = mouseX - tooltipWidth - offset;
    }

    // 确保不超出视口边界
    if (x < 20) x = 20;
    if (x + tooltipWidth > viewportWidth - 20) x = viewportWidth - tooltipWidth - 20;
    if (y < 20) y = 20;
    if (y + tooltipHeight > viewportHeight - 20) y = viewportHeight - tooltipHeight - 20;



    setPosition({ x, y });
    setIsVisible(true);
  };

  // 处理鼠标离开
  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  return (
    <>
      <span
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={`contents ${className}`} // 使用contents让子元素直接参与父级布局
      >
        {children}
      </span>

      {/* 悬停提示框 */}
      {isVisible && (
        <div
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-64 max-w-72"
          style={{
            left: position.x + 'px',
            top: position.y + 'px',
            pointerEvents: 'none' // 防止鼠标事件干扰
          }}
        >
          <div className="space-y-3">
            {/* 标题 */}
            <div className="border-b border-gray-200 pb-2">
              <h3 className="text-lg font-semibold text-gray-800">巡检任务信息</h3>
            </div>

            {/* 任务信息 */}
            <div className="space-y-2">
              <div className="flex items-start">
                <span className="text-sm font-medium text-gray-600 w-20 flex-shrink-0">巡检任务:</span>
                <span className="text-sm text-gray-800 break-words">
                  {inspectionTaskInfo.task_name || (
                    <span className="text-gray-400 italic">暂无数据</span>
                  )}
                </span>
              </div>

              <div className="flex items-start">
                <span className="text-sm font-medium text-gray-600 w-20 flex-shrink-0">开始时间:</span>
                <span className="text-sm text-gray-800">
                  {inspectionTaskInfo.inspection_start_date && inspectionTaskInfo.inspection_start_date > 0
                    ? formatInspectionDateTime(inspectionTaskInfo.inspection_start_date)
                    : <span className="text-gray-400 italic">暂无数据</span>
                  }
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default InspectionTooltip;
