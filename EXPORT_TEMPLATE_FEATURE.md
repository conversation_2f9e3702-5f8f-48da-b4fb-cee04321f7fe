# 设备台账导出功能升级 - 代码优化版

## 功能概述

将原来的单一导出按钮改为下拉菜单，提供两个导出选项：
1. **导出设备台账**：保持现有的导出功能不变，导出当前表格中的实际设备数据
2. **导出导入模板**：新增功能，向后端websocket发送请求获取用于数据导入的Excel模板文件

## 代码优化

### 冗余消除
- **导出服务优化**：合并 `prepareExportData` 和 `prepareExportDataForCategory` 为通用方法 `prepareExportDataCommon`
- **导出钩子优化**：创建通用的 `exportAndSendToServerCommon` 方法，消除重复的"导出→发送"模式
- **文档整合**：合并多个功能文档，减少信息重复

## 修改文件列表

### 1. 导出组件 (`src/pages/Inventory/Export/index.tsx`)
- 将单一导出按钮改为下拉菜单组件
- 添加了下拉菜单状态管理和点击外部关闭功能
- 新增 `onExportTemplate` 回调属性
- 使用 `ChevronDown` 和 `FileText` 图标

### 2. 导出服务 (`src/services/Inventory/Export/exportService.ts`)
- 新增 `exportImportTemplate` 方法：导出导入模板的核心逻辑
- 新增 `prepareTemplateData` 方法：准备模板数据，包含示例数据
- 新增 `generateTemplateExcelFile` 方法：生成模板Excel文件
- 新增 `generateTemplateCsvFile` 方法：生成模板CSV文件
- 新增 `createTemplateExcelJSWorksheet` 方法：创建模板Excel工作表
- **优化**：新增 `prepareExportDataCommon` 通用方法，消除数据准备代码冗余

### 3. 导出钩子 (`src/hooks/Inventory/Export/useExport.ts`)
- 新增 `exportImportTemplate` 方法：导出模板的钩子方法
- 新增 `exportTemplateAndSendToServer` 方法：导出模板并发送到服务器
- 更新返回类型接口，包含新的模板导出方法
- **优化**：新增 `exportAndSendToServerCommon` 通用方法，消除"导出→发送"模式冗余

### 4. 设备台账主页面 (`src/pages/Inventory/InventoryManagement/index.tsx`)
- 导入并使用 `useExport` 钩子
- 为 Export 组件添加 `onExportTemplate` 回调处理
- 实现模板导出的完整流程，包括文件名生成和错误处理

## 技术特性

### 模板数据特性
- **排除字段**：不包含"巡检状态"和"安全RFID"列，因为这些字段不需要用户手动填写
- **示例数据**：模板中包含一行示例数据，展示各字段的正确填写格式：
  - 部门路径格式示例：`技术部/开发组`（不包含根节点）
  - 时间格式示例：`2024-01-15`
  - 人员名称和备注格式示例：`张三(技术负责人)`
  - 其他关键字段的格式要求

### 样式和格式
- **表格宽度计算**：与现有导出Excel功能保持一致
- **样式设置**：使用相同的ExcelStyleHelper进行样式处理
- **工作表结构**：包含标题行、表头行和示例数据行
- **文件格式**：支持Excel和CSV格式

### 通信方式
- **WebSocket通信**：使用与现有导出功能相同的websocket通信方式
- **任务管理**：集成TaskManager进行任务状态跟踪
- **进度监控**：支持导出进度监控和错误处理

## 用户界面

### 下拉菜单
- 点击导出按钮显示下拉菜单
- 两个选项：
  - 📄 导出设备台账
  - 📝 导出导入模板
- 点击外部自动关闭菜单
- 保持原有的选中项数量显示

### 文件命名
- 设备台账：保持原有命名规则
- 导入模板：`设备台账导入模板_YYYYMMDD_HHMMSS.xlsx`

## 兼容性
- 完全向后兼容，不影响现有导出功能
- 保持所有原有的导出设置和配置
- 使用相同的字段定义和扩展字段支持

## 错误处理
- 完整的try-catch错误处理
- 控制台日志记录
- 与现有错误处理机制保持一致

## 测试建议
1. 测试下拉菜单的显示和隐藏
2. 测试导出设备台账功能（确保不受影响）
3. 测试导出导入模板功能
4. 验证模板文件的格式和内容
5. 测试websocket通信和文件传输
6. 验证示例数据的正确性
