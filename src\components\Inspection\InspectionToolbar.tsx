import React from 'react';
import { Search, X } from 'lucide-react';

/**
 * 工具栏按钮配置接口
 */
export interface ToolbarButton {
  key: string;
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'filter';
  badge?: number;
}

/**
 * 工具栏属性接口
 */
interface InspectionToolbarProps {
  title?: string;
  buttons?: ToolbarButton[];
  rightButtons?: ToolbarButton[];
  searchProps?: {
    value: string;
    onChange: (value: string) => void;
    onSearch: () => void;
    onClear: () => void;
    placeholder?: string;
  };
  settingsMenu?: React.ReactNode;
  className?: string;
}

/**
 * 巡检管理通用工具栏组件
 * 统一的工具栏样式和交互
 */
const InspectionToolbar: React.FC<InspectionToolbarProps> = ({
  title,
  buttons = [],
  rightButtons = [],
  searchProps,
  settingsMenu,
  className = ''
}) => {
  // 获取按钮样式
  const getButtonClass = (variant: <PERSON><PERSON><PERSON><PERSON>utton['variant'] = 'secondary', disabled?: boolean, badge?: number) => {
    const baseClass = 'inline-flex items-center px-4 py-2.5 border text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-lg';
    
    if (disabled) {
      return `${baseClass} opacity-50 cursor-not-allowed border-gray-300 text-gray-400 bg-gray-100`;
    }

    switch (variant) {
      case 'primary':
        return `${baseClass} border-transparent text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`;
      case 'filter': {
        const hasFilter = badge && badge > 0;
        return `${baseClass} ${
          hasFilter
            ? 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100'
            : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
        }`;
      }
      case 'secondary':
      default:
        return `${baseClass} border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`;
    }
  };

  // 处理搜索键盘事件
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && searchProps) {
      searchProps.onSearch();
    }
  };

  return (
    <div className={`bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50 ${className}`}>
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* 左侧：标题和主要按钮 */}
          <div className="flex items-center space-x-3">
            {title && (
              <h2 className="text-lg font-semibold text-gray-900 mr-4">{title}</h2>
            )}
            {buttons.map((button) => (
              <button
                key={button.key}
                onClick={button.onClick}
                disabled={button.disabled}
                className={getButtonClass(button.variant, button.disabled, button.badge)}
              >
                {button.icon}
                {button.label}
                {button.badge && button.badge > 0 && (
                  <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-blue-600 rounded-full">
                    {button.badge}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* 右侧：搜索框和右侧按钮 */}
          <div className="flex items-center space-x-3">
            {/* 搜索框 */}
            {searchProps && (
              <div className="relative flex-1 max-w-md">
                <input
                  type="text"
                  placeholder={searchProps.placeholder || '搜索...'}
                  value={searchProps.value}
                  onChange={(e) => searchProps.onChange(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-all duration-200"
                />
                <Search className="absolute search-icon left-3 top-3 h-4 w-4 text-gray-400" />
                {searchProps.value && (
                  <button
                    onClick={searchProps.onClear}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            )}

            {/* 右侧按钮 */}
            {rightButtons.map((button) => (
              <div key={button.key} className="relative">
                <button
                  onClick={button.onClick}
                  disabled={button.disabled}
                  className={getButtonClass(button.variant, button.disabled, button.badge)}
                >
                  {button.icon}
                  {button.label}
                  {button.badge && button.badge > 0 && (
                    <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-blue-600 rounded-full">
                      {button.badge}
                    </span>
                  )}
                </button>
                {/* 如果是设置按钮且有设置菜单，则显示菜单 */}
                {button.key === 'settings' && settingsMenu && settingsMenu}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InspectionToolbar;
