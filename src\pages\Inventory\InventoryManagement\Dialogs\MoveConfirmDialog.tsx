import React from 'react';
import { X } from 'lucide-react';

interface MoveConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  draggedNode: {
    id: string;
    name: string;
    type: 'department' | 'person';
  } | null;
  targetNode: {
    id: string;
    name: string;
  } | null;
  isLoading?: boolean;
}

/**
 * 移动确认对话框
 * 用于在拖动人员后显示确认对话框（部门拖拽功能已禁用）
 */
const MoveConfirmDialog: React.FC<MoveConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  draggedNode,
  targetNode,
  isLoading = false
}) => {
  if (!isOpen || !draggedNode || !targetNode) return null;

  // 根据节点类型生成不同的提示文本
  const getConfirmText = () => {
    if (draggedNode.type === 'department') {
      // 部门拖拽功能已禁用，但保留代码以防将来需要
      return `确定要将部门 "${draggedNode.name}" 移动到 "${targetNode.name}" 下吗？`;
    } else {
      return `确定要将人员 "${draggedNode.name}" 移动到 "${targetNode.name}" 部门下吗？`;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-96 max-w-full animate-fadeIn">
        {/* 对话框标题 */}
        <div className="flex items-center justify-between px-4 py-2.5 border-b">
          <h3 className="text-lg font-medium text-gray-900">确认移动</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
            disabled={isLoading}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="px-4 py-4">
          <p className="text-gray-700">{getConfirmText()}</p>
          <p className="mt-2 text-sm text-gray-500">
            此操作将更改组织结构，请确认是否继续。
          </p>
        </div>

        {/* 对话框按钮 */}
        <div className="px-4 py-3 bg-gray-50 flex justify-end space-x-3 rounded-b-lg">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            取消
          </button>
          <button
            onClick={onConfirm}
            className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              isLoading ? 'opacity-75 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            {isLoading ? '处理中...' : '确认移动'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MoveConfirmDialog;
