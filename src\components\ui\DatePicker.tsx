import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface DatePickerProps {
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}

// 日期选择器组件
const DatePicker: React.FC<DatePickerProps> = ({
  name,
  value,
  onChange,
  placeholder = '请选择日期',
  error,
  disabled = false,
  required = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [displayValue, setDisplayValue] = useState('');
  const [year, setYear] = useState(new Date().getFullYear());
  const [month, setMonth] = useState(new Date().getMonth());
  const inputRef = useRef<HTMLInputElement>(null);
  const calendarRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  // 解析日期字符串为日期对象 - 支持多种格式
  const parseDate = (dateStr: string): Date | null => {
    if (!dateStr || dateStr === '') return null;

    try {
      // 首先尝试解析YYYY-MM-DD格式
      const match = dateStr.match(/^(\d{4})-(\d{2})-(\d{2})$/);
      if (match) {
        const year = parseInt(match[1], 10);
        const month = parseInt(match[2], 10) - 1; // 月份从0开始
        const day = parseInt(match[3], 10);

        const date = new Date(year, month, day);

        // 验证日期是否有效
        if (
          date.getFullYear() === year &&
          date.getMonth() === month &&
          date.getDate() === day
        ) {
          return date;
        }
      }

      // 尝试解析ISO格式 (YYYY-MM-DDTHH:mm:ss.sssZ)
      if (dateStr.includes('T') || dateStr.includes('Z')) {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // 尝试解析时间戳格式
      if (/^\d+$/.test(dateStr)) {
        const timestamp = parseInt(dateStr);
        // 判断是秒还是毫秒
        const date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // 尝试其他常见格式
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        return date;
      }

    } catch (error) {
      console.warn('Date parsing failed:', dateStr, error);
    }

    return null;
  };

  // 格式化日期对象为字符串
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 格式化日期为显示格式
  const formatDisplayDate = (dateStr: string): string => {
    const date = parseDate(dateStr);
    if (!date) return '';

    // 使用 YYYY-MM-DD 格式显示，与表格格式保持一致
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 初始化日期状态
  useEffect(() => {
    console.log('DatePicker value changed:', value, typeof value);

    if (value && value !== '') {
      const date = parseDate(value);
      if (date) {
        setYear(date.getFullYear());
        setMonth(date.getMonth());
        setDisplayValue(formatDisplayDate(value));
        console.log('DatePicker initialized with date:', date, 'display:', formatDisplayDate(value));
      } else {
        console.warn('DatePicker: Invalid date value:', value);
        setDisplayValue('');
      }
    } else {
      console.log('DatePicker: Empty value, clearing display');
      setDisplayValue('');
    }
  }, [value]);

  // 计算下拉菜单位置
  useEffect(() => {
    if (isOpen && inputRef.current) {
      // 使用requestAnimationFrame确保DOM更新后再计算位置
      requestAnimationFrame(() => {
        if (!inputRef.current) return;

        const rect = inputRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;
        const calendarHeight = 320; // 日历大约高度

        // 默认向下展开
        let top = rect.bottom + window.scrollY + 2;

        // 如果下方空间不足且上方空间更充足，则向上展开
        if (spaceBelow < calendarHeight && spaceAbove > spaceBelow && spaceAbove > calendarHeight) {
          top = rect.top + window.scrollY - calendarHeight - 2;
        }

        // 计算下拉框位置
        setDropdownPosition({
          top: top,
          left: rect.left + window.scrollX,
          width: rect.width
        });
      });
    }
  }, [isOpen]);

  // 处理点击外部关闭日历
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && calendarRef.current) {
        const isOutsideInput = !inputRef.current.contains(event.target as Node);
        const isOutsideCalendar = !calendarRef.current.contains(event.target as Node);

        if (isOutsideInput && isOutsideCalendar) {
          setIsOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 处理窗口滚动和大小变化
  useEffect(() => {
    if (!isOpen) return;

    // 重新计算位置的函数
    const recalculatePosition = () => {
      if (isOpen && inputRef.current) {
        const rect = inputRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;
        const calendarHeight = 320; // 日历大约高度

        // 默认向下展开
        let top = rect.bottom + window.scrollY + 2;

        // 如果下方空间不足且上方空间更充足，则向上展开
        if (spaceBelow < calendarHeight && spaceAbove > spaceBelow && spaceAbove > calendarHeight) {
          top = rect.top + window.scrollY - calendarHeight - 2;
        }

        // 更新位置
        setDropdownPosition({
          top: top,
          left: rect.left + window.scrollX,
          width: rect.width
        });
      }
    };

    // 使用防抖函数处理滚动和大小变化事件
    let debounceTimer: number | null = null;

    const debouncedRecalculate = () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      debounceTimer = window.setTimeout(recalculatePosition, 100);
    };

    // 添加事件监听器
    window.addEventListener('scroll', debouncedRecalculate, true);
    window.addEventListener('resize', debouncedRecalculate);

    // 清理函数
    return () => {
      window.removeEventListener('scroll', debouncedRecalculate, true);
      window.removeEventListener('resize', debouncedRecalculate);
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [isOpen]);

  // 获取当月的天数
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };

  // 获取当月第一天是星期几
  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };

  // 生成日历网格
  const generateCalendarGrid = () => {
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);

    const grid: (number | null)[] = [];

    // 填充前导空白
    for (let i = 0; i < firstDay; i++) {
      grid.push(null);
    }

    // 填充日期
    for (let i = 1; i <= daysInMonth; i++) {
      grid.push(i);
    }

    return grid;
  };

  // 处理日期选择
  const handleDateSelect = (day: number) => {
    const selectedDate = new Date(year, month, day);
    const dateStr = formatDate(selectedDate);

    // 创建合成事件
    const syntheticEvent = {
      target: { name, value: dateStr }
    } as React.ChangeEvent<HTMLInputElement>;

    onChange(syntheticEvent);
    setDisplayValue(formatDisplayDate(dateStr));
    setIsOpen(false);
  };

  // 处理月份变化
  const handleMonthChange = (increment: number) => {
    let newMonth = month + increment;
    let newYear = year;

    if (newMonth < 0) {
      newMonth = 11;
      newYear--;
    } else if (newMonth > 11) {
      newMonth = 0;
      newYear++;
    }

    setMonth(newMonth);
    setYear(newYear);
  };

  // 处理年份变化
  const handleYearChange = (increment: number) => {
    setYear(year + increment);
  };

  // 清除日期
  const handleClear = () => {
    const syntheticEvent = {
      target: { name, value: '' }
    } as React.ChangeEvent<HTMLInputElement>;

    onChange(syntheticEvent);
    setDisplayValue('');
    setIsOpen(false);
  };

  // 处理输入框点击
  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // 获取当前选中的日期
  const getSelectedDay = (): number | null => {
    const date = parseDate(value);
    if (date && date.getFullYear() === year && date.getMonth() === month) {
      return date.getDate();
    }
    return null;
  };

  // 月份名称
  const monthNames = [
    '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月'
  ];

  // 星期名称
  const weekdayNames = ['日', '一', '二', '三', '四', '五', '六'];

  return (
    <div className="relative">
      <div
        ref={inputRef}
        className={`w-full px-3 py-1.5 border rounded-md focus:outline-none cursor-pointer flex justify-between items-center transition-all duration-200
          ${error ? 'border-rose-300 focus:border-rose-400 focus:ring-2 focus:ring-rose-100' : 'border-slate-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100'}
          ${disabled ? 'bg-slate-100 cursor-not-allowed' : 'bg-white hover:border-slate-400'}`}
        onClick={handleInputClick}
      >
        <span className={`text-sm ${!displayValue ? 'text-slate-400' : 'text-slate-700'}`}>
          {displayValue || placeholder}
        </span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 text-slate-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>

      {isOpen && !disabled && createPortal(
        <div
          ref={calendarRef}
          className="fixed z-[9999] bg-white border border-slate-300 p-3 shadow-lg rounded-lg"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${Math.max(dropdownPosition.width, 240)}px`
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {/* 日历头部 - 年月选择 */}
          <div className="flex justify-between items-center mb-2">
            <button
              type="button"
              onClick={() => handleYearChange(-1)}
              className="p-1 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded text-sm transition-colors"
            >
              &lt;&lt;
            </button>
            <button
              type="button"
              onClick={() => handleMonthChange(-1)}
              className="p-1 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded text-sm transition-colors"
            >
              &lt;
            </button>
            <span className="font-medium text-sm text-slate-800">
              {year}年 {monthNames[month]}
            </span>
            <button
              type="button"
              onClick={() => handleMonthChange(1)}
              className="p-1 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded text-sm transition-colors"
            >
              &gt;
            </button>
            <button
              type="button"
              onClick={() => handleYearChange(1)}
              className="p-1 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded text-sm transition-colors"
            >
              &gt;&gt;
            </button>
          </div>

          {/* 星期标题 */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {weekdayNames.map((day, index) => (
              <div
                key={index}
                className="text-center text-xs font-medium text-slate-600 py-1"
              >
                {day}
              </div>
            ))}
          </div>

          {/* 日期网格 */}
          <div className="grid grid-cols-7 gap-1">
            {generateCalendarGrid().map((day, index) => (
              <div
                key={index}
                className={`
                  text-center py-1 text-xs w-7 h-7 flex items-center justify-center transition-colors
                  ${day === null ? 'text-slate-300' : 'cursor-pointer hover:bg-blue-50 text-slate-700'}
                  ${day !== null && day === getSelectedDay() ? 'bg-blue-500 text-white hover:bg-blue-600' : ''}
                  ${day !== null ? 'rounded-md' : ''}
                `}
                onClick={() => day !== null && handleDateSelect(day)}
              >
                {day}
              </div>
            ))}
          </div>

          {/* 底部按钮 */}
          <div className="flex justify-between mt-3 pt-2 border-t border-slate-200">
            <button
              type="button"
              onClick={handleClear}
              className="text-xs text-slate-600 hover:text-slate-900 px-2 py-1 rounded hover:bg-slate-100 transition-colors"
            >
              清除
            </button>
            <button
              type="button"
              onClick={() => {
                const today = new Date();
                setYear(today.getFullYear());
                setMonth(today.getMonth());
                handleDateSelect(today.getDate());
              }}
              className="text-xs text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors"
            >
              今天
            </button>
          </div>
        </div>,
        document.body
      )}

      {error && <p className="mt-1 text-sm text-rose-600">{error}</p>}
    </div>
  );
};

export default DatePicker;
