import { useState, useEffect, useCallback } from 'react';
import AnnouncementService from '../../services/Announcement/announcementService';

/**
 * 公告Hook状态接口
 */
interface UseAnnouncementState {
  isLoading: boolean;
  announcement: string;
  error: string | null;
}

/**
 * 公告Hook返回值接口
 */
interface UseAnnouncementReturn extends UseAnnouncementState {
  // 公告相关方法
  loadAnnouncement: () => Promise<void>;
  retryLoadAnnouncement: () => void;
}

/**
 * 公告Hook
 * Hook层：只负责公告相关的业务逻辑
 */
export const useAnnouncement = (): UseAnnouncementReturn => {
  // 状态管理
  const [state, setState] = useState<UseAnnouncementState>({
    isLoading: false,
    announcement: '',
    error: null
  });

  // 获取服务实例
  const announcementService = AnnouncementService.getInstance();

  /**
   * 更新状态的辅助函数
   */
  const updateState = useCallback((newState: Partial<UseAnnouncementState>) => {
    setState(prev => ({ ...prev, ...newState }));
  }, []);

  /**
   * 加载公告信息
   */
  const loadAnnouncement = useCallback(async () => {
    try {
      updateState({ isLoading: true, error: null });
      
      const announcement = await announcementService.getAnnouncement();
      
      updateState({ 
        isLoading: false, 
        announcement,
        error: null 
      });
      
      console.log('公告加载成功:', announcement);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载公告失败';
      console.error('加载公告失败:', errorMessage);
      
      updateState({ 
        isLoading: false, 
        error: errorMessage 
      });
    }
  }, [announcementService, updateState]);

  /**
   * 重试加载公告
   */
  const retryLoadAnnouncement = useCallback(() => {
    console.log('重试加载公告');
    loadAnnouncement();
  }, [loadAnnouncement]);

  /**
   * 监听服务状态变化
   */
  useEffect(() => {
    const handleStateChange = (serviceState: any) => {
      updateState({
        isLoading: serviceState.isLoading,
        announcement: serviceState.announcement,
        error: serviceState.error
      });
    };

    const handleAnnouncementLoaded = (announcement: string) => {
      console.log('公告加载完成事件:', announcement);
    };

    const handleError = (error: string) => {
      console.error('公告服务错误事件:', error);
    };

    // 订阅事件
    announcementService.on('state-change', handleStateChange);
    announcementService.on('announcement-loaded', handleAnnouncementLoaded);
    announcementService.on('error', handleError);

    // 清理函数
    return () => {
      announcementService.off('state-change', handleStateChange);
      announcementService.off('announcement-loaded', handleAnnouncementLoaded);
      announcementService.off('error', handleError);
    };
  }, [announcementService, updateState]);

  return {
    // 状态
    ...state,
    
    // 公告相关方法
    loadAnnouncement,
    retryLoadAnnouncement
  };
};
