# 统一部门路径获取工具使用指南

## 概述

`DepartmentPathResolver` 是一个统一的部门路径获取工具，提供了各种场景下的部门路径获取方法。无论是API调用、UI显示还是数据导出，都可以通过这个工具获取正确格式的部门路径。

## 主要功能

### 1. 路径格式类型

- **API路径**: 用于API调用，不包含根节点，如 `"技术部/开发组/前端组"`
- **完整路径**: 用于UI显示，包含根节点，如 `"公司名称/技术部/开发组/前端组"`
- **导出路径**: 用于数据导出，不包含根节点，如 `"技术部/开发组/前端组"`

### 2. 使用方式

#### 方式一：通过DepartmentService创建（推荐）

```typescript
import DepartmentService from './departmentService';

const departmentService = DepartmentService.getInstance();
const pathResolver = departmentService.createPathResolver();
```

#### 方式二：直接创建

```typescript
import { DepartmentPathResolver } from './pathResolver';

const pathResolver = new DepartmentPathResolver(departmentCategories);
```

#### 方式三：使用快捷工具函数

```typescript
import { DepartmentPathHelper } from './pathResolver';

const apiPath = DepartmentPathHelper.getApiPath(departmentService, department);
```

## 常用方法

### 1. 获取API路径（用于API调用）

```typescript
// 用于添加、删除、更新部门的API调用
const apiPath = pathResolver.getApiPath(department);
// 结果: "技术部/开发组/前端组"

// API调用示例
const params = {
  action: "delete_department",
  action_params: {
    department_path: apiPath
  }
};
```

### 2. 获取完整路径（用于UI显示）

```typescript
// 用于UI显示、面包屑导航等
const fullPath = pathResolver.getFullPath(department);
// 结果: "公司名称/技术部/开发组/前端组"

// UI显示示例
console.log('当前部门路径:', fullPath);
```

### 3. 获取导出路径（用于数据导出）

```typescript
// 用于Excel、PDF、Word导出
const exportPath = pathResolver.getExportPath(department);
// 结果: "技术部/开发组/前端组"

// 导出示例
worksheet.cell(row, col).value = exportPath;
```

### 4. 根据ID获取路径

```typescript
// 当只有部门ID时
const apiPath = pathResolver.getApiPathById('dept-123');
const fullPath = pathResolver.getFullPathById('dept-123');
```

### 5. 构建新部门路径

```typescript
// 添加新部门时构建路径
const newPath = pathResolver.buildNewDepartmentPath('dept-parent', '新部门名称');
// 结果: "技术部/开发组/新部门名称"
```

### 6. 路径验证

```typescript
// 验证路径格式是否正确
const isValid = pathResolver.isValidPath('技术部/开发组/前端组');
// 结果: true
```

## 快捷工具函数

为了更方便的使用，提供了 `DepartmentPathHelper` 静态工具类：

```typescript
import { DepartmentPathHelper } from './pathResolver';

// 快速获取API路径
const apiPath = DepartmentPathHelper.getApiPath(departmentService, department);

// 快速根据ID获取路径
const pathById = DepartmentPathHelper.getApiPathById(departmentService, 'dept-123');

// 快速构建新部门路径
const newPath = DepartmentPathHelper.buildNewDepartmentPath(
  departmentService, 
  'dept-parent', 
  '新部门'
);

// 快速验证路径
const isValid = DepartmentPathHelper.validatePath(departmentService, 'tech/dev/frontend');
```

## 实际应用场景

### 1. API调用场景

```typescript
// 添加部门
const newDepartmentPath = pathResolver.buildNewDepartmentPath(parentId, departmentName);
const addParams = {
  action: "add_department",
  action_params: { department_path: newDepartmentPath }
};

// 删除部门
const deletePath = pathResolver.getApiPath(department);
const deleteParams = {
  action: "delete_department", 
  action_params: { department_path: deletePath }
};
```

### 2. UI显示场景

```typescript
// 面包屑导航
const breadcrumbPath = pathResolver.getFullPath(department);
const breadcrumbs = breadcrumbPath.split('/');

// 部门选择器显示
const displayText = pathResolver.getFullPath(department);
```

### 3. 数据导出场景

```typescript
// Excel导出
const exportPath = pathResolver.getExportPath(department);
worksheet.cell(row, 1).value = exportPath;

// PDF导出
const pdfPath = pathResolver.getExportPath(department);
doc.text(pdfPath, x, y);
```

## 注意事项

1. **根节点处理**: API路径和导出路径都不包含根节点，完整路径包含根节点
2. **路径格式**: 使用斜杠(/)分隔层级结构
3. **缓存机制**: 内部使用缓存提升性能，无需担心重复调用的性能问题
4. **错误处理**: 当部门不存在时，相关方法会返回空字符串或抛出错误
5. **路径验证**: 建议在使用路径前进行验证，确保格式正确

## 迁移指南

如果你之前使用的是 `DepartmentUtils.getDepartmentPath` 或 `DepartmentUtils.getDepartmentApiPath`，可以按以下方式迁移：

```typescript
// 旧方式
const oldApiPath = DepartmentUtils.getDepartmentApiPath(department, departmentCategories);
const oldFullPath = DepartmentUtils.getDepartmentPath(department, departmentCategories);

// 新方式
const pathResolver = new DepartmentPathResolver(departmentCategories);
const newApiPath = pathResolver.getApiPath(department);
const newFullPath = pathResolver.getFullPath(department);

// 或者使用快捷方式
const quickApiPath = DepartmentPathHelper.getApiPath(departmentService, department);
```

## 总结

统一的部门路径获取工具提供了：
- 🎯 **统一接口**: 一个工具解决所有路径获取需求
- 🚀 **性能优化**: 内置缓存机制，提升性能
- 🛡️ **类型安全**: 完整的TypeScript类型支持
- 📝 **易于使用**: 简洁的API设计，支持多种调用方式
- 🔧 **灵活配置**: 支持不同场景的路径格式需求
