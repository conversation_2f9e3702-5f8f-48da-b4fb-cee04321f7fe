/**
 * InventoryService操作工具类
 * 统一管理设备列表的RFID相关更新操作
 */

import { extractDeviceId } from '../../../utils/deviceUtils';
import InventoryService from '../inventoryService';

/**
 * RFID设备信息接口
 */
interface RfidDeviceInfo {
  device_id: number;
  device_name: string;
  plain_code: string | null;
  cipher_code: string | null;
  has_rfid: boolean;
}

/**
 * InventoryService更新工具类
 */
export class InventoryUpdater {
  /**
   * 获取InventoryService实例
   * @returns InventoryService实例
   */
  private static getInventoryService() {
    return InventoryService.getInstance();
  }

  /**
   * 获取当前设备列表
   * @returns 当前设备列表
   */
  private static getCurrentInventoryList() {
    const inventoryService = this.getInventoryService();
    const currentState = inventoryService.getState();
    return currentState.inventoryList;
  }

  /**
   * 更新设备的RFID码信息
   * @param deviceIds 设备ID数组
   * @param rfidDevices RFID设备信息数组
   */
  public static async updateRfidCodes(deviceIds: number[], rfidDevices: RfidDeviceInfo[]): Promise<void> {
    try {
      console.log('开始更新设备RFID码:', deviceIds);

      const inventoryService = this.getInventoryService();
      const inventoryList = this.getCurrentInventoryList();

      // 更新设备列表中的RFID码信息
      const updatedList = inventoryList.map(item => {
        const deviceId = extractDeviceId(item.id);

        // 查找对应的RFID信息
        const rfidInfo = rfidDevices.find(device => device.device_id === deviceId);

        if (rfidInfo) {
          console.log(`更新设备 ${deviceId} 的RFID码:`, {
            deviceId,
            deviceName: item.deviceName,
            oldSecurityRfid: item.securityRfid,
            newPlainCode: rfidInfo.plain_code,
            hasRfid: rfidInfo.has_rfid
          });

          // 更新plain_code字段（明码），null转换为空字符串
          const updatedItem = {
            ...item,
            securityRfid: rfidInfo.plain_code || ''
          };

          console.log(`设备 ${deviceId} RFID码更新完成:`, {
            securityRfid: updatedItem.securityRfid
          });

          return updatedItem;
        }

        return item;
      });

      // 通过InventoryService更新状态
      inventoryService.updateInventoryList(updatedList);

      console.log('设备RFID码更新完成');
    } catch (error) {
      console.error('更新设备RFID码失败:', error);
      throw error;
    }
  }

  /**
   * 清除设备的RFID码信息
   * @param deviceIds 设备ID数组
   */
  public static async clearRfidCodes(deviceIds: number[]): Promise<void> {
    try {
      console.log('开始清除设备RFID码:', deviceIds);

      const inventoryService = this.getInventoryService();
      const inventoryList = this.getCurrentInventoryList();

      // 更新设备列表，清除RFID码信息
      const updatedList = inventoryList.map(item => {
        const deviceId = extractDeviceId(item.id);
        
        // 如果是要删除RFID码的设备，清除securityRfid字段
        if (deviceIds.includes(deviceId)) {
          return {
            ...item,
            securityRfid: undefined
          };
        }
        
        return item;
      });

      // 通过InventoryService更新状态
      inventoryService.updateInventoryList(updatedList);

      console.log('设备RFID码清除完成');
    } catch (error) {
      console.error('清除设备RFID码失败:', error);
      throw error;
    }
  }
}
