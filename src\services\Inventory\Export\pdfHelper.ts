import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';

/**
 * PDF助手类
 * 提供PDF文件生成相关的辅助方法，使用tableExport方式
 */
export class PdfHelper {
  // 常量配置 - 避免重复定义
  private static readonly STYLES = {
    FONT_FAMILY: 'Arial, sans-serif',
    TABLE_FONT_SIZE: '10px',
    TITLE_FONT_SIZE: 14,
    DATE_FONT_SIZE: 10,
    BORDER: '1px solid #333',
    HEADER_BG: '#f0f0f0',
    CELL_PADDING: '8px 4px',
    CELL_HEIGHT: '28px',
    IMAGE_QUALITY: 0.9,
    CANVAS_SCALE: 2.0
  };

  // 样式模板常量
  private static readonly STYLE_TEMPLATES = {
    BASE_CELL: `text-align:center;vertical-align:middle;display:table-cell;position:relative;`,
    FLEX_CENTER: `display:flex;align-items:center;justify-content:center;height:100%;width:100%;box-sizing:border-box;`
  };

  private static readonly PAGE_CONFIG = {
    MARGIN: 15,
    PAGE_WIDTH_RATIO: 0.95,
    BASE_CONTAINER_WIDTH: 1200, // 基础容器宽度
    ROW_HEIGHT_ESTIMATE: 35, // 估算每行高度（包括边框和内边距）
    HEADER_HEIGHT_ESTIMATE: 80, // 估算标题高度
    FOOTER_HEIGHT_ESTIMATE: 30 // 估算页脚高度
  };

  /**
   * 格式化日期 - 统一的日期格式化方法
   * @param date 日期对象
   * @returns 格式化的日期字符串
   */
  private static formatDate(date: Date = new Date()): string {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }

  /**
   * 计算所有列的宽度分配
   * @param headers 所有列名
   * @param data 数据数组
   * @returns 列宽映射对象
   */
  private static calculateAllColumnWidths(headers: string[], data: any[]): { [key: string]: number } {
    const widths: { [key: string]: number } = {};
    let totalWidth = 0;

    // 第一轮：计算每列的基础宽度
    headers.forEach(header => {
      const width = this.calculateSingleColumnWidth(header, data);
      widths[header] = width;
      totalWidth += width;
    });

    // 第二轮：标准化到100%
    const scale = 100 / totalWidth;
    headers.forEach(header => {
      widths[header] = Math.round(widths[header] * scale * 10) / 10; // 保留一位小数
    });

    return widths;
  }

  /**
   * 动态计算单列宽度 - 完全基于内容长度自适应
   * @param header 列名
   * @param data 数据数组，用于分析内容长度
   * @returns 列宽数值（不带%）
   */
  private static calculateSingleColumnWidth(header: string, data: any[]): number {
    // 计算列名长度
    let maxLength = header.length;

    // 分析该列的数据内容长度
    if (data && data.length > 0) {
      data.forEach(row => {
        const value = row[header];
        if (value !== null && value !== undefined) {
          const valueLength = String(value).length;
          maxLength = Math.max(maxLength, valueLength);
        }
      });
    }

    // 动态计算列宽 - 基于内容长度，无硬编码
    const baseWidth = Math.max(3, Math.min(maxLength * 0.8, 20)); // 基础宽度：3-20之间

    // 根据内容长度动态调整权重
    if (maxLength <= 5) {
      return Math.max(4, baseWidth * 0.8); // 短内容：紧凑显示
    } else if (maxLength <= 10) {
      return Math.max(6, baseWidth * 0.9); // 中等内容：适中显示
    } else if (maxLength <= 20) {
      return Math.max(8, baseWidth * 1.0); // 长内容：正常显示
    } else {
      return Math.max(10, baseWidth * 0.7); // 超长内容：压缩显示，允许换行
    }
  }

  /**
   * 动态计算容器宽度 - 基于列数和内容复杂度
   * @param headers 列名数组
   * @param data 数据数组
   * @returns 动态计算的容器宽度
   */
  private static calculateContainerWidth(headers: string[], data: any[]): number {
    const columnCount = headers.length;
    const baseWidth = this.PAGE_CONFIG.BASE_CONTAINER_WIDTH;

    // 根据列数动态调整容器宽度
    if (columnCount <= 5) {
      return baseWidth * 0.8; // 少列：紧凑布局
    } else if (columnCount <= 10) {
      return baseWidth * 1.0; // 中等列数：标准布局
    } else if (columnCount <= 15) {
      return baseWidth * 1.3; // 多列：扩展布局
    } else {
      return baseWidth * 1.6; // 超多列：最大布局
    }
  }

  /**
   * 动态计算每页行数 - 基于页面尺寸和内容密度
   * @param format PDF格式
   * @param orientation 页面方向
   * @param columnCount 列数
   * @returns 动态计算的每页行数
   */
  private static calculateRowsPerPage(format: string, orientation: string, columnCount: number): number {
    // 获取页面高度（mm）
    const pageHeights: { [key: string]: number } = {
      'a3': orientation === 'landscape' ? 297 : 420,
      'a4': orientation === 'landscape' ? 210 : 297,
      'a5': orientation === 'landscape' ? 148 : 210,
      'letter': orientation === 'landscape' ? 216 : 279,
      'legal': orientation === 'landscape' ? 216 : 356
    };

    const pageHeight = pageHeights[format] || pageHeights['a4'];

    // 计算可用高度（减去边距、标题、页脚）
    const availableHeight = pageHeight - (this.PAGE_CONFIG.MARGIN * 2) -
                           (this.PAGE_CONFIG.HEADER_HEIGHT_ESTIMATE / 3.78) - // 转换px到mm
                           (this.PAGE_CONFIG.FOOTER_HEIGHT_ESTIMATE / 3.78);

    // 根据列数调整行高估算
    const adjustedRowHeight = this.PAGE_CONFIG.ROW_HEIGHT_ESTIMATE / 3.78 *
                              (columnCount > 10 ? 1.2 : 1.0); // 多列时增加行高

    // 计算每页行数
    const rowsPerPage = Math.floor(availableHeight / adjustedRowHeight);

    // 确保最小和最大值
    return Math.max(5, Math.min(rowsPerPage, 50));
  }

  /**
   * 动态判断是否为长文本列 - 基于内容长度而非硬编码列名
   * @param header 列名
   * @param data 数据数组
   * @returns 是否为长文本列
   */
  private static isLongTextColumn(header: string, data: any[]): boolean {
    if (!data || data.length === 0) return false;

    // 计算该列的平均内容长度
    let totalLength = 0;
    let validCount = 0;

    data.forEach(row => {
      const value = row[header];
      if (value !== null && value !== undefined) {
        totalLength += String(value).length;
        validCount++;
      }
    });

    const averageLength = validCount > 0 ? totalLength / validCount : 0;

    // 动态判断：平均长度超过15个字符的列认为是长文本列
    return averageLength > 15;
  }

  /**
   * 创建简化的标题和日期元素
   * @param title 标题
   * @returns 包含标题和日期的div元素
   */
  private static createHeaderElement(title: string): HTMLDivElement {
    // 创建容器
    const header = document.createElement('div');
    header.style.width = '100%';
    header.style.marginBottom = '10px';
    header.style.fontFamily = this.STYLES.FONT_FAMILY;
    header.style.position = 'relative';

    // 创建标题 - 居中、字体放大加粗
    const titleElement = document.createElement('div');
    titleElement.textContent = title;
    titleElement.style.fontWeight = 'bold';
    titleElement.style.fontSize = '24px'; // 放大字体
    titleElement.style.color = '#000';
    titleElement.style.textAlign = 'center'; // 居中
    titleElement.style.marginBottom = '5px';

    // 创建日期 - 保持在右侧位置不变
    const dateElement = document.createElement('div');
    dateElement.textContent = this.formatDate();
    dateElement.style.fontSize = `${this.STYLES.DATE_FONT_SIZE}px`;
    dateElement.style.color = '#666';
    dateElement.style.position = 'absolute';
    dateElement.style.right = '0';
    dateElement.style.top = '0';

    // 添加到容器
    header.appendChild(titleElement);
    header.appendChild(dateElement);

    return header;
  }

  /**
   * 创建简化的页脚元素
   * @returns 页脚div元素
   */
  private static createFooterElement(): HTMLDivElement {
    // 创建容器 - 最小化页脚
    const footer = document.createElement('div');
    footer.style.width = '100%';
    footer.style.marginTop = '2px';
    footer.style.fontFamily = this.STYLES.FONT_FAMILY;

    // 创建页脚内容 - 使用统一的日期格式化
    const footerContent = document.createElement('div');
    footerContent.textContent = this.formatDate();
    footerContent.style.textAlign = 'right';
    footerContent.style.fontSize = this.STYLES.TABLE_FONT_SIZE;

    // 添加到容器
    footer.appendChild(footerContent);

    return footer;
  }

  /**
   * 从数据生成PDF
   * @param data 导出数据
   * @param title 标题
   * @param options 可选配置项
   * @returns PDF文件的Blob对象
   */
  public static async generatePdfFromData(
    data: any[],
    title: string = '设备台账',
    options: {
      format?: 'a3' | 'a4' | 'a5' | 'letter' | 'legal',
      orientation?: 'portrait' | 'landscape',
      scale?: number,
      containerWidth?: number,
      autoSize?: boolean, // 自动调整大小选项
      allowMultiplePages?: boolean // 允许多页显示选项
    } = {}
  ): Promise<Blob> {
    try {
      // 获取数据结构信息用于动态计算
      const headers = data.length > 0 ? Object.keys(data[0]) : [];

      // 动态计算配置参数
      const containerWidth = options.containerWidth || this.calculateContainerWidth(headers, data);
      const pdfFormat = options.format || 'a4';
      const pdfOrientation = options.orientation || 'landscape';
      const allowMultiplePages = options.allowMultiplePages !== undefined ? options.allowMultiplePages : true;

      // 创建PDF文档 - 使用配置的纸张大小和方向
      const pdf = new jsPDF({
        orientation: pdfOrientation,
        unit: 'mm',
        format: pdfFormat
      });

      // 获取PDF页面尺寸
      const pageWidth = pdf.internal.pageSize.getWidth();

      // 如果允许多页显示，使用分页方式处理
      if (allowMultiplePages && data.length > 0) {
        // 动态计算每页可以显示的行数
        const rowsPerPage = this.calculateRowsPerPage(pdfFormat, pdfOrientation, headers.length);

        // 分批处理数据
        for (let i = 0; i < data.length; i += rowsPerPage) {
          // 如果不是第一页，添加新页
          if (i > 0) {
            pdf.addPage();
          }

          // 获取当前批次的数据
          const batchData = data.slice(i, i + rowsPerPage);

          // 创建包含标题的完整页面内容
          const pageContainer = document.createElement('div');
          pageContainer.style.width = `${containerWidth}px`;
          pageContainer.style.padding = '0'; // 移除padding，避免双重边距
          pageContainer.style.backgroundColor = 'white';
          pageContainer.style.fontFamily = this.STYLES.FONT_FAMILY;

          // 添加标题（只在第一页显示）
          if (i === 0) {
            const header = this.createHeaderElement(title);
            pageContainer.appendChild(header);
          }

          // 创建表格
          const tableHtml = this.createTableHtml(batchData, true, data[0]);
          const tableDiv = document.createElement('div');
          tableDiv.innerHTML = tableHtml;
          pageContainer.appendChild(tableDiv);

          // 将页面容器添加到文档
          document.body.appendChild(pageContainer);

          try {
            // 使用html2canvas将页面容器转换为canvas - 使用常量配置
            const canvas = await html2canvas(pageContainer, {
              scale: this.STYLES.CANVAS_SCALE,
              useCORS: true,
              logging: false,
              backgroundColor: '#ffffff',
              windowWidth: containerWidth
            });

            // 计算缩放比例，确保宽度适应页面
            const imgWidth = canvas.width;
            const imgHeight = canvas.height;

            // 使用常量配置的比例计算
            const ratio = (pageWidth * this.PAGE_CONFIG.PAGE_WIDTH_RATIO) / imgWidth;

            // 计算位置 - 居中显示
            const x = (pageWidth - imgWidth * ratio) / 2;
            const y = 2; // 最小顶部边距

            // 将canvas添加到PDF - 使用常量图像质量
            pdf.addImage(
              canvas.toDataURL('image/png', this.STYLES.IMAGE_QUALITY), // 使用PNG格式提高质量
              'PNG',
              x,
              y,
              imgWidth * ratio,
              imgHeight * ratio
            );
          } finally {
            // 清理
            document.body.removeChild(pageContainer);
          }
        }

        // 导出为PDF
        return new Blob([pdf.output('arraybuffer')], { type: 'application/pdf' });
      } else {
        // 单页模式
        // 创建临时容器
        const container = document.createElement('div');
        container.style.width = `${containerWidth}px`;
        container.style.padding = '0'; // 移除padding，避免双重边距
        container.style.backgroundColor = 'white';
        container.style.fontFamily = this.STYLES.FONT_FAMILY;
        container.style.fontSize = this.STYLES.TABLE_FONT_SIZE;

        // 添加标题和日期
        const header = this.createHeaderElement(title);
        container.appendChild(header);

        // 添加表格
        const tableHtml = this.createTableHtml(data, true, data[0]);
        const tableDiv = document.createElement('div');
        tableDiv.innerHTML = tableHtml;
        container.appendChild(tableDiv);

        // 添加页脚
        const footer = this.createFooterElement();
        container.appendChild(footer);

        // 将容器添加到文档中
        document.body.appendChild(container);

        try {
          // 使用html2canvas将容器转换为canvas
          const canvas = await html2canvas(container, {
            scale: this.STYLES.CANVAS_SCALE,
            useCORS: true,
            logging: false,
            backgroundColor: '#ffffff',
            windowWidth: containerWidth
          });

          // 计算缩放比例
          const imgWidth = canvas.width;
          const imgHeight = canvas.height;

          // 使用常量配置的比例计算
          const ratio = (pageWidth * 0.95) / imgWidth;

          // 计算位置
          const x = (pageWidth - imgWidth * ratio) / 2;
          const y = 2; // 最小顶部边距

          // 将canvas添加到PDF
          pdf.addImage(
            canvas.toDataURL('image/png', this.STYLES.IMAGE_QUALITY),
            'PNG',
            x,
            y,
            imgWidth * ratio,
            imgHeight * ratio
          );

          // 导出为PDF
          return new Blob([pdf.output('arraybuffer')], { type: 'application/pdf' });
        } finally {
          // 清理：从文档中移除临时容器
          document.body.removeChild(container);
        }
      }
    } catch (error) {
      console.error('生成PDF文件失败:', error);
      throw new Error('生成PDF文件失败: ' + (error as Error).message);
    }
  }

  /**
   * 创建HTML格式的表格
   * @param data 表格数据
   * @param includeHeader 是否包含表头
   * @param headerRow 表头行数据（用于获取所有列名）
   * @returns HTML表格字符串
   */
  private static createTableHtml(data: any[], includeHeader: boolean = true, headerRow?: any): string {
    if (!data || data.length === 0) return '<table></table>';

    // 获取所有列名
    const headers = headerRow ? Object.keys(headerRow) : Object.keys(data[0]);

    // 构建HTML表格 - 使用自适应布局，完美居中
    let html = `<table style="width:100%;border-collapse:collapse;font-family:${this.STYLES.FONT_FAMILY};font-size:${this.STYLES.TABLE_FONT_SIZE};table-layout:auto;margin:0;border-spacing:0;">`;

    // 添加表头
    if (includeHeader) {
      html += `<thead><tr style="background-color:${this.STYLES.HEADER_BG};font-weight:bold;margin:0;padding:0;">`;

      // 计算所有列的宽度分配
      const columnWidths = this.calculateAllColumnWidths(headers, data);

      // 添加表头单元格 - 使用动态计算的列宽，强制垂直居中
      headers.forEach(header => {
        const width = columnWidths[header];
        const headerCellStyle = `border:${this.STYLES.BORDER};width:${width}%;height:${this.STYLES.CELL_HEIGHT};${this.STYLE_TEMPLATES.BASE_CELL}`;
        const headerInnerDivStyle = `${this.STYLE_TEMPLATES.FLEX_CENTER}padding:${this.STYLES.CELL_PADDING};font-weight:bold;`;
        html += `<th style="${headerCellStyle}"><div style="${headerInnerDivStyle}">${header}</div></th>`;
      });

      html += `</tr></thead>`;
    }

    // 添加表体
    html += '<tbody>';

    // 添加数据行 - 固定行高，强制垂直居中
    data.forEach(row => {
      html += `<tr style="height:${this.STYLES.CELL_HEIGHT};">`;

      // 添加单元格 - 强制垂直居中
      headers.forEach(header => {
        const value = row[header] !== null && row[header] !== undefined ? String(row[header]) : '';
        // 动态判断是否为长文本列
        const isLongText = this.isLongTextColumn(header, data);

        const cellStyle = isLongText
          ? `border:${this.STYLES.BORDER};word-wrap:break-word;height:${this.STYLES.CELL_HEIGHT};${this.STYLE_TEMPLATES.BASE_CELL}`
          : `border:${this.STYLES.BORDER};white-space:nowrap;height:${this.STYLES.CELL_HEIGHT};${this.STYLE_TEMPLATES.BASE_CELL}`;

        // 使用内部div来确保完美垂直居中
        const innerDivStyle = `${this.STYLE_TEMPLATES.FLEX_CENTER}padding:${this.STYLES.CELL_PADDING};`;
        html += `<td style="${cellStyle}"><div style="${innerDivStyle}">${value}</div></td>`;
      });

      html += '</tr>';
    });

    html += '</tbody></table>';

    return html;
  }
}

export default PdfHelper;
