import React, { createContext, useContext, useState } from 'react';

// 标签页上下文
type TabsContextValue = {
  value: string;
  onValueChange: (value: string) => void;
};

const TabsContext = createContext<TabsContextValue | undefined>(undefined);

// 使用标签页上下文
function useTabsContext() {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tabs组件必须在TabsProvider内部使用');
  }
  return context;
}

// 标签页属性
interface TabsProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

// 标签页组件
export const Tabs: React.FC<TabsProps> = ({ 
  value, 
  onValueChange, 
  children,
  className = ''
}) => {
  return (
    <TabsContext.Provider value={{ value, onValueChange }}>
      <div className={className}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

// 标签页列表属性
interface TabsListProps {
  children: React.ReactNode;
  className?: string;
}

// 标签页列表组件
export const TabsList: React.FC<TabsListProps> = ({ 
  children,
  className = ''
}) => {
  return (
    <div className={`flex ${className}`}>
      {children}
    </div>
  );
};

// 标签页触发器属性
interface TabsTriggerProps {
  value: string;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

// 标签页触发器组件
export const TabsTrigger: React.FC<TabsTriggerProps> = ({ 
  value, 
  children,
  className = '',
  disabled = false
}) => {
  const { value: selectedValue, onValueChange } = useTabsContext();
  const isSelected = selectedValue === value;

  return (
    <button
      type="button"
      role="tab"
      aria-selected={isSelected}
      data-state={isSelected ? 'active' : 'inactive'}
      disabled={disabled}
      onClick={() => onValueChange(value)}
      className={`${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
};

// 标签页内容属性
interface TabsContentProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

// 标签页内容组件
export const TabsContent: React.FC<TabsContentProps> = ({ 
  value, 
  children,
  className = ''
}) => {
  const { value: selectedValue } = useTabsContext();
  const isSelected = selectedValue === value;

  if (!isSelected) {
    return null;
  }

  return (
    <div
      role="tabpanel"
      data-state={isSelected ? 'active' : 'inactive'}
      className={className}
    >
      {children}
    </div>
  );
};
