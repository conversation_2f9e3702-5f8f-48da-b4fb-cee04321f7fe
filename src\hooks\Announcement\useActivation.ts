import { useState, useEffect, useCallback } from 'react';
import ActivationService from '../../services/Announcement/activationService';

/**
 * 激活Hook状态接口
 */
interface UseActivationState {
  isLoading: boolean;
  isQueryingCompany: boolean;
  isActivating: boolean;
  error: string | null;
  companyName: string;
  encryptedData: string;
  activationSuccess: boolean;
  showSuccessDialog: boolean;
  showErrorDialog: boolean;
}

/**
 * 激活Hook返回值接口
 */
interface UseActivationReturn extends UseActivationState {
  // 表单相关方法
  setCompanyName: (name: string) => void;
  
  // 激活相关方法
  handleQueryCompany: () => Promise<void>;
  handleActivateSystem: () => Promise<void>;
  
  // 弹窗控制方法
  showActivationSuccess: () => void;
  hideActivationSuccess: () => void;
  showActivationError: (errorMessage: string) => void;
  hideActivationError: () => void;
  
  // 重置和清理方法
  resetActivation: () => void;
  clearError: () => void;
}

/**
 * 激活Hook
 * Hook层：只负责激活相关的业务逻辑
 */
export const useActivation = (): UseActivationReturn => {
  // 状态管理
  const [state, setState] = useState<UseActivationState>({
    isLoading: false,
    isQueryingCompany: false,
    isActivating: false,
    error: null,
    companyName: '',
    encryptedData: '',
    activationSuccess: false,
    showSuccessDialog: false,
    showErrorDialog: false
  });

  // 获取服务实例
  const activationService = ActivationService.getInstance();

  /**
   * 更新状态的辅助函数
   */
  const updateState = useCallback((newState: Partial<UseActivationState>) => {
    setState(prev => ({ ...prev, ...newState }));
  }, []);

  /**
   * 设置公司名称
   */
  const setCompanyName = useCallback((name: string) => {
    updateState({ companyName: name, error: null });
  }, [updateState]);

  /**
   * 查询公司名称
   */
  const handleQueryCompany = useCallback(async () => {
    if (!state.companyName.trim()) {
      updateState({ error: '请输入公司名称' });
      return;
    }

    try {
      updateState({ error: null });

      const result = await activationService.queryCompany(state.companyName);

      updateState({
        encryptedData: result.encryptedData,
        error: null
      });

      console.log('公司查询成功，准备初始化数据库并激活系统');

      // 直接使用查询结果进行系统激活（包含数据库初始化），而不是依赖state
      await activationService.activateSystemWithDatabaseInit(result.companyName, result.encryptedData);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '公司查询失败';
      console.error('公司查询失败:', errorMessage);

      // 显示错误弹窗而不是简单的错误提示
      showActivationError(errorMessage);
    }
  }, [state.companyName, activationService]);

  /**
   * 激活系统
   */
  const handleActivateSystem = useCallback(async (companyName?: string, encryptedData?: string) => {
    const finalCompanyName = companyName || state.companyName.trim();
    const finalEncryptedData = encryptedData || state.encryptedData;

    if (!finalCompanyName || !finalEncryptedData) {
      updateState({ error: '缺少必要的激活数据' });
      return;
    }

    try {
      const successMessage = await activationService.activateSystem(
        finalCompanyName,
        finalEncryptedData
      );

      console.log('系统激活成功:', successMessage);
      // 状态更新由事件处理器自动完成，无需手动更新

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '系统激活失败';
      console.error('系统激活失败:', errorMessage);
      // 错误状态由事件处理器自动完成，无需手动更新
    }
  }, [state.companyName, state.encryptedData, activationService]);

  /**
   * 显示激活成功弹窗
   */
  const showActivationSuccess = useCallback(() => {
    updateState({ showSuccessDialog: true });
  }, [updateState]);

  /**
   * 隐藏激活成功弹窗
   */
  const hideActivationSuccess = useCallback(() => {
    updateState({ showSuccessDialog: false });
  }, [updateState]);

  /**
   * 显示激活错误弹窗
   */
  const showActivationError = useCallback((errorMessage: string) => {
    updateState({
      showErrorDialog: true,
      error: errorMessage
    });
  }, [updateState]);

  /**
   * 隐藏激活错误弹窗
   */
  const hideActivationError = useCallback(() => {
    updateState({
      showErrorDialog: false,
      error: null
    });
  }, [updateState]);

  /**
   * 重置激活状态
   */
  const resetActivation = useCallback(() => {
    activationService.resetState();
    updateState({
      isLoading: false,
      isQueryingCompany: false,
      isActivating: false,
      error: null,
      companyName: '',
      encryptedData: '',
      activationSuccess: false,
      showSuccessDialog: false,
      showErrorDialog: false
    });
  }, [activationService, updateState]);

  /**
   * 清除错误信息
   */
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  /**
   * 监听服务状态变化
   */
  useEffect(() => {
    const handleStateChange = (serviceState: any) => {
      updateState({
        isLoading: serviceState.isLoading,
        isQueryingCompany: serviceState.isQueryingCompany,
        isActivating: serviceState.isActivating,
        error: serviceState.error,
        encryptedData: serviceState.encryptedData,
        activationSuccess: serviceState.activationSuccess
      });
    };

    const handleCompanyQuerySuccess = (data: { companyName: string; encryptedData: string }) => {
      console.log('公司查询成功事件:', data);
    };

    const handleCompanyQueryFailed = (error: string) => {
      console.error('公司查询失败事件:', error);
      // 显示错误弹窗
      showActivationError(error);
    };

    const handleActivationSuccess = (message: string) => {
      console.log('激活成功事件:', message);
      // 只显示成功弹窗，其他状态由handleStateChange统一处理
      updateState({ showSuccessDialog: true });
    };

    const handleActivationFailed = (error: string) => {
      console.error('激活失败事件:', error);
      // 显示错误弹窗
      showActivationError(error);
    };

    const handleError = (error: string) => {
      console.error('激活服务错误事件:', error);
      // 显示错误弹窗
      showActivationError(error);
    };

    // 订阅事件
    activationService.on('state-change', handleStateChange);
    activationService.on('company-query-success', handleCompanyQuerySuccess);
    activationService.on('company-query-failed', handleCompanyQueryFailed);
    activationService.on('activation-success', handleActivationSuccess);
    activationService.on('activation-failed', handleActivationFailed);
    activationService.on('error', handleError);

    // 清理函数
    return () => {
      activationService.off('state-change', handleStateChange);
      activationService.off('company-query-success', handleCompanyQuerySuccess);
      activationService.off('company-query-failed', handleCompanyQueryFailed);
      activationService.off('activation-success', handleActivationSuccess);
      activationService.off('activation-failed', handleActivationFailed);
      activationService.off('error', handleError);
    };
  }, [activationService, updateState]);

  return {
    // 状态
    ...state,
    
    // 表单相关方法
    setCompanyName,
    
    // 激活相关方法
    handleQueryCompany,
    handleActivateSystem,
    
    // 弹窗控制方法
    showActivationSuccess,
    hideActivationSuccess,
    showActivationError,
    hideActivationError,

    // 重置和清理方法
    resetActivation,
    clearError
  };
};
