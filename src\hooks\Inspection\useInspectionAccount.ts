import { useState, useEffect, useCallback } from 'react';
import InspectionAccountService, {
  InspectionAccountServiceState,
  AccountInfo
} from '../../services/Inspection/inspectionAccountService';

// 重新导出类型，这样UI层可以从Hook导入，而不是直接从Service层导入
export type { AccountInfo };

/**
 * 巡检账户Hook返回值接口
 */
interface UseInspectionAccountReturn {
  // 状态
  isLoading: boolean;
  error?: string;
  accounts: AccountInfo[];
  currentAccount?: AccountInfo;

  // 方法
  getAccounts: (forceRefresh?: boolean) => Promise<AccountInfo[]>;
  loadAccounts: () => Promise<AccountInfo[]>;
  addAccount: (account: Omit<AccountInfo, 'id'> & { password: string }) => Promise<AccountInfo>;
  updateAccount: (id: string, updates: Partial<AccountInfo>, currentPassword?: string, newPassword?: string) => Promise<AccountInfo>;
  deleteAccount: (id: string) => Promise<boolean>;
  changePassword: (username: string, oldPassword: string, newPassword: string) => Promise<boolean>;

  // 辅助方法
  getAccountById: (id: string) => AccountInfo | undefined;
  getAccountByUsername: (username: string) => AccountInfo | undefined;
  getAdminAccounts: () => AccountInfo[];
  getUserAccounts: () => AccountInfo[];
  getLockedAccounts: () => AccountInfo[];
  checkPersonAccountExists: (personName: string, personAlias?: string) => AccountInfo | null;
}

/**
 * 巡检账户Hook
 * 提供巡检账户管理功能和状态管理
 */
export function useInspectionAccount(): UseInspectionAccountReturn {
  // 获取服务实例
  const service = InspectionAccountService.getInstance();

  // 状态
  const [state, setState] = useState<InspectionAccountServiceState>(service.getState());

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: InspectionAccountServiceState) => {
      setState(newState);
    };

    // 订阅状态变化事件
    service.on('state-change', handleStateChange);

    // 初始化数据监听器
    const removeDataListener = service.initializeDataListener();

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
      removeDataListener();
    };
  }, [service]);

  // 获取账户列表（智能缓存）
  const getAccounts = useCallback(async (forceRefresh = false) => {
    try {
      return await service.getAccounts(forceRefresh);
    } catch (error) {
      console.error('获取账户失败:', error);
      throw error;
    }
  }, [service]);

  // 加载账户列表（强制刷新）
  const loadAccounts = useCallback(async () => {
    return await service.loadAccounts();
  }, [service]);

  // 添加账户
  const addAccount = useCallback(async (account: Omit<AccountInfo, 'id'> & { password: string }) => {
    return await service.addAccount(account);
  }, [service]);

  // 更新账户信息
  const updateAccount = useCallback(async (
    id: string,
    updates: Partial<AccountInfo>,
    currentPassword?: string,
    newPassword?: string
  ) => {
    return await service.updateAccount(id, updates, currentPassword, newPassword);
  }, [service]);

  // 删除账户
  const deleteAccount = useCallback(async (id: string) => {
    return await service.deleteAccount(id);
  }, [service]);

  // 修改账户密码
  const changePassword = useCallback(async (username: string, oldPassword: string, newPassword: string) => {
    return await service.changePassword(username, oldPassword, newPassword);
  }, [service]);

  // 根据ID获取账户
  const getAccountById = useCallback((id: string) => {
    return state.accounts.find(account => account.id === id);
  }, [state.accounts]);

  // 根据用户名获取账户
  const getAccountByUsername = useCallback((username: string) => {
    return state.accounts.find(account => account.username === username);
  }, [state.accounts]);

  // 获取管理员账户
  const getAdminAccounts = useCallback(() => {
    return state.accounts.filter(account => account.role === 'admin');
  }, [state.accounts]);

  // 获取普通用户账户
  const getUserAccounts = useCallback(() => {
    return state.accounts.filter(account => account.role === 'user');
  }, [state.accounts]);

  // 获取锁定的账户
  const getLockedAccounts = useCallback(() => {
    return state.accounts.filter(account => account.isLocked);
  }, [state.accounts]);

  // 检查人员是否已存在账户
  const checkPersonAccountExists = useCallback((personName: string, personAlias?: string) => {
    return service.checkPersonAccountExists(personName, personAlias);
  }, [service]);

  return {
    // 状态
    isLoading: state.isLoading,
    error: state.error,
    accounts: state.accounts,
    currentAccount: state.currentAccount,

    // 方法
    getAccounts,
    loadAccounts,
    addAccount,
    updateAccount,
    deleteAccount,
    changePassword,

    // 辅助方法
    getAccountById,
    getAccountByUsername,
    getAdminAccounts,
    getUserAccounts,
    getLockedAccounts,
    checkPersonAccountExists
  };
}

export default useInspectionAccount;
