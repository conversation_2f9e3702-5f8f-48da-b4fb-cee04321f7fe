import React, { useState, useEffect, useRef } from 'react';
import { 
  RefreshCw, 
  Filter, 
  X, 
  ChevronDown, 
  ChevronLeft, 
  ChevronRight,
  FileText,
  User,
  Clock
} from 'lucide-react';
import { useAccountLog, AccountLogFilter } from '../../hooks/Inspection/useAccountLog';
import { useListFocus } from '../../hooks/base';
import CustomScrollbar from '../../components/ui/CustomScrollbar';
import { formatInspectionDateTime } from '../../utils/formatUtils';

/**
 * 账户管理日志页面属性接口
 */
interface AccountLogProps {
  searchText?: string;
  startDate?: string;
  endDate?: string;
  refreshTrigger?: number;
}

/**
 * 账户管理日志页面
 * 提供查看账户管理日志的功能
 */
const AccountLog: React.FC<AccountLogProps> = ({
  searchText = '',
  startDate = '',
  endDate = '',
  refreshTrigger = 0
}) => {
  // 表格焦点管理
  const { listRef, handleKeyDown: handleTableKeyDown } = useListFocus({
    itemSelector: 'tr[data-row-id]',
    autoFocusOnAdd: true
  });

  // 使用账户管理日志Hook
  const {
    error,
    filteredLogs,
    isLoading,
    getAccountLogs,
    forceRefreshAccountLogs,
    setFilter,
    // 分页相关
    currentPage,
    pageSize,
    totalPages,
    paginatedLogs,
    setPage,
    setPageSize
  } = useAccountLog();

  // 本地状态
  const [showFilters, setShowFilters] = useState(false);
  const [localFilter, setLocalFilter] = useState<AccountLogFilter>({});
  const [showPageSizeDropdown, setShowPageSizeDropdown] = useState(false);

  // 引用
  const pageSizeDropdownRef = useRef<HTMLDivElement>(null);

  // 初始化数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        await getAccountLogs();
      } catch (err) {
        console.error('初始化账户管理日志数据失败:', err);
      }
    };

    initializeData();
  }, [getAccountLogs]);

  // 处理刷新数据（强制刷新，忽略缓存）
  const handleGetLogs = async () => {
    try {
      await forceRefreshAccountLogs();
    } catch (err) {
      console.error('刷新账户管理日志失败:', err);
    }
  };

  // 响应外部刷新触发器
  useEffect(() => {
    if (refreshTrigger > 0) {
      handleGetLogs();
    }
  }, [refreshTrigger, handleGetLogs]);

  // 响应外部搜索和日期筛选
  useEffect(() => {
    const newFilter: Partial<AccountLogFilter> = {};
    
    if (searchText) {
      newFilter.searchText = searchText;
    }
    if (startDate) {
      newFilter.startDate = startDate;
    }
    if (endDate) {
      newFilter.endDate = endDate;
    }

    setFilter(newFilter);
  }, [searchText, startDate, endDate, setFilter]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pageSizeDropdownRef.current && !pageSizeDropdownRef.current.contains(event.target as Node)) {
        setShowPageSizeDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理应用筛选
  const handleApplyFilter = () => {
    setFilter(localFilter);
    setShowFilters(false);
  };

  // 处理清除筛选
  const handleClearFilter = () => {
    setLocalFilter({});
    setFilter({});
  };

  // 使用统一的时间戳格式化函数，自动处理秒级/毫秒级时间戳
  const formatTimestamp = formatInspectionDateTime;

  return (
    <div className="h-full flex flex-col">
      {/* 工具栏 */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-3">
          {/* 刷新按钮 */}
          <button
            onClick={handleGetLogs}
            disabled={isLoading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新数据
          </button>

          {/* 筛选按钮 */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </button>
        </div>

        {/* 统计信息 */}
        <div className="text-sm text-gray-600">
          共 {filteredLogs.length} 条记录
        </div>
      </div>

      {/* 筛选面板 */}
      {showFilters && (
        <div className="flex-shrink-0 bg-gray-50 border-b border-gray-200 p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 日志类型筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
              <select
                value={localFilter.logType || ''}
                onChange={(e) => setLocalFilter({
                  ...localFilter,
                  logType: e.target.value ? parseInt(e.target.value) : undefined
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="">全部类型</option>
                <option value="50">添加移动端用户</option>
                <option value="51">修改移动端用户</option>
                <option value="52">删除移动端用户</option>
              </select>
            </div>

            {/* 用户名筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <input
                type="text"
                value={localFilter.userName || ''}
                onChange={(e) => setLocalFilter({
                  ...localFilter,
                  userName: e.target.value
                })}
                placeholder="输入用户名"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>

            {/* 人员姓名筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">人员姓名</label>
              <input
                type="text"
                value={localFilter.personName || ''}
                onChange={(e) => setLocalFilter({
                  ...localFilter,
                  personName: e.target.value
                })}
                placeholder="输入人员姓名"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
          </div>

          {/* 筛选操作按钮 */}
          <div className="flex items-center justify-end space-x-3 mt-4">
            <button
              onClick={handleClearFilter}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <X className="h-4 w-4 mr-2" />
              清除
            </button>
            <button
              onClick={handleApplyFilter}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              应用筛选
            </button>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="flex-shrink-0 bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 自适应数据表格 */}
      <div className="flex-1 min-h-0 bg-white shadow-sm adaptive-table-container">
        <div className="adaptive-table-content">
          <CustomScrollbar
            className="h-full"
            horizontal={true}
            vertical={true}
          >
            <div ref={listRef as React.RefObject<HTMLDivElement>} onKeyDown={handleTableKeyDown}>
              <table className="w-full adaptive-table" style={{ minWidth: '1000px' }}>
                {/* 表头 */}
                <thead>
                  <tr>
                    <th className="table-header-cell table-cell-left w-32">操作类型</th>
                    <th className="table-header-cell table-cell-left w-24">用户名</th>
                    <th className="table-header-cell table-cell-left w-24">人员姓名</th>
                    <th className="table-header-cell table-cell-left w-32">人员备注</th>
                    <th className="table-header-cell table-cell-left w-32">操作时间</th>
                  </tr>
                </thead>

                {/* 表体 */}
                <tbody className="bg-white">
                  {filteredLogs.length === 0 ? (
                    <tr className="adaptive-table-empty">
                      <td colSpan={5} className="px-4 py-12 text-center">
                        <div className="flex flex-col items-center justify-center h-full min-h-[200px]">
                          <RefreshCw className={`h-8 w-8 mb-3 ${
                            isLoading ? 'text-blue-600 animate-spin' : 'text-gray-400'
                          }`} />
                          <p className="text-gray-500 text-sm font-medium">
                            {isLoading ? '获取数据中...' : '暂无账户管理日志数据'}
                          </p>
                          {!isLoading && (
                            <p className="text-gray-400 text-xs mt-1">
                              数据已自动获取，如需最新数据请点击"刷新数据"按钮
                            </p>
                          )}
                        </div>
                      </td>
                    </tr>
                  ) : (
                    paginatedLogs.map((log) => (
                      <tr
                        key={log.id}
                        data-row-id={log.id}
                        className="adaptive-table-row hover:bg-gray-50 transition-colors duration-150"
                      >
                        {/* 操作类型 */}
                        <td className="table-cell whitespace-nowrap w-32">
                          <div className="flex items-center">
                            <FileText className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                            <span className="text-xs text-gray-700">
                              {log.log_type_text || '未知操作'}
                            </span>
                          </div>
                        </td>

                        {/* 用户名 */}
                        <td className="table-cell whitespace-nowrap w-24">
                          <span className="text-xs text-gray-700">
                            {(() => {
                              const newData = log.new_data || {};
                              const originalData = log.original_data || {};
                              return newData.user_name || originalData.user_name || '-';
                            })()}
                          </span>
                        </td>

                        {/* 人员姓名 */}
                        <td className="table-cell whitespace-nowrap w-24">
                          <div className="flex items-center">
                            <User className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                            <span className="text-xs text-gray-700">
                              {(() => {
                                const newData = log.new_data || {};
                                const originalData = log.original_data || {};
                                return newData.person_name || originalData.person_name || '-';
                              })()}
                            </span>
                          </div>
                        </td>

                        {/* 人员备注 */}
                        <td className="table-cell w-32">
                          <div className="truncate" title={(() => {
                            const newData = log.new_data || {};
                            const originalData = log.original_data || {};
                            return newData.person_alias || originalData.person_alias || '-';
                          })()}>
                            <span className="text-xs text-gray-700">
                              {(() => {
                                const newData = log.new_data || {};
                                const originalData = log.original_data || {};
                                return newData.person_alias || originalData.person_alias || '-';
                              })()}
                            </span>
                          </div>
                        </td>

                        {/* 操作时间 */}
                        <td className="table-cell whitespace-nowrap w-32">
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                            <span className="text-xs text-gray-700" title={formatTimestamp(log.timestamp)}>
                              {formatTimestamp(log.timestamp)}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </CustomScrollbar>
        </div>
      </div>

      {/* 分页组件 */}
      {filteredLogs.length > 0 && (
        <div className="flex-shrink-0 flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-600">
            <span className="mr-2">每页显示</span>
            <div className="relative inline-block mx-1" style={{ width: '65px' }} ref={pageSizeDropdownRef}>
              <div
                className="bg-white border border-slate-300 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer relative"
                style={{ padding: '3px 24px 3px 6px', fontSize: '14px', textAlign: 'center', height: '28px', lineHeight: '22px' }}
                onClick={() => setShowPageSizeDropdown(!showPageSizeDropdown)}
              >
                <span className="font-medium text-slate-700">
                  {pageSize === -1 ? '全部' : pageSize}
                </span>
                <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
              </div>
              {showPageSizeDropdown && (
                <div className="absolute bottom-full mb-1 left-0 bg-white border border-slate-300 rounded shadow-lg z-50" style={{ minWidth: '65px' }}>
                  {[10, 20, 50, 100, -1].map((size) => (
                    <div
                      key={size}
                      className="px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 cursor-pointer text-center"
                      onClick={() => {
                        setPageSize(size);
                        setShowPageSizeDropdown(false);
                      }}
                    >
                      {size === -1 ? '全部' : size}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <span className="ml-2">条记录</span>
          </div>

          {pageSize !== -1 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                第 {currentPage + 1} 页，共 {totalPages} 页
              </span>
              <div className="flex space-x-1">
                <button
                  onClick={() => setPage(currentPage - 1)}
                  disabled={currentPage === 0}
                  className="p-1 rounded border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setPage(currentPage + 1)}
                  disabled={currentPage >= totalPages - 1}
                  className="p-1 rounded border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AccountLog;
