import GlobalConfigService from './globalConfigService';
import WebSocketService from '../utils/websocket';
import TaskManager from '../utils/taskManager';

/**
 * 系统初始化服务
 * 负责在应用启动时进行根节点验证等初始化工作
 */
class SystemInitService {
  private static instance: SystemInitService;
  private static isInitialized: boolean = false;
  private static initializationPromise: Promise<boolean> | null = null;
  private ws: WebSocketService;
  private task: TaskManager;

  /**
   * 获取系统初始化服务实例
   */
  public static getInstance(): SystemInitService {
    if (!SystemInitService.instance) {
      SystemInitService.instance = new SystemInitService();
    }
    return SystemInitService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this.ws = WebSocketService.getInstance();
    this.task = TaskManager.getInstance();
  }

  /**
   * 初始化系统
   * @returns 初始化是否成功
   */
  public async initialize(): Promise<boolean> {
    // 如果已经有初始化过程在进行中，等待结果
    if (SystemInitService.initializationPromise) {
      return SystemInitService.initializationPromise;
    }

    // 如果已经初始化过，直接返回结果
    if (SystemInitService.isInitialized) {
      return GlobalConfigService.isRootNodeValidated();
    }

    // 开始初始化过程
    SystemInitService.initializationPromise = this.performInitialization();
    return SystemInitService.initializationPromise;
  }

  /**
   * 执行系统初始化 - 步骤间留有响应时间
   */
  private async performInitialization(): Promise<boolean> {
    // 系统级步骤间延迟时间（毫秒）
    const SYSTEM_STEP_DELAY = 200;

    try {
      console.log('开始系统初始化...');

      // 步骤1: 确保WebSocket连接
      console.log('SystemInitService: 步骤1 - 确保WebSocket连接');
      if (!this.ws.isConnected()) {
        await this.ws.connect();
      }

      // 系统级步骤间延迟
      await new Promise(resolve => setTimeout(resolve, SYSTEM_STEP_DELAY));

      // 步骤2: 验证根节点
      console.log('SystemInitService: 步骤2 - 验证公司名称');
      const rootNodeValid = await GlobalConfigService.validateRootNode();

      // 如果根节点验证失败，直接返回，不进行数据库初始化
      if (!rootNodeValid) {
        console.error('SystemInitService: 公司名称验证失败，跳过数据库初始化');
        SystemInitService.isInitialized = true; // 标记为已初始化，但失败
        return false;
      }

      // 系统级步骤间延迟
      await new Promise(resolve => setTimeout(resolve, SYSTEM_STEP_DELAY));

      // 步骤3: 初始化数据库连接
      console.log('SystemInitService: 步骤3 - 初始化数据库连接');
      await this.initializeDatabase();

      // 如果执行到这里，说明根节点验证和数据库初始化都成功了
      console.log('SystemInitService: 系统初始化成功');
      SystemInitService.isInitialized = true;
      return true;
    } catch (error) {
      console.error('SystemInitService: 系统初始化过程出错:', error);
      SystemInitService.isInitialized = true; // 标记为已初始化，但失败
      return false;
    } finally {
      SystemInitService.initializationPromise = null;
    }
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase(): Promise<void> {
    try {
      console.log('开始数据库初始化...');

      // 准备数据库初始化参数
      const params = {
        "action": "set_database_path",
        "action_params": {
          "path": "AccountTableDll.db"
        }
      };

      console.log('数据库初始化请求参数:', JSON.stringify(params));

      // 提交数据库初始化任务
      const taskId = await this.task.submitTask('AccountTableDll', 'DbFun', params);

      // 等待任务完成
      await new Promise<void>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            console.log('数据库初始化成功');
            resolve();
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || '数据库初始化失败'));
          }
        };
        this.task.onTaskUpdate(taskId, handleTaskUpdate);
      });

    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查系统是否已初始化
   */
  public static isSystemInitialized(): boolean {
    return SystemInitService.isInitialized;
  }

  /**
   * 检查系统是否可用（初始化成功且根节点验证通过）
   */
  public static isSystemAvailable(): boolean {
    return SystemInitService.isInitialized && 
           GlobalConfigService.isRootNodeValidated() && 
           !GlobalConfigService.isRootNodeValidationFailed();
  }

  /**
   * 重置系统状态（用于测试或重新初始化）
   */
  public static reset(): void {
    SystemInitService.isInitialized = false;
    SystemInitService.initializationPromise = null;
    console.log('系统状态已重置');
  }
}

export default SystemInitService;
