# 硬盘序列号多值显示功能

## 功能概述
为添加设备台账信息菜单中的扩展字段"硬盘序列号"添加多值支持，允许用户输入多个硬盘序列号并用分号分隔，同时在表格中提供友好的多值显示方式。

## 设计原则
- **保持现有布局**：尽量不对现有布局做过大改动
- **用户友好**：提供清晰的输入提示和直观的显示方式
- **响应式设计**：适配不同屏幕尺寸
- **交互便捷**：支持快捷键和展开/收起功能

## 实现方案

### 1. 输入组件设计 (HardDriveSerialField)

#### 核心特性
- **分隔符提示**：明确提示用户使用 `;` 号分隔多个序列号
- **双模式编辑**：支持单行和多行编辑模式
- **智能展开**：当内容较长或有多个序列号时显示展开按钮
- **实时计数**：显示当前序列号数量和字节计数
- **快捷键支持**：Ctrl+Enter 快速切换编辑模式

#### 界面元素
```
硬盘序列号 * [2个序列号]
┌─────────────────────────────────────────────┐
│ SN001; SN002                           ↓ 15/512 │
└─────────────────────────────────────────────┘
  ┌─ 聚焦时显示提示（虚拟空间，不影响布局）─┐
  │ 多个硬盘序列号用 ; 号间隔，支持 Ctrl+Enter 切换编辑模式 │
  └─────────────────────────────────────────┘
```

#### 展开模式
```
硬盘序列号 * [3个序列号]
┌─────────────────────────────────────────────┐
│ 请输入硬盘序列号，多个序列号用 ; 号间隔        ↑ │
│ 例如：SN001; SN002; SN003                    │
│                                             │
│                                             │
└─────────────────────────────────────────────┘
多个硬盘序列号用 ; 号间隔，支持 Ctrl+Enter 切换编辑模式
```

### 2. 表格显示组件设计 (HardDriveSerialDisplay)

#### 显示策略
- **单个序列号**：直接显示，使用等宽字体
- **多个序列号**：列表形式显示，支持折叠
- **超出限制**：默认显示前2个，其余折叠显示

#### 显示效果
```
单个序列号：
SN001234567890

多个序列号（展开）：
• SN001234567890
• SN002345678901
• SN003456789012

多个序列号（折叠）：
• SN001234567890
• SN002345678901
+1个 ↓
```

### 3. 技术实现

#### 文件结构
```
src/components/ui/
├── HardDriveSerialField.tsx      # 输入组件
└── HardDriveSerialDisplay.tsx    # 显示组件

src/pages/Inventory/InventoryManagement/Dialogs/
└── InventoryForm.tsx             # 集成输入组件

src/pages/Inventory/InventoryManagement/
├── InventoryTable.tsx            # 集成显示组件
└── InventoryTableNew.tsx         # 集成显示组件
```

#### 核心逻辑
```typescript
// 序列号解析
const parseSerialNumbers = (input: string): string[] => {
  if (!input) return [];
  return input.split(';').map(s => s.trim()).filter(s => s.length > 0);
};

// 格式化显示
const formatSerialNumbers = (input: string): string => {
  const serials = parseSerialNumbers(input);
  if (serials.length <= 1) return input;
  return serials.join('; ');
};
```

#### 字段识别
系统通过以下条件识别硬盘序列号字段：
- `field.title === '硬盘序列号'`
- `field.key === 'hard_drive_serial'`
- `field.title.includes('硬盘序列号')`

### 4. 用户体验优化

#### 输入体验
1. **智能提示**：聚焦时显示分隔符使用说明，使用虚拟空间不影响布局
2. **实时反馈**：显示当前序列号数量和字节使用情况
3. **智能格式化**：自动整理分号间距
4. **模式切换**：支持单行/多行编辑模式切换
5. **快捷操作**：Ctrl+Enter 快速切换模式
6. **布局协调**：提示信息不占用实际空间，保持字段间距一致

#### 显示体验
1. **层次清晰**：使用项目符号区分不同序列号
2. **空间优化**：默认折叠多余内容，节省表格空间
3. **交互友好**：点击展开/收起，不影响表格其他操作
4. **信息完整**：显示总数量，便于快速了解设备配置

### 5. 兼容性考虑

#### 数据兼容
- **向后兼容**：单个序列号的现有数据无需修改
- **存储格式**：仍使用字符串存储，以分号分隔
- **API兼容**：不改变现有API接口

#### 浏览器兼容
- **基础功能**：支持所有现代浏览器
- **快捷键**：Ctrl+Enter 在所有浏览器中正常工作
- **样式兼容**：使用标准CSS，确保在m108内核中正常显示

### 6. 验证规则

#### 输入验证
- **字节限制**：总长度不超过512字节
- **格式检查**：自动去除空白序列号
- **重复检测**：可选择是否允许重复序列号

#### 显示验证
- **空值处理**：空值显示为 `-`
- **异常处理**：格式错误时显示原始内容
- **长度限制**：超长序列号自动截断并显示省略号

## 使用示例

### 输入示例
```
正确格式：
SN001; SN002; SN003
SN001234567890; SN002345678901

自动处理：
SN001 ; SN002 ; SN003  → SN001; SN002; SN003
SN001;;SN002           → SN001; SN002
```

### 显示示例
```
表格中的显示效果：
┌─────────────────────┐
│ • SN001234567890    │
│ • SN002345678901    │
│ +1个 ↓              │
└─────────────────────┘
```

## 测试要点

### 功能测试
1. **输入测试**：验证分号分隔、格式化、字节限制
2. **显示测试**：验证单个/多个序列号的显示效果
3. **交互测试**：验证展开/收起、模式切换功能
4. **兼容测试**：验证现有数据的兼容性

### 界面测试
1. **响应式测试**：不同屏幕尺寸下的显示效果
2. **样式测试**：与现有界面风格的一致性
3. **可用性测试**：用户操作的便捷性和直观性

## 后续扩展

### 可能的增强功能
1. **序列号验证**：添加序列号格式验证规则
2. **重复检测**：检测并提示重复的序列号
3. **批量导入**：支持从文件批量导入序列号
4. **导出优化**：在导出时优化多序列号的显示格式

### 其他字段扩展
该设计模式可以扩展到其他需要多值输入的字段：
- MAC地址列表
- IP地址列表
- 软件版本列表
- 配件序列号列表

## 布局优化更新

### 问题描述
初始版本中，提示信息"多个硬盘序列号用 ; 号间隔，支持 Ctrl+Enter 切换编辑模式"占用了实际的DOM空间，导致与其他字段的间距不协调。

### 解决方案
将提示信息改为使用绝对定位的虚拟空间：

#### 技术实现
```css
/* 提示信息使用绝对定位，不占用布局空间 */
.hint-text {
  position: absolute;
  left: 0;
  top: 100%;
  margin-top: 4px;
  pointer-events: none;
  z-index: 10;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #e5e7eb;
}
```

#### 交互优化
- **按需显示**：只在输入框聚焦时显示提示信息
- **视觉层次**：添加背景色和边框，确保在任何背景下都清晰可读
- **不干扰布局**：使用`pointer-events: none`确保不影响其他元素的交互

#### 最终效果
- ✅ 提示信息不再占用实际空间
- ✅ 字段间距与其他字段保持一致
- ✅ 聚焦时智能显示，不聚焦时隐藏
- ✅ 视觉效果更加协调统一

## 表格显示优化更新

### 问题描述
表格中硬盘序列号显示组件底部的"共 X 个序列号"提示信息占用了额外空间，影响表格的紧凑性。

### 解决方案
移除表格显示中的序列号总数提示信息，保持表格内容的简洁性。

#### 优化前
```
• ABCD-YU10-JKLH-GHJK
• ZBCD-YU10-JKLH-GHJK
+2个 ↓
共 4 个序列号
```

#### 优化后
```
• ABCD-YU10-JKLH-GHJK
• ZBCD-YU10-JKLH-GHJK
+2个 ↓
```

#### 改进效果
- ✅ 减少了表格单元格的垂直空间占用
- ✅ 保持了展开/收起功能的完整性
- ✅ 提升了表格整体的视觉紧凑性
- ✅ 用户仍可通过展开按钮的提示获知总数信息
