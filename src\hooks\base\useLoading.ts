import { useState, useCallback } from 'react';

// 加载状态钩子返回值接口
export interface UseLoadingReturn {
  // 加载状态
  isLoading: boolean;
  error: Error | null;
  
  // 加载方法
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
  reset: () => void;
  
  // 包装异步函数
  withLoading: <T>(fn: () => Promise<T>) => Promise<T>;
}

/**
 * 加载状态钩子
 * 提供异步操作的加载状态管理
 * @param initialLoading 初始加载状态
 */
export function useLoading(initialLoading: boolean = false): UseLoadingReturn {
  // 加载状态
  const [isLoading, setIsLoading] = useState<boolean>(initialLoading);
  // 错误状态
  const [error, setError] = useState<Error | null>(null);
  
  // 设置加载状态
  const setLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);
  
  // 设置错误状态
  const setErrorState = useCallback((error: Error | null) => {
    setError(error);
  }, []);
  
  // 重置状态
  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
  }, []);
  
  // 包装异步函数，自动处理加载状态
  const withLoading = useCallback(async <T>(fn: () => Promise<T>): Promise<T> => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await fn();
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  return {
    isLoading,
    error,
    setLoading,
    setError: setErrorState,
    reset,
    withLoading
  };
}

/**
 * 多任务加载状态钩子
 * 提供多个异步任务的加载状态管理
 */
export function useMultiLoading(): {
  isLoading: boolean;
  loadingTasks: Set<string>;
  startLoading: (taskId: string) => void;
  stopLoading: (taskId: string) => void;
  withTaskLoading: <T>(taskId: string, fn: () => Promise<T>) => Promise<T>;
} {
  // 加载中的任务集合
  const [loadingTasks, setLoadingTasks] = useState<Set<string>>(new Set());
  
  // 开始加载任务
  const startLoading = useCallback((taskId: string) => {
    setLoadingTasks(prev => {
      const newTasks = new Set(prev);
      newTasks.add(taskId);
      return newTasks;
    });
  }, []);
  
  // 停止加载任务
  const stopLoading = useCallback((taskId: string) => {
    setLoadingTasks(prev => {
      const newTasks = new Set(prev);
      newTasks.delete(taskId);
      return newTasks;
    });
  }, []);
  
  // 包装异步函数，自动处理特定任务的加载状态
  const withTaskLoading = useCallback(async <T>(taskId: string, fn: () => Promise<T>): Promise<T> => {
    try {
      startLoading(taskId);
      const result = await fn();
      return result;
    } finally {
      stopLoading(taskId);
    }
  }, [startLoading, stopLoading]);
  
  return {
    isLoading: loadingTasks.size > 0,
    loadingTasks,
    startLoading,
    stopLoading,
    withTaskLoading
  };
}
