# 键盘快捷键快速参考

## 🚀 快速参考卡片

### 全局导航
```
Tab             → 下一个元素
Shift + Tab     → 上一个元素
Enter           → 激活/确认
Space           → 激活按钮/切换
Escape          → 关闭/取消
```

### 页面导航
```
Alt + 1         → 跳转到主要内容
Alt + 2         → 跳转到导航菜单
Home            → 页面顶部
End             → 页面底部
```

### 对话框操作
```
Escape          → 关闭对话框
Tab             → 对话框内导航
Enter           → 确认操作
```

### 表单操作
```
Tab             → 下一个字段
Shift + Tab     → 上一个字段
Enter           → 提交表单
Escape          → 取消编辑
```

### 列表/表格操作
```
↑               → 上一项/上一行
↓               → 下一项/下一行
←               → 左移（表格）
→               → 右移（表格）
Home            → 第一项/行首
End             → 最后一项/行尾
Enter           → 激活/编辑
```

## 📱 移动端手势

### iOS VoiceOver
```
单指右滑        → 下一个元素
单指左滑        → 上一个元素
双击            → 激活元素
三指上滑        → 滚动向上
三指下滑        → 滚动向下
```

### Android TalkBack
```
右滑            → 下一个元素
左滑            → 上一个元素
双击            → 激活元素
上滑再下滑      → 激活
下滑再上滑      → 返回
```

## 🎯 焦点指示器

### 视觉指示
- **蓝色边框**：标准焦点状态
- **高亮背景**：列表项选中状态
- **下划线**：链接焦点状态
- **阴影效果**：按钮焦点状态

### 屏幕阅读器反馈
- **元素类型**：按钮、链接、输入框等
- **状态信息**：选中、展开、必填等
- **位置信息**：第X项，共Y项
- **操作提示**：可编辑、可选择等

## 🔧 开发者快捷操作

### 调试快捷键
```
F12             → 开发者工具
Ctrl + Shift + I → 检查元素
Ctrl + Shift + C → 选择元素
```

### 焦点调试
```javascript
// 控制台命令
document.activeElement          // 查看当前焦点
$0.focus()                     // 设置焦点到选中元素
document.body.classList.add('keyboard-navigation')  // 启用键盘模式
```

## 📋 测试检查清单

### ✅ 基础测试
- [ ] 所有交互元素可通过Tab访问
- [ ] 焦点顺序符合逻辑
- [ ] 焦点指示清晰可见
- [ ] Escape键可关闭对话框
- [ ] Enter键可激活按钮和链接

### ✅ 高级测试
- [ ] 表单验证错误时焦点正确设置
- [ ] 对话框打开时焦点陷阱生效
- [ ] 页面切换时焦点合理设置
- [ ] 列表操作时焦点跟随
- [ ] 屏幕阅读器正确朗读

### ✅ 无障碍测试
- [ ] 支持高对比度模式
- [ ] 不依赖颜色传达信息
- [ ] 所有图片有替代文本
- [ ] 表单字段有正确标签
- [ ] 错误信息清晰明确

## 🎨 自定义配置

### 修改焦点样式
```css
/* 在 src/styles/focusStyles.css 中修改 */
.focus-ring {
  outline: 2px solid #your-color;
  outline-offset: 2px;
}
```

### 添加自定义快捷键
```typescript
// 在组件中添加
useEffect(() => {
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.ctrlKey && e.key === 'k') {
      // 自定义快捷键逻辑
      e.preventDefault();
      openSearchDialog();
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
}, []);
```

### 禁用焦点管理
```html
<!-- 在元素上添加属性 -->
<div data-focus-ignore="true">
  <!-- 此元素将被焦点管理忽略 -->
</div>
```

## 🆘 故障排除

### 常见问题快速修复

**焦点不可见**
```css
/* 确保焦点样式正确 */
*:focus { outline: 2px solid blue; }
```

**Tab键无效**
```html
<!-- 添加tabindex使元素可聚焦 -->
<div tabindex="0">可聚焦的div</div>
```

**对话框焦点逃逸**
```typescript
// 使用DialogBase组件
<DialogBase isOpen={true} onClose={handleClose}>
  {/* 对话框内容 */}
</DialogBase>
```

**屏幕阅读器无法识别**
```html
<!-- 添加ARIA标签 -->
<button aria-label="关闭对话框">×</button>
<div role="dialog" aria-labelledby="dialog-title">
  <h2 id="dialog-title">对话框标题</h2>
</div>
```

## 📞 获取帮助

### 在线资源
- [WCAG 2.1 指南](https://www.w3.org/WAI/WCAG21/quickref/)
- [MDN 无障碍文档](https://developer.mozilla.org/en-US/docs/Web/Accessibility)
- [WebAIM 屏幕阅读器测试](https://webaim.org/articles/screenreader_testing/)

### 工具推荐
- **axe DevTools**：无障碍检测浏览器扩展
- **WAVE**：Web无障碍评估工具
- **Lighthouse**：Google性能和无障碍审计工具
- **Color Contrast Analyzer**：颜色对比度检测工具

---

**打印版本**：适合打印并贴在显示器旁作为快速参考  
**最后更新**：2025-01-09
