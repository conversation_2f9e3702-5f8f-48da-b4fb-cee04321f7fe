# 部门图标更新

## 修改内容

已成功更新部门分类树中的部门节点图标，使用更符合部门概念的图标。

### 修改前：
- **根节点（全部部门）**：`Building2` 图标（公司建筑）
- **部门节点**：`Building` 图标（建筑物）
- **人员节点**：`User` 图标（保持不变）

### 修改后：
- **根节点（全部部门）**：`Building2` 图标（保持原样）
- **部门节点**：`MapPin` 图标（位置标记，更符合部门概念）
- **人员节点**：`User` 图标（保持不变）

## 设计理念

1. **Building2 图标用于根节点**：
   - 保持原有的公司/组织概念
   - 作为整个部门树的根节点标识

2. **MapPin 图标用于部门节点**：
   - 表示部门的位置和定位概念
   - 避免了建筑物图标给人的"公司"印象
   - 更好地体现了部门作为组织内部单位的特性

3. **User 图标保持不变**：
   - 人员节点继续使用用户图标
   - 保持清晰的层级区分

## 技术实现

### 修改的文件
`src/pages/Inventory/InventoryManagement/CategoryTree.tsx`

### 导入的新图标
```typescript
import {
  // ... 其他图标
  MapPin
} from 'lucide-react';
```

### 更新的函数
```typescript
const getDepartmentIcon = (category: DeviceCategory | DepartmentCategory) => {
  // 根节点 - 全部部门
  if (category.id === 'all-dept') {
    return <Building2 className="w-3.5 h-3.5 mr-0.5 text-blue-600" />;
  }

  // 判断是部门还是人员
  if (category.id.startsWith('person-') || category.id.startsWith('resp-')) {
    // 所有人员节点统一使用同一个图标
    return <User className="w-3.5 h-3.5 mr-0.5 text-purple-600" />;
  } else if (category.id.startsWith('dept-')) {
    // 所有部门节点使用位置图标，更符合部门概念
    return <MapPin className="w-3.5 h-3.5 mr-0.5 text-blue-600" />;
  }

  // 其他未知类型的节点使用默认图标
  return <Folder className="w-3.5 h-3.5 mr-0.5 text-gray-600" />;
};
```

## 用户体验改进

1. **更直观的视觉识别**：MapPin 图标让用户更容易理解这是一个部门/位置概念
2. **避免混淆**：不再使用建筑物图标，避免与公司概念混淆
3. **保持一致性**：所有部门节点使用统一的图标风格

## 影响范围

此修改仅影响部门分类树的视觉显示，不影响：
- 数据结构
- 业务逻辑
- API调用
- 其他功能模块

## 测试验证

1. 代码已通过编译测试
2. 图标更新符合用户需求
3. 视觉效果更符合部门概念

修改已完成并通过编译测试。
