/**
 * 任务工具函数
 * 提供与任务ID解析、格式化等相关的通用工具函数
 */

import { formatDuration as formatDurationUtil } from './formatUtils';

/**
 * 任务类型信息
 */
export interface TaskTypeInfo {
  dllName: string;
  funcName: string;
  taskType: string;
  uniqueId?: string;
}

/**
 * 从任务ID中提取任务类型信息
 * 任务ID格式通常为: dllName_funcName_uniqueId
 * 
 * @param taskId 任务ID
 * @returns 任务类型信息对象，包含dllName, funcName和taskType
 */
export function parseTaskId(taskId: string): TaskTypeInfo {
  const parts = taskId.split('_');
  
  if (parts.length >= 3) {
    const dllName = parts[0];
    const funcName = parts[1];
    const taskType = `${dllName}_${funcName}`;
    const uniqueId = parts.slice(2).join('_');
    
    return {
      dllName,
      funcName,
      taskType,
      uniqueId
    };
  }
  
  if (parts.length === 2) {
    const dllName = parts[0];
    const funcName = parts[1];
    const taskType = `${dllName}_${funcName}`;
    
    return {
      dllName,
      funcName,
      taskType
    };
  }
  
  // 如果格式不符合预期，返回默认值
  return {
    dllName: 'unknown',
    funcName: 'unknown',
    taskType: 'unknown'
  };
}

/**
 * 生成函数标识符
 * 
 * @param dllName DLL名称
 * @param funcName 函数名称
 * @returns 函数标识符，格式为 dllName_funcName
 */
export function generateFunctionId(dllName: string, funcName: string): string {
  return `${dllName}_${funcName}`;
}

/**
 * 生成WebSocket API函数标识符
 * 
 * @param dllName DLL名称
 * @param funcName 函数名称
 * @returns WebSocket API函数标识符，格式为 dllName::funcName
 */
export function generateWebSocketFunctionId(dllName: string, funcName: string): string {
  return `${dllName}::${funcName}`;
}

/**
 * 格式化任务类型显示
 * 将任务类型转换为更友好的显示格式
 * 
 * @param type 任务类型
 * @returns 格式化后的任务类型显示文本
 */
export function formatTaskType(type: string): string {
  if (!type || type === 'unknown') {
    return '未知任务';
  }
  
  // 分割任务类型（通常是 dllName_funcName 格式）
  const parts = type.split('_');
  if (parts.length >= 2) {
    // 尝试转换为更友好的显示格式
    const dllName = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    const funcName = parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
    
    // 特殊处理某些已知任务类型
    if (dllName === 'Ntfstool') {
      if (funcName === 'ScanDeletedFilesStr') return '文件恢复扫描';
      if (funcName === 'RecoverFileStr') return '文件恢复';
      if (funcName === 'GetDiskListStr') return '获取磁盘列表';
      if (funcName === 'GetVolumeListStr') return '获取分区列表';
      if (funcName === 'ExportScanResultStr') return '导出扫描结果';
    }
    
    // 常规格式化
    return `${dllName}/${funcName}`;
  }
  
  return type;
}

/**
 * 将毫秒转换为可读格式
 *
 * @param ms 毫秒数
 * @returns 格式化后的时间字符串
 */
export function formatDuration(ms: number): string {
  // 使用通用的格式化工具
  return formatDurationUtil(ms);
}

/**
 * 生成缓存键
 * 
 * @param dllName DLL名称
 * @param funcName 函数名称
 * @param params 参数对象
 * @returns 缓存键
 */
export function generateCacheKey(dllName: string, funcName: string, params: any): string {
  return `${dllName}_${funcName}_${JSON.stringify(params)}`;
}

/**
 * 映射任务状态数字到状态字符串
 * 
 * @param status 状态码
 * @returns 状态字符串
 */
export function mapTaskStatus(status: number): 'waiting' | 'running' | 'completed' | 'failed' | 'cancelled' {
  switch (status) {
    case 0: return 'waiting';
    case 1: return 'running';
    case 2: return 'completed';
    case 3: return 'failed';
    case 4: return 'cancelled';
    default: return 'waiting';
  }
}
