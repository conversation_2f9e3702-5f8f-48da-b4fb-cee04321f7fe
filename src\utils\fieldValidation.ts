/**
 * 字段验证工具
 * 提供统一的字段验证功能，包括长度限制、格式验证等
 */

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * 字节长度计算
 * 中文字符按3个字节计算，英文字符按1个字节计算
 */
export function getByteLength(str: string): number {
  let byteLength = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charAt(i);
    // 中文字符范围
    if (/[\u4e00-\u9fa5]/.test(char)) {
      byteLength += 3;
    } else {
      byteLength += 1;
    }
  }
  return byteLength;
}

/**
 * 验证部门路径
 * 限制：1024字节长度 或者最多30个层级的深度
 */
export function validateDepartmentPath(path: string): ValidationResult {
  if (!path || path.trim() === '') {
    return { isValid: false, error: '部门路径不能为空' };
  }

  const trimmedPath = path.trim();

  // 检查字节长度
  const byteLength = getByteLength(trimmedPath);
  if (byteLength > 1024) {
    return {
      isValid: false,
      error: `部门路径过长，当前${byteLength}字节，最大允许1024字节`
    };
  }

  // 检查层级深度（以/分隔）
  const levels = trimmedPath.split('/').filter(level => level.trim() !== '');
  if (levels.length > 30) {
    return {
      isValid: false,
      error: `部门层级过深，当前${levels.length}级，最大允许30级`
    };
  }

  return { isValid: true };
}

/**
 * 验证联系方式
 * 限制：只能输入数字和-，最大11位
 */
export function validateMobileNumber(mobile: string): ValidationResult {
  if (!mobile || mobile.trim() === '') {
    return { isValid: true }; // 联系方式可以为空
  }

  const trimmedMobile = mobile.trim();

  // 检查格式：只能包含数字和-
  if (!/^[0-9-]+$/.test(trimmedMobile)) {
    return {
      isValid: false,
      error: '联系方式只能包含数字和连字符(-)'
    };
  }

  // 检查长度：最大11位
  if (trimmedMobile.length > 11) {
    return {
      isValid: false,
      error: `联系方式过长，当前${trimmedMobile.length}位，最大允许11位`
    };
  }

  return { isValid: true };
}

/**
 * 通用名称验证函数
 * 限制：最大108个字节
 */
function validateNameWithByteLimit(
  name: string,
  fieldType: string,
  allowEmpty: boolean = false
): ValidationResult {
  if (!name || name.trim() === '') {
    if (allowEmpty) {
      return { isValid: true };
    }
    return { isValid: false, error: `${fieldType}不能为空` };
  }

  const trimmedName = name.trim();
  const byteLength = getByteLength(trimmedName);

  if (byteLength > 108) {
    return {
      isValid: false,
      error: `${fieldType}过长，当前${byteLength}字节，最大允许108字节`
    };
  }

  return { isValid: true };
}

/**
 * 验证父类名称（一级分类）
 * 限制：最大108个字节
 */
export function validateParentCategoryName(name: string): ValidationResult {
  return validateNameWithByteLimit(name, '分类名称');
}

/**
 * 验证设备子类（二级分类）
 * 限制：最大108个字节
 */
export function validateSubCategoryName(name: string): ValidationResult {
  return validateNameWithByteLimit(name, '类型名称');
}

/**
 * 验证部门名称
 * 限制：最大108个字节
 */
export function validateDepartmentName(name: string): ValidationResult {
  return validateNameWithByteLimit(name, '部门名称');
}

/**
 * 验证人员名称
 * 限制：最大108个字节
 */
export function validatePersonName(name: string): ValidationResult {
  return validateNameWithByteLimit(name, '人员名称');
}

/**
 * 验证人员备注
 * 限制：最大108个字节（可以为空）
 */
export function validatePersonAlias(alias: string): ValidationResult {
  return validateNameWithByteLimit(alias, '人员备注', true);
}

/**
 * 通用字段验证器
 * 根据字段类型自动选择合适的验证函数
 */
export function validateField(fieldType: string, value: string): ValidationResult {
  switch (fieldType) {
    case 'departmentPath':
      return validateDepartmentPath(value);
    case 'mobileNumber':
      return validateMobileNumber(value);
    case 'parentCategoryName':
      return validateParentCategoryName(value);
    case 'subCategoryName':
      return validateSubCategoryName(value);
    case 'departmentName':
      return validateDepartmentName(value);
    case 'personName':
      return validatePersonName(value);
    case 'personAlias':
      return validatePersonAlias(value);
    default:
      return { isValid: true };
  }
}

/**
 * 根据分类ID和模式获取验证类型
 * 用于统一处理CategoryTree中的验证逻辑
 */
export function getValidationTypeByCategory(categoryId: string, categoryMode: 'device' | 'department'): string {
  if (categoryMode === 'device') {
    // 设备分类模式
    if (categoryId.startsWith('parent-') && !categoryId.includes('-', 'parent-'.length)) {
      // 一级分类（父类）
      return 'parentCategoryName';
    } else {
      // 二级分类（子类）
      return 'subCategoryName';
    }
  } else {
    // 部门分类模式
    if (categoryId.startsWith('dept-')) {
      return 'departmentName';
    } else if (categoryId.startsWith('person-')) {
      return 'personName';
    }
  }

  return 'parentCategoryName'; // 默认值
}

/**
 * 批量验证多个字段
 */
export function validateFields(fields: Array<{ type: string; value: string; label?: string }>): {
  isValid: boolean;
  errors: Record<string, string>;
} {
  const errors: Record<string, string> = {};
  let isValid = true;

  fields.forEach(({ type, value, label }) => {
    const result = validateField(type, value);
    if (!result.isValid) {
      errors[type] = result.error || `${label || type}验证失败`;
      isValid = false;
    }
  });

  return { isValid, errors };
}