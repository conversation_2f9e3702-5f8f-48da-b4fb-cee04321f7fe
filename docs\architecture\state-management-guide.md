# 状态管理最佳实践指南

## 📋 概述

本项目采用多层状态管理架构，包括全局状态、组件状态和业务状态。本文档说明各状态管理方案的使用场景和最佳实践。

## 🏗️ 状态管理架构

### 1. **CacheContext** - 全局应用状态
**位置**: `src/contexts/CacheContext.tsx`

**适用场景**:
- 🌐 跨路由的全局状态
- 🎨 用户界面偏好设置
- 📍 导航和路由状态
- 🔄 应用级的持久化状态

**使用示例**:
```typescript
import { useCacheContext } from '../contexts/CacheContext';

function GlobalNavigation() {
  const { expandedItems, setExpandedItems } = useCacheContext();
  
  // 全局导航展开状态
  const toggleGlobalItem = (itemId: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };
}
```

### 2. **useTreeState** - 组件级树状态
**位置**: `src/hooks/Inventory/useTreeState.ts`

**适用场景**:
- 🌳 具体树组件的状态管理
- 🎯 组件内部的交互状态
- ⚡ 高频更新的UI状态
- 🔧 可复用的状态逻辑

**使用示例**:
```typescript
import { useTreeState } from './useTreeState';

function DepartmentTree() {
  const treeState = useTreeState({
    defaultExpandedNodes: ['root'],
    onNodeSelect: (nodeId) => {
      console.log('选中节点:', nodeId);
    }
  });

  return (
    <TreeRenderer
      nodes={nodes}
      {...treeState}
    />
  );
}
```

### 3. **Service State** - 业务逻辑状态
**位置**: `src/services/*/`

**适用场景**:
- 💼 业务数据状态
- 🔄 数据加载状态
- 📊 业务逻辑状态
- 🎯 跨组件的业务状态

**使用示例**:
```typescript
import { DepartmentService } from '../services/Inventory/departmentService';

function DepartmentManager() {
  const departmentService = DepartmentService.getInstance();
  const [state, setState] = useState(departmentService.getState());

  useEffect(() => {
    const unsubscribe = departmentService.subscribe(setState);
    return unsubscribe;
  }, []);
}
```

## 🎯 选择指南

### **何时使用 CacheContext**

✅ **适合的场景**:
- 侧边栏展开/收起状态
- 主题设置和用户偏好
- 全局搜索状态
- 路由级别的状态持久化

❌ **不适合的场景**:
- 频繁变化的UI状态
- 组件内部的临时状态
- 大量数据的存储
- 业务逻辑相关的状态

### **何时使用 useTreeState**

✅ **适合的场景**:
- 树形组件的展开/收起
- 节点选择状态
- 拖拽状态管理
- 组件内部的交互状态

❌ **不适合的场景**:
- 跨组件的状态共享
- 需要持久化的状态
- 业务数据状态
- 全局配置状态

### **何时使用 Service State**

✅ **适合的场景**:
- 业务数据管理
- API调用状态
- 数据缓存管理
- 跨组件的业务逻辑

❌ **不适合的场景**:
- 纯UI交互状态
- 临时的组件状态
- 用户偏好设置
- 路由导航状态

## 🔄 状态同步策略

### **避免状态冲突**

❌ **错误示例 - 状态冲突**:
```typescript
function ConflictExample() {
  // 同时使用两种状态管理方式管理相同的数据
  const { expandedItems } = useCacheContext(); // 全局状态
  const treeState = useTreeState(); // 组件状态
  
  // 这会导致状态不一致！
}
```

✅ **正确示例 - 明确职责**:
```typescript
function CorrectExample() {
  // 全局导航使用 CacheContext
  const { globalExpandedItems } = useCacheContext();
  
  // 具体树组件使用 useTreeState
  const treeState = useTreeState({
    // 可以从全局状态初始化
    defaultExpandedNodes: globalExpandedItems.treeNodes || []
  });
}
```

### **状态层级原则**

```
🌐 CacheContext (全局应用状态)
    ↓
💼 Service State (业务逻辑状态)
    ↓
🎯 useTreeState (组件交互状态)
    ↓
🔧 useState (局部临时状态)
```

## 📊 性能优化

### **1. 避免不必要的重渲染**

```typescript
// 使用 useMemo 优化计算
const treeState = useTreeState({
  defaultExpandedNodes: useMemo(() => 
    computeDefaultNodes(data), [data]
  )
});

// 使用 useCallback 优化回调
const handleNodeSelect = useCallback((nodeId: string) => {
  onNodeSelect?.(nodeId);
}, [onNodeSelect]);
```

### **2. 状态更新批处理**

```typescript
// 批量更新状态
const updateMultipleStates = useCallback(() => {
  // 使用 React 18 的自动批处理
  setExpandedNodes(newExpandedNodes);
  setSelectedNode(newSelectedNode);
  setLoadingState(false);
}, []);
```

### **3. 条件性状态订阅**

```typescript
// 只在需要时订阅状态变化
useEffect(() => {
  if (isActive) {
    const unsubscribe = departmentService.subscribe(setState);
    return unsubscribe;
  }
}, [isActive]);
```

## 🧪 测试策略

### **1. 状态管理测试**

```typescript
// 测试 useTreeState Hook
import { renderHook, act } from '@testing-library/react';
import { useTreeState } from './useTreeState';

test('should toggle node expansion', () => {
  const { result } = renderHook(() => useTreeState());
  
  act(() => {
    result.current.toggleNode('node1');
  });
  
  expect(result.current.isNodeExpanded('node1')).toBe(true);
});
```

### **2. 状态同步测试**

```typescript
// 测试状态同步
test('should sync state between components', () => {
  // 测试全局状态和组件状态的同步
});
```

## 🚨 常见问题和解决方案

### **1. 状态不一致**

**问题**: 不同状态管理系统中的相同数据不同步

**解决方案**:
```typescript
// 建立明确的数据流向
const syncStates = useCallback(() => {
  // 从权威数据源同步到其他状态
  const authoritative = departmentService.getState();
  setLocalState(authoritative.someData);
}, []);
```

### **2. 内存泄漏**

**问题**: 组件卸载后状态订阅未清理

**解决方案**:
```typescript
useEffect(() => {
  const unsubscribe = service.subscribe(callback);
  
  // 确保清理订阅
  return () => {
    unsubscribe();
  };
}, []);
```

### **3. 状态更新丢失**

**问题**: 异步状态更新被覆盖

**解决方案**:
```typescript
// 使用函数式更新
setExpandedNodes(prev => {
  const newSet = new Set(prev);
  newSet.add(nodeId);
  return newSet;
});
```

## 🔧 调试工具

### **1. 状态日志**

```typescript
// 添加状态变化日志
useEffect(() => {
  console.log('Tree state changed:', {
    expandedNodes: expandedNodes.size,
    selectedNode
  });
}, [expandedNodes, selectedNode]);
```

### **2. 状态快照**

```typescript
// 导出状态快照用于调试
const getStateSnapshot = () => ({
  global: cacheContext.getSnapshot(),
  tree: treeState.getSnapshot(),
  service: departmentService.getState()
});
```

## 📝 最佳实践总结

### **DO ✅**
- 明确各状态管理系统的职责边界
- 使用合适的状态管理方案
- 及时清理状态订阅
- 使用 TypeScript 确保类型安全
- 编写状态管理相关的测试

### **DON'T ❌**
- 在同一功能中混用多种状态管理
- 在全局状态中存储临时数据
- 忘记清理状态订阅
- 过度使用全局状态
- 忽略状态更新的性能影响

## 🚀 未来改进方向

1. **状态管理工具升级**: 考虑引入 Zustand 或 Valtio
2. **状态持久化**: 实现更完善的状态持久化机制
3. **状态调试**: 开发专用的状态调试工具
4. **性能监控**: 添加状态更新的性能监控

遵循本指南可以构建清晰、高效、可维护的状态管理架构。
