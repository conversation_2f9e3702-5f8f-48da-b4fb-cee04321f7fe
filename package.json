{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-table": "^8.13.2", "@types/pako": "^2.0.3", "@types/pdfjs-dist": "^2.10.377", "@types/react-highlight-words": "^0.20.0", "@types/react-window": "^1.8.8", "antd": "^5.24.0", "docx": "^9.4.1", "exceljs": "^4.4.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jspdf-font": "^1.0.7", "lucide-react": "^0.344.0", "mitt": "^3.0.1", "pako": "^2.1.0", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.3.1", "react-highlight-words": "^0.21.0", "react-router-dom": "^6.22.3", "react-window": "^1.8.11", "recharts": "^2.15.1", "tableexport": "^5.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.13.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}