/* 自定义滚动条样式 */

/* 表格样式系统 - CSS变量定义 */
:root {
  --table-header-bg: #f9fafb;
  --table-header-text: #4b5563;
  --table-cell-text: #6b7280;
  --table-border: #e5e7eb;
  --table-border-light: #f3f4f6;
  --table-hover-bg: #f9fafb;
  --table-min-width: 800px;
  --table-header-height: 48px;
  --table-row-height: 48px;
}

/* 隐藏原生滚动条但保留滚动功能 */
.custom-scrollbar-container {
  position: relative;
  overflow: hidden !important;
}

/* 内容容器 */
.custom-scrollbar-content {
  overflow: auto;
  height: 100%;
  width: 100%;
  /* 隐藏原生滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏Webkit浏览器的原生滚动条 */
.custom-scrollbar-content::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
}

/* 统一的滚动条隐藏样式 - 合并重复定义 */
.custom-scrollbar-content {
  -ms-overflow-style: none !important; /* IE and Edge */
  scrollbar-width: none !important; /* Firefox */
}

/* 垂直滚动条轨道 */
.custom-scrollbar-track-vertical {
  position: absolute;
  right: 8px; /* 调整位置，避免与拖拽手柄冲突 */
  top: 0;
  width: 4px; /* 进一步减小宽度 */
  height: 100%;
  background-color: transparent; /* 设置为透明，避免白色框 */
  border-radius: 2px;
  opacity: 0.6;
  transition: opacity 0.2s;
  z-index: 5; /* 降低z-index，确保不会遮挡拖拽手柄 */
}

/* 水平滚动条轨道 */
.custom-scrollbar-track-horizontal {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 8px;
  width: 100%;
  background-color: #f1f1f1;
  border-radius: 4px;
  opacity: 0.6;
  transition: opacity 0.2s;
}

/* 滚动条轨道悬停状态 */
.custom-scrollbar-track-vertical:hover,
.custom-scrollbar-track-horizontal:hover {
  opacity: 0.8;
}

/* 垂直滚动条滑块 */
.custom-scrollbar-thumb-vertical {
  position: absolute;
  width: 4px; /* 与轨道宽度保持一致 */
  background-color: #c1c1c1;
  border-radius: 2px;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* 水平滚动条滑块 */
.custom-scrollbar-thumb-horizontal {
  position: absolute;
  height: 8px;
  background-color: #c1c1c1;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* 滚动条滑块悬停状态 */
.custom-scrollbar-thumb-vertical:hover,
.custom-scrollbar-thumb-horizontal:hover {
  background-color: #a8a8a8;
}

/* 滚动条滑块激活状态 */
.custom-scrollbar-thumb-vertical.active,
.custom-scrollbar-thumb-horizontal.active {
  background-color: #909090;
}

/* 滚动条在拖动时的样式 */
.custom-scrollbar-dragging {
  cursor: grabbing !important;
}

/* 滚动条容器悬停时显示滚动条 */
.custom-scrollbar-container:hover .custom-scrollbar-track-vertical,
.custom-scrollbar-container:hover .custom-scrollbar-track-horizontal {
  opacity: 0.8;
}

/* 滚动条在不活动时的样式 */
.custom-scrollbar-container:not(:hover) .custom-scrollbar-track-vertical,
.custom-scrollbar-container:not(:hover) .custom-scrollbar-track-horizontal {
  opacity: 0.4;
}

/* 适配表格特定样式 */
.table-custom-scrollbar .custom-scrollbar-track-horizontal {
  bottom: 0;
  left: 0;
  right: 8px; /* 为垂直滚动条留出空间 */
  width: calc(100% - 8px);
}

.table-custom-scrollbar .custom-scrollbar-track-vertical {
  top: 0;
  right: 0;
  bottom: 8px; /* 为水平滚动条留出空间 */
  height: calc(100% - 8px);
}

/* 表格区域滚动条隐藏 - 优化后的统一样式 */
.table-custom-scrollbar,
.table-custom-scrollbar * {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

.table-custom-scrollbar *::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* 自适应表格容器样式 */
.adaptive-table-container {
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  position: relative; /* 为悬浮框提供定位上下文 */
}

.adaptive-table-content {
  flex: 1;
  min-height: 200px;
  max-height: 100%;
  overflow: visible; /* 允许悬浮框显示 */
  position: relative; /* 确保悬浮框能正确定位 */
}

/* 表格自适应高度和宽度 */
.adaptive-table {
  height: auto;
  min-height: fit-content;
  min-width: var(--table-min-width); /* 统一最小宽度 */
}

/* 确保表格在小数据量时不占满整个容器 */
.adaptive-table tbody {
  height: auto;
}

/* 空状态时的表格样式 */
.adaptive-table-empty {
  /* 不要设置 display: flex，保持表格行的正常布局 */
}

/* 空状态时的表格单元格样式 */
.adaptive-table-empty td {
  min-height: 300px;
  vertical-align: middle;
}

/* 响应式表格优化 - 统一断点和尺寸 */
@media (max-width: 1024px) {
  :root {
    --table-min-width: 1000px;
  }
}

@media (max-width: 768px) {
  :root {
    --table-min-width: 800px;
  }

  .adaptive-table-content {
    min-height: 150px;
  }
}

/* 确保表格容器在各种情况下都能正确显示 */
.adaptive-table-container {
  position: relative;
  overflow: hidden;
}

.adaptive-table-container .custom-scrollbar-container {
  height: 100%;
  min-height: 200px;
}

/* 确保表格在数据较少时不会过度拉伸 */
.adaptive-table tbody {
  height: auto;
  min-height: fit-content;
}

/* 统一表格样式系统 - 使用CSS变量 */
.table-header-cell {
  padding: 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--table-header-text);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: var(--table-header-bg);
  border-bottom: 1px solid var(--table-border);
  min-height: var(--table-header-height);
}

.table-cell {
  padding: 0.75rem;
  font-size: 0.875rem;
  color: var(--table-cell-text);
  min-height: var(--table-row-height);
}

.table-cell-left {
  text-align: left;
}

.table-cell-right {
  text-align: right;
}

.table-cell-center {
  text-align: center;
}

/* 统一表格头部样式 - 使用CSS变量 */
.adaptive-table thead {
  position: sticky;
  top: 0;
  z-index: 30;
  background-color: var(--table-header-bg);
}

/* 表头单元格样式 - 统一边框实现方式 */
.adaptive-table th {
  position: relative;
  background-color: var(--table-header-bg);
  border-bottom: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border-light);
}

.adaptive-table th:last-child {
  border-right: none;
}

/* 表格数据行样式 - 使用CSS变量 */
.adaptive-table tbody tr {
  position: relative;
  height: auto;
  min-height: var(--table-row-height);
  border-bottom: 1px solid var(--table-border-light);
  transition: background-color 0.15s ease-in-out;
}

.adaptive-table tbody tr:hover {
  background-color: var(--table-hover-bg);
}

/* 表格数据单元格样式 - 统一边框实现 */
.adaptive-table td {
  position: relative;
  overflow: visible;
  border-right: 1px solid var(--table-border-light);
}

.adaptive-table td:last-child {
  border-right: none;
}

/* 确保表格单元格内的悬浮框能正确显示 */
.adaptive-table td .group {
  position: relative;
}

.adaptive-table td .group .absolute {
  position: absolute;
  z-index: 9999;
}

/* 确保悬停框能够正确显示在表格上方 */
.adaptive-table .group:hover .absolute {
  z-index: 9999 !important;
}

/* 防止表格容器裁剪悬停框 - 已在上面统一设置 */

.custom-scrollbar-content {
  overflow: auto;
  position: relative;
}

/* 特殊处理表格的滚动容器，允许悬浮框溢出 */
.adaptive-table-container .custom-scrollbar-container {
  overflow: visible; /* 允许悬浮框显示 */
}

.adaptive-table-container .custom-scrollbar-content {
  overflow: auto; /* 保持滚动功能 */
}

/* 悬停框的额外样式 */
.department-tooltip {
  position: absolute;
  z-index: 1000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 12px;
  min-width: 200px;
  max-width: 320px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
  pointer-events: none;
}

.group:hover .department-tooltip {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}
