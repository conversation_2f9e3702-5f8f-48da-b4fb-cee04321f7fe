import mitt from 'mitt';
import { BaseService, BaseServiceState, BaseServiceEvents } from '../base/baseService';
import { formatInspectionDateTime } from '../../utils/formatUtils';

/**
 * 巡检任务日志项接口 - 直接匹配后端返回的数据结构
 */
export interface InspectionTaskLogItem {
  id: number;
  log_type: number;
  log_type_text: string;
  new_data: any;
  original_data: any;
  target_id: number;
  target_name: string;
  timestamp: number;

  // 前端添加的格式化字段
  operation_time?: string;
}

/**
 * 巡检任务日志过滤条件接口
 */
export interface InspectionTaskLogFilter {
  startDate?: string;
  endDate?: string;
  logType?: number;
  taskName?: string;
  personName?: string;
  searchText?: string;
}

/**
 * 巡检任务日志服务状态接口
 */
export interface InspectionTaskLogServiceState extends BaseServiceState {
  logs: InspectionTaskLogItem[];
  filter: InspectionTaskLogFilter;
  filteredLogs: InspectionTaskLogItem[];
  isLoading: boolean;
}

/**
 * 巡检任务日志服务事件接口
 */
export interface InspectionTaskLogServiceEvents extends BaseServiceEvents<InspectionTaskLogServiceState> {
  'logs-loaded': InspectionTaskLogItem[];
  'filter-changed': InspectionTaskLogFilter;
}

/**
 * 巡检任务日志服务类
 * 提供巡检任务日志的查询和过滤功能
 */
class InspectionTaskLogService extends BaseService<InspectionTaskLogServiceState, InspectionTaskLogServiceEvents> {
  private static instance: InspectionTaskLogService;

  // 防重复调用机制
  private isLoadingLogs = false;
  private lastLoadTime = 0;
  private readonly CACHE_DURATION = 30000; // 30秒缓存

  /**
   * 获取巡检任务日志服务实例
   * @returns 巡检任务日志服务实例
   */
  public static getInstance(): InspectionTaskLogService {
    if (!InspectionTaskLogService.instance) {
      InspectionTaskLogService.instance = new InspectionTaskLogService();
    }
    return InspectionTaskLogService.instance;
  }

  /**
   * 构造函数
   * 初始化巡检任务日志服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      logs: [],
      filter: {},
      filteredLogs: []
    });
  }

  /**
   * 获取巡检任务日志数据
   * @param logType 日志类型，默认14获取巡检任务日志。具体类型：10=创建任务，11=修改任务，12=删除任务
   */
  public async getInspectionTaskLogs(logType?: number): Promise<InspectionTaskLogItem[]> {
    try {
      await this.ensureInitialized();

      // 防重复调用检查
      const now = Date.now();
      if (this.isLoadingLogs) {
        return this.state.logs;
      }

      // 缓存检查（只有在已有数据且在缓存时间内才使用缓存）
      if (this.state.logs.length > 0 &&
          this.lastLoadTime > 0 &&
          (now - this.lastLoadTime) < this.CACHE_DURATION) {
        return this.state.logs;
      }

      this.isLoadingLogs = true;
      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 调用API获取日志数据
      const logs = await this.fetchInspectionTaskLogs(logType);
      this.lastLoadTime = now;

      this.updateState({
        logs,
        filteredLogs: this.applyFilter(logs, this.state.filter),
        isLoading: false
      });

      this.emitter.emit('logs-loaded', logs);
      return logs;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取巡检任务日志失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    } finally {
      this.isLoadingLogs = false;
    }
  }

  /**
   * 强制刷新巡检任务日志数据（忽略缓存）
   * @param logType 日志类型，默认14获取巡检任务日志。具体类型：10=创建任务，11=修改任务，12=删除任务
   */
  public async forceRefreshInspectionTaskLogs(logType?: number): Promise<InspectionTaskLogItem[]> {
    // 重置缓存时间，强制重新获取
    this.lastLoadTime = 0;
    return await this.getInspectionTaskLogs(logType);
  }

  /**
   * 调用获取巡检任务日志API
   */
  private async fetchInspectionTaskLogs(logType?: number): Promise<InspectionTaskLogItem[]> {
    try {
      // 构建API参数 - 按照您提供的正确格式
      const apiParams = {
        action: 'query_logs',
        action_params: {
          log_table: 'inspection',
          log_type: logType !== undefined ? logType : 14 // 默认使用14获取巡检任务日志
        }
      };

      console.log('调用获取巡检任务日志API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams) as any;

      console.log('获取巡检任务日志API原始返回结果:', rawResult);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
          console.log('解析JSON字符串后的结果:', result);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      console.log('最终处理的结果:', result);

      // 检查API返回状态
      if (result && result.success === true) {
        if (result.data && result.data.logs && Array.isArray(result.data.logs)) {
          // 直接使用后端返回的数据，只添加格式化的时间字段
          const logs = result.data.logs.map((log: any) => ({
            ...log,
            operation_time: formatInspectionDateTime(log.timestamp || 0)
          }));
          console.log('处理后的日志数据:', logs);
          return logs;
        } else {
          console.warn('API返回成功但数据格式不正确:', result);
          return [];
        }
      } else {
        const errorMsg = result?.message || result?.error || '获取巡检任务日志失败';
        console.error('API返回错误:', errorMsg, '完整响应:', result);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('获取巡检任务日志API调用失败:', error);
      throw error;
    }
  }





  /**
   * 设置过滤条件
   */
  public setFilter(filter: Partial<InspectionTaskLogFilter>): void {
    const newFilter = { ...this.state.filter, ...filter };
    this.updateState({
      filter: newFilter,
      filteredLogs: this.applyFilter(this.state.logs, newFilter)
    });
    this.emitter.emit('filter-changed', newFilter);
  }

  /**
   * 清除所有过滤条件
   */
  public clearFilter(): void {
    this.updateState({
      filter: {},
      filteredLogs: this.state.logs
    });
    this.emitter.emit('filter-changed', {});
  }

  /**
   * 应用过滤条件 - 直接基于后端数据结构
   */
  private applyFilter(logs: InspectionTaskLogItem[], filter: InspectionTaskLogFilter): InspectionTaskLogItem[] {
    let filtered = [...logs];

    // 日期范围过滤
    if (filter.startDate) {
      const startTimestamp = new Date(filter.startDate).getTime() / 1000;
      filtered = filtered.filter(log => log.timestamp >= startTimestamp);
    }

    if (filter.endDate) {
      const endTimestamp = new Date(filter.endDate).getTime() / 1000;
      filtered = filtered.filter(log => log.timestamp <= endTimestamp);
    }

    // 日志类型过滤
    if (filter.logType !== undefined) {
      filtered = filtered.filter(log => log.log_type === filter.logType);
    }

    // 任务名称过滤
    if (filter.taskName) {
      filtered = filtered.filter(log => {
        const data = log.log_type === 12 ? log.original_data : log.new_data;
        const taskName = data?.task_name || '';
        return taskName.toLowerCase().includes(filter.taskName!.toLowerCase());
      });
    }

    // 责任人过滤
    if (filter.personName) {
      filtered = filtered.filter(log => {
        const data = log.log_type === 12 ? log.original_data : log.new_data;
        const personName = data?.person_name || '';
        const personAlias = data?.person_alias || '';
        return personName.toLowerCase().includes(filter.personName!.toLowerCase()) ||
               personAlias.toLowerCase().includes(filter.personName!.toLowerCase());
      });
    }

    // 搜索文本过滤
    if (filter.searchText) {
      const searchText = filter.searchText.toLowerCase();
      filtered = filtered.filter(log => {
        const data = log.log_type === 12 ? log.original_data : log.new_data;
        const taskName = data?.task_name || '';
        const taskDesc = data?.task_description || '';
        const personName = data?.person_name || '';
        const personAlias = data?.person_alias || '';

        return taskName.toLowerCase().includes(searchText) ||
               taskDesc.toLowerCase().includes(searchText) ||
               personName.toLowerCase().includes(searchText) ||
               personAlias.toLowerCase().includes(searchText) ||
               log.log_type_text.toLowerCase().includes(searchText);
      });
    }

    return filtered;
  }

  /**
   * 获取所有日志类型
   */
  public getLogTypes(): string[] {
    const types = new Set<string>();
    this.state.logs.forEach(log => {
      if (log.log_type_text) {
        types.add(log.log_type_text);
      }
    });
    return Array.from(types).sort();
  }

  /**
   * 获取所有责任人
   */
  public getPersons(): string[] {
    const persons = new Set<string>();
    this.state.logs.forEach(log => {
      const data = log.log_type === 12 ? log.original_data : log.new_data;
      if (data?.person_name) {
        persons.add(data.person_name);
      }
    });
    return Array.from(persons).sort();
  }

  /**
   * 获取所有任务名称
   */
  public getTaskNames(): string[] {
    const taskNames = new Set<string>();
    this.state.logs.forEach(log => {
      const data = log.log_type === 12 ? log.original_data : log.new_data;
      if (data?.task_name) {
        taskNames.add(data.task_name);
      }
    });
    return Array.from(taskNames).sort();
  }
}

export default InspectionTaskLogService;
