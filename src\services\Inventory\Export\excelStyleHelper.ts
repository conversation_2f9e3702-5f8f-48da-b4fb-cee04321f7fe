import ExcelJS from 'exceljs';
import { getStringWidth } from '../../../utils/formatUtils';

/**
 * Excel样式助手类
 * 提供Excel文件样式设置相关的方法
 */
export class ExcelStyleHelper {
  /**
   * 设置工作表基本属性
   * @param worksheet 工作表
   */
  public static setWorksheetProperties(worksheet: ExcelJS.Worksheet): void {
    // 设置工作表属性
    worksheet.properties.tabColor = { argb: '4472C4' }; // 蓝色标签
    worksheet.views = [{ state: 'frozen', ySplit: 2 }]; // 冻结前两行（标题行和表头行）
  }

  /**
   * 设置单元格样式
   * @param cell 单元格
   * @param isHeader 是否为表头
   * @param rowIndex 行索引
   * @param colIndex 列索引
   */
  public static setCellStyle(cell: ExcelJS.Cell, isHeader: boolean, rowIndex: number, colIndex: number): void {
    if (isHeader) {
      // 优化后的表头样式
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '2E5BBA' } // 更深的专业蓝色背景
      };
      cell.font = {
        name: '微软雅黑',
        size: 11, // 恢复原来的字体大小
        bold: true,
        color: { argb: 'FFFFFF' } // 白色文字
      };
      cell.alignment = {
        vertical: 'middle',
        horizontal: 'center',
        wrapText: false // 表头不换行，保持列宽自适应
      };
    } else {
      // 优化后的数据行样式
      const fillColor = rowIndex % 2 === 0 ? 'F8F9FA' : 'FFFFFF'; // 偶数行使用更淡的灰色背景
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: fillColor }
      };
      cell.font = {
        name: '微软雅黑',
        size: 10,
        color: { argb: '333333' } // 深灰色文字，提高可读性
      };
      cell.alignment = {
        vertical: 'middle',
        horizontal: colIndex === 1 ? 'center' : 'left', // 第一列居中，其他列左对齐
        wrapText: false // 数据行不自动换行，保持列宽自适应
      };
    }

    // 设置边框
    if (isHeader) {
      // 表头使用更粗的边框
      cell.border = {
        top: { style: 'medium', color: { argb: '1F4788' } }, // 深蓝色边框
        left: { style: 'thin', color: { argb: 'FFFFFF' } }, // 白色分隔线
        bottom: { style: 'medium', color: { argb: '1F4788' } },
        right: { style: 'thin', color: { argb: 'FFFFFF' } }
      };
    } else {
      // 数据行使用细边框
      cell.border = {
        top: { style: 'thin', color: { argb: 'D0D0D0' } }, // 浅灰色边框
        left: { style: 'thin', color: { argb: 'D0D0D0' } },
        bottom: { style: 'thin', color: { argb: 'D0D0D0' } },
        right: { style: 'thin', color: { argb: 'D0D0D0' } }
      };
    }
  }

  /**
   * 确保所有单元格都存在，包括空单元格
   * @param worksheet 工作表
   * @param rowCount 行数
   * @param columnCount 列数
   */
  public static ensureAllCellsExist(worksheet: ExcelJS.Worksheet, rowCount: number, columnCount: number): void {
    // 遍历所有行和列，确保每个单元格都存在并设置样式
    // 注意：只处理实际数据行，不添加额外的空白行
    for (let i = 1; i <= rowCount; i++) {
      const row = worksheet.getRow(i);
      // 第1行是标题行，第2行是表头，第3行开始是数据行
      const isTitle = i === 1;
      const isHeader = i === 2;

      for (let j = 1; j <= columnCount; j++) {
        // 获取单元格，如果不存在会自动创建
        const cell = row.getCell(j);

        // 如果单元格值为undefined，设置为空字符串（但跳过标题行，因为已经设置过了）
        if (!isTitle && (cell.value === undefined || cell.value === null)) {
          cell.value = '';
        }

        // 设置单元格样式（跳过标题行，因为已经设置过样式了）
        if (!isTitle) {
          this.setCellStyle(cell, isHeader, i, j);
        }
      }

      // 确保行有正确的高度（标题行25，表头行30，数据行22）
      if (!isTitle) { // 标题行高度已经在insertTitleRow中设置
        row.height = isHeader ? 30 : 22; // 增加表头行高度
      }

      // 提交行，确保所有更改都被应用
      row.commit();
    }

    // 确保工作表有足够的列
    for (let col = 1; col <= columnCount; col++) {
      const column = worksheet.getColumn(col);
      if (!column.width) {
        column.width = 10; // 设置默认宽度
      }

      // 确保列标题存在（现在表头在第2行）
      const headerCell = worksheet.getRow(2).getCell(col);
      if (!headerCell.value) {
        // 如果没有标题，尝试使用列索引作为标题
        headerCell.value = `列 ${col}`;
      }
    }
  }

  /**
   * 设置表格边框
   * @param worksheet 工作表
   * @param rowCount 行数
   * @param columnCount 列数
   */
  public static setTableBorders(worksheet: ExcelJS.Worksheet, rowCount: number, columnCount: number): void {
    const headerRow = worksheet.getRow(2); // 表头行（现在在第2行）
    const lastRow = worksheet.getRow(rowCount); // 使用实际的最后一行，不添加额外行

    // 设置表格外边框
    for (let col = 1; col <= columnCount; col++) {
      // 设置表头顶部边框（现在表头在第2行）
      const headerCell = headerRow.getCell(col);
      headerCell.border = {
        ...headerCell.border,
        top: { style: 'medium', color: { argb: '1F4788' } } // 使用新的深蓝色
      };

      // 设置底部边框
      const lastCell = lastRow.getCell(col);
      lastCell.border = {
        ...lastCell.border,
        bottom: { style: 'medium', color: { argb: '1F4788' } } // 使用新的深蓝色
      };

      // 设置左右边框
      if (col === 1) {
        // 第一列左边框
        headerCell.border.left = { style: 'medium', color: { argb: '1F4788' } }; // 使用新的深蓝色
        lastCell.border.left = { style: 'medium', color: { argb: '1F4788' } };

        // 所有行的第一列左边框 - 从第3行开始（跳过标题行和表头行）
        for (let row = 3; row <= rowCount; row++) {
          const cell = worksheet.getRow(row).getCell(1);
          cell.border = {
            ...cell.border,
            left: { style: 'medium', color: { argb: '1F4788' } }
          };
        }
      }

      if (col === columnCount) {
        // 最后一列右边框
        headerCell.border.right = { style: 'medium', color: { argb: '1F4788' } }; // 使用新的深蓝色
        lastCell.border.right = { style: 'medium', color: { argb: '1F4788' } };

        // 所有行的最后一列右边框 - 从第3行开始（跳过标题行和表头行）
        for (let row = 3; row <= rowCount; row++) {
          const cell = worksheet.getRow(row).getCell(columnCount);
          cell.border = {
            ...cell.border,
            right: { style: 'medium', color: { argb: '1F4788' } }
          };
        }
      }
    }
  }

  /**
   * 调整列宽以更好地适应内容
   * @param worksheet 工作表
   * @param data 数据
   */
  public static adjustColumnWidths(worksheet: ExcelJS.Worksheet, data: any[]): void {
    if (!data || !data.length) return;

    // 获取所有列名
    const columns = Object.keys(data[0]);

    // 添加调试日志
    console.log(`调整列宽 - 工作表: ${worksheet.name}, 列数: ${columns.length}`);
    console.log(`调整列宽 - 列名: ${columns.slice(0, 5).join(', ')}...`);

    // 获取工作表中的所有列（现在表头在第2行）
    const headerRow = worksheet.getRow(2);

    // 创建列名到列索引的映射
    const columnToIndex = new Map<string, number>();

    // 收集工作表中的所有列名和索引
    for (let i = 1; i <= worksheet.columnCount || 0; i++) {
      const cellValue = headerRow.getCell(i).value;
      if (cellValue) {
        const colName = String(cellValue);
        columnToIndex.set(colName, i);
        console.log(`列映射 - 名称: ${colName}, 索引: ${i}`);
      }
    }

    // 获取工作表中实际存在的列名
    const actualColumns: string[] = [];
    for (let i = 1; i <= worksheet.columnCount || 0; i++) {
      const cellValue = headerRow.getCell(i).value;
      if (cellValue) {
        actualColumns.push(String(cellValue));
      }
    }

    // 分离基础字段和扩展字段 - 只处理工作表中实际存在的列
    const baseColumns = actualColumns.filter(col => ['序号', '设备类型分类', '设备类型', '保密编号', '密级', '责任人', '名称', '型号', '所属部门', '放置地点', '启用时间', '使用情况', '用途', '安全码',
                                            'id', 'parentCategory', 'type', 'securityCode', 'securityLevel', 'responsible', 'name', 'model', 'department', 'location', 'startTime', 'status', 'purpose', 'securityRfid'].includes(col));

    const extColumns = actualColumns.filter(col => !baseColumns.includes(col));

    console.log(`基础字段数量: ${baseColumns.length}, 扩展字段数量: ${extColumns.length}`);
    console.log(`基础字段: ${baseColumns.join(', ')}`);
    console.log(`扩展字段: ${extColumns.join(', ')}`);

    // 先处理基础字段的列宽
    baseColumns.forEach(col => {
      // 获取列在工作表中的索引
      const colIndex = columnToIndex.get(col);
      if (!colIndex) {
        console.log(`未找到列 ${col} 的索引`);
        return;
      }

      // 计算列宽 - 基于内容
      let maxLength = this.getStringWidth(col) * 1.3; // 列名长度，乘以1.3给予更多空间

      // 遍历所有行，找出最长的内容
      data.forEach(row => {
        const cellValue = row[col];
        if (cellValue !== null && cellValue !== undefined) {
          const valueStr = String(cellValue);
          const adjustedLength = this.getStringWidth(valueStr);
          // 乘以1.3给予更多空间，确保内容完全可见
          maxLength = Math.max(maxLength, adjustedLength * 1.3);
        }
      });

      // 根据内容类型调整列宽
      let width = maxLength;

      // 根据列名特征调整基础字段的列宽
      if (col === '序号' || col === 'id' || col === 'ID') {
        // ID列通常较短，但确保能显示完整内容
        width = Math.min(Math.max(width, 8), 12);
      } else if (col === '设备类型分类' || col === 'parentCategory') {
        // 分类列可能较长，给予更多空间
        width = Math.min(Math.max(width, 15), 25);
      } else if (col === '设备类型' || col === 'type') {
        // 类型列通常中等长度
        width = Math.min(Math.max(width, 12), 22);
      } else if (col === '保密编号' || col === 'securityCode' || col.includes('编号')) {
        // 编号列通常有固定格式，但允许更长的编号
        width = Math.min(Math.max(width, 12), 18);
      } else if (col === '密级' || col === 'securityLevel' || col === '使用情况' || col === 'status') {
        // 状态类列通常较短
        width = Math.min(Math.max(width, 8), 12);
      } else if (col === '责任人' || col === 'responsible') {
        // 人名列通常中等长度，考虑较长的姓名
        width = Math.min(Math.max(width, 10), 18);
      } else if (col === '名称' || col === 'name' || col === '设备名称') {
        // 名称列可能较长，给予更多空间
        width = Math.min(Math.max(width, 15), 30);
      } else if (col === '型号' || col === 'model') {
        // 型号列可能包含特殊字符，给予更多空间
        width = Math.min(Math.max(width, 12), 25);
      } else if (col === '所属部门' || col === 'department') {
        // 部门列可能较长，给予更多空间显示完整路径
        width = Math.min(Math.max(width, 15), 35);
      } else if (col === '放置地点' || col === 'location') {
        // 地点列可能较长
        width = Math.min(Math.max(width, 15), 25);
      } else if (col === '启用时间' || col === 'startTime' || col.includes('日期') || col.includes('时间')) {
        // 日期时间列，确保能显示完整的日期格式 YYYY-MM-DD
        width = Math.max(width, 14);
      } else if (col === '用途' || col === 'purpose') {
        // 用途列可能较长，给予更多空间
        width = Math.min(Math.max(width, 15), 30);
      } else if (col === '安全码' || col === 'securityRfid') {
        // 安全码列通常有固定长度
        width = Math.min(Math.max(width, 12), 18);
      } else if (col.includes('IP') || col.includes('MAC')) {
        // 网络地址列通常有固定格式
        width = Math.min(Math.max(width, 15), 20);
      }

      // 限制列宽在合理范围内，给予更大的最大宽度以适应长内容
      width = Math.min(Math.max(width, 8), 60);

      // 设置列宽
      const col_obj = worksheet.getColumn(colIndex);
      col_obj.width = width;

      console.log(`设置基础字段列宽 - 列: ${col}, 索引: ${colIndex}, 宽度: ${width}`);
    });

    // 然后处理扩展字段的列宽
    extColumns.forEach(col => {
      // 获取列在工作表中的索引
      const colIndex = columnToIndex.get(col);
      if (!colIndex) {
        console.log(`未找到列 ${col} 的索引`);
        return;
      }

      // 计算列宽 - 基于内容
      let maxLength = this.getStringWidth(col) * 1.3; // 列名长度，乘以1.3给予更多空间

      // 遍历所有行，找出最长的内容
      data.forEach(row => {
        const cellValue = row[col];
        if (cellValue !== null && cellValue !== undefined) {
          const valueStr = String(cellValue);
          const adjustedLength = this.getStringWidth(valueStr);
          // 乘以1.3给予更多空间，确保内容完全可见
          maxLength = Math.max(maxLength, adjustedLength * 1.3);
        }
      });

      // 根据内容类型调整列宽
      let width = maxLength;

      // 根据列名特征调整扩展字段的列宽
      if (col.includes('速度') || col.includes('高度') || col.includes('重量') ||
          col.includes('数量') || col.includes('number')) {
        // 数值类型列，给予适当空间显示数值和单位
        width = Math.min(Math.max(width, 10), 15);
      } else if (col.includes('日期') || col.includes('时间')) {
        // 日期时间类型列，确保能显示完整的日期格式
        width = Math.max(width, 14);
      } else if (col.includes('编号') || col.includes('code')) {
        // 编号类型列，允许更长的编号
        width = Math.min(Math.max(width, 12), 18);
      } else if (col.includes('名称') || col.includes('name')) {
        // 名称类型列，给予更多空间
        width = Math.min(Math.max(width, 15), 25);
      } else if (col.includes('描述') || col.includes('备注') || col.includes('说明')) {
        // 描述类字段，给予更多空间
        width = Math.min(Math.max(width, 20), 35);
      } else if (col.includes('地址') || col.includes('位置') || col.includes('路径')) {
        // 地址路径类字段，给予更多空间
        width = Math.min(Math.max(width, 18), 30);
      } else {
        // 其他扩展字段 - 根据内容长度自动调整，给予更合理的范围
        width = Math.min(Math.max(width, 12), 25);
      }

      // 限制列宽在合理范围内，给予更大的最大宽度以适应长内容
      width = Math.min(Math.max(width, 8), 60);

      // 设置列宽
      const col_obj = worksheet.getColumn(colIndex);
      col_obj.width = width;

      console.log(`设置扩展字段列宽 - 列: ${col}, 索引: ${colIndex}, 宽度: ${width}`);
    });

    // 确保所有列都有适当的宽度
    for (let i = 1; i <= worksheet.columnCount || 0; i++) {
      const column = worksheet.getColumn(i);
      if (!column.width) {
        column.width = 12; // 设置默认宽度
        console.log(`设置默认列宽 - 索引: ${i}, 宽度: 12`);
      }
    }
  }

  /**
   * 估算字符串宽度，考虑中文字符
   * @param str 字符串
   * @returns 估算宽度
   */
  private static getStringWidth(str: string): number {
    // 使用通用的字符串宽度计算工具
    return getStringWidth(str);
  }

  /**
   * 在工作表上方插入标题行
   * @param worksheet 工作表
   * @param rootNodeName 根节点名称
   * @param sheetName 工作表名称
   * @param columnCount 列数
   */
  private static insertTitleRow(worksheet: ExcelJS.Worksheet, rootNodeName: string, sheetName: string, columnCount: number): void {
    try {
      // 构建标题文本：根节点名称 + 工作表名称
      const titleText = `${rootNodeName}${sheetName}`;

      console.log(`为工作表 ${sheetName} 插入标题行: ${titleText}`);

      // 在第一行插入新行
      worksheet.spliceRows(1, 0, []);

      // 获取第一行（新插入的标题行）
      const titleRow = worksheet.getRow(1);

      // 设置标题行高度
      titleRow.height = 35; // 增加标题行高度

      // 在第一个单元格设置标题文本
      const titleCell = titleRow.getCell(1);
      titleCell.value = titleText;

      // 设置标题单元格样式（与优化后的表头样式一致）
      titleCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '2E5BBA' } // 更深的专业蓝色背景
      };
      titleCell.font = {
        name: '微软雅黑',
        size: 13, // 标题字体比表头大2号（11+2=13）
        bold: true,
        color: { argb: 'FFFFFF' } // 白色文字
      };
      titleCell.alignment = {
        vertical: 'middle',
        horizontal: 'center',
        wrapText: false // 标题行不换行
      };
      titleCell.border = {
        top: { style: 'medium', color: { argb: '1F4788' } },
        left: { style: 'medium', color: { argb: '1F4788' } },
        bottom: { style: 'medium', color: { argb: '1F4788' } },
        right: { style: 'medium', color: { argb: '1F4788' } }
      };

      // 如果有多列，合并标题单元格
      if (columnCount > 1) {
        worksheet.mergeCells(1, 1, 1, columnCount);

        // 为合并区域的其他单元格设置相同的样式
        for (let col = 2; col <= columnCount; col++) {
          const cell = titleRow.getCell(col);
          cell.fill = titleCell.fill;
          cell.font = titleCell.font;
          cell.alignment = titleCell.alignment;
          cell.border = titleCell.border;
        }
      }

      // 提交行更改
      titleRow.commit();

      console.log(`标题行插入完成，合并范围: A1:${String.fromCharCode(64 + columnCount)}1`);
    } catch (error) {
      console.error(`插入标题行失败:`, error);
    }
  }

  /**
   * 将Excel原始数据转换为格式化的Excel
   * @param buffer 原始Excel缓冲区
   * @param data 原始数据
   * @returns 格式化后的Excel文件Blob
   */
  public static async enhanceExcelFormat(buffer: ArrayBuffer, data: any[]): Promise<Blob> {
    try {
      // 创建新的工作簿并加载原始数据
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(buffer);

      // 获取根节点名称
      const CompanyNameHelper = (await import('../../../utils/companyNameHelper')).default;
      const rootNodeName = await CompanyNameHelper.getExportTitle('设备台账');
      console.log('Excel导出使用根节点名称:', rootNodeName);

      // 添加调试日志
      console.log(`工作簿中的工作表数量: ${workbook.worksheets.length}`);
      workbook.worksheets.forEach(ws => console.log(`工作表名称: ${ws.name}`));

      // 处理工作簿中的所有工作表
      workbook.eachSheet((worksheet, sheetId) => {
        if (!worksheet) return;

        console.log(`处理工作表: ${worksheet.name}, ID: ${sheetId}`);

        // 设置工作表属性
        this.setWorksheetProperties(worksheet);

        // 获取实际的列数和行数
        const actualColumnCount = worksheet.columnCount || 0;
        const actualRowCount = worksheet.rowCount || 0;

        console.log(`工作表 ${worksheet.name} 的实际列数: ${actualColumnCount}, 行数: ${actualRowCount}`);

        // 获取当前工作表的数据行数
        // 如果是第一个工作表（总表），使用全部数据
        // 否则，尝试获取工作表名称对应的分类数据
        let sheetData = data;
        if (sheetId > 1) {
          const sheetName = worksheet.name;
          console.log(`为工作表 ${sheetName} 筛选数据`);

          // 按一级分类筛选数据 - 优先使用'设备类型分类'字段
          sheetData = data.filter(item => {
            // 获取一级分类，优先使用'设备类型分类'字段
            const parentCategory = item['设备类型分类'] || item.parentCategory || '未分类';

            // 处理Excel工作表名称可能被截断的情况
            const isMatch = parentCategory === sheetName ||
                  (sheetName.endsWith('...') && parentCategory.startsWith(sheetName.substring(0, sheetName.length - 3)));

            return isMatch;
          });

          console.log(`工作表 ${sheetName} 筛选后的数据条数: ${sheetData.length}`);
        }

        const dataRowCount = sheetData.length;
        console.log(`工作表 ${worksheet.name} 的数据行数: ${dataRowCount}`);

        // 1. 在表格上方插入标题行
        this.insertTitleRow(worksheet, rootNodeName, worksheet.name, actualColumnCount);

        // 使用工作表实际列数，不使用总表的列数
        const columnCount = actualColumnCount; // 只使用工作表实际的列数
        const rowCount = Math.max(actualRowCount + 1, dataRowCount + 2); // +1 for title, +1 for header

        console.log(`工作表 ${worksheet.name} 的最终列数: ${columnCount}, 行数: ${rowCount}`);

        // 2. 调整列宽
        this.adjustColumnWidths(worksheet, sheetData.length > 0 ? sheetData : data);

        // 3. 确保所有单元格都存在并设置样式
        this.ensureAllCellsExist(worksheet, rowCount, columnCount);

        // 4. 设置表格边框
        this.setTableBorders(worksheet, rowCount, columnCount);

        console.log(`工作表 ${worksheet.name} 处理完成`);
      });

      // 导出为Excel
      const enhancedBuffer = await workbook.xlsx.writeBuffer();
      return new Blob([enhancedBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
    } catch (error: any) {
      console.error('增强Excel格式失败:', error);
      // 如果增强格式失败，返回原始Excel文件
      return new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
    }
  }
}

export default ExcelStyleHelper;
