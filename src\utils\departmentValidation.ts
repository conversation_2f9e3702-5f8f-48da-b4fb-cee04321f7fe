import { DepartmentCategory } from '../services/Inventory/departmentService';

/**
 * 部门验证工具函数
 */

/**
 * 检查部门分类树是否为空（只有根节点，没有部门和人员）
 * @param departmentCategories 部门分类树
 * @returns 是否为空
 */
/**
 * 递归检查树中是否有人员节点
 */
function hasPersonsInTree(node: DepartmentCategory): boolean {
  // 检查当前节点是否是人员
  if (node.id.startsWith('person-')) {
    return true;
  }

  // 递归检查子节点
  if (node.children && node.children.length > 0) {
    return node.children.some(child => hasPersonsInTree(child));
  }

  return false;
}

/**
 * 递归检查树中是否有部门节点
 */
function hasDepartmentsInTree(node: DepartmentCategory): boolean {
  // 检查当前节点是否是部门（排除根节点）
  if (node.id !== 'all-dept' && node.id.startsWith('dept-')) {
    return true;
  }

  // 递归检查子节点
  if (node.children && node.children.length > 0) {
    return node.children.some(child => hasDepartmentsInTree(child));
  }

  return false;
}

export function isDepartmentTreeEmpty(departmentCategories: DepartmentCategory[]): boolean {
  // 如果没有分类数据，认为是空的
  if (!departmentCategories || departmentCategories.length === 0) {
    return true;
  }

  // 查找根节点
  const rootNode = departmentCategories.find(cat => cat.id === 'all-dept');
  if (!rootNode) {
    return true;
  }

  // 检查根节点是否有子节点
  if (!rootNode.children || rootNode.children.length === 0) {
    return true;
  }

  // 递归检查整个树中是否有部门和人员
  const hasDepartments = hasDepartmentsInTree(rootNode);
  const hasPersons = hasPersonsInTree(rootNode);

  // 如果没有部门也没有人员，认为是空的
  if (!hasDepartments && !hasPersons) {
    return true;
  }

  // 如果有部门但没有人员，也认为是空的（根据需求）
  if (hasDepartments && !hasPersons) {
    return true;
  }

  // 如果有人员，认为不是空的
  return false;
}

/**
 * 检查部门分类树是否只有部门没有人员
 * @param departmentCategories 部门分类树
 * @returns 是否只有部门没有人员
 */
export function hasDepartmentsButNoPersons(departmentCategories: DepartmentCategory[]): boolean {
  if (!departmentCategories || departmentCategories.length === 0) {
    return false;
  }

  const rootNode = departmentCategories.find(cat => cat.id === 'all-dept');
  if (!rootNode || !rootNode.children || rootNode.children.length === 0) {
    return false;
  }

  const hasDepartments = rootNode.children.some(child => child.id.startsWith('dept-'));
  const hasPersons = rootNode.children.some(child => child.id.startsWith('person-'));

  return hasDepartments && !hasPersons;
}

/**
 * 获取部门分类树的统计信息
 * @param departmentCategories 部门分类树
 * @returns 统计信息
 */
export function getDepartmentTreeStats(departmentCategories: DepartmentCategory[]): {
  departmentCount: number;
  personCount: number;
  isEmpty: boolean;
} {
  if (!departmentCategories || departmentCategories.length === 0) {
    return { departmentCount: 0, personCount: 0, isEmpty: true };
  }

  const rootNode = departmentCategories.find(cat => cat.id === 'all-dept');
  if (!rootNode || !rootNode.children || rootNode.children.length === 0) {
    return { departmentCount: 0, personCount: 0, isEmpty: true };
  }

  const departmentCount = rootNode.children.filter(child => child.id.startsWith('dept-')).length;
  const personCount = rootNode.children.filter(child => child.id.startsWith('person-')).length;

  return {
    departmentCount,
    personCount,
    isEmpty: departmentCount === 0 && personCount === 0
  };
}
