import React, { useState, useEffect } from 'react';
import { X, MapPin, Building, User, Check, ChevronDown, ChevronRight, RotateCcw, CheckSquare } from 'lucide-react';
import { DepartmentCategory } from '../../services/Inventory/departmentService';
import { removeRedundantHierarchical } from '../../utils/arrayUtils';

interface DepartmentSelectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (selectedDepartments: string[]) => void;
  departmentCategories: DepartmentCategory[];
  selectedDepartments: string[];
  title?: string;
}

/**
 * 部门选择弹窗组件
 * 用于巡检任务创建时选择部门
 */
const DepartmentSelectorDialog: React.FC<DepartmentSelectorDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  departmentCategories,
  selectedDepartments,
  title = '选择巡检部门'
}) => {
  // 本地选中状态
  const [localSelectedDepartments, setLocalSelectedDepartments] = useState<string[]>(selectedDepartments);
  
  // 展开的节点状态
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // 初始化展开状态
  useEffect(() => {
    if (departmentCategories.length > 0) {
      const rootId = departmentCategories[0].id;
      setExpandedNodes(new Set([rootId]));
    }
  }, [departmentCategories]);

  // 同步外部选中状态
  useEffect(() => {
    setLocalSelectedDepartments(selectedDepartments);
  }, [selectedDepartments]);

  if (!isOpen) return null;

  // 检查是否为部门节点（包括根节点）
  const isDepartmentNode = (id: string): boolean => {
    return id.startsWith('dept-') || id === 'all-dept';
  };

  // 检查是否为根节点的各种表示方式
  const isRootNodeRepresentation = (name: string): boolean => {
    const rootNodeName = departmentCategories[0]?.name;
    return name === rootNodeName || name === '' || name === '/';
  };

  // 通用的树遍历函数 - 查找部门路径
  const findDepartmentPath = (targetName: string, items: DepartmentCategory[], currentPath: string[] = []): string[] => {
    for (const item of items) {
      const newPath = [...currentPath, item.name];

      if (item.name === targetName) {
        return newPath;
      }

      if (item.children && item.children.length > 0) {
        const result = findDepartmentPath(targetName, item.children, newPath);
        if (result.length > 0) {
          return result;
        }
      }
    }
    return [];
  };

  // 切换节点展开/折叠状态
  const toggleNode = (nodeId: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };



  // 检查某个部门是否被其上级部门覆盖
  const isParentSelected = (categoryName: string): boolean => {
    const fullPath = findDepartmentPath(categoryName, departmentCategories);
    const parentPaths = fullPath.slice(0, -1); // 返回上级路径，不包括自己
    return parentPaths.some(parentPath => {
      // 检查选中的部门中是否包含此父级路径（考虑根节点的多种表示）
      return localSelectedDepartments.some(selected => {
        if (selected === parentPath) return true;
        // 如果父级路径是根节点，检查选中的部门是否也是根节点的表示
        if (isRootNodeRepresentation(parentPath) && isRootNodeRepresentation(selected)) return true;
        return false;
      });
    });
  };

  // 移除被上级部门覆盖的下级部门
  const removeRedundantDepartments = (departments: string[]): string[] => {
    return removeRedundantHierarchical(
      departments,
      (dept) => findDepartmentPath(dept, departmentCategories),
      (parent, child) => {
        if (parent === child) return false;

        // 如果两个都是根节点的表示，认为是重复的
        if (isRootNodeRepresentation(parent) && isRootNodeRepresentation(child)) {
          return true;
        }

        // 使用 findDepartmentPath 获取 child 的完整路径，检查 parent 是否在其父级路径中
        const childPath = findDepartmentPath(child, departmentCategories);
        return childPath.some(pathPart => {
          if (pathPart === parent) return true;
          // 检查根节点的多种表示
          if (isRootNodeRepresentation(pathPart) && isRootNodeRepresentation(parent)) return true;
          return false;
        });
      }
    );
  };

  // 切换部门选择状态
  const toggleDepartmentSelection = (categoryId: string, categoryName: string) => {
    // 处理部门节点和根节点，忽略人员节点
    if (!isDepartmentNode(categoryId) || !categoryName) {
      return;
    }

    const isSelected = localSelectedDepartments.includes(categoryName);

    if (isSelected) {
      // 取消选择该部门
      const newSelected = localSelectedDepartments.filter(path => path !== categoryName);
      setLocalSelectedDepartments(newSelected);
    } else {
      // 选择该部门，并移除被覆盖的下级部门
      const newSelected = [...localSelectedDepartments, categoryName];
      const cleanedSelected = removeRedundantDepartments(newSelected);
      setLocalSelectedDepartments(cleanedSelected);
    }
  };

  // 全选功能 - 只选择根节点，逻辑上包含所有子部门
  const handleSelectAll = () => {
    // 找到根节点
    const rootDepartment = departmentCategories.find(item => item.id === 'all-dept');
    if (rootDepartment && rootDepartment.name) {
      setLocalSelectedDepartments([rootDepartment.name]);
    } else {
      // 如果没有根节点，选择所有顶级部门
      const topLevelDepts = departmentCategories
        .filter(item => item.id.startsWith('dept-') && item.name)
        .map(item => item.name);
      setLocalSelectedDepartments(topLevelDepts);
    }
  };

  // 重置功能
  const handleReset = () => {
    setLocalSelectedDepartments([]);
  };

  // 获取图标
  const getIcon = (category: DepartmentCategory) => {
    if (category.id.startsWith('person-') || category.id.startsWith('resp-')) {
      return <User className="w-3.5 h-3.5 mr-0.5 text-purple-600" />;
    } else if (category.id.startsWith('dept-')) {
      return <Building className="w-3.5 h-3.5 mr-0.5 text-blue-600" />;
    } else {
      return <MapPin className="w-3.5 h-3.5 mr-0.5 text-gray-600" />;
    }
  };

  // 计算缩进
  const calculateIndent = (depth: number): number => {
    return 12 + depth * 16;
  };

  // 检查节点是否被选中（直接选中或被上级覆盖）
  const isNodeSelected = (categoryId: string, categoryName: string): boolean => {
    if (!isDepartmentNode(categoryId) || !categoryName) {
      return false;
    }

    // 直接选中 - 考虑根节点的多种表示
    const isDirectlySelected = localSelectedDepartments.some(selected => {
      if (selected === categoryName) return true;
      // 如果都是根节点的表示，认为是选中的
      if (isRootNodeRepresentation(selected) && isRootNodeRepresentation(categoryName)) return true;
      return false;
    });

    if (isDirectlySelected) {
      return true;
    }

    // 被上级部门覆盖
    return isParentSelected(categoryName);
  };

  // 递归渲染部门树（过滤掉人员节点）
  const renderTree = (items: DepartmentCategory[], depth = 0) => {
    // 过滤掉人员节点，只显示部门节点
    const departmentItems = items.filter(item => isDepartmentNode(item.id));

    return departmentItems.map(category => {
      // 过滤子节点中的人员节点
      const departmentChildren = category.children ?
        category.children.filter(child => isDepartmentNode(child.id)) :
        [];

      const hasChildren = departmentChildren.length > 0;
      const isExpanded = expandedNodes.has(category.id);
      const isDirectlySelected = localSelectedDepartments.includes(category.name);
      const isParentCovered = isParentSelected(category.name);
      const isSelected = isDirectlySelected || isParentCovered;
      const isDepartment = isDepartmentNode(category.id) && category.name;

      return (
        <div key={category.id} className="select-none">
          <div
            className={`flex items-center py-0.5 px-1 my-0.5 rounded transition-colors ${
              isDepartment
                ? `cursor-pointer hover:bg-gray-100 ${isSelected ? 'bg-blue-50 text-blue-700' : ''}`
                : 'cursor-default text-gray-500'
            }`}
            style={{ paddingLeft: calculateIndent(depth) }}
            onClick={() => isDepartment && toggleDepartmentSelection(category.id, category.name)}
          >
            {/* 展开/折叠图标 */}
            {hasChildren ? (
              <span
                className="inline-flex items-center justify-center text-gray-500 w-4 h-4 mr-1 cursor-pointer"
                onClick={(e) => toggleNode(category.id, e)}
              >
                {isExpanded ?
                  <ChevronDown className="w-4 h-4" /> :
                  <ChevronRight className="w-4 h-4" />}
              </span>
            ) : (
              <span className="w-4 h-4 mr-1"></span>
            )}

            {/* 选择框 - 只对部门显示 */}
            {isDepartment && (
              <div className={`flex items-center justify-center w-4 h-4 mr-1 border rounded ${
                isDirectlySelected
                  ? 'border-blue-500 bg-blue-500'
                  : isParentCovered
                    ? 'border-blue-300 bg-blue-100'
                    : 'border-gray-300 bg-white'
              }`}>
                {isDirectlySelected && <Check className="w-3 h-3 text-white" />}
                {isParentCovered && <Check className="w-3 h-3 text-blue-500" />}
              </div>
            )}

            {/* 部门图标 */}
            {getIcon(category)}

            {/* 部门名称和计数 */}
            <span className={`text-sm truncate flex-1 ${
              isDepartment
                ? isParentCovered && !isDirectlySelected
                  ? 'text-blue-600 italic'
                  : ''
                : 'italic'
            }`}>
              {category.name}
              {isParentCovered && !isDirectlySelected && (
                <span className="text-xs text-blue-500 ml-1">(已包含)</span>
              )}
            </span>
            <span className="ml-auto text-xs whitespace-nowrap text-blue-400">
              ({category.count})
            </span>
          </div>

          {/* 子节点 - 只渲染部门子节点 */}
          {hasChildren && isExpanded && (
            <div className="ml-2">
              {renderTree(departmentChildren, depth + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  // 获取部门的API路径（不包含根节点）
  const getDepartmentFullPath = (departmentName: string): string => {
    const path = findDepartmentPath(departmentName, departmentCategories);

    // 如果选择的是根节点，返回空字符串（统一根节点表示）
    if (path.length === 1 && isRootNodeRepresentation(path[0])) {
      return '';
    }

    // 如果路径包含根节点，去掉根节点返回从一级部门开始的路径
    if (path.length > 1 && isRootNodeRepresentation(path[0])) {
      return path.slice(1).join('/');
    }

    // 其他情况直接返回完整路径（包括空路径的情况）
    const result = path.join('/');

    // 如果结果是根节点的表示方式，统一返回空字符串
    if (isRootNodeRepresentation(result)) {
      return '';
    }

    return result;
  };

  // 获取所有一级部门名称（通用函数）
  const getTopLevelDepartments = (): string[] => {
    const rootNode = departmentCategories.find(item => item.id === 'all-dept');

    if (rootNode && rootNode.children) {
      return rootNode.children
        .filter(child => child.id.startsWith('dept-') && child.name)
        .map(child => child.name);
    }

    return departmentCategories
      .filter(item => item.id.startsWith('dept-') && item.name)
      .map(item => item.name);
  };

  // 获取用于UI显示的部门名称列表
  const getDisplayDepartments = (): string[] => {
    const hasRootNode = localSelectedDepartments.some(dept => isRootNodeRepresentation(dept));

    if (hasRootNode) {
      const topLevelDepts = getTopLevelDepartments();
      console.log('选择了公司名称，显示所有一级部门:', topLevelDepts);
      return topLevelDepts;
    }

    return localSelectedDepartments;
  };

  // 确认选择
  const handleConfirm = () => {
    // 获取用于UI显示的部门名称
    const displayDepartments = getDisplayDepartments();

    // 将部门名称转换为完整路径用于API调用
    const departmentPaths = localSelectedDepartments.map(deptName => getDepartmentFullPath(deptName));



    // 传递显示用的部门名称给父组件
    onConfirm(displayDepartments);
    onClose();
  };

  // 取消选择
  const handleCancel = () => {
    setLocalSelectedDepartments(selectedDepartments);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-96 h-[600px] mx-4 overflow-hidden flex flex-col">
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
          <div className="flex items-center">
            <MapPin className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 选择提示和操作按钮 */}
        <div className="px-4 py-2 bg-blue-50 border-b border-blue-100 flex-shrink-0">
          <div className="flex items-center justify-between">
            <p className="text-sm text-blue-700">
              已选择 {localSelectedDepartments.length} 个部门
            </p>
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                className="flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                title="全选"
              >
                <CheckSquare className="h-3 w-3 mr-1" />
                全选
              </button>
              <button
                onClick={handleReset}
                className="flex items-center px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                title="重置"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                重置
              </button>
            </div>
          </div>
        </div>

        {/* 部门树 - 固定高度，内容滚动 */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            {departmentCategories.length > 0 ? (
              <div className="space-y-1">
                {renderTree(departmentCategories)}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                暂无部门数据
              </div>
            )}
          </div>
        </div>

        {/* 弹窗底部 */}
        <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200 bg-gray-50 flex-shrink-0">
          <div className="text-sm text-gray-600">
            {localSelectedDepartments.length > 0 && (
              <span>将在 {localSelectedDepartments.length} 个部门执行巡检</span>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              disabled={localSelectedDepartments.length === 0}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DepartmentSelectorDialog;
