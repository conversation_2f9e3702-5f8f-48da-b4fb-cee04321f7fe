import React, { Suspense } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useCache } from '../contexts/CacheContext';

const KeepAliveOutlet: React.FC = () => {
  const location = useLocation();
  const { cache } = useCache();
  const cacheKey = location.pathname;

  // 如果组件已经在缓存中,直接返回缓存的组件
  if (cache.has(cacheKey)) {
    return (
      <div key={cacheKey} style={{ display: 'contents' }}>
        {cache.get(cacheKey)}
      </div>
    );
  }

  // 渲染新组件并缓存
  const element = (
    <Suspense fallback={<div>Loading...</div>}>
      <Outlet />
    </Suspense>
  );
  
  cache.set(cacheKey, element);

  return (
    <div key={cacheKey} style={{ display: 'contents' }}>
      {element}
    </div>
  );
};

export default React.memo(KeepAliveOutlet); 