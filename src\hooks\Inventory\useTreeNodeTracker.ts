import { useEffect, useLayoutEffect, useRef, useCallback } from 'react';
import { DepartmentCategory } from '../../services/Inventory/departmentService';
import { DeviceCategory } from '../../types/inventory';

/**
 * 树节点追踪Hook
 * 基于React渲染周期的位置追踪，不依赖事件或延迟机制
 */
export function useTreeNodeTracker(
  categories: DepartmentCategory[] | DeviceCategory[],
  expandedNodes: Set<string>,
  setExpandedNodes: (nodes: string[]) => void,
  onSelectNode?: (nodeId: string) => void
) {
  // 保存上一次的节点ID集合，用于精确比较
  const prevNodeIdsRef = useRef<Set<string>>(new Set());

  // 保存上一次的节点内容哈希，用于检测编辑操作
  const prevNodeHashRef = useRef<Map<string, string>>(new Map());

  // 保存上一次的categories数组引用，用于检测重新生成
  const prevCategoriesRefRef = useRef<any>(null);

  // 保存删除节点的父节点映射，避免在删除后查找
  const deletedNodeParentsRef = useRef<Map<string, string>>(new Map());

  // 保存需要追踪的新节点
  const pendingTrackingRef = useRef<{
    nodeId: string;
    parentId: string;
    operation: 'add' | 'edit' | 'delete';
  } | null>(null);



  /**
   * 查找节点的父节点ID
   */
  const findParentId = useCallback((
    categories: DepartmentCategory[] | DeviceCategory[],
    targetId: string,
    parentId?: string
  ): string | undefined => {
    for (const item of categories) {
      if (item.id === targetId) {
        return parentId;
      }
      if (item.children && item.children.length > 0) {
        const found = findParentId(item.children, targetId, item.id);
        if (found !== undefined) return found;
      }
    }
    return undefined;
  }, []);



  /**
   * 生成节点内容的哈希值，用于检测编辑操作
   */
  const generateNodeHash = useCallback((node: any): string => {
    // 对于部门节点，包含名称和备注
    if (node.name !== undefined) {
      const name = node.name || '';
      const alias = node.alias || '';
      const count = node.count || 0;
      return `${name}|${alias}|${count}`;
    }

    // 对于设备分类节点，包含名称和数量
    if (node.title !== undefined) {
      const title = node.title || '';
      const count = node.count || 0;
      return `${title}|${count}`;
    }

    // 默认情况
    return JSON.stringify(node);
  }, []);

  /**
   * 收集所有节点的哈希值
   */
  const collectNodeHashes = useCallback((items: any[]): Map<string, string> => {
    const hashMap = new Map<string, string>();

    const traverse = (nodes: any[]) => {
      nodes.forEach(node => {
        hashMap.set(node.id, generateNodeHash(node));
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };

    traverse(items);
    return hashMap;
  }, [generateNodeHash]);

  /**
   * 构建所有节点的父节点映射
   */
  const buildParentMapping = useCallback((items: any[]): Map<string, string> => {
    const parentMap = new Map<string, string>();

    const traverse = (nodes: any[], parentId?: string) => {
      nodes.forEach(node => {
        if (parentId) {
          parentMap.set(node.id, parentId);
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children, node.id);
        }
      });
    };

    traverse(items);
    return parentMap;
  }, []);

  /**
   * 展开到指定节点的路径
   */
  const expandPathToNode = useCallback((
    categories: DepartmentCategory[] | DeviceCategory[],
    targetId: string
  ): string[] => {
    const path: string[] = [];
    
    const findPath = (items: any[], target: string, currentPath: string[]): boolean => {
      for (const item of items) {
        const newPath = [...currentPath, item.id];
        
        if (item.id === target) {
          path.push(...currentPath); // 不包含目标节点本身
          return true;
        }
        
        if (item.children && item.children.length > 0) {
          if (findPath(item.children, target, newPath)) {
            return true;
          }
        }
      }
      return false;
    };

    findPath(categories, targetId, []);
    return path;
  }, []);

  /**
   * 滚动到指定节点并自动选中
   */
  const scrollToNodeAndSelect = useCallback((nodeId: string, onSelectNode?: (nodeId: string) => void) => {
    const element = document.querySelector(`[data-node-id="${nodeId}"]`) as HTMLElement;
    if (element) {
      console.log(`useTreeNodeTracker: 滚动到节点并选中 ${nodeId}`);

      // 计算滚动位置，让目标节点显示在视口中央
      const rect = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const targetTop = rect.top + window.pageYOffset - (viewportHeight / 2);

      window.scrollTo({
        top: Math.max(0, targetTop),
        behavior: 'smooth'
      });

      // 自动选中节点
      if (onSelectNode) {
        onSelectNode(nodeId);
      }

      return true;
    } else {
      console.warn(`useTreeNodeTracker: 未找到节点元素 ${nodeId}`);
      return false;
    }
  }, []);



  // 监听分类数据变化，检测新增和编辑节点
  useEffect(() => {
    // 收集当前所有节点ID和哈希值
    const currentNodeIds = new Set<string>();
    const collectIds = (items: any[]) => {
      items.forEach(item => {
        currentNodeIds.add(item.id);
        if (item.children && item.children.length > 0) {
          collectIds(item.children);
        }
      });
    };
    collectIds(categories);

    const currentNodeHashes = collectNodeHashes(categories);

    // 首次加载，保存数据
    if (prevNodeIdsRef.current.size === 0) {
      prevNodeIdsRef.current = new Set(currentNodeIds);
      prevNodeHashRef.current = new Map(currentNodeHashes);
      prevCategoriesRefRef.current = categories;
      deletedNodeParentsRef.current = buildParentMapping(categories);
      console.log(`useTreeNodeTracker: 首次加载，保存 ${currentNodeIds.size} 个节点`);
      return;
    }

    // 检测数组引用是否发生变化（用于设备分类树的重新生成检测）
    const categoriesRefChanged = prevCategoriesRefRef.current !== null &&
                                 prevCategoriesRefRef.current !== categories;

    // 检测新增的节点
    const newNodeIds: string[] = [];
    currentNodeIds.forEach(id => {
      if (!prevNodeIdsRef.current.has(id)) {
        newNodeIds.push(id);
      }
    });

    // 检测编辑的节点（内容发生变化但ID相同）
    const editedNodeIds: string[] = [];
    currentNodeIds.forEach(id => {
      if (prevNodeIdsRef.current.has(id)) {
        const currentHash = currentNodeHashes.get(id);
        const prevHash = prevNodeHashRef.current.get(id);
        if (currentHash && prevHash && currentHash !== prevHash) {
          editedNodeIds.push(id);
        }
      }
    });

    // 检测重新生成的节点（数组引用变化但节点ID和数量相同，用于设备分类树）
    const regeneratedNodeIds: string[] = [];
    if (categoriesRefChanged &&
        newNodeIds.length === 0 &&
        currentNodeIds.size === prevNodeIdsRef.current.size) {

      // 数组引用变化，但节点ID和数量相同，可能是重新生成
      // 检查是否有任何节点的哈希值发生变化
      currentNodeIds.forEach(id => {
        if (prevNodeIdsRef.current.has(id)) {
          const currentHash = currentNodeHashes.get(id);
          const prevHash = prevNodeHashRef.current.get(id);
          if (currentHash && prevHash && currentHash !== prevHash) {
            regeneratedNodeIds.push(id);
          }
        }
      });

      console.log(`useTreeNodeTracker: 检测到数组引用变化，可能的重新生成节点`, regeneratedNodeIds);
    }

    // 检测删除的节点（之前存在但现在不存在的节点）
    const deletedNodeIds: string[] = [];

    prevNodeIdsRef.current.forEach(id => {
      if (!currentNodeIds.has(id)) {
        deletedNodeIds.push(id);
      }
    });

    // 处理新增操作
    if (newNodeIds.length > 0) {
      console.log(`useTreeNodeTracker: 检测到新增节点`, newNodeIds);
      console.log(`useTreeNodeTracker: 节点数量变化 ${prevNodeIdsRef.current.size} -> ${currentNodeIds.size}`);

      const newNodeId = newNodeIds[0];
      console.log(`useTreeNodeTracker: 处理新增节点 ${newNodeId}`);

      // 找到父节点路径并展开
      const pathToExpand = expandPathToNode(categories, newNodeId);
      if (pathToExpand.length > 0) {
        console.log(`useTreeNodeTracker: 新增节点展开路径`, pathToExpand);
        setExpandedNodes(pathToExpand);
      }

      // 设置待追踪的节点
      pendingTrackingRef.current = {
        nodeId: newNodeId,
        parentId: findParentId(categories, newNodeId) || '',
        operation: 'add'
      };
    }
    // 处理编辑操作
    else if (editedNodeIds.length > 0) {
      console.log(`useTreeNodeTracker: 检测到编辑节点`, editedNodeIds);

      const editedNodeId = editedNodeIds[0];
      console.log(`useTreeNodeTracker: 处理编辑节点 ${editedNodeId}`);

      // 找到节点路径并展开（编辑操作需要展开到节点本身）
      const pathToExpand = expandPathToNode(categories, editedNodeId);
      if (pathToExpand.length > 0) {
        console.log(`useTreeNodeTracker: 编辑节点展开路径`, pathToExpand);
        setExpandedNodes(pathToExpand);
      }

      // 设置待追踪的节点
      pendingTrackingRef.current = {
        nodeId: editedNodeId,
        parentId: findParentId(categories, editedNodeId) || '',
        operation: 'edit'
      };
    }
    // 处理重新生成操作（主要用于设备分类树）
    else if (regeneratedNodeIds.length > 0) {
      console.log(`useTreeNodeTracker: 检测到重新生成节点`, regeneratedNodeIds);

      const regeneratedNodeId = regeneratedNodeIds[0];
      console.log(`useTreeNodeTracker: 处理重新生成节点 ${regeneratedNodeId}`);

      // 找到节点路径并展开（重新生成操作需要展开到节点本身）
      const pathToExpand = expandPathToNode(categories, regeneratedNodeId);
      if (pathToExpand.length > 0) {
        console.log(`useTreeNodeTracker: 重新生成节点展开路径`, pathToExpand);
        setExpandedNodes(pathToExpand);
      }

      // 设置待追踪的节点
      pendingTrackingRef.current = {
        nodeId: regeneratedNodeId,
        parentId: findParentId(categories, regeneratedNodeId) || '',
        operation: 'edit'
      };
    }
    // 处理删除操作
    else if (deletedNodeIds.length > 0) {
      console.log(`useTreeNodeTracker: 检测到删除节点`, deletedNodeIds);
      console.log(`useTreeNodeTracker: 节点数量变化 ${prevNodeIdsRef.current.size} -> ${currentNodeIds.size}`);

      const deletedNodeId = deletedNodeIds[0];
      // 从预先保存的映射中获取父节点ID
      const parentNodeId = deletedNodeParentsRef.current.get(deletedNodeId);

      if (parentNodeId) {
        console.log(`useTreeNodeTracker: 处理删除节点 ${deletedNodeId}，追踪到父节点 ${parentNodeId}`);

        // 找到父节点路径并展开（删除操作需要展开到父节点）
        const pathToParent = expandPathToNode(categories, parentNodeId);
        // 对于删除操作，需要展开父节点本身，所以要将父节点添加到路径中
        const pathToExpand = [...pathToParent, parentNodeId];

        if (pathToExpand.length > 0) {
          console.log(`useTreeNodeTracker: 删除节点父节点展开路径`, pathToExpand);
          setExpandedNodes(pathToExpand);
        }

        // 设置待追踪的节点（追踪到父节点）
        pendingTrackingRef.current = {
          nodeId: parentNodeId,
          parentId: findParentId(categories, parentNodeId) || '',
          operation: 'delete'
        };
      } else {
        console.log(`useTreeNodeTracker: 删除的节点 ${deletedNodeId} 没有找到父节点，可能是根节点`);
      }
    }

    // 如果有任何变化，更新保存的数据
    if (newNodeIds.length > 0 || editedNodeIds.length > 0 || regeneratedNodeIds.length > 0 || deletedNodeIds.length > 0) {
      prevNodeIdsRef.current = new Set(currentNodeIds);
      prevNodeHashRef.current = new Map(currentNodeHashes);
      prevCategoriesRefRef.current = categories;
      deletedNodeParentsRef.current = buildParentMapping(categories);
    }
  }, [categories, collectNodeHashes, buildParentMapping, expandPathToNode, setExpandedNodes, findParentId]); // 依赖所有使用的函数

  // 在DOM更新后处理滚动和选中
  useLayoutEffect(() => {
    if (pendingTrackingRef.current) {
      const { nodeId } = pendingTrackingRef.current;
      console.log(`useTreeNodeTracker: 尝试滚动到节点并选中 ${nodeId}`);

      // 尝试滚动到节点并选中
      const success = scrollToNodeAndSelect(nodeId, onSelectNode);

      if (success) {
        // 滚动成功，清除待追踪节点
        pendingTrackingRef.current = null;
      } else {
        // 滚动失败，在下一帧再试
        requestAnimationFrame(() => {
          if (pendingTrackingRef.current?.nodeId === nodeId) {
            const retrySuccess = scrollToNodeAndSelect(nodeId, onSelectNode);
            if (retrySuccess) {
              pendingTrackingRef.current = null;
            }
          }
        });
      }
    }
  });

  // Hook 不需要返回值，所有功能都通过回调处理
}

export default useTreeNodeTracker;
