/**
 * 统一表格组件 - BaseTable
 * 基于 @tanstack/react-table 实现，整合所有表格功能
 */

import React, { useMemo, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  createColumnHelper,
  flexRender,
  ColumnDef,
  SortingState,
  VisibilityState,
} from '@tanstack/react-table';
import { ChevronUp, ChevronDown, ArrowUpDown, RefreshCw } from 'lucide-react';
import CustomScrollbar from './CustomScrollbar';
import { 
  BaseTableProps, 
  BaseTableRef, 
  BaseTableColumn,
  TABLE_PRESETS 
} from '../../types/table';
import '../../styles/customScrollbar.css';

/**
 * 统一表格组件
 */
const BaseTable = forwardRef<BaseTableRef, BaseTableProps>(({
  data,
  columns,
  features = TABLE_PRESETS.BASIC.features,
  pagination = false,
  selection,
  loading = false,
  empty = {},
  events = {},
  style = TABLE_PRESETS.BASIC.style,
  scroll,
  rowKey = 'id',
}, ref) => {
  // 状态管理
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState('');
  
  // 引用
  const tableContainerRef = useRef<HTMLDivElement>(null);
  
  // 处理加载状态
  const loadingConfig = typeof loading === 'boolean' 
    ? { loading, loadingText: '加载中...' }
    : loading;
  
  // 处理行键值获取
  const getRowKey = typeof rowKey === 'string' 
    ? (row: any) => row[rowKey] 
    : rowKey;
  
  // 创建列助手
  const columnHelper = createColumnHelper<any>();
  
  // 转换列定义为 @tanstack/react-table 格式
  const tableColumns = useMemo<ColumnDef<any, any>[]>(() => {
    const cols: ColumnDef<any, any>[] = [];
    
    // 添加选择列
    if (features?.selection && selection) {
      cols.push(
        columnHelper.display({
          id: 'select',
          header: ({ table }) => (
            <input
              type="checkbox"
              checked={table.getIsAllRowsSelected()}
              ref={(input) => {
                if (input) {
                  input.indeterminate = table.getIsSomeRowsSelected();
                }
              }}
              onChange={table.getToggleAllRowsSelectedHandler()}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={loadingConfig.loading}
            />
          ),
          cell: ({ row }) => (
            <input
              type="checkbox"
              checked={row.getIsSelected()}
              onChange={row.getToggleSelectedHandler()}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={loadingConfig.loading}
            />
          ),
          enableSorting: false,
          size: selection.columnWidth || 40,
        })
      );
    }
    
    // 添加数据列
    columns.forEach((column) => {
      cols.push(
        columnHelper.accessor(
          (row) => row[column.key],
          {
            id: column.key,
            header: ({ column: tableColumn }) => (
              <div className="flex items-center space-x-1 group">
                <button
                  onClick={() => features?.sorting && tableColumn.toggleSorting()}
                  className={`font-medium text-gray-900 flex items-center ${
                    features?.sorting ? 'cursor-pointer' : 'cursor-default'
                  }`}
                  style={{ whiteSpace: 'nowrap', fontSize: '0.95rem' }}
                  disabled={!features?.sorting || !column.sortable}
                >
                  {column.title}
                  {features?.sorting && column.sortable !== false && (
                    <>
                      {{
                        asc: <ChevronUp className="ml-1 h-4 w-4 text-blue-600" />,
                        desc: <ChevronDown className="ml-1 h-4 w-4 text-blue-600" />,
                      }[tableColumn.getIsSorted() as string] ?? (
                        <ArrowUpDown className="ml-1 h-4 w-4 text-gray-500 opacity-0 group-hover:opacity-100" />
                      )}
                    </>
                  )}
                </button>
              </div>
            ),
            cell: (info) => {
              const value = info.getValue();
              const record = info.row.original;
              const index = info.row.index;
              
              if (column.render) {
                return column.render(value, record, index);
              }
              
              return value ?? '-';
            },
            enableSorting: features?.sorting && column.sortable !== false,
            size: typeof column.width === 'number' ? column.width : 150,
            meta: {
              align: column.align || 'left',
              fixed: column.fixed,
            },
          }
        )
      );
    });
    
    return cols;
  }, [columns, features, selection, loadingConfig.loading, columnHelper]);
  
  // 创建表格实例
  const table = useReactTable({
    data,
    columns: tableColumns,
    state: {
      sorting,
      columnVisibility,
      globalFilter,
      ...(pagination && {
        pagination: {
          pageIndex: pagination.pageIndex || 0,
          pageSize: pagination.pageSize || 10,
        },
      }),
    },
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    ...(pagination && {
      getPaginationRowModel: getPaginationRowModel(),
      manualPagination: false,
    }),
    enableRowSelection: features?.selection,
    getRowId: getRowKey,
  });
  
  // 暴露表格实例方法
  useImperativeHandle(ref, () => ({
    refresh: () => {
      // 触发数据刷新逻辑
      console.log('Table refresh triggered');
    },
    reset: () => {
      setSorting([]);
      setColumnVisibility({});
      setGlobalFilter('');
      table.resetRowSelection();
    },
    scrollTo: (position) => {
      if (tableContainerRef.current) {
        const container = tableContainerRef.current.querySelector('.custom-scrollbar-content');
        if (container) {
          if (position.x !== undefined) {
            container.scrollLeft = position.x;
          }
          if (position.y !== undefined) {
            container.scrollTop = position.y;
          }
        }
      }
    },
    getSelectedRows: () => {
      return table.getSelectedRowModel().rows.map(row => row.original);
    },
    setSelectedRows: (keys) => {
      table.resetRowSelection();
      keys.forEach(key => {
        const row = table.getRowModel().rows.find(r => getRowKey(r.original) === key);
        if (row) {
          row.toggleSelected(true);
        }
      });
    },
    clearSelection: () => {
      table.resetRowSelection();
    },
  }), [table, getRowKey]);
  
  // 渲染空状态
  const renderEmpty = () => (
    <tr>
      <td colSpan={tableColumns.length} className="px-4 py-12 text-center">
        <div className="flex flex-col items-center justify-center h-full min-h-[200px]">
          {empty.emptyIcon || (
            <RefreshCw className={`h-8 w-8 mb-3 ${
              loadingConfig.loading ? 'text-blue-600 animate-spin' : 'text-gray-400'
            }`} />
          )}
          <p className="text-gray-500 text-sm font-medium">
            {loadingConfig.loading ? loadingConfig.loadingText : (empty.emptyText || '暂无数据')}
          </p>
        </div>
      </td>
    </tr>
  );
  
  // 计算表格样式类名
  const tableClassName = [
    'w-full adaptive-table',
    style?.size === 'small' && 'table-sm',
    style?.size === 'large' && 'table-lg',
    style?.bordered && 'table-bordered',
    style?.striped && 'table-striped',
    style?.className,
  ].filter(Boolean).join(' ');
  
  return (
    <div 
      className={`flex-1 min-h-0 bg-white shadow-sm adaptive-table-container ${style?.className || ''}`}
      style={style?.style}
    >
      <div className="adaptive-table-content">
        <CustomScrollbar
          className="h-full"
          horizontal={true}
          vertical={true}
        >
          <div ref={tableContainerRef}>
            <table
              className={tableClassName}
              style={{
                minWidth: scroll?.x || 'var(--table-min-width)',
                ...style?.style
              }}
              role="table"
              aria-label="数据表格"
              aria-rowcount={data.length + 1}
              aria-colcount={tableColumns.length}
            >
              <thead role="rowgroup">
                {table.getHeaderGroups().map((headerGroup, groupIndex) => (
                  <tr key={headerGroup.id} role="row" aria-rowindex={groupIndex + 1}>
                    {headerGroup.headers.map((header, headerIndex) => (
                      <th
                        key={header.id}
                        className={`table-header-cell table-cell-${header.column.columnDef.meta?.align || 'left'}`}
                        style={{
                          width: header.getSize(),
                          minWidth: header.getSize(),
                        }}
                        onClick={(e) => events.onHeaderClick?.(
                          columns.find(col => col.key === header.id)!,
                          e
                        )}
                        role="columnheader"
                        aria-colindex={headerIndex + 1}
                        aria-sort={
                          header.column.getIsSorted() === 'asc' ? 'ascending' :
                          header.column.getIsSorted() === 'desc' ? 'descending' : 'none'
                        }
                        tabIndex={features?.sorting && header.column.getCanSort() ? 0 : -1}
                        onKeyDown={(e) => {
                          if ((e.key === 'Enter' || e.key === ' ') && features?.sorting && header.column.getCanSort()) {
                            e.preventDefault();
                            header.column.toggleSorting();
                          }
                        }}
                      >
                        {header.isPlaceholder ? null : (
                          flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )
                        )}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="bg-white" role="rowgroup">
                {table.getRowModel().rows.length === 0 ? (
                  renderEmpty()
                ) : (
                  table.getRowModel().rows.map((row, rowIndex) => (
                    <tr
                      key={row.id}
                      className={`relative ${events.onRowClick ? 'cursor-pointer' : ''} ${
                        style?.rowClassName?.(row.original, row.index) || ''
                      }`}
                      style={style?.rowStyle?.(row.original, row.index)}
                      onClick={(e) => events.onRowClick?.(row.original, row.index, e)}
                      onDoubleClick={(e) => events.onRowDoubleClick?.(row.original, row.index, e)}
                      onContextMenu={(e) => events.onRowContextMenu?.(row.original, row.index, e)}
                      role="row"
                      aria-rowindex={rowIndex + 2}
                      aria-selected={row.getIsSelected()}
                      tabIndex={events.onRowClick ? 0 : -1}
                      onKeyDown={(e) => {
                        if ((e.key === 'Enter' || e.key === ' ') && events.onRowClick) {
                          e.preventDefault();
                          events.onRowClick(row.original, row.index, e as any);
                        }
                      }}
                    >
                      {row.getVisibleCells().map((cell, cellIndex) => (
                        <td
                          key={cell.id}
                          className={`table-cell table-cell-${cell.column.columnDef.meta?.align || 'left'}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            events.onCellClick?.(
                              cell.row.original,
                              columns.find(col => col.key === cell.column.id)!,
                              e
                            );
                          }}
                          role="cell"
                          aria-colindex={cellIndex + 1}
                          tabIndex={events.onCellClick ? 0 : -1}
                          onKeyDown={(e) => {
                            if ((e.key === 'Enter' || e.key === ' ') && events.onCellClick) {
                              e.preventDefault();
                              e.stopPropagation();
                              events.onCellClick(
                                cell.row.original,
                                columns.find(col => col.key === cell.column.id)!,
                                e as any
                              );
                            }
                          }}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      ))}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CustomScrollbar>
      </div>
      
      {/* 分页组件 */}
      {pagination && table.getPageCount() > 1 && (
        <div className="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">
              显示 {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} 到{' '}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )}{' '}
              条，共 {table.getFilteredRowModel().rows.length} 条
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50"
            >
              上一页
            </button>
            <span className="text-sm text-gray-700">
              第 {table.getState().pagination.pageIndex + 1} 页，共 {table.getPageCount()} 页
            </span>
            <button
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  );
});

BaseTable.displayName = 'BaseTable';

export default BaseTable;
