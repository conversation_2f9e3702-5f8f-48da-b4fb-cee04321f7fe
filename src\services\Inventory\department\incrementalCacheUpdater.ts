import { DepartmentCategory } from './types';
import { CacheManager } from './cacheManager';
import { TreeUtils } from './treeUtils';
import { ROOT_NODE_ID, NODE_ID_PREFIXES, formatLogMessage, LOG_PREFIXES } from './constants';

/**
 * 增量缓存更新器
 * 负责在部门树结构变化时进行增量缓存更新，避免全量刷新
 */
export class IncrementalCacheUpdater {
  private static cacheManager = CacheManager.getInstance();

  /**
   * 添加部门节点到树中（增量操作）
   * @param categories 当前部门分类树
   * @param parentId 父部门ID
   * @param newDepartment 新部门数据
   * @param realDepartmentId 真实的部门ID（可选，如果提供则使用真实ID）
   * @returns 更新后的部门树和新节点ID
   */
  public static addDepartmentNode(
    categories: DepartmentCategory[],
    parentId: string,
    newDepartment: any,
    realDepartmentId?: number
  ): { updatedCategories: DepartmentCategory[], newNodeId: string } {
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `增量添加部门节点: ${newDepartment.name} 到父节点: ${parentId}`));

    const updatedCategories = this.deepClone(categories);

    // 查找父节点
    const parentNode = TreeUtils.findNodeById(updatedCategories, parentId);
    if (!parentNode) {
      throw new Error(`未找到父节点: ${parentId}`);
    }

    // 生成节点ID：优先使用真实ID，否则使用临时ID
    const nodeId = realDepartmentId ? `dept-${realDepartmentId}` : `dept-temp-${Date.now()}`;

    // 创建新的部门节点
    const newNode: DepartmentCategory = {
      id: nodeId,
      name: newDepartment.name,
      count: 0, // 新部门初始计数为0
      children: [],
      departmentPath: newDepartment.department_path,
      // 如果是临时ID，标记为待更新
      isTemporary: !realDepartmentId
    };

    // 添加到父节点
    if (!parentNode.children) {
      parentNode.children = [];
    }
    parentNode.children.push(newNode);

    // 对子节点进行排序
    TreeUtils.sortDepartmentTree([parentNode]);

    // 增量更新缓存
    this.updateCacheForAddedDepartment(newNode, parentNode);

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `部门节点添加完成: ${newNode.name}, ID: ${nodeId}`));
    return { updatedCategories, newNodeId: nodeId };
  }

  /**
   * 添加人员节点到树中（增量操作）
   * @param categories 当前部门分类树
   * @param departmentId 部门ID
   * @param newPerson 新人员数据
   * @returns 更新后的部门树
   */
  public static addPersonNode(
    categories: DepartmentCategory[],
    departmentId: string,
    newPerson: any
  ): DepartmentCategory[] {
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `增量添加人员节点: ${newPerson.user_name} 到部门: ${departmentId}`));

    const updatedCategories = this.deepClone(categories);

    // 查找目标部门
    const departmentNode = TreeUtils.findNodeById(updatedCategories, departmentId);
    if (!departmentNode) {
      throw new Error(`未找到部门: ${departmentId}`);
    }

    // 创建新的人员节点
    const personName = newPerson.user_name + (newPerson.alias ? ` (${newPerson.alias})` : '');
    const personId = newPerson.id || Date.now(); // 使用真实ID或临时ID

    // 生成人员节点ID：如果部门有真实ID，则使用完整格式
    let personNodeId: string;
    if (departmentId.startsWith('dept-temp-')) {
      // 临时部门，使用简单格式
      personNodeId = `person-${personId}`;
    } else {
      // 真实部门，使用完整格式（包含部门ID）
      const deptId = departmentId.replace('dept-', '');
      personNodeId = `person-${personId}-dept-${deptId}`;
    }

    const newNode: DepartmentCategory = {
      id: personNodeId,
      name: personName,
      count: 0, // 新人员初始计数为0
      originalPersonId: typeof personId === 'number' ? personId : parseInt(personId.toString())
    };

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `创建人员节点: ID=${newNode.id}, originalPersonId=${newNode.originalPersonId}, 显示名称=${personName}`));

    // 添加到部门节点
    if (!departmentNode.children) {
      departmentNode.children = [];
    }
    departmentNode.children.push(newNode);

    // 对子节点进行排序
    TreeUtils.sortDepartmentTree([departmentNode]);

    // 增量更新缓存
    this.updateCacheForAddedPerson(newNode, departmentNode);

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `人员节点添加完成: ${newNode.name}`));
    return updatedCategories;
  }

  /**
   * 从树中删除节点（增量操作）
   * @param categories 当前部门分类树
   * @param nodeId 要删除的节点ID
   * @returns 更新后的部门树
   */
  public static removeNode(
    categories: DepartmentCategory[],
    nodeId: string
  ): DepartmentCategory[] {
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `增量删除节点: ${nodeId}`));

    const updatedCategories = this.deepClone(categories);
    
    // 查找要删除的节点和其父节点
    const nodeToDelete = TreeUtils.findNodeById(updatedCategories, nodeId);
    if (!nodeToDelete) {
      throw new Error(`未找到要删除的节点: ${nodeId}`);
    }

    // 递归删除节点
    const removeNodeRecursive = (nodes: DepartmentCategory[]): DepartmentCategory[] => {
      return nodes
        .filter(node => node.id !== nodeId)
        .map(node => ({
          ...node,
          children: node.children ? removeNodeRecursive(node.children) : undefined
        }));
    };

    const result = removeNodeRecursive(updatedCategories);

    // 增量更新缓存
    this.updateCacheForRemovedNode(nodeToDelete);

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `节点删除完成: ${nodeId}`));
    return result;
  }

  /**
   * 重命名节点（增量操作）
   * @param categories 当前部门分类树
   * @param nodeId 节点ID
   * @param newName 新名称
   * @returns 更新后的部门树
   */
  public static renameNode(
    categories: DepartmentCategory[],
    nodeId: string,
    newName: string
  ): DepartmentCategory[] {
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `增量重命名节点: ${nodeId} -> ${newName}`));

    const updatedCategories = this.deepClone(categories);

    // 查找要重命名的节点
    const nodeToRename = TreeUtils.findNodeById(updatedCategories, nodeId);
    if (!nodeToRename) {
      throw new Error(`未找到要重命名的节点: ${nodeId}`);
    }

    const oldName = nodeToRename.name;
    nodeToRename.name = newName;

    // 增量更新缓存
    this.updateCacheForRenamedNode(nodeToRename, oldName, newName);

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `节点重命名完成: ${oldName} -> ${newName}`));
    return updatedCategories;
  }

  /**
   * 更新临时部门ID为真实ID（增量操作）
   * @param categories 当前部门分类树
   * @param tempDepartmentId 临时部门ID
   * @param realDepartmentId 真实部门ID
   * @returns 更新后的部门树
   */
  public static updateTemporaryDepartmentId(
    categories: DepartmentCategory[],
    tempDepartmentId: string,
    realDepartmentId: number
  ): DepartmentCategory[] {
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `更新临时部门ID: ${tempDepartmentId} -> dept-${realDepartmentId}`));

    const updatedCategories = this.deepClone(categories);

    // 查找临时部门节点
    const tempDepartmentNode = TreeUtils.findNodeById(updatedCategories, tempDepartmentId);
    if (!tempDepartmentNode) {
      console.warn(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `未找到临时部门节点: ${tempDepartmentId}`));
      return updatedCategories;
    }

    // 更新部门节点ID
    const newDepartmentId = `dept-${realDepartmentId}`;
    tempDepartmentNode.id = newDepartmentId;
    tempDepartmentNode.isTemporary = false;

    // 更新该部门下所有人员节点的ID格式
    if (tempDepartmentNode.children) {
      for (const child of tempDepartmentNode.children) {
        if (child.id.startsWith(NODE_ID_PREFIXES.PERSON)) {
          // 从简单格式转换为完整格式
          const personIdMatch = child.id.match(/^person-(\d+)$/);
          if (personIdMatch) {
            const personId = personIdMatch[1];
            child.id = `person-${personId}-dept-${realDepartmentId}`;
            console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `更新人员节点ID: person-${personId} -> ${child.id}`));
          }
        }
      }
    }

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `临时部门ID更新完成: ${tempDepartmentId} -> ${newDepartmentId}`));
    return updatedCategories;
  }

  /**
   * 更新人员节点信息（增量操作）
   * @param categories 当前部门分类树
   * @param personId 人员ID（数字）
   * @param updatedPersonInfo 更新后的人员信息
   * @returns 更新后的部门树
   */
  public static updatePersonNode(
    categories: DepartmentCategory[],
    personId: number,
    updatedPersonInfo: any
  ): DepartmentCategory[] {
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `增量更新人员节点: ID=${personId}, 姓名=${updatedPersonInfo.user_name}`));

    const updatedCategories = this.deepClone(categories);

    // 查找人员节点（可能在多个部门中）
    const personNodes: DepartmentCategory[] = [];

    TreeUtils.traverseTree(updatedCategories, (node) => {
      if (node.id.startsWith(NODE_ID_PREFIXES.PERSON) && node.originalPersonId === personId) {
        personNodes.push(node);
      }
    });

    if (personNodes.length === 0) {
      console.warn(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `未找到人员节点: ID=${personId}`));
      return updatedCategories;
    }

    // 更新所有找到的人员节点
    for (const personNode of personNodes) {
      const oldName = personNode.name;
      const newName = updatedPersonInfo.user_name + (updatedPersonInfo.alias ? ` (${updatedPersonInfo.alias})` : '');

      personNode.name = newName;

      console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `人员节点已更新: ${oldName} -> ${newName}`));
    }

    // 增量更新缓存
    this.updateCacheForUpdatedPerson(personNodes);

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `人员节点更新完成: 更新了 ${personNodes.length} 个节点`));
    return updatedCategories;
  }

  /**
   * 为新添加的部门更新缓存
   */
  private static updateCacheForAddedDepartment(newNode: DepartmentCategory, parentNode: DepartmentCategory): void {
    // 清除路径相关缓存（因为新增了节点，路径可能发生变化）
    this.cacheManager.clearCache('pathCache');
    this.cacheManager.clearCache('departmentPathsCache');
    
    // 清除同名部门缓存（可能有新的同名部门）
    this.cacheManager.clearCache('sameNameDepartmentsCache');
    
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, '已清除路径和同名部门相关缓存'));
  }

  /**
   * 为新添加的人员更新缓存
   */
  private static updateCacheForAddedPerson(newNode: DepartmentCategory, departmentNode: DepartmentCategory): void {
    // 清除部门人员缓存
    if (this.cacheManager.hasDepartmentPersonsCache(departmentNode.id)) {
      this.cacheManager.clearCache('departmentPersonsCache');
      console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `已清除部门 ${departmentNode.name} 的人员缓存`));
    }
  }

  /**
   * 为删除的节点更新缓存
   */
  private static updateCacheForRemovedNode(deletedNode: DepartmentCategory): void {
    // 清除与删除节点相关的所有缓存
    this.cacheManager.clearCache('pathCache');
    this.cacheManager.clearCache('departmentPathsCache');
    this.cacheManager.clearCache('sameNameDepartmentsCache');
    
    if (deletedNode.id.startsWith(NODE_ID_PREFIXES.PERSON)) {
      this.cacheManager.clearCache('departmentPersonsCache');
    }
    
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `已清除删除节点 ${deletedNode.name} 相关的缓存`));
  }



  /**
   * 为重命名的节点更新缓存
   */
  private static updateCacheForRenamedNode(node: DepartmentCategory, oldName: string, newName: string): void {
    // 清除路径相关缓存（名称变化会影响路径）
    this.cacheManager.clearCache('pathCache');
    this.cacheManager.clearCache('departmentPathsCache');
    this.cacheManager.clearCache('sameNameDepartmentsCache');

    if (node.id.startsWith(NODE_ID_PREFIXES.PERSON)) {
      this.cacheManager.clearCache('departmentPersonsCache');
    }

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `已清除重命名节点相关的缓存: ${oldName} -> ${newName}`));
  }

  /**
   * 为更新的人员节点更新缓存
   */
  private static updateCacheForUpdatedPerson(personNodes: DepartmentCategory[]): void {
    // 清除人员相关的缓存
    this.cacheManager.clearCache('departmentPersonsCache');

    // 如果人员名称变化，也需要清除路径相关缓存
    this.cacheManager.clearCache('pathCache');
    this.cacheManager.clearCache('departmentPathsCache');

    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `已清除更新人员节点相关的缓存，影响节点数: ${personNodes.length}`));
  }

  /**
   * 深拷贝对象
   */
  private static deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }

  /**
   * 检查操作是否只影响结构而不影响计数
   * @param operation 操作类型
   * @returns 是否只是结构变化
   */
  public static isStructuralChangeOnly(operation: 'add' | 'delete' | 'rename' | 'move'): boolean {
    // 添加、删除、重命名、移动操作通常不会立即影响设备计数
    // 因为设备计数是基于设备的责任人和部门关系，而不是树结构本身
    return true;
  }

  /**
   * 获取操作影响的缓存类型
   * @param operation 操作类型
   * @param nodeType 节点类型
   * @returns 需要清除的缓存类型列表
   */
  public static getAffectedCacheTypes(
    operation: 'add' | 'delete' | 'rename' | 'move',
    nodeType: 'department' | 'person'
  ): string[] {
    const affectedCaches: string[] = [];

    // 所有操作都会影响路径缓存
    affectedCaches.push('pathCache', 'departmentPathsCache');

    // 部门操作会影响同名部门缓存
    if (nodeType === 'department') {
      affectedCaches.push('sameNameDepartmentsCache');
    }

    // 人员操作会影响部门人员缓存
    if (nodeType === 'person') {
      affectedCaches.push('departmentPersonsCache');
    }

    return affectedCaches;
  }
}
