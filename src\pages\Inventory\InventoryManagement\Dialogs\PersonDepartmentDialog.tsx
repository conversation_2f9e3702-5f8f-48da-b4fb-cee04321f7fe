import React, { useState, useEffect } from 'react';
import { PersonDetailInfo } from '../../../../types/inventory';
import DepartmentService from '../../../../services/Inventory/departmentService';
import DepartmentTreeSelect from '../../../../components/DepartmentTreeSelect';

interface PersonDepartmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  personId: string; // 人员节点ID，格式为 "person-{id}"
  personName: string; // 人员姓名
  onUpdate: () => void; // 更新完成后的回调
}

/**
 * 人员部门管理对话框
 * 用于管理人员的多部门关联
 */
const PersonDepartmentDialog: React.FC<PersonDepartmentDialogProps> = ({
  isOpen,
  onClose,
  personId,
  personName,
  onUpdate
}) => {
  const [personInfo, setPersonInfo] = useState<PersonDetailInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [departmentOptions, setDepartmentOptions] = useState<any[]>([]);

  // 当对话框打开时，获取人员信息和部门选项
  useEffect(() => {
    if (isOpen && personId) {
      loadPersonInfo();
      loadDepartmentOptions();
    }
  }, [isOpen, personId]);

  // 加载部门选项
  const loadDepartmentOptions = async () => {
    try {
      const departmentService = DepartmentService.getInstance();
      const categories = departmentService.getDepartmentCategories();

      // 转换为部门选择器需要的格式
      const options = categories.map(dept => ({
        id: dept.id,
        name: dept.name,
        children: dept.children?.filter(child => !child.id.startsWith('person-')).map(child => ({
          id: child.id,
          name: child.name,
          children: child.children?.filter(grandChild => !grandChild.id.startsWith('person-')).map(grandChild => ({
            id: grandChild.id,
            name: grandChild.name
          })) || []
        })) || []
      }));

      setDepartmentOptions(options);
    } catch (error) {
      console.error('加载部门选项失败:', error);
      setError('加载部门选项失败');
    }
  };

  // 加载人员信息
  const loadPersonInfo = async () => {
    // 从personId中提取数字ID（支持新旧格式）
    // 新格式：person-1-dept-2 -> 1
    // 旧格式：person-1 -> 1
    const match = personId.match(/^person-(\d+)/);
    const numericId = match ? parseInt(match[1], 10) : NaN;

    if (isNaN(numericId)) {
      setError('无效的人员ID');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const departmentService = DepartmentService.getInstance();

      // 使用统一的人员信息获取服务
      const person = await departmentService.getPersonInfo(numericId);

      if (person) {
        setPersonInfo(person);
      } else {
        setError('未找到人员信息');
      }
    } catch (error) {
      console.error('获取人员信息失败:', error);
      setError('获取人员信息失败');
    } finally {
      setIsLoading(false);
    }
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            管理人员部门关联
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isSaving}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* 加载状态 */}
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : personInfo ? (
          <div className="space-y-6">
            {/* 人员基本信息 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">人员信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">姓名：</span>
                  <span className="font-medium">{personInfo.user_name}</span>
                </div>
                {personInfo.alias && (
                  <div>
                    <span className="text-gray-600">备注：</span>
                    <span>{personInfo.alias}</span>
                  </div>
                )}
                {personInfo.mobile_number && (
                  <div>
                    <span className="text-gray-600">手机：</span>
                    <span>{personInfo.mobile_number}</span>
                  </div>
                )}
                <div>
                  <span className="text-gray-600">岗位密级：</span>
                  <span>{personInfo.position_security_level_text}</span>
                </div>
              </div>
            </div>

            {/* 部门关联信息 */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">所属部门</h4>
              {personInfo.departments && personInfo.departments.length > 0 ? (
                <div className="space-y-3">
                  {personInfo.departments.map((dept, index) => (
                    <div key={dept.id} className="flex items-center p-3 border border-gray-200 rounded-lg">
                      <span className="font-medium text-gray-900">
                        {dept.name}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  该人员暂未关联任何部门
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                disabled={isSaving}
              >
                关闭
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            未找到人员信息
          </div>
        )}
      </div>
    </div>
  );
};

export default PersonDepartmentDialog;
