/**
 * 设备相关工具函数
 */

import { InventoryItem } from '../types/inventory';

/**
 * 从设备ID中提取数字ID（现在ID已经是数字格式）
 * @param deviceIdString 设备ID字符串，如 "123"
 * @returns 提取的数字ID，如 123
 */
export const extractDeviceId = (deviceIdString: string): number => {
  if (!deviceIdString) return 0;

  // 直接转换为数字（现在ID已经是数字格式）
  const numericId = parseInt(deviceIdString, 10);
  return isNaN(numericId) ? 0 : numericId;
};

/**
 * 从设备列表中提取所有有效的数字ID
 * @param items 设备列表
 * @returns 数字ID数组
 */
export const extractDeviceIds = (items: InventoryItem[]): number[] => {
  return items
    .map(item => extractDeviceId(item.id))
    .filter(id => id > 0);
};

/**
 * 根据数字ID在设备列表中查找设备
 * @param items 设备列表
 * @param deviceId 数字ID
 * @returns 找到的设备或undefined
 */
export const findDeviceById = (items: InventoryItem[], deviceId: number): InventoryItem | undefined => {
  return items.find(item => extractDeviceId(item.id) === deviceId);
};

/**
 * 根据数字ID数组在设备列表中查找设备
 * @param items 设备列表
 * @param deviceIds 数字ID数组
 * @returns 找到的设备数组
 */
export const findDevicesByIds = (items: InventoryItem[], deviceIds: number[]): InventoryItem[] => {
  return items.filter(item => deviceIds.includes(extractDeviceId(item.id)));
};
