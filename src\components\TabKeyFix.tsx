import React, { useEffect } from 'react';

/**
 * Tab键修复组件
 * 专门解决m108 miniblink环境中Tab键打出空格的问题
 */
const TabKeyFix: React.FC = () => {
  useEffect(() => {
    console.log('🔧 Tab键修复组件已加载');

    // 创建一个非常早期的Tab键拦截器
    const earlyTabInterceptor = (e: KeyboardEvent) => {
      // 检测Tab键的多种方式
      const isTab = e.key === 'Tab' || e.keyCode === 9 || e.which === 9 || e.code === 'Tab';
      
      if (isTab) {
        console.log('🚫 早期Tab键拦截器触发');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // 手动处理Tab键导航
        handleManualTabNavigation(e);
        
        return false;
      }
    };

    // 在最早的阶段拦截Tab键
    document.addEventListener('keydown', earlyTabInterceptor, true);
    window.addEventListener('keydown', earlyTabInterceptor, true);
    
    // 也在keypress阶段拦截，防止字符输入
    const keypressInterceptor = (e: KeyboardEvent) => {
      const isTab = e.key === 'Tab' || e.keyCode === 9 || e.which === 9;
      if (isTab) {
        console.log('🚫 keypress阶段Tab键拦截');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        return false;
      }
    };
    
    document.addEventListener('keypress', keypressInterceptor, true);
    
    // 添加CSS样式确保Tab键不会产生可见字符
    const style = document.createElement('style');
    style.textContent = `
      /* 防止Tab键产生可见字符 */
      * {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      
      input, textarea, [contenteditable] {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
      }
      
      /* 确保Tab字符不可见 */
      *::before, *::after {
        content: none !important;
      }
    `;
    document.head.appendChild(style);

    console.log('🔧 Tab键修复器已激活');

    return () => {
      document.removeEventListener('keydown', earlyTabInterceptor, true);
      window.removeEventListener('keydown', earlyTabInterceptor, true);
      document.removeEventListener('keypress', keypressInterceptor, true);
      document.head.removeChild(style);
      console.log('🔧 Tab键修复器已清理');
    };
  }, []);

  // 手动处理Tab键导航
  const handleManualTabNavigation = (event: KeyboardEvent) => {
    console.log('🎯 手动处理Tab键导航');
    
    // 获取所有可聚焦元素
    const focusableSelector = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(',');
    
    const focusableElements = Array.from(
      document.querySelectorAll(focusableSelector)
    ) as HTMLElement[];
    
    // 过滤掉不可见的元素
    const visibleFocusableElements = focusableElements.filter(element => {
      const rect = element.getBoundingClientRect();
      const style = window.getComputedStyle(element);
      return (
        rect.width > 0 &&
        rect.height > 0 &&
        style.visibility !== 'hidden' &&
        style.display !== 'none' &&
        !element.hasAttribute('hidden')
      );
    });
    
    console.log(`🎯 找到 ${visibleFocusableElements.length} 个可聚焦元素`);
    
    if (visibleFocusableElements.length === 0) {
      console.log('⚠️ 没有找到可聚焦元素');
      return;
    }
    
    // 获取当前聚焦元素
    const activeElement = document.activeElement as HTMLElement;
    const currentIndex = visibleFocusableElements.indexOf(activeElement);
    
    console.log(`🎯 当前聚焦元素索引: ${currentIndex}`);
    
    let nextIndex: number;
    
    if (event.shiftKey) {
      // Shift+Tab: 向前导航
      nextIndex = currentIndex <= 0 ? visibleFocusableElements.length - 1 : currentIndex - 1;
      console.log(`🎯 Shift+Tab: 导航到索引 ${nextIndex}`);
    } else {
      // Tab: 向后导航
      nextIndex = currentIndex >= visibleFocusableElements.length - 1 ? 0 : currentIndex + 1;
      console.log(`🎯 Tab: 导航到索引 ${nextIndex}`);
    }
    
    const nextElement = visibleFocusableElements[nextIndex];
    if (nextElement) {
      try {
        nextElement.focus();
        
        // 滚动到元素位置
        nextElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'nearest'
        });
        
        console.log('✅ Tab导航成功:', nextElement);
        
        // 添加视觉反馈
        nextElement.style.outline = '2px solid #3b82f6';
        setTimeout(() => {
          nextElement.style.outline = '';
        }, 200);
        
      } catch (error) {
        console.error('❌ Tab导航失败:', error);
      }
    }
  };

  return null; // 这是一个无UI组件
};

export default TabKeyFix;
