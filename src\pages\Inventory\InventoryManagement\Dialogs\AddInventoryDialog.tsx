import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Alert, Spin } from 'antd';
import { InventoryItem, ExtFieldDefinition } from '../../../../types/inventory';
import InventoryForm from './InventoryForm';
import useDeviceService from '../../../../hooks/Inventory/useDeviceService';
import { useInventory } from '../../../../hooks/Inventory/useInventory';
import DialogBase from '../../../../components/ui/DialogBase';
import { useFormFocus } from '../../../../hooks/base/useFormFocus';
import TaskManager from '../../../../utils/taskManager';

interface AddInventoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: Omit<InventoryItem, 'id'>) => Promise<void>;
  initialCategoryInfo: {
    type: string;
    name: string;
    parentCategory?: string;
    department?: string;
    responsible?: string;
    fromContextMenu?: boolean; // 是否来自右键菜单
  } | null;
  extFields?: ExtFieldDefinition[];  // 扩展字段定义
  extFieldValues?: Record<string, any>;  // 扩展字段值
  onExtFieldChange?: (key: string, value: any) => void;  // 扩展字段变更回调
  disableTypeSelection?: boolean; // 是否禁用设备类型选择，用于从树状图联动添加时
  disableDepartmentSelection?: boolean; // 是否禁用部门选择，用于从树状图联动添加时
  disableResponsibleSelection?: boolean; // 是否禁用责任人选择，用于从树状图联动添加时
}

const AddInventoryDialog: React.FC<AddInventoryDialogProps> = ({
  isOpen,
  onClose,
  onAdd,
  initialCategoryInfo = null,
  extFields = [],
  extFieldValues = {},
  onExtFieldChange,
  disableTypeSelection = false,
  disableDepartmentSelection = false,
  disableResponsibleSelection = false
}) => {
  // 引入设备服务Hook获取加载状态
  const { isLoading, progress } = useDeviceService();
  const { formExtFields, clearFormExtFields } = useInventory();
  const [apiError, setApiError] = useState<string | null>(null);

  // 获取本机信息相关状态
  const [isCollectingInfo, setIsCollectingInfo] = useState(false);
  const [collectInfoError, setCollectInfoError] = useState<string | null>(null);

  // 存储获取到的系统信息，用于在扩展字段加载后填充
  const [systemInfo, setSystemInfo] = useState<any>(null);

  // 使用表单焦点管理
  const { formRef, focusFirstError } = useFormFocus({
    autoFocusOnError: true,
    scrollToError: true
  });

  // 在对话框关闭时清除所有状态
  const handleClose = () => {
    // 清除表单扩展字段
    clearFormExtFields();
    // 清除系统信息状态
    setSystemInfo(null);
    // 清除错误状态
    setApiError(null);
    setCollectInfoError(null);
    // 调用原有的关闭函数
    onClose();
  };

  // 处理表单提交
  const handleSubmit = async (data: Omit<InventoryItem, 'id'>) => {
    try {
      setApiError(null);

      // 使用原有的onAdd方法更新前端状态
      // 这个方法会调用inventoryService.addInventoryItem，已经包含了向后端发送请求的逻辑
      await onAdd(data);

      // 成功添加后关闭对话框
      onClose();
    } catch (err) {
      console.error('添加设备台账失败:', err);
      setApiError(err instanceof Error ? err.message : '添加设备失败，请稍后重试');
    }
  };

  // 获取本机信息
  const handleCollectSystemInfo = async () => {
    try {
      setIsCollectingInfo(true);
      setCollectInfoError(null);

      console.log('开始获取本机信息...');

      // 获取TaskManager实例
      const taskManager = TaskManager.getInstance();

      // 调用SystemInfoDLL.dll的CollectInventoryInfo函数
      const taskId = await taskManager.submitTask('SystemInfoDLL', 'CollectInventoryInfo', {});
      console.log('获取本机信息任务提交成功，任务ID:', taskId);

      // 等待任务完成
      const result = await taskManager.waitForTaskResult(taskId);
      console.log('获取本机信息结果:', result);

      // 解析返回的数据
      if (result && result.data) {
        const sysInfo = result.data;
        console.log('解析到的系统信息:', sysInfo);

        // 存储系统信息
        setSystemInfo(sysInfo);

        // 立即填充基础字段
        const formUpdates: Partial<InventoryItem> = {};

        if (sysInfo.computerName) {
          formUpdates.name = sysInfo.computerName;
        }

        if (sysInfo.model) {
          formUpdates.model = sysInfo.model;
        }

        if (sysInfo.computerType) {
          const computerType = sysInfo.computerType;
          if (computerType === '台式机' || computerType === '便携机') {
            formUpdates.type = computerType;
          }
        }

        // 直接更新基础字段 - 通过更新initialFormData来实现
        if (Object.keys(formUpdates).length > 0) {
          setSystemInfo(prev => ({ ...prev, formUpdates }));
          console.log('基础字段将通过initialFormData更新:', formUpdates);
        }

        console.log('本机信息获取完成');
      } else {
        throw new Error('未获取到有效的系统信息');
      }

    } catch (error) {
      console.error('获取本机信息失败:', error);
      setCollectInfoError(error instanceof Error ? error.message : '获取本机信息失败');
    } finally {
      setIsCollectingInfo(false);
    }
  };

  // 准备初始表单数据，融合当前选中的分类信息和系统信息
  const initialFormData = useMemo(() => {
    const baseData = initialCategoryInfo ? {
      type: initialCategoryInfo.type || '',
      name: initialCategoryInfo.name || '',
      department: initialCategoryInfo.department || '',
      responsible: initialCategoryInfo.responsible || ''
    } : {};

    // 如果有系统信息，合并到表单数据中
    if (systemInfo?.formUpdates) {
      return { ...baseData, ...systemInfo.formUpdates };
    }

    return baseData;
  }, [initialCategoryInfo, systemInfo]);

  // 只在对话框打开时记录日志，避免每次渲染都输出
  useEffect(() => {
    if (isOpen) {
      console.log('AddInventoryDialog接收到的initialCategoryInfo:', initialCategoryInfo);
      console.log('AddInventoryDialog生成的initialFormData:', initialFormData);
    }
  }, [isOpen, initialCategoryInfo, initialFormData]); // 现在可以安全地包含initialFormData，因为使用了useMemo

  // 对话框打开时重置状态
  useEffect(() => {
    if (isOpen) {
      // 确保每次打开对话框时状态都是干净的
      setSystemInfo(null);
      setApiError(null);
      setCollectInfoError(null);
      setIsCollectingInfo(false);
    }
  }, [isOpen]);

  // 监听扩展字段加载完成，自动填充系统信息
  useEffect(() => {
    if (systemInfo && formExtFields.length > 0) {
      console.log('扩展字段已加载，开始填充系统信息');
      fillExtFieldsFromSystemInfo();
    }
  }, [systemInfo, formExtFields]);

  // 添加一个函数来填充扩展字段
  const fillExtFieldsFromSystemInfo = () => {
    if (!systemInfo || formExtFields.length === 0) {
      console.log('无法填充扩展字段：', { hasSystemInfo: !!systemInfo, extFieldsCount: formExtFields.length });
      return;
    }

    console.log('开始填充扩展字段，系统信息:', systemInfo);
    console.log('可用扩展字段:', formExtFields.map(f => ({ key: f.key, title: f.title })));

    const updates: Record<string, any> = {};

    // 简单直接的字段映射
    formExtFields.forEach(field => {
      const title = field.title.toLowerCase();
      const key = field.key.toLowerCase();

      // 操作系统
      if ((title.includes('操作系统') && !title.includes('安装') && !title.includes('日期')) ||
          key === 'os' || key === 'system') {
        if (systemInfo.version) {
          updates[field.key] = systemInfo.version;
        }
      }
      // 安装日期
      else if (title.includes('安装日期') || (field.type === 'date' && title.includes('安装'))) {
        if (systemInfo.installDate) {
          try {
            const date = new Date(systemInfo.installDate);
            updates[field.key] = date.toISOString().slice(0, 19);
          } catch {
            updates[field.key] = systemInfo.installDate;
          }
        }
      }
      // 硬盘序列号
      else if (title.includes('硬盘') || title.includes('序列号') || key.includes('disk') || key.includes('serial')) {
        if (systemInfo.diskSerialNumber) {
          updates[field.key] = systemInfo.diskSerialNumber;
        }
      }
      // MAC地址
      else if (title.includes('mac') || key.includes('mac')) {
        if (systemInfo.macAddress) {
          updates[field.key] = systemInfo.macAddress;
        }
      }
      // IP地址
      else if (title.includes('ip') || key.includes('ip')) {
        if (systemInfo.ipAddresses) {
          updates[field.key] = systemInfo.ipAddresses;
        }
      }
    });

    // 填充数据
    Object.keys(updates).forEach(key => {
      onExtFieldChange(key, updates[key]);
      console.log(`填充扩展字段 ${key}: ${updates[key]}`);
    });

    console.log('扩展字段填充完成');
  };

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleClose}
      width="100%"
      maxWidth="64rem"
      maxHeight="95vh"
      autoFocus={true}
      restoreFocus={true}
      closeOnOverlayClick={false}
    >
      <div className="flex flex-col h-full max-h-[95vh]">
        {/* 对话框标题 - 固定高度 */}
        <div className="flex-shrink-0 flex justify-between items-center px-4 sm:px-6 py-3 border-b">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800">添加设备台账</h2>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="text-gray-500 hover:text-gray-700 focus-ring rounded-full p-1 flex-shrink-0"
            aria-label="关闭对话框"
          >
            <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 - 可滚动区域 */}
        <div className="flex-1 overflow-y-auto min-h-0 px-4 sm:px-6 py-4">{/* 响应式内边距 */}

          {/* 错误提示 */}
          {apiError && (
            <Alert
              message="错误"
              description={apiError}
              type="error"
              showIcon
              className="mb-3 sm:mb-4"
              closable
              onClose={() => setApiError(null)}
            />
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="mb-3 sm:mb-4 flex items-center space-x-2">
              <Spin />
              <span className="text-blue-600 text-sm sm:text-base">正在处理请求 ({Math.round(progress)}%)</span>
            </div>
          )}

          {/* 表单内容区域 - 不包含按钮 */}
          <div ref={formRef}>
            <InventoryForm
              initialData={initialFormData}
              extFields={formExtFields}
              extFieldValues={extFieldValues}
              onExtFieldChange={onExtFieldChange}
              onSubmit={handleSubmit}
              onCancel={handleClose}
              disabled={isLoading}
              disableTypeSelection={disableTypeSelection && !!initialCategoryInfo?.type}
              disableDepartmentSelection={disableDepartmentSelection && !!initialCategoryInfo?.department}
              disableResponsibleSelection={disableResponsibleSelection && !!initialCategoryInfo?.responsible}
              hideButtons={true} // 新增属性，隐藏表单内的按钮
            />
          </div>
        </div>

        {/* 对话框底部按钮 - 固定在底部，与导出菜单保持一致 */}
        <div className="flex-shrink-0 px-3 sm:px-4 py-2.5 border-t bg-white flex justify-between items-center">
          {/* 左侧：获取本机信息按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleCollectSystemInfo}
              className="px-6 py-3 border border-blue-300 rounded text-base font-medium text-blue-600 hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              disabled={isLoading || isCollectingInfo}
            >
              {isCollectingInfo ? (
                <>
                  <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>获取中...</span>
                </>
              ) : (
                <>
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span>获取本机信息</span>
                </>
              )}
            </button>
            {collectInfoError && (
              <span className="text-xs text-red-600">{collectInfoError}</span>
            )}
          </div>

          {/* 右侧：取消和保存按钮 */}
          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              className="px-6 py-3 border border-gray-300 rounded text-base font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={isLoading || isCollectingInfo}
            >
              取消
            </button>
            <button
              onClick={() => {
                // 触发表单提交 - 使用ref确保选择正确的表单
                const form = formRef.current?.querySelector('form');
                if (form) {
                  form.requestSubmit();
                }
              }}
              className="px-6 py-3 bg-blue-600 text-white rounded text-base font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              disabled={isLoading || isCollectingInfo}
            >
              保存
            </button>
          </div>
        </div>
      </div>
    </DialogBase>
  );
};

export default AddInventoryDialog;