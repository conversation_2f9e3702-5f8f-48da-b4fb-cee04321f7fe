import { useCallback } from 'react';
import { getKeyboardManager } from './useKeyboardManager';
import { getFocusManager } from './useFocusManager';

interface Managers {
  keyboardManager: ReturnType<typeof getKeyboardManager>;
  focusManager: ReturnType<typeof getFocusManager>;
}

/**
 * 统一的管理器获取Hook
 * 提供统一的错误处理和管理器获取逻辑
 */
export function useManagers() {
  // 获取管理器实例（延迟获取，避免初始化顺序问题）
  const getManagers = useCallback((): Managers | null => {
    try {
      return {
        keyboardManager: getKeyboardManager(),
        focusManager: getFocusManager()
      };
    } catch (error) {
      console.warn('管理器未初始化，功能将降级处理:', error);
      return null;
    }
  }, []);

  return { getManagers };
}

/**
 * 带错误处理的管理器获取Hook
 * 提供更详细的错误信息和调试支持
 */
export function useManagersWithDebug(debugId: string = 'unknown') {
  const getManagers = useCallback((): Managers | null => {
    try {
      return {
        keyboardManager: getKeyboardManager(),
        focusManager: getFocusManager()
      };
    } catch (error) {
      console.warn(`${debugId}: 管理器未初始化，功能将降级处理:`, error);
      return null;
    }
  }, [debugId]);

  return { getManagers };
}
