# 巡检任务时间选择器秒级精度升级

## 📋 功能概述

将巡检任务页面中的时间选择器升级为支持精确到秒的时间选择功能，从原来的`HH:mm`格式升级为`HH:mm:ss`格式，提供更精确的时间控制。

## 🎯 升级范围

### **已升级的页面**
- ✅ **巡检任务创建页面**：`src/pages/Inspection/CreateTaskDialog.tsx`
- ✅ **巡检任务编辑页面**：`src/components/Inspection/TaskEditDialog.tsx`

### **核心组件升级**
- ✅ **DatePicker组件**：`src/components/Common/DatePicker.tsx`
- ✅ **新增showSeconds属性**：控制是否显示秒级选择
- ✅ **向后兼容**：不影响其他页面的现有功能

## 🔧 技术实现

### **1. DatePicker组件扩展**

#### **接口扩展**
```typescript
interface DatePickerProps {
  value?: string;
  onChange: (date: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: boolean;
  showTime?: boolean;        // 是否显示时间选择
  showSeconds?: boolean;     // 🔥 新增：是否显示秒选择
}
```

#### **时间状态管理**
```typescript
// 扩展时间状态，支持秒
const [selectedTime, setSelectedTime] = useState(() => {
  if (value && showTime) {
    const date = new Date(value);
    return { 
      hours: date.getHours(), 
      minutes: date.getMinutes(),
      seconds: showSeconds ? date.getSeconds() : 0  // 🔥 新增秒支持
    };
  }
  return { hours: 9, minutes: 0, seconds: 0 };
});
```

#### **格式化函数升级**
```typescript
// 支持秒级格式化
const formatDate = useCallback((date: Date, time?: { hours: number; minutes: number; seconds?: number }, forDisplay = false): string => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  if (forDisplay) {
    const baseStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    if (showTime && time) {
      let timeStr = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
      if (showSeconds && time.seconds !== undefined) {
        timeStr += `:${String(time.seconds).padStart(2, '0')}`;  // 🔥 秒级显示
      }
      return `${baseStr} ${timeStr}`;
    }
    return baseStr;
  }

  const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  if (showTime && time) {
    let timeStr = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
    if (showSeconds && time.seconds !== undefined) {
      timeStr += `:${String(time.seconds).padStart(2, '0')}`;  // 🔥 秒级存储
    }
    return `${dateStr}T${timeStr}`;
  }
  return dateStr;
}, [showTime, showSeconds]);
```

### **2. 用户界面设计**

#### **时间选择器布局**
```typescript
{/* 时间选择器 - 动态宽度 */}
{showTime && (
  <div className={`${showSeconds ? 'w-48' : 'w-32'} border-l border-gray-200 bg-gray-50 flex`}>
    <TimeSelector type="小时" max={24} selected={selectedTime.hours} onSelect={(hours) => updateTime(hours, selectedTime.minutes, selectedTime.seconds)} />
    <TimeSelector type="分钟" max={60} selected={selectedTime.minutes} onSelect={(minutes) => updateTime(selectedTime.hours, minutes, selectedTime.seconds)} />
    {showSeconds && (
      <TimeSelector type="秒" max={60} selected={selectedTime.seconds} onSelect={(seconds) => updateTime(selectedTime.hours, selectedTime.minutes, seconds)} />
    )}
  </div>
)}
```

#### **界面布局特点**
- **动态宽度**：根据是否显示秒调整选择器宽度（32px → 48px）
- **三列布局**：小时 | 分钟 | 秒，每列独立滚动选择
- **视觉一致性**：秒选择器与小时、分钟选择器样式完全一致
- **直观操作**：点击选择，实时预览格式化结果

### **3. 巡检任务页面集成**

#### **创建任务页面**
```typescript
<DatePicker
  value={formData.startDate}
  onChange={(date) => setFormData(prev => ({ ...prev, startDate: date }))}
  placeholder="请选择开始日期和时间"
  disabled={isLoading}
  error={!!errors.startDate}
  showTime={true}
  showSeconds={true}  // 🔥 启用秒级选择
  className="text-sm"
/>
```

#### **编辑任务页面**
```typescript
<DatePicker
  value={formData.start_date}
  onChange={(date) => setFormData(prev => ({ ...prev, start_date: date }))}
  placeholder="请选择开始日期和时间"
  disabled={isLoading}
  showTime={true}
  showSeconds={true}  // 🔥 启用秒级选择
  className="text-sm"
/>
```

## 📱 用户体验改进

### **升级前（分钟精度）**
```
┌─────────────────────────────────────┐
│ 开始时间                           │
├─────────────────────────────────────┤
│ 2024-01-15 14:30            ▼     │
└─────────────────────────────────────┘

时间选择器：
┌─────────┬─────────┐
│  小时   │  分钟   │
├─────────┼─────────┤
│   14    │   30    │
└─────────┴─────────┘
```

### **升级后（秒级精度）**
```
┌─────────────────────────────────────┐
│ 开始时间                           │
├─────────────────────────────────────┤
│ 2024-01-15 14:30:45         ▼     │
└─────────────────────────────────────┘

时间选择器：
┌─────────┬─────────┬─────────┐
│  小时   │  分钟   │   秒    │
├─────────┼─────────┼─────────┤
│   14    │   30    │   45    │
└─────────┴─────────┴─────────┘
```

## 🔍 格式规范

### **显示格式**
- **完整格式**：`YYYY-MM-DD HH:mm:ss`
- **示例**：`2024-01-15 14:30:45`
- **分隔符**：日期使用`-`，时间使用`:`，日期时间之间使用空格

### **存储格式**
- **ISO格式**：`YYYY-MM-DDTHH:mm:ss`
- **示例**：`2024-01-15T14:30:45`
- **兼容性**：与现有API和数据库格式完全兼容

### **向后兼容**
- **不启用showSeconds**：继续使用`HH:mm`格式
- **现有数据**：自动解析并补充秒为00
- **API兼容**：支持新旧格式的自动转换

## 🧪 测试场景

### **功能测试**
1. ✅ **秒级选择**：验证可以选择0-59秒
2. ✅ **格式显示**：确认显示格式为`YYYY-MM-DD HH:mm:ss`
3. ✅ **数据保存**：验证保存的数据包含秒信息
4. ✅ **数据加载**：确认编辑时正确显示秒信息

### **兼容性测试**
1. ✅ **向后兼容**：不启用showSeconds的页面正常工作
2. ✅ **数据兼容**：旧格式数据正确解析和显示
3. ✅ **API兼容**：新旧格式数据正确处理

### **用户体验测试**
1. ✅ **界面响应**：时间选择器操作流畅
2. ✅ **视觉一致**：三列选择器样式统一
3. ✅ **实时预览**：选择过程中实时更新显示

## 🚀 扩展性

### **配置化支持**
- **页面级配置**：每个页面可独立决定是否启用秒级精度
- **全局配置**：可添加全局开关控制默认行为
- **用户偏好**：未来可支持用户自定义时间精度偏好

### **国际化支持**
- **时间格式**：支持不同地区的时间显示格式
- **语言本地化**：时间选择器标签支持多语言
- **时区支持**：可扩展支持时区选择

## 📝 维护说明

### **关键文件**
- **核心组件**：`src/components/Common/DatePicker.tsx`
- **巡检创建**：`src/pages/Inspection/CreateTaskDialog.tsx`
- **巡检编辑**：`src/components/Inspection/TaskEditDialog.tsx`

### **注意事项**
- 确保showSeconds只在showTime为true时生效
- 维护格式化函数的一致性
- 保持与后端API的数据格式同步

### **性能考虑**
- 秒选择器按需渲染，不影响页面性能
- 时间更新优化，避免不必要的重新渲染
- 内存管理良好，组件卸载时正确清理
