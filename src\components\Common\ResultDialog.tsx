import React from 'react';
import { CheckCircle, XCircle, AlertCircle, X, Smartphone, Download } from 'lucide-react';

interface ResultDialogProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'success' | 'error' | 'warning';
  title: string;
  message?: string;
  details?: Array<{
    label: string;
    value: string | number;
  }>;
  icon?: 'import' | 'export' | 'default';
}

/**
 * 结果弹窗组件
 * 用于显示操作结果，支持成功、失败、警告三种类型
 */
const ResultDialog: React.FC<ResultDialogProps> = ({
  isOpen,
  onClose,
  type,
  title,
  message,
  details,
  icon = 'default'
}) => {
  if (!isOpen) return null;

  // 获取类型样式
  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          iconColor: 'text-green-600',
          titleColor: 'text-green-800',
          buttonColor: 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
        };
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          iconColor: 'text-red-600',
          titleColor: 'text-red-800',
          buttonColor: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          iconColor: 'text-yellow-600',
          titleColor: 'text-yellow-800',
          buttonColor: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
        };
      default:
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          iconColor: 'text-blue-600',
          titleColor: 'text-blue-800',
          buttonColor: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
        };
    }
  };

  // 获取图标
  const getIcon = () => {
    const styles = getTypeStyles();
    const iconClass = `h-6 w-6 ${styles.iconColor}`;

    if (icon === 'import') {
      return <Download className={iconClass} />;
    } else if (icon === 'export') {
      return <Smartphone className={iconClass} />;
    }

    switch (type) {
      case 'success':
        return <CheckCircle className={iconClass} />;
      case 'error':
        return <XCircle className={iconClass} />;
      case 'warning':
        return <AlertCircle className={iconClass} />;
      default:
        return <CheckCircle className={iconClass} />;
    }
  };

  const styles = getTypeStyles();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded shadow-lg max-w-md w-full mx-4 max-h-96 overflow-hidden">
        {/* 对话框头部 */}
        <div className={`flex items-center justify-between p-4 border-b ${styles.borderColor} ${styles.bgColor}`}>
          <div className="flex items-center">
            <div className="flex-shrink-0 w-8 h-8 bg-white rounded-full flex items-center justify-center">
              {getIcon()}
            </div>
            <div className="ml-3">
              <h3 className={`text-base font-medium ${styles.titleColor}`}>
                {title}
              </h3>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-4 max-h-64 overflow-y-auto">
          {message && (
            <div className="text-sm text-gray-700 mb-3">
              {message}
            </div>
          )}

          {details && details.length > 0 && (
            <div className={`rounded p-3 border ${styles.borderColor} ${styles.bgColor}`}>
              <div className="space-y-2">
                {details.map((detail, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 mr-3">
                      {detail.label}：
                    </span>
                    <span className="text-sm text-gray-900">
                      {detail.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 对话框底部 */}
        <div className="flex items-center justify-end p-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className={`px-4 py-2 text-sm font-medium text-white rounded ${styles.buttonColor}`}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResultDialog;
