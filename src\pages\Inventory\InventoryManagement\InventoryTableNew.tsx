/**
 * 新版台账表格组件 - 基于BaseTable重构
 * 统一表格实现，消除代码重复
 */

import React, { useMemo, useCallback, useRef } from 'react';
import { Pencil, Trash, Tag } from 'lucide-react';
import BaseTable from '../../../components/ui/BaseTable';
import { InventoryItem, FieldDefinition } from '../../../types/inventory';
import { BaseTableColumn, BaseTableRef } from '../../../types/table';
import InspectionTooltip from '../../../components/InspectionTooltip';
import { isTimeField, formatTimeValue } from '../../../utils/fieldUtils';
import { useSimpleTable } from '../../../hooks/useBaseTable';
import HardDriveSerialDisplay from '../../../components/ui/HardDriveSerialDisplay';

interface InventoryTableProps {
  items: InventoryItem[];
  columnVisibility: Record<string, boolean>;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onSelect: (id: string) => void;
  onSelectAll: () => void;
  isSelected: (id: string) => boolean;
  isAllSelected: () => boolean;
  someSelected: () => boolean;
  tableFields?: FieldDefinition[];
  fieldDictionary?: Record<string, string>;
  isLoading?: boolean;
  onRfidManagement?: () => void;
  rememberPosition?: boolean;
  initialPageIndex?: number;
  onPageChange?: (pageIndex: number) => void;
  lastOperatedItemId?: string;
}

/**
 * 新版台账表格组件
 */
const InventoryTableNew: React.FC<InventoryTableProps> = ({
  items,
  columnVisibility,
  onEdit,
  onDelete,
  onSelect,
  onSelectAll,
  isSelected,
  isAllSelected,
  someSelected,
  tableFields = [],
  fieldDictionary = {},
  isLoading = false,
  onRfidManagement,
  rememberPosition = true,
  initialPageIndex = 0,
  onPageChange,
  lastOperatedItemId
}) => {
  const tableRef = useRef<BaseTableRef>(null);
  
  // 使用表格Hook管理状态
  const tableState = useSimpleTable(items, 'id');
  
  // 转换字段定义为BaseTable列格式
  const columns = useMemo<BaseTableColumn<InventoryItem>[]>(() => {
    const cols: BaseTableColumn<InventoryItem>[] = [];
    
    tableFields.forEach(field => {
      if (field.key === 'select' || field.key === 'actions') {
        return; // 这些列由BaseTable自动处理
      }
      
      // 扩展字段处理
      if (field.key.startsWith('ext_')) {
        const fieldKey = field.key.substring(4);
        cols.push({
          key: field.key,
          title: field.title,
          width: field.width || 150,
          sortable: field.sortable !== false,
          render: (value, record) => {
            const extValue = record.extendedFields?.[fieldKey];

            // 硬盘序列号字段特殊处理
            if (field.title === '硬盘序列号' || fieldKey === 'hard_drive_serial' || field.title.includes('硬盘序列号')) {
              return <HardDriveSerialDisplay value={extValue || ''} />;
            }

            // 时间字段格式化 - 扩展字段显示时分秒
            if ((isTimeField(field.title) || isTimeField(fieldKey) || field.type === 'date') && extValue) {
              return formatTimeValue(extValue, true, true); // showTime=true, showSeconds=true
            }

            // 对于不可编辑的下拉选择字段，需要将ID转换为显示文本
            if (extValue && field.editMode === 'select_only' && field.options) {
              const selectedOption = field.options.find(opt => opt.code === extValue);
              return selectedOption ? selectedOption.value : extValue;
            }

            return extValue || '';
          }
        });
        return;
      }
      
      // 标准字段处理
      cols.push({
        key: field.key,
        title: field.title,
        width: field.width || 150,
        sortable: field.sortable !== false,
        render: (value, record) => {
          // 巡检状态特殊渲染
          if (field.key === 'inspectionStatus') {
            if (!value || value.trim() === '') {
              return '';
            }
            
            const statusBadge = (
              <span
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  value === '正常'
                    ? 'bg-green-50 text-green-700 border border-green-200'
                    : value === '异常'
                    ? 'bg-red-50 text-red-700 border border-red-200'
                    : value === '未知'
                    ? 'bg-gray-50 text-gray-700 border border-gray-200'
                    : 'bg-gray-50 text-gray-700 border border-gray-200'
                }`}
              >
                {value}
              </span>
            );
            
            // 异常状态添加悬浮提示
            if (value === '异常' && record.inspection_status === 1) {
              const taskInfo = record.inspection_task_info || {
                task_name: '',
                inspection_start_date: 0
              };
              
              return (
                <InspectionTooltip inspectionTaskInfo={taskInfo}>
                  {statusBadge}
                </InspectionTooltip>
              );
            }
            
            return statusBadge;
          }

          // 基础字段时间格式化 - 只显示日期
          if (field.key === 'startTime' && value) {
            return formatTimeValue(value, false); // showTime=false，只显示日期
          }

          return value;
        }
      });
    });
    
    // 添加操作列
    cols.push({
      key: 'actions',
      title: '操作',
      width: 80,
      align: 'center',
      sortable: false,
      fixed: 'right',
      render: (_, record) => (
        <div className="flex justify-center space-x-1">
          <button
            onClick={() => onEdit(record.id)}
            className="p-0.5 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
            title="编辑"
            disabled={isLoading}
          >
            <Pencil className="h-4 w-4" />
          </button>
          <button
            onClick={() => onDelete(record.id)}
            className="p-0.5 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed"
            title="删除"
            disabled={isLoading}
          >
            <Trash className="h-4 w-4" />
          </button>
        </div>
      )
    });
    
    return cols;
  }, [tableFields, fieldDictionary, onEdit, onDelete, isLoading]);
  
  // 处理行选择
  const handleRowSelection = useCallback((selectedKeys: string[], selectedRows: InventoryItem[]) => {
    // 同步选择状态到父组件
    const currentSelected = items.filter(item => isSelected(item.id)).map(item => item.id);
    
    // 找出需要切换的项目
    const toSelect = selectedKeys.filter(key => !currentSelected.includes(key));
    const toDeselect = currentSelected.filter(key => !selectedKeys.includes(key));
    
    // 执行切换
    [...toSelect, ...toDeselect].forEach(id => onSelect(id));
  }, [items, isSelected, onSelect]);
  
  // 处理页码变化
  const handlePageChange = useCallback((pageIndex: number) => {
    onPageChange?.(pageIndex);
  }, [onPageChange]);
  
  // 处理双击编辑
  const handleRowDoubleClick = useCallback((record: InventoryItem) => {
    onEdit(record.id);
  }, [onEdit]);
  
  return (
    <div className="flex flex-col h-full">
      {/* 提示栏 */}
      <div className="px-4 bg-gray-50 text-gray-600 text-sm border-b flex items-center justify-between" 
           style={{ paddingTop: '4px', paddingBottom: '4px' }}>
        <span>双击表格内容单元格可以快速编辑该条记录（勾选列和操作列除外）</span>
        <div className="flex items-center">
          <button
            className="flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 hover:text-blue-800 hover:from-blue-100 hover:to-indigo-100 border border-blue-200 hover:border-blue-300 rounded-md shadow-sm hover:shadow-md transition-colors duration-100"
            onClick={onRfidManagement}
          >
            <Tag className="h-5 w-5" />
            <span className="text-sm font-medium">RFID码管理</span>
          </button>
        </div>
      </div>
      
      {/* 表格主体 */}
      <BaseTable
        ref={tableRef}
        data={items}
        columns={columns}
        features={{
          sorting: true,
          pagination: true,
          selection: true,
          positionMemory: rememberPosition,
        }}
        pagination={{
          pageIndex: initialPageIndex,
          pageSize: 10,
          total: items.length,
          pageSizeOptions: [10, 20, 50, 100],
          showQuickJumper: true,
          showTotal: true,
        }}
        selection={{
          selectedRowKeys: items.filter(item => isSelected(item.id)).map(item => item.id),
          onChange: handleRowSelection,
          columnWidth: 40,
        }}
        loading={{
          loading: isLoading,
          loadingText: '正在加载数据...',
        }}
        empty={{
          emptyText: '暂无设备数据',
        }}
        events={{
          onRowDoubleClick: handleRowDoubleClick,
        }}
        style={{
          size: 'middle',
          bordered: true,
          className: 'inventory-table',
        }}
        scroll={{
          x: 1200,
          y: '100%',
        }}
        rowKey="id"
      />
    </div>
  );
};

export default InventoryTableNew;
