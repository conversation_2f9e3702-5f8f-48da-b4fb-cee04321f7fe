import React from 'react';
import DialogBase from '../../../../components/ui/DialogBase';
import { AlertTriangle } from 'lucide-react';

interface DepartmentRequiredDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

/**
 * 部门必需提示对话框
 * 当用户尝试添加设备台账但部门分类树为空时显示
 */
const DepartmentRequiredDialog: React.FC<DepartmentRequiredDialogProps> = ({
  isOpen,
  onClose,
  onConfirm
}) => {
  // 处理确认按钮点击
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={onClose}
      width="100%"
      maxWidth="28rem"
      autoFocus={true}
      restoreFocus={true}
      closeOnOverlayClick={false}
    >
      <div className="flex flex-col">
        {/* 对话框标题 */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
            <h2 className="text-lg font-semibold text-gray-800">提示</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus-ring rounded-full p-1"
            aria-label="关闭对话框"
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="px-6 py-4">
          <div className="flex items-start">
            <AlertTriangle className="h-6 w-6 text-amber-500 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-gray-700 text-sm leading-relaxed">
                请先建立部门和人员后再添加设备台账
              </p>
              <p className="text-gray-500 text-xs mt-2">
                系统需要部门和人员信息来管理设备的归属关系
              </p>
            </div>
          </div>
        </div>

        {/* 对话框底部按钮 */}
        <div className="flex justify-end px-6 py-4 border-t bg-gray-50">
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors focus-ring"
          >
            确认
          </button>
        </div>
      </div>
    </DialogBase>
  );
};

export default DepartmentRequiredDialog;
