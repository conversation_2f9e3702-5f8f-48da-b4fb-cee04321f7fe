// 设备台账项目接口
export interface InventoryItem {
  id: string;          // 序号，原始数字ID
  name: string;        // 设备名称
  type: string;        // 设备类型（二级分类）
  parentCategory?: string; // 一级分类
  status: string;      // 使用情况
  manufacturer: string;// 厂商
  model: string;       // 型号
  department: string;  // 部门
  responsible: string; // 责任人
  location: string;    // 放置地点
  startTime: string;   // 启用时间
  securityCode: string;// 保密编号
  securityLevel: string;// 密级
  purpose: string;     // 用途
  securityRfid?: string;    // 安全码
  inspectionStatus?: string; // 巡检状态：正常/异常

  // 巡检相关字段
  inspection_status?: number;        // 巡检状态（0=正常，1=异常）
  inspection_status_text?: string;   // 巡检状态文本描述
  inspection_task_info?: {           // 巡检任务详细信息（仅当 inspection_status=1 时返回）
    task_name: string;               // 巡检任务名称
    inspection_start_date: number;   // 巡检开始时间戳
  };

  // 分类相关ID
  subCategoryId?: number;   // 二级分类ID
  parentCategoryId?: number; // 一级分类ID

  // 扩展字段
  extendedFields?: Record<string, any>; // 扩展字段数据

  // 原始数据
  rawData?: any;       // 原始数据

  [key: string]: any;  // 允许任意额外字段
}

// 字段定义接口
export interface FieldDefinition {
  key: string;        // 字段键名
  title: string;      // 字段中文名称
  sortable?: boolean; // 是否可排序
  width?: number;     // 宽度
  fixed?: boolean;    // 是否固定列
  titleStyle?: React.CSSProperties; // 标题样式属性
}

// 设备分类项目接口
export interface DeviceCategory {
  id: string;
  name: string;
  count: number;
  children?: DeviceCategory[];
  isNew?: boolean; // 新添加的标志
  originalId?: number | string; // 原始 ID
  parentId?: number | string; // 父级 ID
  customIcon?: string; // 自定义图标类型
}

// 扩展字段编辑模式枚举
export enum ExtFieldEditMode {
  Editable = 'editable',      // 可编辑
  SelectOnly = 'select_only', // 不可编辑（仅下拉选择）
  Required = 'required'       // 必选
}

// 扩展字段定义接口
export interface ExtFieldDefinition {
  key: string;        // 字段键名
  title: string;      // 字段中文名称
  type: string;       // 字段类型：text, number, select, date等
  required: boolean;  // 是否必填
  editMode?: ExtFieldEditMode; // 编辑模式
  options?: {         // 选项（用于select类型或不可编辑模式）
    code: string;
    value: string;
  }[];
  defaultValue?: any; // 默认值
}

// 树节点信息接口
export interface TreeSelectedNode {
  id: string;           // 节点ID
  name: string;         // 节点名称
  type: 'device' | 'department'; // 节点类型：设备分类或部门分类
  level: number;        // 节点层级：0-根节点，1-一级分类，2-二级分类
  parentId?: string;    // 父节点ID
  parentName?: string;  // 父节点名称
}

// 人员详细信息接口
export interface PersonDetailInfo {
  id: number;                    // 人员ID
  user_name: string;             // 姓名
  alias?: string;                // 备注
  mobile_number?: string;        // 联系方式
  position?: string;             // 职位
  position_security_level: number;        // 岗位密级值
  position_security_level_text: string;   // 岗位密级文本
  primary_department_id: number;          // 主要部门ID
  primary_department_name: string;        // 主要部门名称
  departments: Array<{           // 所属部门列表
    id: number;
    name: string;
    path_name?: string;          // 部门完整路径（可选）
    is_primary: number;          // 是否为主要部门
  }>;
}

// 管理台账状态接口
export interface InventoryState {
  inventoryList: InventoryItem[];
  deviceCategories: DeviceCategory[];
  departmentCategories?: DeviceCategory[]; // 部门分类
  currentCategory: string;
  currentDepartmentCategory?: string;      // 当前选中的部门分类
  isLoading: boolean;
  error?: string;
  selectedItems: string[];
  searchQuery: string;
  tableFields: FieldDefinition[]; // 添加表格字段定义
  categoryExtFields?: Record<string, ExtFieldDefinition[]>; // 分类对应的扩展字段定义
  currentExtFields?: ExtFieldDefinition[];  // 当前选中分类的扩展字段定义
  treeSelectedNode?: TreeSelectedNode;     // 当前树状图选中的节点信息
}

// 事件类型
export type InventoryEvents = {
  'state-change': InventoryState;
  'error': string;
  'loading': boolean;
  'data-loaded': InventoryItem[];
  'table-fields-updated': FieldDefinition[]; // 表格字段更新事件
};

// 字典类型
export interface DictionaryItem {
  code: string;  // 代码
  value: string; // 值
  sort?: number; // 排序号
}

// 字典映射类型
export interface DictionaryMap {
  [key: string]: DictionaryItem[];
}