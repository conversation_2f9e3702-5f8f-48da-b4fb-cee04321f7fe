
import {
  InventoryItem,
  FieldDefinition,
  DeviceCategory,
  InventoryState,
  InventoryEvents,
  DictionaryItem,
  DictionaryMap,
  TreeSelectedNode
} from '../../types/inventory';
import { DepartmentCategory } from './department/types';
// 导入服务，用于调用更新和删除设备的方法
import InventoryUpdateService from './inventoryUpdateService';
import InventoryDeleteService from './inventoryDeleteService';
import DeviceService from './deviceService';

import ExtFieldService from './extFieldService';
// 不再需要导入 ExtFieldDefinition
import ColumnVisibilityService from './columnVisibilityService';
import { isResponsibleMatch } from '../../utils/personUtils';
import ServiceRegistry from '../../services/serviceRegistry';
import DepartmentService from './departmentService';

import TreeCountManager from './treeCountManager';
// 导入拆分后的服务
import {
  InventoryDataService,
  InventoryStateService,
  InventoryFieldService,
  InventoryCategoryService,
  InventoryFilterService,
  InventorySelectionService
} from './services';

class InventoryService {
  private static instance: InventoryService;

  // 拆分后的服务实例
  private dataService: InventoryDataService;
  private stateService: InventoryStateService;
  private fieldService: InventoryFieldService;
  private categoryService: InventoryCategoryService;
  private filterService: InventoryFilterService;
  private selectionService: InventorySelectionService;

  // 保留必要的服务引用
  private columnVisibilityService: ColumnVisibilityService;

  private constructor() {
    // 初始化拆分后的服务
    this.dataService = InventoryDataService.getInstance();
    this.stateService = InventoryStateService.getInstance();
    this.fieldService = InventoryFieldService.getInstance();
    this.categoryService = InventoryCategoryService.getInstance();
    this.filterService = InventoryFilterService.getInstance();
    this.selectionService = InventorySelectionService.getInstance();

    // 初始化列可见性服务
    this.columnVisibilityService = ColumnVisibilityService.getInstance();

    // 监听列可见性变更事件
    this.columnVisibilityService.on('column-visibility-changed', (_) => {
      // 使用当前选中的分类信息重新生成表格字段，确保只显示当前分类的扩展字段
      this.generateTableFields(this.fieldService.getCurrentSelectedCategory());
    });

    // 监听扩展字段服务的事件，保持状态同步
    const extFieldService = ExtFieldService.getInstance();
    extFieldService.on('current-ext-fields-changed', (extFields) => {
      this.stateService.updateState({
        currentExtFields: extFields
      });
    });

    // 初始化基本表格字段，不包含扩展字段
    const basicFields = this.fieldService.initializeBasicTableFields();
    this.stateService.updateState({ tableFields: basicFields });

    // 不在构造函数中立即初始化，等待外部调用 initializeWhenReady()
  }

  /**
   * 在根节点验证完成后初始化数据连接
   * 这个方法应该在系统初始化完成后由外部调用
   */
  public async initializeWhenReady(): Promise<void> {
    console.log('InventoryService: 开始初始化数据连接...');
    await this.initializeDataConnection();
  }

  /**
   * 初始化数据连接 - 串行执行避免并发冲突，步骤间留有响应时间
   */
  private async initializeDataConnection(): Promise<void> {
    // 主要步骤间延迟时间（毫秒）
    const STEP_DELAY = 200;

    try {
      console.log('InventoryService: 步骤1 - 确保数据已加载');
      // 步骤1: 确保数据已加载
      const success = await this.dataService.ensureDataLoaded();
      if (!success) {
        throw new Error('数据加载失败');
      }

      // 步骤间延迟
      await new Promise(resolve => setTimeout(resolve, STEP_DELAY));

      console.log('InventoryService: 步骤2 - 加载设备列表');
      // 步骤2: 加载设备列表
      await this.loadInventoryList();

      // 步骤间延迟
      await new Promise(resolve => setTimeout(resolve, STEP_DELAY));

      console.log('InventoryService: 步骤3 - 加载设备分类树');
      // 步骤3: 加载设备分类树
      await this.generateCategoryTree();

      // 步骤间延迟
      await new Promise(resolve => setTimeout(resolve, STEP_DELAY));

      console.log('InventoryService: 步骤4 - 加载部门分类树');
      // 步骤4: 加载部门分类树
      await this.loadDepartmentTree();

      console.log('InventoryService: 数据连接初始化完成');
    } catch (error) {
      console.error('初始化数据连接失败:', error);
      this.stateService.setError('初始化数据连接失败');
      throw error; // 重新抛出错误，让上层处理
    }
  }





  public static getInstance(): InventoryService {
    if (!InventoryService.instance) {
      InventoryService.instance = new InventoryService();
    }
    return InventoryService.instance;
  }

  // 获取数据字典
  public getDataDictionary(): DictionaryMap {
    return this.fieldService.getDataDictionary();
  }

  // 获取指定类型的字典项
  public getDictionaryItems(dictType: string): DictionaryItem[] {
    return this.fieldService.getDictionaryItems(dictType);
  }

  // 设置数据字典
  public setDataDictionary(dictType: string, items: DictionaryItem[]): void {
    this.fieldService.setDataDictionary(dictType, items);
  }





  /**
   * 动态生成表格字段
   * @param currentCategory 当前选中的分类信息，如果提供则只显示该分类的扩展字段
   */
  public generateTableFields(currentCategory?: { parentCategory?: string; subCategory?: string }): void {
    // 获取当前的数据用于计算自适应列宽
    // 优先使用filteredItems，如果不存在则使用inventoryList
    const state = this.stateService.getState();
    const currentData = state.filteredItems || state.inventoryList || [];

    const fields = this.fieldService.generateTableFields(currentCategory, currentData);
    this.stateService.updateState({ tableFields: fields });
    this.stateService.emit('table-fields-updated', fields);
  }







  /**
   * 公开的生成分类树方法，允许外部调用
   * @param inventoryList 设备列表（可选，如果不提供则使用当前状态中的列表）
   * @param forceRefresh 是否强制刷新，忽略缓存
   */
  public async generateCategoryTree(inventoryList?: InventoryItem[], forceRefresh: boolean = false): Promise<DeviceCategory[]> {
    try {
      // 如果没有提供设备列表，使用当前状态中的列表
      const deviceList = inventoryList || this.stateService.getState().inventoryList;
      console.log(`重新生成分类树，设备数量: ${deviceList.length}, 强制刷新: ${forceRefresh}`);

      const updatedCategories = await this.categoryService.generateCategoryTree(deviceList, forceRefresh);

      this.stateService.updateState({ deviceCategories: updatedCategories });
      this.stateService.forceUpdate();

      console.log('分类树已更新并强制刷新状态');
      return updatedCategories;
    } catch (error) {
      console.error('外部调用获取设备分类树失败:', error);

      const deviceList = inventoryList || this.stateService.getState().inventoryList;
      const emptyCategories = this.categoryService.createEmptyCategories(deviceList.length);
      this.stateService.updateState({ deviceCategories: emptyCategories });

      return emptyCategories;
    }
  }

  /**
   * 加载部门分类树
   * @param forceRefresh 是否强制刷新，忽略缓存
   */
  public async loadDepartmentTree(forceRefresh: boolean = false): Promise<DepartmentCategory[]> {
    try {
      const departmentService = DepartmentService.getInstance();
      const departmentCategories = await departmentService.loadDepartmentTree(forceRefresh);

      // 更新状态
      this.stateService.updateState({ departmentCategories });

      return departmentCategories;
    } catch (error) {
      console.error('加载部门分类树失败:', error);

      // 创建空的部门分类树
      const emptyDepartmentCategories: DepartmentCategory[] = [{
        id: 'all-dept',
        name: '全部部门',
        count: 0,
        children: []
      }];

      this.stateService.updateState({ departmentCategories: emptyDepartmentCategories });
      return emptyDepartmentCategories;
    }
  }

  /**
   * 强制触发状态更新
   */
  public forceUpdate(): void {
    this.stateService.forceUpdate();
  }

  /**
   * 强制刷新当前筛选结果并触发状态更新
   * 用于在设备列表更新后立即刷新界面显示
   */
  public forceRefreshCurrentFilter(): void {
    const state = this.stateService.getState();
    const currentCategory = state.currentCategory;

    if (currentCategory && currentCategory !== 'all') {
      console.log('强制刷新当前筛选结果:', currentCategory);
      // 清除筛选缓存
      this.filterService.clearFilterCache();
      // 使用公开的filterByCategory方法，它会更新状态
      this.filterByCategory(currentCategory, true);
    }

    // 强制触发状态更新事件，确保界面重新渲染
    this.stateService.forceUpdate();

    // 额外触发一次状态变化，确保React组件能够检测到变化
    const updatedState = this.stateService.getState();
    this.stateService.updateState({
      inventoryList: [...updatedState.inventoryList] // 创建新的数组引用
    });
  }



  /**
   * 更新设备分类树计数
   * @deprecated 建议使用 TreeCountManager.getInstance().requestUpdate('device', immediate) 替代
   * @param forceUpdate 是否强制更新UI，即使计数没有变化
   */
  public updateCategoryTreeCounts(forceUpdate: boolean = false): void {
    const state = this.stateService.getState();
    const updatedCategories = this.categoryService.updateCategoryTreeCounts(
      state.deviceCategories,
      state.inventoryList,
      forceUpdate,
      true // immediate
    );

    if (updatedCategories) {
      this.stateService.updateState({ deviceCategories: updatedCategories });
    }
  }

  /**
   * 更新树状图选中的节点信息
   * @param treeSelectedNode 树状图选中的节点信息
   */
  public updateTreeSelectedNode(treeSelectedNode: TreeSelectedNode): void {
    this.stateService.updateTreeSelectedNode(treeSelectedNode);
  }

  // 获取字段字典表
  public getFieldDictionary(): Record<string, string> {
    return this.fieldService.getFieldDictionary();
  }

  // 获取表格字段定义
  public getTableFields(): FieldDefinition[] {
    return this.stateService.getState().tableFields;
  }

  // 设置字段字典
  public setFieldDictionary(dictionary: Record<string, string>): void {
    this.fieldService.setFieldDictionary(dictionary);
    // 更新字段后重新生成表格字段
    this.generateTableFields();
  }

  // 订阅状态变化
  public on<K extends keyof InventoryEvents>(event: K, callback: (data: InventoryEvents[K]) => void): void {
    this.stateService.on(event, callback);
  }

  // 取消订阅
  public off<K extends keyof InventoryEvents>(event: K, callback: (data: InventoryEvents[K]) => void): void {
    this.stateService.off(event, callback);
  }

  // 获取当前状态
  public getState(): InventoryState {
    return this.stateService.getState();
  }

  /**
   * 获取当前选中的分类信息
   * @returns 当前选中的分类信息
   */
  public getCurrentSelectedCategory(): { parentCategory?: string; subCategory?: string } | undefined {
    return this.fieldService.getCurrentSelectedCategory();
  }

  /**
   * 清除当前选中的分类信息
   */
  public clearCurrentSelectedCategory(): void {
    this.fieldService.clearCurrentSelectedCategory();
  }

  // 加载设备列表
  public async loadInventoryList(forceRefresh: boolean = false): Promise<InventoryItem[]> {
    try {
      this.stateService.setLoading(true);
      this.stateService.clearError();

      // 从数据服务加载数据
      const inventoryList = await this.dataService.loadInventoryFromBackend(forceRefresh);

      // 更新状态
      this.stateService.updateState({ inventoryList });
      this.stateService.setLoading(false);

      // 触发数据加载完成事件
      this.stateService.emit('data-loaded', inventoryList);

      // 数据加载完成后，重新生成表格字段以应用自适应列宽
      // 总是重新生成表格字段，无论是否有分类筛选
      this.generateTableFields();

      return inventoryList;
    } catch (error: any) {
      console.error('加载设备列表失败:', error.message);
      this.stateService.setLoading(false);
      this.stateService.setError(error.message);
      this.stateService.emit('error', error.message);

      // 返回当前状态中的数据
      return this.stateService.getState().inventoryList;
    }
  }

  // 根据分类筛选设备列表
  public filterByCategory(categoryId: string, forceRefresh: boolean = false): InventoryItem[] {
    const state = this.stateService.getState();

    // 更新当前选中的分类
    this.stateService.updateState({ currentCategory: categoryId });

    // 执行筛选
    const filteredResult = this.filterService.filterByCategory(categoryId, state.inventoryList, state.deviceCategories, forceRefresh);

    return filteredResult;
  }

  // 设置当前选中的分类
  public setCurrentCategory(categoryId: string): void {
    console.log(`设置当前分类为: ${categoryId}`);
    this.stateService.updateState({ currentCategory: categoryId });

    // 清除筛选缓存，确保下次筛选时使用新的分类
    this.filterService.clearFilterCache();
  }







  /**
   * 更新本地设备列表中的单个设备
   * @param device 设备数据
   */
  public updateLocalDevice(device: any): void {
    // 转换设备数据为前端格式
    const inventoryItem = this.dataService.convertDeviceToInventoryItem(device);

    const state = this.stateService.getState();
    const index = state.inventoryList.findIndex(item => item.id === inventoryItem.id);

    if (index === -1) {
      // 新设备，添加到列表
      const updatedList = [...state.inventoryList, inventoryItem];
      this.stateService.updateState({ inventoryList: updatedList });

      // 清除筛选缓存
      this.filterService.clearFilterCache();
    } else {
      // 更新现有设备
      const updatedList = [...state.inventoryList];
      updatedList[index] = inventoryItem;
      this.stateService.updateState({ inventoryList: updatedList });
    }
  }

  /**
   * 更新整个设备列表
   * @param inventoryList 新的设备列表
   */
  public updateInventoryList(inventoryList: InventoryItem[]): void {
    // 获取当前状态
    const currentState = this.stateService.getState();
    const currentCategory = currentState.currentCategory;

    // 清除筛选缓存，确保后续筛选使用最新数据
    this.filterService.clearFilterCache();

    // 根据当前显示状态，更新相应的数据
    if (currentCategory && currentCategory !== 'all') {
      // 当前显示的是筛选结果，需要重新计算筛选数据
      console.log('总表更新后，重新计算筛选结果:', currentCategory);

      // 使用新的总表数据重新计算筛选结果
      const newFilteredResult = this.filterService.filterByCategory(
        currentCategory,
        inventoryList,
        currentState.deviceCategories,
        true // 强制重新计算，不使用缓存
      );

      // 一次性更新总表和筛选结果
      this.stateService.updateState({
        inventoryList,
        filteredItems: newFilteredResult
      });

      console.log('筛选结果已更新，设备数量:', newFilteredResult.length);
    } else {
      // 当前显示的是总表，确保React能检测到inventoryList的变化
      // 通过创建新的数组引用来触发React重新渲染
      this.stateService.updateState({
        inventoryList: [...inventoryList],
        filteredItems: [...inventoryList] // 总表状态下，filteredItems应该等于inventoryList
      });

      console.log('总表数据已更新，设备数量:', inventoryList.length);
    }
  }

  /**
   * 强制清除所有筛选缓存
   */
  public clearAllFilterCache(): void {
    this.filterService.clearFilterCache();
    console.log('已清除所有筛选缓存');
  }

  /**
   * 清除特定分类的筛选缓存
   * @param categoryName 分类名称
   */
  public clearCategoryFilterCache(categoryName: string): void {
    this.filterService.clearCategoryCache(categoryName);
    console.log(`已清除分类 "${categoryName}" 的筛选缓存`);
  }

  /**
   * 强制重新计算筛选结果（不使用缓存）
   * @param categoryId 分类ID
   * @param inventoryList 设备列表
   * @param deviceCategories 设备分类树
   */
  public calculateFilteredResult(categoryId: string, inventoryList: InventoryItem[], deviceCategories: DeviceCategory[]): InventoryItem[] {
    return this.filterService.calculateFilteredResult(categoryId, inventoryList, deviceCategories);
  }

  /**
   * 根据子分类名称查找对应的ID
   * @param subCategoryName 子分类名称（二级分类，如"台式机"、"便携机"、"服务器"）
   * @returns 子分类ID或null
   */
  private async findSubCategoryIdByName(subCategoryName: string): Promise<number | null> {
    try {
      const state = this.stateService.getState();
      const deviceCategories = state.deviceCategories;

      // 遍历分类树查找匹配的子分类（二级分类）
      for (const rootCategory of deviceCategories) {
        if (rootCategory.children) {
          // 遍历父分类（一级分类，如"计算机类"、"移动存储类"）
          for (const parentCategory of rootCategory.children) {
            if (parentCategory.children) {
              // 遍历子分类（二级分类，如"台式机"、"便携机"）
              for (const subCategory of parentCategory.children) {
                if (subCategory.name === subCategoryName) {
                  console.log(`✅ 找到子分类 "${subCategoryName}" (ID: ${subCategory.originalId}) 属于父分类 "${parentCategory.name}"`);
                  return typeof subCategory.originalId === 'number' ? subCategory.originalId : parseInt(subCategory.originalId as string);
                }
              }
            }
          }
        }
      }

      console.warn(`❌ 未找到子分类 "${subCategoryName}"`);
      return null;
    } catch (error) {
      console.error('❌ 查找子分类ID失败:', error);
      return null;
    }
  }

  /**
   * 根据父分类名称查找对应的ID
   * @param parentCategoryName 父分类名称（一级分类，如"计算机类"、"移动存储类"）
   * @returns 父分类ID或null
   */
  private async findParentCategoryIdByName(parentCategoryName: string): Promise<number | null> {
    try {
      const state = this.stateService.getState();
      const deviceCategories = state.deviceCategories;

      // 遍历分类树查找匹配的父分类（一级分类）
      for (const rootCategory of deviceCategories) {
        if (rootCategory.children) {
          // 遍历父分类（一级分类，如"计算机类"、"移动存储类"）
          for (const parentCategory of rootCategory.children) {
            if (parentCategory.name === parentCategoryName) {
              console.log(`✅ 找到父分类 "${parentCategoryName}" (ID: ${parentCategory.originalId})`);
              return typeof parentCategory.originalId === 'number' ? parentCategory.originalId : parseInt(parentCategory.originalId as string);
            }
          }
        }
      }

      console.warn(`❌ 未找到父分类 "${parentCategoryName}"`);
      return null;
    } catch (error) {
      console.error('❌ 查找父分类ID失败:', error);
      return null;
    }
  }

  /**
   * 根据设备ID获取设备信息
   * @param deviceId 设备ID
   * @returns 设备信息或null
   */
  public getDeviceById(deviceId: string): InventoryItem | null {
    const state = this.stateService.getState();
    return state.inventoryList.find(item => item.id === deviceId) || null;
  }

  /**
   * 从前端缓存的部门名称构造部门信息
   * 当后端返回的设备数据缺少部门信息时，使用前端缓存的部门名称来补充
   * @param device 设备数据
   * @param cachedDepartmentName 前端缓存的部门名称
   */
  public enrichDeviceWithCachedDepartmentInfo(device: any, cachedDepartmentName?: string): void {
    // 如果设备数据中已经有部门信息，直接返回
    if (device.responsible_person_departments &&
        Array.isArray(device.responsible_person_departments) &&
        device.responsible_person_departments.length > 0) {
      return;
    }

    // 如果提供了缓存的部门名称，使用它来构造部门信息
    if (cachedDepartmentName) {
      // 尝试从部门树中查找完整的部门路径
      const departmentService = DepartmentService.getInstance();
      const departmentCategories = departmentService.getState().departmentCategories;

      // 查找部门节点
      const findDepartmentByName = (categories: DepartmentCategory[], name: string): DepartmentCategory | null => {
        for (const category of categories) {
          if (category.name === name && category.id !== 'all-dept') {
            return category;
          }
          if (category.children && category.children.length > 0) {
            const found = findDepartmentByName(category.children, name);
            if (found) return found;
          }
        }
        return null;
      };

      const departmentNode = findDepartmentByName(departmentCategories, cachedDepartmentName);

      let departmentInfo;
      if (departmentNode) {
        // 如果找到了部门节点，获取完整路径
        const pathResolver = departmentService.createPathResolver();
        const fullPath = pathResolver.getFullPath(departmentNode);

        departmentInfo = {
          name: cachedDepartmentName,
          path_name: fullPath
        };
      } else {
        // 如果没找到部门节点，使用部门名称作为路径
        departmentInfo = {
          name: cachedDepartmentName,
          path_name: cachedDepartmentName
        };
      }

      // 添加到设备数据中
      device.responsible_person_departments = [departmentInfo];
      device.department_name = cachedDepartmentName; // 同时设置department_name字段
    }
  }





  /**
   * 从本地设备列表中移除设备
   * @param id 设备ID（数字格式，如123）
   */
  public removeLocalDevice(id: string): void {
    console.log(`从本地设备列表中移除设备，ID: ${id}`);
    const state = this.stateService.getState();
    const updatedList = state.inventoryList.filter(item => item.id !== id);
    this.stateService.updateState({ inventoryList: updatedList });

    // 清除筛选缓存
    this.filterService.clearFilterCache();
  }



  // 更新设备信息 - 调用更新服务
  public async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem> {
    try {
      this.stateService.setLoading(true);

      // 查找要更新的设备
      const state = this.stateService.getState();
      const existingItem = state.inventoryList.find(item => item.id === id);
      if (!existingItem) {
        throw new Error(`设备未找到: ${id}`);
      }

      // 保存原始数据用于后续事件
      const originalData = existingItem.rawData || existingItem;

      // 调用更新服务
      const updateService = InventoryUpdateService.getInstance();
      await updateService.updateInventoryItem(id, updates);

      // 直接在本地更新设备数据
      const updatedItem = { ...existingItem, ...updates };

      // 关键修复：同步更新rawData字段和分类ID，确保筛选和计数能正确识别变更
      if (updatedItem.rawData) {
        updatedItem.rawData = { ...updatedItem.rawData };

        // 同步子分类（二级分类）
        if (updates.type !== undefined) {
          updatedItem.rawData.sub_category_name = updates.type;

          // 根据新的子分类名称查找并更新subCategoryId
          const newSubCategoryId = await this.findSubCategoryIdByName(updates.type);
          if (newSubCategoryId !== null) {
            updatedItem.subCategoryId = newSubCategoryId;
            updatedItem.rawData.sub_category_id = newSubCategoryId;
            console.log(`✅ 子分类已更新: "${updates.type}" (ID: ${newSubCategoryId})`);
          } else {
            console.warn(`❌ 未找到子分类 "${updates.type}" 对应的ID`);
          }
        }
        // 同步父分类（一级分类）
        if (updates.parentCategory !== undefined) {
          updatedItem.rawData.parent_category_name = updates.parentCategory;

          // 根据新的父分类名称查找并更新parentCategoryId
          const newParentCategoryId = await this.findParentCategoryIdByName(updates.parentCategory);
          if (newParentCategoryId !== null) {
            updatedItem.parentCategoryId = newParentCategoryId;
            updatedItem.rawData.parent_category_id = newParentCategoryId;
            console.log(`✅ 父分类已更新: "${updates.parentCategory}" (ID: ${newParentCategoryId})`);
          } else {
            console.warn(`❌ 未找到父分类 "${updates.parentCategory}" 对应的ID`);
          }
        }

        // 同步其他可能影响分类的字段
        if (updates.name !== undefined) {
          updatedItem.rawData.device_name_brand = updates.name;
        }
        if (updates.model !== undefined) {
          updatedItem.rawData.model = updates.model;
        }
        if (updates.location !== undefined) {
          updatedItem.rawData.location = updates.location;
        }
        if (updates.responsible !== undefined) {
          const responsibleName = updates.responsible.split(' (')[0].trim();
          updatedItem.rawData.responsible_person_name = responsibleName;
        }
        if (updates.department !== undefined) {
          updatedItem.rawData.department_path = updates.department;
        }
      }

      // 更新本地设备列表
      const updatedList = [...state.inventoryList];
      const index = updatedList.findIndex(item => item.id === id);
      if (index !== -1) {
        updatedList[index] = updatedItem;
        this.stateService.updateState({ inventoryList: updatedList });
        console.log('本地设备数据已更新，包括rawData同步');
      }

      // 清除筛选缓存
      this.filterService.clearFilterCache();

      // 手动触发设备更新事件，包含原始数据和字段映射
      const deviceService = DeviceService.getInstance();

      // 创建包含字段映射的更新数据
      const mappedUpdates = { ...updates };

      // 添加后端字段映射，确保事件处理能检测到分类变更
      if (updates.type !== undefined) {
        mappedUpdates.sub_category_name = updates.type;
      }
      if (updates.parentCategory !== undefined) {
        mappedUpdates.parent_category_name = updates.parentCategory;
      }

      console.log('触发设备更新事件，原始updates:', updates);
      console.log('触发设备更新事件，映射后updates:', mappedUpdates);

      deviceService.emitDeviceUpdated({
        id,
        updates: mappedUpdates,  // 使用映射后的字段
        result: { success: true },
        originalData
      });

      return updatedItem;
    } catch (error: any) {
      this.stateService.setLoading(false);
      this.stateService.setError(error.message);
      this.stateService.emit('error', error.message);
      throw error;
    } finally {
      this.stateService.setLoading(false);
    }
  }

  // 删除设备 - 调用删除服务
  public async deleteInventoryItems(ids: string[]): Promise<void> {
    try {
      this.stateService.setLoading(true);

      // 调用删除服务
      const deleteService = InventoryDeleteService.getInstance();
      await deleteService.deleteInventoryItems(ids);

      // 更新选中项
      const state = this.stateService.getState();
      const newSelectedItems = state.selectedItems.filter(id => !ids.includes(id));
      this.stateService.updateState({ selectedItems: newSelectedItems });

      // 直接从本地数据中移除已删除的设备
      for (const id of ids) {
        this.removeLocalDevice(id);
      }

      // 清除筛选缓存
      this.filterService.clearFilterCache();

      // 更新树状图计数 - 删除设备后立即更新
      try {
        // 使用统一的计数管理器，立即更新所有树状图计数
        const treeCountManager = TreeCountManager.getInstance();
        treeCountManager.requestUpdate('both', true); // 立即更新设备和部门树计数
      } catch (countError) {
        console.error('删除设备后更新树状图计数失败:', countError);
        // 计数更新失败不影响删除操作的完成
      }

    } catch (error: any) {
      this.stateService.setLoading(false);
      this.stateService.setError(error.message);
      this.stateService.emit('error', error.message);
      throw error;
    } finally {
      this.stateService.setLoading(false);
    }
  }

  // 设置选中的设备
  public setSelectedItems(ids: string[]): void {
    this.selectionService.setSelectedItems(ids);
    this.stateService.updateState({ selectedItems: ids });
  }

  // 切换选中状态
  public toggleSelectItem(id: string): void {
    const selectedItems = this.selectionService.toggleSelectItem(id);
    this.stateService.updateState({ selectedItems });
  }

  // 全选/取消全选
  public toggleSelectAll(filteredItems: InventoryItem[]): void {
    const selectedItems = this.selectionService.toggleSelectAll(filteredItems);
    this.stateService.updateState({ selectedItems });
  }

  // 设置搜索查询 - 支持空搜索自动取消
  public setSearchQuery(query: string): void {
    this.stateService.setSearchQuery(query);
  }

  // 设置列可见性
  public setColumnVisibility(columnId: string, isVisible: boolean): void {
    // 使用列可见性服务设置列可见性
    this.columnVisibilityService.setColumnVisibility(columnId, isVisible);

    // 注意：不需要在这里重新生成表格字段，因为我们已经在构造函数中监听了列可见性变更事件
  }

  // 重置列可见性
  public resetColumnVisibility(): void {
    // 使用列可见性服务重置列可见性
    this.columnVisibilityService.resetColumnVisibility();

    // 注意：不需要在这里重新生成表格字段，因为我们已经在构造函数中监听了列可见性变更事件
  }

  // 重置所有列可见性（包括扩展字段）
  public resetAllColumnVisibility(): void {
    // 使用列可见性服务重置所有列可见性
    this.columnVisibilityService.resetAllColumnVisibility();

    // 注意：不需要在这里重新生成表格字段，因为我们已经在构造函数中监听了列可见性变更事件
  }

  // 获取指定分类的扩展字段定义
  public async getCategoryExtFields(categoryType: string, categoryName: string): Promise<any[]> {
    try {
      // 使用扩展字段服务获取扩展字段定义
      const extFieldService = ExtFieldService.getInstance();
      const extFields = await extFieldService.getCategoryExtFields(categoryType, categoryName);

      // 更新状态
      const state = this.stateService.getState();
      this.stateService.updateState({
        categoryExtFields: {
          ...state.categoryExtFields,
          [`${categoryType}::${categoryName}`]: extFields
        }
      });

      return extFields;
    } catch (error) {
      console.error('获取分类扩展字段失败:', error);
      return [];
    }
  }

  // 设置当前选中分类的扩展字段
  public setCurrentExtFields(extFields: any[]): void {
    // 使用扩展字段服务设置当前扩展字段
    const extFieldService = ExtFieldService.getInstance();
    extFieldService.setCurrentExtFields(extFields);
  }

  // 清除当前扩展字段
  public clearCurrentExtFields(): void {
    // 使用扩展字段服务清除当前扩展字段
    const extFieldService = ExtFieldService.getInstance();
    extFieldService.clearCurrentExtFields();
  }





  /**
   * 根据二级分类名称查找其所属的一级分类
   * @param subCategoryName 二级分类名称
   * @returns 一级分类名称，如果找不到则返回"未分类"
   */
  public findParentCategoryBySubCategory(subCategoryName: string): string {
    const state = this.stateService.getState();
    return this.categoryService.findParentCategoryBySubCategory(subCategoryName, state.deviceCategories) || '未分类';
  }

  /**
   * 更新总表中的设备类型列
   * 当二级分类名称更新时，直接更新总表中对应的设备类型列，而不是通过请求数据库
   * @param oldSubCategoryName 旧的二级分类名称
   * @param newSubCategoryName 新的二级分类名称
   */
  public updateDeviceTypeInInventoryList(oldSubCategoryName: string, newSubCategoryName: string): void {
    console.log('updateDeviceTypeInInventoryList 开始执行');
    console.log('参数:', { oldSubCategoryName, newSubCategoryName });

    // 检查参数
    if (!oldSubCategoryName || !newSubCategoryName) {
      console.warn('更新设备类型参数无效:', { oldSubCategoryName, newSubCategoryName });
      return;
    }

    const state = this.stateService.getState();
    // 复制当前设备列表
    const updatedList = [...state.inventoryList];
    let updateCount = 0;

    // 遍历设备列表，更新匹配的设备类型
    updatedList.forEach(item => {
      if (item.type === oldSubCategoryName) {
        item.type = newSubCategoryName;
        updateCount++;

        // 同时更新原始数据中的二级分类名称，确保数据一致性
        if (item.rawData) {
          item.rawData.sub_category_name = newSubCategoryName;
        }
      }
    });

    // 只有在有更新时才更新状态和清除缓存
    if (updateCount > 0) {
      // 更新设备列表状态
      this.stateService.updateState({ inventoryList: updatedList });

      // 清除筛选缓存，确保界面显示最新数据
      this.filterService.clearFilterCache();
      console.log('已清除筛选缓存，确保界面显示更新后的分类名称');

      // 如果当前有选中的分类，强制刷新筛选结果
      const currentCategory = state.currentCategory;
      if (currentCategory && currentCategory !== 'all') {
        console.log('强制刷新当前分类的筛选结果:', currentCategory);
        // 使用更新后的设备列表重新计算筛选结果
        const newFilteredResult = this.filterService.filterByCategory(currentCategory, updatedList, state.deviceCategories, true);
        console.log('筛选结果已更新，设备数量:', newFilteredResult.length);
      }
    }
  }

  /**
   * 更新总表中的部门名称
   * 当部门重命名时，直接更新总表中对应的部门名称，而不是通过请求数据库
   * @param oldDepartmentName 旧的部门名称
   * @param newDepartmentName 新的部门名称
   */
  public updateDepartmentNameInInventoryList(oldDepartmentName: string, newDepartmentName: string): void {
    console.log('updateDepartmentNameInInventoryList 开始执行');
    console.log('参数:', { oldDepartmentName, newDepartmentName });

    // 检查参数
    if (!oldDepartmentName || !newDepartmentName) {
      console.warn('更新部门名称参数无效:', { oldDepartmentName, newDepartmentName });
      return;
    }

    const state = this.stateService.getState();
    // 复制当前设备列表
    const updatedList = [...state.inventoryList];
    let updateCount = 0;

    // 遍历设备列表，更新匹配的部门名称
    updatedList.forEach(item => {
      if (item.department === oldDepartmentName) {
        item.department = newDepartmentName;
        updateCount++;

        // 同时更新原始数据中的部门名称，确保数据一致性
        if (item.rawData) {
          item.rawData.department_name = newDepartmentName;
        }

        console.log('更新设备 ' + item.id + ' 的部门: ' + oldDepartmentName + ' -> ' + newDepartmentName);
      }
    });

    console.log('部门名称更新完成，更新了 ' + updateCount + ' 个设备');

    // 只有在有更新时才更新状态和清除缓存
    if (updateCount > 0) {
      // 更新设备列表状态
      this.stateService.updateState({ inventoryList: updatedList });

      // 清除筛选缓存，确保界面显示最新数据
      this.filterService.clearFilterCache();
      console.log('已清除筛选缓存，确保界面显示更新后的部门名称');

      // 如果当前有选中的分类，强制刷新筛选结果
      const currentCategory = state.currentCategory;
      if (currentCategory && currentCategory !== 'all') {
        console.log('强制刷新当前分类的筛选结果:', currentCategory);
        // 使用更新后的设备列表重新计算筛选结果
        const newFilteredResult = this.filterService.filterByCategory(currentCategory, updatedList, state.deviceCategories, true);
        console.log('筛选结果已更新，设备数量:', newFilteredResult.length);
      }
    }
  }

  /**
   * 更新总表中的人员名称
   * 当人员重命名时，直接更新总表中对应的责任人名称，而不是通过请求数据库
   * @param oldPersonName 旧的人员名称
   * @param newPersonName 新的人员名称
   * @param oldAlias 旧的人员备注（可选）
   * @param newAlias 新的人员备注（可选）
   */
  public updatePersonNameInInventoryList(
    oldPersonName: string,
    newPersonName: string,
    oldAlias?: string,
    newAlias?: string
  ): void {
    console.log('updatePersonNameInInventoryList 开始执行');
    console.log('参数:', { oldPersonName, newPersonName, oldAlias, newAlias });

    // 检查参数
    if (!oldPersonName || !newPersonName) {
      console.warn('更新人员名称参数无效:', { oldPersonName, newPersonName, oldAlias, newAlias });
      return;
    }

    const state = this.stateService.getState();
    // 复制当前设备列表
    const updatedList = [...state.inventoryList];
    let updateCount = 0;

    // 构建旧的完整人员名称（包含备注）
    const oldFullPersonName = oldAlias ? `${oldPersonName} (${oldAlias})` : oldPersonName;
    // 构建新的完整人员名称（包含备注）
    const newFullPersonName = newAlias ? `${newPersonName} (${newAlias})` : newPersonName;

    // 遍历设备列表，更新匹配的责任人名称
    updatedList.forEach(item => {
      let shouldUpdate = false;

      // 检查是否匹配（支持多种格式）
      if (item.responsible === oldFullPersonName ||
          item.responsible === oldPersonName ||
          (oldAlias && item.responsible === `${oldPersonName} (${oldAlias})`)) {
        shouldUpdate = true;
      }

      if (shouldUpdate) {
        item.responsible = newFullPersonName;
        updateCount++;

        // 同时更新原始数据中的责任人名称，确保数据一致性
        if (item.rawData) {
          item.rawData.responsible_person_name = newPersonName;
          if (newAlias) {
            item.rawData.responsible_person_alias = newAlias;
          }
        }

        console.log('更新设备 ' + item.id + ' 的责任人: ' + oldFullPersonName + ' -> ' + newFullPersonName);
      }
    });

    console.log('人员名称更新完成，更新了 ' + updateCount + ' 个设备');

    // 只有在有更新时才更新状态和清除缓存
    if (updateCount > 0) {
      // 更新设备列表状态
      this.stateService.updateState({ inventoryList: updatedList });

      // 清除筛选缓存，确保界面显示最新数据
      this.filterService.clearFilterCache();
      console.log('已清除筛选缓存，确保界面显示更新后的人员名称');

      // 如果当前有选中的分类，强制刷新筛选结果
      const currentCategory = state.currentCategory;
      if (currentCategory && currentCategory !== 'all') {
        console.log('强制刷新当前分类的筛选结果:', currentCategory);
        // 使用更新后的设备列表重新计算筛选结果
        const newFilteredResult = this.filterService.filterByCategory(currentCategory, updatedList, state.deviceCategories, true);
        console.log('筛选结果已更新，设备数量:', newFilteredResult.length);
      }
    }
  }

  /**
   * 更新总表中人员的所属部门
   * 当人员移动到新部门时，直接更新总表中该人员的所属部门，而不是通过请求数据库
   * @param personName 人员名称
   * @param oldDepartmentName 旧的部门名称
   * @param newDepartmentName 新的部门名称
   * @param personAlias 人员备注（可选）
   */
  public updatePersonDepartmentInInventoryList(
    personName: string,
    oldDepartmentName: string,
    newDepartmentName: string,
    personAlias?: string
  ): void {
    // 检查参数
    if (!personName || !oldDepartmentName || !newDepartmentName) {
      console.warn('更新人员所属部门参数无效:', { personName, personAlias, oldDepartmentName, newDepartmentName });
      return;
    }

    // 构建完整的人员名称（包含备注）
    let fullPersonName = personName;
    if (personAlias) {
      fullPersonName = personName + ' (' + personAlias + ')';
    }

    const state = this.stateService.getState();
    // 复制当前设备列表
    const updatedList = [...state.inventoryList];
    let updateCount = 0;

    // 遍历设备列表，更新匹配的人员所属部门
    updatedList.forEach(item => {
      // 检查责任人是否匹配 - 使用统一的匹配逻辑
      const responsibleMatches = isResponsibleMatch(item.responsible || '', fullPersonName);

      // 检查部门是否匹配旧部门
      const isDepartmentMatch = item.department === oldDepartmentName;

      // 如果责任人和部门都匹配，则更新部门
      if (responsibleMatches && isDepartmentMatch) {
        item.department = newDepartmentName;
        updateCount++;

        // 同时更新原始数据中的部门名称，确保数据一致性
        if (item.rawData) {
          item.rawData.department_name = newDepartmentName;
        }

        console.log('更新设备 ' + item.id + ' 的部门: ' + oldDepartmentName + ' -> ' + newDepartmentName);
      }
      // 如果只有责任人匹配，但部门不匹配，记录日志但不更新
      else if (responsibleMatches && !isDepartmentMatch) {
        console.log('找到责任人匹配的设备 ' + item.id + '，但部门不匹配: 当前部门="' + item.department + '", 期望旧部门="' + oldDepartmentName + '"');

        // 如果责任人匹配，但部门为空，也进行更新
        if (!item.department) {
          item.department = newDepartmentName;
          updateCount++;

          // 同时更新原始数据中的部门名称，确保数据一致性
          if (item.rawData) {
            item.rawData.department_name = newDepartmentName;
          }

          console.log('更新设备 ' + item.id + ' 的空部门为: ' + newDepartmentName);
        }
      }
    });

    // 只有在有更新时才更新状态和清除缓存
    if (updateCount > 0) {
      // 更新设备列表状态
      this.stateService.updateState({ inventoryList: updatedList });

      // 清除筛选缓存，确保界面显示最新数据
      this.filterService.clearFilterCache();
      console.log('已清除筛选缓存，确保界面显示更新后的部门名称');

      // 如果当前有选中的分类，强制刷新筛选结果
      const currentCategory = state.currentCategory;
      if (currentCategory && currentCategory !== 'all') {
        console.log('强制刷新当前分类的筛选结果:', currentCategory);
        // 使用更新后的设备列表重新计算筛选结果
        const newFilteredResult = this.filterService.filterByCategory(currentCategory, updatedList, state.deviceCategories, true);
        console.log('筛选结果已更新，设备数量:', newFilteredResult.length);
      }
    } else {
      // 如果没有找到匹配的设备，尝试只匹配责任人，不考虑部门
      let secondPassUpdateCount = 0;

      updatedList.forEach(item => {
        // 检查责任人是否匹配 - 使用统一的匹配逻辑
        const responsibleMatches = isResponsibleMatch(item.responsible || '', fullPersonName);

        if (responsibleMatches) {
          console.log('第二次尝试: 找到责任人匹配的设备 ' + item.id + '，当前部门="' + item.department + '"');

          // 更新部门
          item.department = newDepartmentName;
          secondPassUpdateCount++;

          // 同时更新原始数据中的部门名称，确保数据一致性
          if (item.rawData) {
            item.rawData.department_name = newDepartmentName;
          }

          console.log('第二次尝试: 更新设备 ' + item.id + ' 的部门: ' + item.department + ' -> ' + newDepartmentName);
        }
      });

      // 如果第二次尝试找到了匹配的设备，更新状态和清除缓存
      if (secondPassUpdateCount > 0) {
        // 更新设备列表状态
        this.stateService.updateState({ inventoryList: updatedList });

        // 清除筛选缓存，确保界面显示最新数据
        this.filterService.clearFilterCache();
        console.log('第二次尝试成功，已清除筛选缓存，确保界面显示更新后的部门名称');

        // 如果当前有选中的分类，强制刷新筛选结果
        const currentCategory = state.currentCategory;
        if (currentCategory && currentCategory !== 'all') {
          console.log('强制刷新当前分类的筛选结果:', currentCategory);
          // 使用更新后的设备列表重新计算筛选结果
          const newFilteredResult = this.filterService.filterByCategory(currentCategory, updatedList, state.deviceCategories, true);
          console.log('筛选结果已更新，设备数量:', newFilteredResult.length);
        }
      }
    }
  }



  /**
   * 清理资源
   */
  public cleanup(): void {
    console.log('清理InventoryService资源');
    // 清理所有子服务
    // 注意：子服务的清理方法需要在各自的服务中实现
  }
}

// 注册服务
ServiceRegistry.getInstance().registerFactory('inventoryService', () => InventoryService.getInstance());

export default InventoryService;