/**
 * 分类图标服务 - 纯前端实现
 * 使用localStorage保存和管理分类的自定义图标
 */

export interface CategoryIconMapping {
  [categoryId: string]: string; // categoryId -> iconType
}

export class CategoryIconService {
  private static instance: CategoryIconService;
  private readonly STORAGE_KEY = 'category_custom_icons';
  private iconMappings: CategoryIconMapping = {};

  private constructor() {
    this.loadFromStorage();
  }

  public static getInstance(): CategoryIconService {
    if (!CategoryIconService.instance) {
      CategoryIconService.instance = new CategoryIconService();
    }
    return CategoryIconService.instance;
  }

  /**
   * 从localStorage加载图标映射
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.iconMappings = JSON.parse(stored);
        console.log('CategoryIconService: 从localStorage加载图标映射:', this.iconMappings);
      }
    } catch (error) {
      console.error('CategoryIconService: 加载图标映射失败:', error);
      this.iconMappings = {};
    }
  }

  /**
   * 保存图标映射到localStorage
   */
  private saveToStorage(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.iconMappings));
      console.log('CategoryIconService: 图标映射已保存到localStorage');
    } catch (error) {
      console.error('CategoryIconService: 保存图标映射失败:', error);
    }
  }

  /**
   * 设置分类的自定义图标
   * @param categoryId 分类ID（如 "parent-1" 或 "parent-1-2"）
   * @param iconType 图标类型
   */
  public setIcon(categoryId: string, iconType: string): void {
    console.log(`CategoryIconService: 设置分类 ${categoryId} 的图标为 ${iconType}`);
    this.iconMappings[categoryId] = iconType;
    this.saveToStorage();
  }

  /**
   * 获取分类的自定义图标
   * @param categoryId 分类ID
   * @returns 图标类型，如果没有自定义图标则返回undefined
   */
  public getIcon(categoryId: string): string | undefined {
    return this.iconMappings[categoryId];
  }

  /**
   * 删除分类的自定义图标
   * @param categoryId 分类ID
   */
  public removeIcon(categoryId: string): void {
    console.log(`CategoryIconService: 删除分类 ${categoryId} 的自定义图标`);
    delete this.iconMappings[categoryId];
    this.saveToStorage();
  }

  /**
   * 获取所有图标映射
   */
  public getAllMappings(): CategoryIconMapping {
    return { ...this.iconMappings };
  }

  /**
   * 清除所有图标映射
   */
  public clearAll(): void {
    console.log('CategoryIconService: 清除所有图标映射');
    this.iconMappings = {};
    this.saveToStorage();
  }

  /**
   * 根据原始ID生成分类ID
   * @param originalId 原始分类ID
   * @param isSubCategory 是否为二级分类
   * @param parentId 父级分类ID（仅二级分类需要）
   */
  public generateCategoryId(originalId: string | number, isSubCategory: boolean = false, parentId?: string | number): string {
    if (isSubCategory && parentId) {
      return `parent-${parentId}-${originalId}`;
    }
    return `parent-${originalId}`;
  }

  /**
   * 批量设置图标映射（用于数据迁移或批量导入）
   * @param mappings 图标映射对象
   */
  public batchSetIcons(mappings: CategoryIconMapping): void {
    console.log('CategoryIconService: 批量设置图标映射:', mappings);
    this.iconMappings = { ...this.iconMappings, ...mappings };
    this.saveToStorage();
  }

  /**
   * 导出图标映射（用于备份）
   */
  public exportMappings(): string {
    return JSON.stringify(this.iconMappings, null, 2);
  }

  /**
   * 导入图标映射（用于恢复备份）
   * @param jsonString JSON字符串
   */
  public importMappings(jsonString: string): boolean {
    try {
      const mappings = JSON.parse(jsonString);
      if (typeof mappings === 'object' && mappings !== null) {
        this.iconMappings = mappings;
        this.saveToStorage();
        console.log('CategoryIconService: 图标映射导入成功');
        return true;
      }
      return false;
    } catch (error) {
      console.error('CategoryIconService: 图标映射导入失败:', error);
      return false;
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): { totalMappings: number; parentCategories: number; subCategories: number } {
    const totalMappings = Object.keys(this.iconMappings).length;
    const parentCategories = Object.keys(this.iconMappings).filter(id => !id.includes('-', 7)).length;
    const subCategories = totalMappings - parentCategories;

    return {
      totalMappings,
      parentCategories,
      subCategories
    };
  }
}

// 导出单例实例
export const categoryIconService = CategoryIconService.getInstance();
