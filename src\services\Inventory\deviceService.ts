import WebSocketManager from '../../utils/websocket';
import TaskManager from '../../utils/taskManager';
import { InventoryItem, ExtFieldEditMode } from '../../types/inventory';
import mitt from 'mitt';
import ExtFieldService from './extFieldService';
import InventoryUpdateService from './inventoryUpdateService';
import { categoryIconService } from './categoryIconService';
import DepartmentService from './departmentService';
import { DepartmentCategory } from './department/types';
import { buildAddExtFieldParams, ExtFieldDefinitionInput, convertToUnixTimestamp } from '../../utils/fieldUtils';

// 定义设备服务事件类型
type DeviceServiceEvents = {
  'task-start': string;
  'task-progress': { taskId: string; progress: number };
  'task-complete': any;
  'task-error': Error;
  'device-added': any; // 添加设备事件
  'device-updated': any; // 更新设备事件
  'category-tree-updated': any; // 设备分类树更新事件
  'parent-category-added': any; // 添加一级分类事件
  'sub-category-added': any; // 添加二级分类事件
  'parent-category-updated': any; // 更新一级分类事件
  'sub-category-updated': any; // 更新二级分类事件
  'parent-category-deleted': any; // 删除一级分类事件
  'sub-category-deleted': any; // 删除二级分类事件
};

/**
 * 设备服务类 - 处理设备相关操作
 */
class DeviceService {
  private static instance: DeviceService;
  private ws: WebSocketManager;
  private task: TaskManager;
  private emitter = mitt<DeviceServiceEvents>();
  private dllName = 'AccountTableDll';
  private isAddingDevice = false; // 添加操作标志位

  private constructor() {
    this.ws = WebSocketManager.getInstance();
    this.task = TaskManager.getInstance();

    // 确保WebSocket连接
    if (!this.ws.isConnected()) {
      this.ws.connect().catch(error => {
        console.error('WebSocket连接失败:', error);
      });
    }

    // 监听更新服务的设备更新事件，并转发
    const updateService = InventoryUpdateService.getInstance();
    updateService.on('device-updated', (data) => {
      console.log('设备更新事件转发:', data);
      this.emitter.emit('device-updated', data);
    });
  }

  /**
   * 获取DeviceService单例
   */
  public static getInstance(): DeviceService {
    if (!DeviceService.instance) {
      DeviceService.instance = new DeviceService();
    }
    return DeviceService.instance;
  }

  /**
   * 添加设备
   * @param deviceData 设备数据
   */
  public async addDevice(deviceData: {
    deviceType: string;        // 设备分类（一级分类）
    deviceName: string;        // 设备名称（二级分类）
    deviceModel: string;       // 设备型号
    deviceManufacturer: string;// 设备厂商
    securityLevel: string;     // 密级
    purpose: string;           // 用途
    location: string;          // 放置位置
    activationTime: number;    // 启用时间（时间戳）
    usage: string;             // 使用情况
    rfidCode: string;          // RFID码
    personalName: string;      // 责任人姓名
    personalAlias?: string;    // 责任人备注
    responsible_person_name?: string; // 责任人姓名（后端字段名）
    responsible_person_alias?: string; // 责任人备注（后端字段名）
    ext1?: string;             // 扩展字段1 - 部门
    ext2?: string;             // 扩展字段2
    ext3?: string;             // 扩展字段3
    extFieldsData?: Record<string, any>; // 扩展字段数据
    securityRfid?: string;     // 安全码
  }): Promise<any> {
    // 防止重复添加
    if (this.isAddingDevice) {
      console.log('正在添加设备中，跳过重复添加请求');
      throw new Error('操作进行中，请稍后再试');
    }

    this.isAddingDevice = true;

    try {
      this.emitter.emit('task-start', 'addDevice');

      // 构建请求参数，按照新的API格式
      // 首先创建action_params对象，只包含必填字段
      const actionParams: Record<string, any> = {
        confidentiality_level: deviceData.securityLevel,
        device_name_brand: deviceData.deviceManufacturer, // 设备名称
        location: deviceData.location,
        model: deviceData.deviceModel,
        purpose: deviceData.purpose,
        sub_category_name: deviceData.deviceName, // 二级分类
        usage_status: deviceData.usage,
        parent_category_name: deviceData.deviceType // 一级分类
      };

      // 责任人姓名处理 - 优先使用 responsible_person_name，如果没有则使用 personalName
      if (deviceData.responsible_person_name) {
        actionParams.responsible_person_name = deviceData.responsible_person_name;
      } else if (deviceData.personalName) {
        actionParams.responsible_person_name = deviceData.personalName.split(' (')[0];
      }

      // 只有当有值时才添加可选字段
      if (deviceData.activationTime) {
        actionParams.activation_timestamp = convertToUnixTimestamp(deviceData.activationTime);
      }

      if (deviceData.rfidCode) {
        actionParams.confidentiality_code = deviceData.rfidCode;
      }

      // 处理部门路径 - 使用新的department_path格式
      if (deviceData.ext1) {
        // 获取部门路径解析器
        const deptService = DepartmentService.getInstance();
        const pathResolver = deptService.createPathResolver();

        // 根据部门名称查找部门节点并获取路径
        const departmentCategories = deptService.getState().departmentCategories;
        const departmentNode = this.findDepartmentByName(departmentCategories, deviceData.ext1);

        if (departmentNode) {
          // 使用路径解析器获取API路径
          const departmentPath = pathResolver.getApiPath(departmentNode);
          actionParams.department_path = departmentPath; // 使用新的department_path字段
          console.log(`部门 "${deviceData.ext1}" 的路径: "${departmentPath}"`);
        } else {
          console.warn(`未找到部门: ${deviceData.ext1}`);
          // 如果找不到部门节点，直接使用部门名称作为路径
          actionParams.department_path = deviceData.ext1;
        }
      }

      // 扩展字段只有在有数据且不为空对象时才添加
      if (deviceData.extFieldsData && Object.keys(deviceData.extFieldsData).length > 0) {
        actionParams.extended_fields = deviceData.extFieldsData;
      }

      // 已移除是否联网字段处理
      // 已移除操作系统字段处理

      // 责任人备注处理 - 优先级：
      // 1. 直接提供的 responsible_person_alias
      // 2. 直接提供的 personalAlias
      // 3. 从 personalName 中提取的备注
      if (deviceData.responsible_person_alias) {
        // 1. 如果直接提供了 responsible_person_alias，优先使用
        actionParams.responsible_person_alias = deviceData.responsible_person_alias;
        console.log('使用提供的 responsible_person_alias:', deviceData.responsible_person_alias);
      } else if (deviceData.personalAlias) {
        // 2. 如果提供了 personalAlias，使用它
        actionParams.responsible_person_alias = deviceData.personalAlias;
        console.log('使用提供的 personalAlias:', deviceData.personalAlias);
      } else if (deviceData.personalName && deviceData.personalName.includes(' (')) {
        // 3. 尝试从 personalName 中提取备注
        const aliasMatch = deviceData.personalName.match(/\(([^)]+)\)/);
        if (aliasMatch && aliasMatch[1]) {
          actionParams.responsible_person_alias = aliasMatch[1];
          console.log('从 personalName 中提取备注:', aliasMatch[1]);
        }
      }

      // 已移除硬盘序列号字段处理
      // 已移除IP地址字段处理
      // 已移除MAC地址字段处理
      // 已移除安装日期字段处理

      // 安全码只有在有值时才添加
      if (deviceData.securityRfid) {
        actionParams.security_code_rfid = deviceData.securityRfid;
      }

      const params = {
        action: "add_device",
        action_params: actionParams
      };

      console.log('开始添加设备:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      // 打印API返回结果，查看分类ID
      console.log('添加设备API返回结果:', result);

      // 确保返回结果中包含分类ID
      if (result && result.success && result.data) {
        // 如果返回结果中没有分类ID，尝试从请求参数中获取
        if (result.data.sub_category_id === undefined || result.data.sub_category_id === null) {
          // 查找二级分类ID
          console.log('API返回结果中没有二级分类ID，尝试从其他地方获取');

          // 这里可以添加查询二级分类ID的逻辑
          // 例如，可以根据二级分类名称查询对应的ID
        }

        if (result.data.parent_category_id === undefined || result.data.parent_category_id === null) {
          // 查找一级分类ID
          console.log('API返回结果中没有一级分类ID，尝试从其他地方获取');

          // 这里可以添加查询一级分类ID的逻辑
          // 例如，可以根据一级分类名称查询对应的ID
        }
      }

      this.emitter.emit('task-complete', result);
      this.emitter.emit('device-added', result);

      return result;
    } catch (error) {
      console.error('添加设备失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    } finally {
      this.isAddingDevice = false; // 完成后重置标志位
    }
  }

  /**
   * 将设备数据对象从前端格式转换为后端格式
   * @param item 前端格式的设备数据
   */
  public convertInventoryItemToDeviceData(item: InventoryItem): any {
    // 将日期字符串转换为Unix时间戳（如果有）
    let activationTime: number = 0;
    if (item.startTime) {
      activationTime = convertToUnixTimestamp(item.startTime);
    }

    // 已移除安装日期处理

    // 从前端格式转换为后端格式
    // 创建包含必填字段的对象
    const result: Record<string, any> = {
      deviceType: item.parentCategory,   // 一级分类
      deviceName: item.type,            // 二级分类
      deviceModel: item.model,          // 设备型号
      deviceManufacturer: item.name,     // 设备名称
      manufacturer: item.manufacturer,   // 厂商
      securityLevel: item.securityLevel,     // 密级
      purpose: item.purpose,            // 用途
      location: item.location,          // 放置位置
      usage: item.status,               // 使用情况
      personalName: item.responsible ? item.responsible.split(' (')[0] : '',   // 责任人姓名（去除备注部分）
    };

    // 只有当有值时才添加可选字段
    if (activationTime) {
      result.activationTime = activationTime;   // 启用时间
    }

    if (item.securityCode) {
      result.rfidCode = item.securityCode;  // RFID码 (使用保密编号)
    }

    // 责任人备注只有在有值时才添加
    if (item.responsible && item.responsible.includes(' (')) {
      const aliasMatch = item.responsible.match(/\(([^)]+)\)/);
      if (aliasMatch && aliasMatch[1]) {
        // 使用正确的字段名 personalAlias，与 addDevice 方法中的 responsible_person_alias 对应
        result.personalAlias = aliasMatch[1];
        // 同时添加 responsible_person_alias 字段，确保在转换过程中不丢失
        result.responsible_person_alias = aliasMatch[1];
      }
    }

    // 已移除是否联网字段处理
    // 已移除硬盘序列号字段处理
    // 已移除IP地址字段处理
    // 已移除MAC地址字段处理
    // 已移除安装日期字段处理
    // 已移除操作系统字段处理

    // 安全码只有在有值时才添加
    if (item.securityRfid) {
      result.securityRfid = item.securityRfid;
    }

    // 部门只有在有值时才添加
    if (item.department) {
      result.department_name = item.department;
      result.ext1 = item.department;  // 扩展字段1 - 使用部门字段
    }

    // 如果有扩展字段，添加到结果中
    if (item.extendedFields && Object.keys(item.extendedFields).length > 0) {
      result.extended_fields = item.extendedFields;
      // 同时添加extFieldsData字段，确保在addDevice方法中能正确处理扩展字段
      result.extFieldsData = item.extendedFields;
    }

    // 如果有分类ID，添加到结果中
    if (item.subCategoryId) {
      result.sub_category_id = item.subCategoryId;
    }
    if (item.parentCategoryId) {
      result.parent_category_id = item.parentCategoryId;
    }

    return result;
  }



  /**
   * 添加分类（一级分类）
   * @param typeName 分类名称
   * @param customIcon 自定义图标类型
   */
  public async addDeviceType(typeName: string, customIcon?: string): Promise<any> {
    try {


      // 构建请求参数，按照新的API格式
      const params = {
        action: "add_parent_category",
        action_params: {
          parent_category_name: typeName,
          ...(customIcon && { custom_icon: customIcon })
        }
      };

      console.log('开始添加分类:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      this.emitter.emit('task-complete', result);

      // 如果添加成功且有自定义图标，保存到前端存储
      if (result && result.success && result.data && result.data.id && customIcon) {
        const categoryId = categoryIconService.generateCategoryId(result.data.id, false);
        categoryIconService.setIcon(categoryId, customIcon);
        console.log(`一级分类添加成功，图标已保存到前端存储: ${categoryId} -> ${customIcon}`);
      }

      // 只触发一级分类添加事件，不再触发分类树更新事件
      // 避免重复刷新，由监听parent-category-added事件的处理函数负责刷新
      // 添加标记，表明这是添加操作，不需要刷新总表
      this.emitter.emit('parent-category-added', { typeName, result, action: 'add', operation: 'add' });

      return result;
    } catch (error) {
      console.error('添加分类失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }

  /**
   * 添加类型（二级分类）
   * @param parentCategoryName 父级分类名称
   * @param subtypeName 类型名称
   * @param customIcon 自定义图标类型
   */
  public async addDeviceSubtype(parentCategoryName: string, subtypeName: string, customIcon?: string): Promise<any> {
    try {


      // 构建请求参数，按照新的API格式
      const params = {
        action: "add_sub_category",
        action_params: {
          parent_category_name: parentCategoryName,
          sub_category_name: subtypeName,
          ...(customIcon && { custom_icon: customIcon })
        }
      };

      console.log('开始添加类型:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      this.emitter.emit('task-complete', result);

      // 如果添加成功且有自定义图标，保存到前端存储
      if (result && result.success && result.data && result.data.id && customIcon) {
        // 使用一个临时的键来保存二级分类图标，等分类树刷新时会正确处理
        const tempKey = `temp_sub_${result.data.id}_${parentCategoryName}`;
        categoryIconService.setIcon(tempKey, customIcon);
        console.log(`二级分类图标已临时保存: ${tempKey} -> ${customIcon}`);
      }

      // 检查是否为安全产品类，如果是则自动添加扩展字段
      if (parentCategoryName === '安全产品类') {
        console.log('检测到安全产品类二级分类，开始自动添加扩展字段');
        await this.autoAddSecurityProductExtFields(subtypeName);
      }

      // 只触发二级分类添加事件，不再触发分类树更新事件
      // 避免重复刷新，由监听sub-category-added事件的处理函数负责刷新
      // 添加标记，表明这是添加操作，不需要刷新总表
      this.emitter.emit('sub-category-added', { parentCategoryName, subtypeName, result, action: 'add', operation: 'add', customIcon });

      return result;
    } catch (error) {
      console.error('添加类型失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }

  /**
   * 为安全产品类自动添加扩展字段
   * @param subtypeName 二级分类名称
   */
  private async autoAddSecurityProductExtFields(subtypeName: string): Promise<void> {
    try {
      // 定义安全产品类需要自动添加的扩展字段
      const securityProductFields = [
        { name: '厂家', type: 'text', required: false, editMode: ExtFieldEditMode.Editable },
        { name: '检测证书名称', type: 'text', required: false, editMode: ExtFieldEditMode.Editable },
        { name: '检测证书编号', type: 'text', required: false, editMode: ExtFieldEditMode.Editable },
        { name: '购置时间', type: 'date', required: false, editMode: ExtFieldEditMode.Editable }
      ];

      console.log(`开始为安全产品类二级分类 "${subtypeName}" 自动添加扩展字段`);

      // 逐个添加扩展字段
      for (const field of securityProductFields) {
        try {
          console.log(`添加扩展字段: ${field.name}`);

          // 使用统一的工具函数构建请求参数
          const fieldDefinition: ExtFieldDefinitionInput = {
            name: field.name,
            type: field.type,
            required: field.required,
            editMode: field.editMode
          };

          const params = buildAddExtFieldParams(subtypeName, fieldDefinition);

          // 提交添加扩展字段的任务
          const fieldResult = await this.submitTask('DbFun', params);

          if (fieldResult && fieldResult.success) {
            console.log(`扩展字段 "${field.name}" 添加成功`);
          } else {
            console.warn(`扩展字段 "${field.name}" 添加失败:`, fieldResult);
          }
        } catch (fieldError) {
          console.error(`添加扩展字段 "${field.name}" 时发生错误:`, fieldError);
          // 继续添加其他字段，不因为一个字段失败而中断整个过程
        }
      }

      console.log(`安全产品类二级分类 "${subtypeName}" 的扩展字段自动添加完成`);

      // 完成后查询该二级分类的扩展字段并进行增量更新
      console.log('自动添加扩展字段完成，查询并更新该二级分类的扩展字段缓存');
      await this.refreshSubCategoryExtFields('安全产品类', subtypeName);

    } catch (error) {
      console.error('自动添加安全产品类扩展字段失败:', error);
      // 不抛出错误，避免影响二级分类的正常添加
    }
  }

  /**
   * 刷新指定二级分类的扩展字段缓存
   * @param parentCategoryName 一级分类名称
   * @param subCategoryName 二级分类名称
   */
  private async refreshSubCategoryExtFields(parentCategoryName: string, subCategoryName: string): Promise<void> {
    try {
      console.log(`开始刷新二级分类 "${parentCategoryName}::${subCategoryName}" 的扩展字段缓存`);

      // 使用ExtFieldService来刷新扩展字段缓存
      // 这样可以确保使用正确的API和缓存机制
      const extFieldService = ExtFieldService.getInstance();

      // 强制刷新所有扩展字段，这会重新从数据库获取最新的扩展字段定义
      await extFieldService.getAllExtFields(true);

      console.log(`成功刷新二级分类 "${subCategoryName}" 的扩展字段缓存`);
    } catch (error) {
      console.error(`刷新二级分类 "${subCategoryName}" 的扩展字段缓存失败:`, error);
    }
  }

  /**
   * 在缓存中更新指定二级分类的扩展字段
   * @param parentCategoryName 一级分类名称
   * @param subCategoryName 二级分类名称
   * @param fieldsData 从后端查询到的扩展字段数据
   */
  private async updateSubCategoryExtFieldsInCache(
    parentCategoryName: string,
    subCategoryName: string,
    fieldsData: any[]
  ): Promise<void> {
    try {
      const extFieldService = ExtFieldService.getInstance();
      const allExtFields = [...extFieldService.getState().allExtFields];

      // 查找或创建对应的分类记录
      let categoryIndex = allExtFields.findIndex(
        cat => cat.parent_category_name.toLowerCase() === parentCategoryName.toLowerCase() &&
               cat.sub_category_name.toLowerCase() === subCategoryName.toLowerCase()
      );

      if (categoryIndex === -1) {
        // 如果分类不存在，创建新的分类记录
        console.log(`创建新的分类记录: ${parentCategoryName}::${subCategoryName}`);
        const newCategory = {
          parent_category_id: 0, // 临时ID，会从fieldsData中获取正确的ID
          parent_category_name: parentCategoryName,
          sub_category_id: 0, // 临时ID，会从fieldsData中获取正确的ID
          sub_category_name: subCategoryName,
          fields: []
        };
        allExtFields.push(newCategory);
        categoryIndex = allExtFields.length - 1;
      }

      const category = allExtFields[categoryIndex];

      // 如果有字段数据，更新分类ID和字段信息
      if (fieldsData.length > 0) {
        const firstField = fieldsData[0];
        if (firstField.sub_category_id) {
          category.sub_category_id = firstField.sub_category_id;
        }
      }

      // 更新字段列表
      category.fields = fieldsData.map(field => ({
        id: field.id,
        field_name: field.field_name,
        field_type: field.field_type,
        field_type_text: this.getFieldTypeText(field.field_type),
        is_required: field.is_required === 1,
        sort_order: field.sort_order,
        is_editable: field.is_editable,
        options: field.options || []
      }));

      // 更新allExtFields
      extFieldService.updateAllExtFields(allExtFields);

      console.log(`成功在缓存中更新分类 "${parentCategoryName}::${subCategoryName}" 的 ${fieldsData.length} 个扩展字段`);
    } catch (error) {
      console.error(`在缓存中更新分类扩展字段失败:`, error);
    }
  }

  /**
   * 根据字段类型数字获取字段类型文本
   * @param fieldType 字段类型数字
   * @returns 字段类型文本
   */
  private getFieldTypeText(fieldType: number): string {
    switch (fieldType) {
      case 0:
        return 'text';
      case 1:
        return 'select';
      case 2:
        return 'date';
      default:
        return 'text';
    }
  }

  /**
   * 根据部门名称查找部门节点
   * @param categories 部门分类树
   * @param departmentName 部门名称
   * @returns 找到的部门节点或null
   */
  private findDepartmentByName(categories: DepartmentCategory[], departmentName: string): DepartmentCategory | null {
    for (const category of categories) {
      // 检查当前节点是否匹配
      if (category.name === departmentName && category.id !== 'all-dept') {
        return category;
      }

      // 递归查找子节点
      if (category.children && category.children.length > 0) {
        const found = this.findDepartmentByName(category.children, departmentName);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  /**
   * 提交任务
   * @param funcName 函数名
   * @param params 参数
   * @returns 任务执行结果
   */
  private async submitTask(funcName: string, params: any): Promise<any> {
    try {
      // 准备WebSocket请求
      const request = {
        type: "execute",
        function: `${this.dllName}::${funcName}`,
        params: params,
        priority: 0
      };

      console.log(`提交任务: ${JSON.stringify(request)}`);

      // 使用TaskManager提交任务
      const taskId = await this.task.submitTask(this.dllName, funcName, params);

      // 监听任务进度更新
      return new Promise((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          // 更新进度
          if (taskInfo.progress !== undefined) {
            this.emitter.emit('task-progress', {
              taskId,
              progress: taskInfo.progress
            });
          }

          // 处理任务完成
          if (taskInfo.status === 'completed') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);

            // 检查业务逻辑是否成功
            if (taskInfo.result) {
              let resultData = taskInfo.result;

              // 如果结果是字符串，尝试解析为JSON
              if (typeof resultData === 'string') {
                try {
                  resultData = JSON.parse(resultData);
                } catch (parseError) {
                  console.warn(`解析任务结果失败: ${funcName}, taskId: ${taskId}`, parseError);
                }
              }

              // 检查业务逻辑是否成功
              if (resultData && typeof resultData === 'object' && resultData.success === false) {
                const errorMessage = resultData.error_message || resultData.message || '操作失败';
                console.error(`任务业务逻辑失败: ${funcName}, taskId: ${taskId}, 错误: ${errorMessage}`);
                reject(new Error(errorMessage));
                return;
              }
            }

            resolve(taskInfo.result);
          }

          // 处理任务失败
          if (taskInfo.status === 'failed') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || '任务执行失败'));
          }
        };

        // 注册任务更新回调
        this.task.onTaskUpdate(taskId, handleTaskUpdate);
      });
    } catch (error) {
      console.error(`执行任务 ${funcName} 失败:`, error);
      throw error;
    }
  }

  /**
   * 订阅事件
   */
  public on<K extends keyof DeviceServiceEvents>(
    event: K,
    callback: (data: DeviceServiceEvents[K]) => void
  ) {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消订阅事件
   */
  public off<K extends keyof DeviceServiceEvents>(
    event: K,
    callback: (data: DeviceServiceEvents[K]) => void
  ) {
    this.emitter.off(event, callback as any);
  }

  /**
   * 重命名设备分类
   * @param categoryId 分类ID
   * @param newName 新的分类名称
   */
  public async renameCategory(categoryId: string, newName: string): Promise<any> {
    try {
      // 构建请求参数，按照新的API格式
      const params = {
        action: "RenameDeviceCategory",
        action_params: {
          categoryId,
          newName
        }
      };

      console.log('开始重命名设备分类:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      this.emitter.emit('task-complete', result);

      return result;
    } catch (error) {
      console.error('重命名设备分类失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }

  /**
   * 删除设备分类
   * @param categoryId 分类ID
   */
  public async deleteCategory(categoryId: string): Promise<any> {
    try {
      // 构建请求参数，按照新的API格式
      const params = {
        action: "DeleteDeviceCategory",
        action_params: {
          categoryId
        }
      };

      console.log('开始删除设备分类:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      this.emitter.emit('task-complete', result);

      return result;
    } catch (error) {
      console.error('删除设备分类失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }



  /**
   * 更新设备父类（一级分类）
   * @param curParentCategoryName 当前父类名称
   * @param newParentCategoryName 新的父类名称
   */
  public async updateParentCategory(curParentCategoryName: string, newParentCategoryName: string): Promise<any> {
    try {
      // 构建请求参数，按照API格式
      const params = {
        action: "update_parent_category",
        action_params: {
          parent_category_name: curParentCategoryName,
          new_parent_category_name: newParentCategoryName
        }
      };

      console.log('开始更新设备父类:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      this.emitter.emit('task-complete', result);

      // 只触发一级分类更新事件，不再触发分类树更新事件
      // 避免重复刷新，由监听parent-category-updated事件的处理函数负责刷新
      // 添加标记，表明这是更新操作，不需要刷新总表
      this.emitter.emit('parent-category-updated', {
        curParentCategoryName,
        newParentCategoryName,
        result,
        action: 'update',
        operation: 'update',
        type: 'update'
      });

      return result;
    } catch (error) {
      console.error('更新设备父类失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }

  /**
   * 检查一级分类是否有二级分类
   * @param parentCategoryName 一级分类名称
   * @returns 如果有二级分类返回true，否则返回false
   */
  public async hasSubCategories(parentCategoryName: string): Promise<boolean> {
    try {
      // 构建请求参数，获取所有二级分类
      const params = {
        action: "get_sub_category",
        action_params: {}
      };

      console.log('检查一级分类是否有二级分类:', parentCategoryName);

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      if (!result || !result.success || !Array.isArray(result.data)) {
        console.warn('获取二级分类失败或数据格式不正确:', result);
        return false;
      }

      // 检查是否有属于该一级分类的二级分类
      const hasSubCategories = result.data.some((subCategory: any) =>
        subCategory.parent_category_name === parentCategoryName
      );

      console.log(`一级分类 ${parentCategoryName} ${hasSubCategories ? '有' : '没有'}二级分类`);
      return hasSubCategories;
    } catch (error) {
      console.error('检查一级分类是否有二级分类失败:', error);
      // 出错时默认返回false，避免阻止删除操作
      return false;
    }
  }

  /**
   * 删除设备父类（一级分类）
   * @param parentCategoryName 父类名称
   */
  public async deleteParentCategory(parentCategoryName: string): Promise<any> {
    try {
      // 先检查该一级分类是否有二级分类
      const hasSubCategories = await this.hasSubCategories(parentCategoryName);
      if (hasSubCategories) {
        // 如果有二级分类，抛出错误
        throw new Error(`该分类下含有设备不能直接删除`);
      }

      // 构建请求参数，按照API格式
      const params = {
        action: "delete_parent_category",
        action_params: {
          parent_category_name: parentCategoryName
        }
      };

      console.log('开始删除设备父类:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      this.emitter.emit('task-complete', result);

      // 只触发一级分类删除事件，不再触发分类树更新事件
      // 避免重复刷新，由监听parent-category-deleted事件的处理函数负责刷新
      // 添加删除操作标记，以便处理函数知道这是删除操作
      this.emitter.emit('parent-category-deleted', { parentCategoryName, result, action: 'delete', operation: 'delete' });

      return result;
    } catch (error) {
      console.error('删除设备父类失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }

  /**
   * 更新设备子类（二级分类）
   * @param curSubCategoryName 当前子类名称
   * @param newSubCategoryName 新的子类名称
   */
  public async updateSubCategory(curSubCategoryName: string, newSubCategoryName: string): Promise<any> {
    try {
      // 构建请求参数，按照API格式
      const params = {
        action: "update_sub_category",
        action_params: {
          sub_category_name: curSubCategoryName,
          new_sub_category_name: newSubCategoryName
        }
      };

      console.log('开始更新设备子类:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      this.emitter.emit('task-complete', result);

      // 只触发二级分类更新事件，不再触发分类树更新事件
      // 避免重复刷新，由监听sub-category-updated事件的处理函数负责刷新
      // 添加标记，表明这是更新操作，不需要通过请求数据库刷新总表
      this.emitter.emit('sub-category-updated', {
        curSubCategoryName,
        newSubCategoryName,
        result,
        action: 'update',
        operation: 'update',
        type: 'update',
        updateLocalOnly: true // 添加标记，表明只需要更新本地数据
      });

      return result;
    } catch (error) {
      console.error('更新设备子类失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }

  /**
   * 检查二级分类是否有台账信息
   * @param subCategoryName 二级分类名称
   * @returns 如果有台账信息返回true，否则返回false
   */
  public async hasDevicesInSubCategory(subCategoryName: string): Promise<boolean> {
    try {
      // 构建请求参数，获取所有设备
      const params = {
        action: "get_device",
        action_params: {}
      };

      console.log('检查二级分类是否有台账信息:', subCategoryName);

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      // 检查返回结果的格式
      if (!result) {
        console.warn('获取设备数据失败，结果为空');
        return false;
      }

      // 使用类型断言处理结果
      const typedResult = result as any;

      if (!typedResult.success || !typedResult.data) {
        console.warn('获取设备数据失败或数据格式不正确:', typedResult);
        return false;
      }

      // 获取设备数据 - 适配新的API数据结构
      let devices = [];
      if (Array.isArray(typedResult.data)) {
        // 新的API格式：data直接是设备数组
        devices = typedResult.data;
      } else if (typedResult.data.devices && Array.isArray(typedResult.data.devices)) {
        // 旧的API格式：data.devices是设备数组
        devices = typedResult.data.devices;
      } else {
        console.warn('设备数据格式不符合预期:', typedResult);
        return false;
      }

      // 检查是否有属于该二级分类的设备
      const hasDevices = devices.some((device: any) =>
        device.sub_category_name === subCategoryName
      );

      console.log(`二级分类 ${subCategoryName} ${hasDevices ? '有' : '没有'}台账信息`);
      return hasDevices;
    } catch (error) {
      console.error('检查二级分类是否有台账信息失败:', error);
      // 出错时默认返回false，避免阻止删除操作
      return false;
    }
  }

  /**
   * 删除设备子类（二级分类）
   * @param subCategoryName 子类名称
   */
  public async deleteSubCategory(subCategoryName: string): Promise<any> {
    try {
      // 构建请求参数，按照API格式
      const params = {
        action: "delete_sub_category",
        action_params: {
          sub_category_name: subCategoryName
        }
      };

      console.log('开始删除设备子类:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('DbFun', params);

      // 检查结果，如果后端返回错误（例如该分类下有设备），则抛出异常
      if (result && !result.success) {
        throw new Error(result.message || `删除设备子类 ${subCategoryName} 失败`);
      }

      this.emitter.emit('task-complete', result);

      // 只触发二级分类删除事件，不再触发分类树更新事件
      // 避免重复刷新，由监听sub-category-deleted事件的处理函数负责刷新
      // 添加删除操作标记，以便处理函数知道这是删除操作
      // 添加标记，表明需要更新扩展字段信息
      this.emitter.emit('sub-category-deleted', {
        subCategoryName,
        result,
        action: 'delete',
        operation: 'delete',
        updateExtFields: true // 添加标记，表明需要更新扩展字段信息
      });

      return result;
    } catch (error) {
      console.error('删除设备子类失败:', error);
      this.emitter.emit('task-error', error as Error);
      throw error;
    }
  }

  /**
   * 将设备数据对象从前端格式转换为后端格式（扩展版）
   * @param item 前端格式的设备数据
   * @param extFields 扩展字段值
   * @deprecated 请使用 ExtFieldService 处理扩展字段
   */
  public convertInventoryItemToDeviceDataExt(item: InventoryItem, extFields: Record<string, any> = {}): any {
    // 首先获取基础转换结果
    const baseData = this.convertInventoryItemToDeviceData(item);

    // 使用扩展字段服务处理扩展字段数据
    const extFieldService = ExtFieldService.getInstance();
    const processedExtFields = extFieldService.convertExtFieldsToBackendFormat(extFields);

    // 将扩展字段合并到基础数据中
    return {
      ...baseData,
      extFieldsData: processedExtFields  // 添加扩展字段数据对象
    };
  }

  /**
   * 触发设备更新事件
   * @param data 设备更新事件数据
   */
  public emitDeviceUpdated(data: any): void {
    console.log('触发设备更新事件:', data);
    this.emitter.emit('device-updated', data);
  }


}

export default DeviceService;