import mitt, { Emitter } from 'mitt';

/**
 * 公告WebSocket事件接口
 */
interface AnnouncementWebSocketEvents {
  'connected': void;
  'disconnected': void;
  'message': any;
  'error': string;
  [key: string]: any;
}

/**
 * 公告WebSocket管理器
 * 专门用于与公告服务器的WebSocket通信
 * 单例模式，避免重复连接
 */
class AnnouncementWebSocketManager {
  private static instance: AnnouncementWebSocketManager;
  private emitter: Emitter<AnnouncementWebSocketEvents> = mitt<AnnouncementWebSocketEvents>();
  private ws: WebSocket | null = null;
  private isConnecting: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000; // 1秒

  // WebSocket服务器配置
  private readonly WS_URL = 'ws://39.107.221.146:8765';
  private readonly CONNECTION_TIMEOUT = 10000; // 10秒连接超时

  /**
   * 获取实例
   */
  public static getInstance(): AnnouncementWebSocketManager {
    if (!AnnouncementWebSocketManager.instance) {
      AnnouncementWebSocketManager.instance = new AnnouncementWebSocketManager();
    }
    return AnnouncementWebSocketManager.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 连接WebSocket服务器
   */
  public async connect(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return; // 已经连接
    }

    if (this.isConnecting) {
      // 如果正在连接，等待连接完成
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            resolve();
          } else if (!this.isConnecting) {
            reject(new Error('连接失败'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        console.log('正在连接公告服务器:', this.WS_URL);
        this.ws = new WebSocket(this.WS_URL);

        // 连接超时处理
        const connectionTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            this.isConnecting = false;
            reject(new Error('连接公告服务器超时'));
          }
        }, this.CONNECTION_TIMEOUT);

        this.ws.onopen = () => {
          clearTimeout(connectionTimeout);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          console.log('公告服务器连接成功');
          this.emitter.emit('connected');
          resolve();
        };

        this.ws.onerror = (error) => {
          clearTimeout(connectionTimeout);
          this.isConnecting = false;
          console.error('公告服务器连接错误:', error);
          this.emitter.emit('error', '连接公告服务器失败');
          reject(new Error('连接公告服务器失败'));
        };

        this.ws.onclose = (event) => {
          clearTimeout(connectionTimeout);
          this.isConnecting = false;
          console.log('公告服务器连接已关闭, code:', event.code, 'reason:', event.reason);
          this.ws = null;
          this.emitter.emit('disconnected');

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('收到公告服务器消息:', data);
            this.emitter.emit('message', data);
          } catch (error) {
            console.error('解析公告服务器消息失败:', error);
            this.emitter.emit('error', '解析消息失败');
          }
        };

      } catch (error) {
        this.isConnecting = false;
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
    
    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连公告服务器...`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连公告服务器失败:', error);
      });
    }, delay);
  }

  /**
   * 发送消息
   */
  public async send(data: any): Promise<void> {
    await this.connect(); // 确保连接

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      console.log('发送公告服务器消息:', message);
      this.ws.send(message);
    } else {
      throw new Error('WebSocket连接未建立');
    }
  }

  /**
   * 发送请求并等待响应
   */
  public async sendRequest(request: any, expectedResponseType: string, timeout: number = 10000): Promise<any> {
    await this.connect(); // 确保连接

    return new Promise((resolve, reject) => {
      // 请求超时处理
      const requestTimeout = setTimeout(() => {
        this.emitter.off('message', messageHandler);
        reject(new Error('请求超时'));
      }, timeout);

      // 监听消息响应
      const messageHandler = (response: any) => {
        try {
          if (response.type === expectedResponseType) {
            clearTimeout(requestTimeout);
            this.emitter.off('message', messageHandler);
            resolve(response);
          }
        } catch (error) {
          console.error('处理响应失败:', error);
        }
      };

      // 注册消息监听器
      this.emitter.on('message', messageHandler);
      
      // 发送请求
      this.send(request).catch(error => {
        clearTimeout(requestTimeout);
        this.emitter.off('message', messageHandler);
        reject(error);
      });
    });
  }

  /**
   * 关闭连接
   */
  public close(): void {
    if (this.ws) {
      this.ws.close(1000, '主动关闭');
      this.ws = null;
    }
  }

  /**
   * 获取连接状态
   */
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 事件订阅
   */
  public on<K extends keyof AnnouncementWebSocketEvents>(
    event: K, 
    callback: (data: AnnouncementWebSocketEvents[K]) => void
  ): void {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消事件订阅
   */
  public off<K extends keyof AnnouncementWebSocketEvents>(
    event: K, 
    callback: (data: AnnouncementWebSocketEvents[K]) => void
  ): void {
    this.emitter.off(event, callback as any);
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    this.close();
    this.emitter.all.clear();
  }
}

export default AnnouncementWebSocketManager;
