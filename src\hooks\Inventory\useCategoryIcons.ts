import React from 'react';
import {
  Server,
  Folder,
  FolderOpen,
  Monitor,
  Database,
  ShieldAlert,
  Printer,
  Smartphone,
  HardDrive,
  Laptop,
  Building2,
  Building,
  Users,
  User,
  Usb,
  Calculator,
  Speaker,
  Cpu,
  Briefcase,
  // 新增图标 - 工具类
  Wrench,
  Hammer,
  Settings,
  Cog,
  // 通信类
  Wifi,
  Radio,
  Bluetooth,
  Signal,
  // 电源类
  Battery,
  Zap,
  Power,
  Plug,
  // 存储类
  Archive,
  Save,
  Download,
  Upload,
  // 安全类
  Lock,
  Key,
  Shield,
  Eye,
  EyeOff,
  // 显示类
  Tv,
  Camera,
  Video,
  Image,
  // 网络类
  Globe,
  Link,
  // 其他设备类
  Gamepad2,
  Headphones,
  Mic,
  Volume2,
  Clock,
  Timer,
  Thermometer,
  Activity,
  // 新增设备相关图标 - 使用存在的图标名称
  Router,
  Tablet,
  Watch,
  MousePointer,
  Keyboard,
  // Webcam, // 不存在，用 Camera 替代
  // Projector, // 不存在，用 Monitor 替代
  ScanLine,
  Fingerprint,
  Lightbulb,
  Fan,
  // Refrigerator, // 不存在，用 Archive 替代
  Car,
  Truck,
  Plane,
  Ship,
  // Train, // 不存在，用 Truck 替代
  Bike,
  Fuel,
  Gauge,
  Compass,
  MapPin,
  Navigation,
  // Radar, // 不存在，用 Radio 替代
  Satellite,
  // Antenna, // 不存在，用 Radio 替代
  Rss,
  Cast,
  // Airplay, // 不存在，用 Cast 替代
  MonitorSpeaker,
  Disc,
  // HardDriveDownload, // 不存在，用 Download 替代
  // HardDriveUpload, // 不存在，用 Upload 替代
  MemoryStick,
  // SdCard, // 不存在，用 Archive 替代
  // Sim, // 不存在，用 Smartphone 替代
  CircuitBoard,
  // Microchip, // 不存在，用 Cpu 替代
  BatteryCharging,
  BatteryLow,
  PowerOff,
  PlugZap,
  Cable,
  // Ethernet, // 不存在，用 Cable 替代
  WifiOff,
  // SignalHigh, // 不存在，用 Signal 替代
  // SignalLow, // 不存在，用 Signal 替代
  // SignalMedium, // 不存在，用 Signal 替代
  // SignalZero // 不存在，用 Signal 替代
} from 'lucide-react';
import { DeviceCategory } from '../../types/inventory';
import { DepartmentCategory } from '../../hooks/Inventory/useDepartment';

// 图标类型
export type IconType =
  | 'server'
  | 'folder'
  | 'folderOpen'
  | 'monitor'
  | 'database'
  | 'shield'
  | 'printer'
  | 'smartphone'
  | 'hardDrive'
  | 'laptop'
  | 'building2'
  | 'building'
  | 'users'
  | 'user'
  | 'usb'
  | 'calculator'
  | 'speaker'
  | 'cpu'
  | 'briefcase'
  // 新增图标类型
  | 'wrench'
  | 'hammer'
  | 'settings'
  | 'cog'
  | 'wifi'
  | 'radio'
  | 'bluetooth'
  | 'signal'
  | 'battery'
  | 'zap'
  | 'power'
  | 'plug'
  | 'archive'
  | 'save'
  | 'download'
  | 'upload'
  | 'lock'
  | 'key'
  | 'shieldAlert'
  | 'eye'
  | 'eyeOff'
  | 'tv'
  | 'camera'
  | 'video'
  | 'image'
  | 'globe'
  | 'link'
  | 'gamepad2'
  | 'headphones'
  | 'mic'
  | 'volume2'
  | 'clock'
  | 'timer'
  | 'thermometer'
  | 'activity'
  // 新增设备相关图标类型 - 只保留存在的图标
  | 'router'
  | 'tablet'
  | 'watch'
  | 'mousePointer'
  | 'keyboard'
  | 'scanLine'
  | 'fingerprint'
  | 'lightbulb'
  | 'fan'
  | 'car'
  | 'truck'
  | 'plane'
  | 'ship'
  | 'bike'
  | 'fuel'
  | 'gauge'
  | 'compass'
  | 'mapPin'
  | 'navigation'
  | 'satellite'
  | 'rss'
  | 'cast'
  | 'monitorSpeaker'
  | 'disc'
  | 'memoryStick'
  | 'circuitBoard'
  | 'batteryCharging'
  | 'batteryLow'
  | 'powerOff'
  | 'plugZap'
  | 'cable'
  | 'wifiOff';

// 图标配置接口
export interface IconConfig {
  icon: IconType;
  className?: string;
  color?: string;
}

// 图标映射
const iconComponents: Record<IconType, React.ComponentType<any>> = {
  server: Server,
  folder: Folder,
  folderOpen: FolderOpen,
  monitor: Monitor,
  database: Database,
  shield: ShieldAlert,
  printer: Printer,
  smartphone: Smartphone,
  hardDrive: HardDrive,
  laptop: Laptop,
  building2: Building2,
  building: Building,
  users: Users,
  user: User,
  usb: Usb,
  calculator: Calculator,
  speaker: Speaker,
  cpu: Cpu,
  briefcase: Briefcase,
  // 新增图标映射
  wrench: Wrench,
  hammer: Hammer,
  settings: Settings,
  cog: Cog,
  wifi: Wifi,
  radio: Radio,
  bluetooth: Bluetooth,
  signal: Signal,
  battery: Battery,
  zap: Zap,
  power: Power,
  plug: Plug,
  archive: Archive,
  save: Save,
  download: Download,
  upload: Upload,
  lock: Lock,
  key: Key,
  shieldAlert: Shield,
  eye: Eye,
  eyeOff: EyeOff,
  tv: Tv,
  camera: Camera,
  video: Video,
  image: Image,
  globe: Globe,
  link: Link,
  gamepad2: Gamepad2,
  headphones: Headphones,
  mic: Mic,
  volume2: Volume2,
  clock: Clock,
  timer: Timer,
  thermometer: Thermometer,
  activity: Activity,
  // 新增设备相关图标映射 - 只保留存在的图标
  router: Router,
  tablet: Tablet,
  watch: Watch,
  mousePointer: MousePointer,
  keyboard: Keyboard,
  scanLine: ScanLine,
  fingerprint: Fingerprint,
  lightbulb: Lightbulb,
  fan: Fan,
  car: Car,
  truck: Truck,
  plane: Plane,
  ship: Ship,
  bike: Bike,
  fuel: Fuel,
  gauge: Gauge,
  compass: Compass,
  mapPin: MapPin,
  navigation: Navigation,
  satellite: Satellite,
  rss: Rss,
  cast: Cast,
  monitorSpeaker: MonitorSpeaker,
  disc: Disc,
  memoryStick: MemoryStick,
  circuitBoard: CircuitBoard,
  batteryCharging: BatteryCharging,
  batteryLow: BatteryLow,
  powerOff: PowerOff,
  plugZap: PlugZap,
  cable: Cable,
  wifiOff: WifiOff
};

// 分类图标配置接口
export interface CategoryIconConfig {
  icon: IconType;
  darkColor: string;  // 一级分类使用的深色
  lightColor: string; // 二级分类使用的浅色
}

// 分类图标配置映射表
export const CATEGORY_ICON_CONFIG: Record<string, CategoryIconConfig> = {
  '计算机类': { icon: 'laptop', darkColor: 'text-indigo-600', lightColor: 'text-indigo-400' },
  '移动存储类': { icon: 'database', darkColor: 'text-orange-600', lightColor: 'text-orange-400' },
  '外接设备类': { icon: 'usb', darkColor: 'text-green-700', lightColor: 'text-green-500' },
  '办公自动化设备类': { icon: 'calculator', darkColor: 'text-violet-600', lightColor: 'text-violet-400' },
  '网络设备类': { icon: 'server', darkColor: 'text-blue-600', lightColor: 'text-blue-400' },
  '安全产品类': { icon: 'shield', darkColor: 'text-red-600', lightColor: 'text-red-400' },
  '声像设备类': { icon: 'speaker', darkColor: 'text-pink-600', lightColor: 'text-pink-400' },
  '打印设备类': { icon: 'printer', darkColor: 'text-purple-600', lightColor: 'text-purple-400' },
  '移动设备类': { icon: 'smartphone', darkColor: 'text-blue-500', lightColor: 'text-blue-300' },
  '存储设备类': { icon: 'hardDrive', darkColor: 'text-gray-600', lightColor: 'text-gray-400' },
  '服务器类': { icon: 'server', darkColor: 'text-blue-700', lightColor: 'text-blue-500' },
  '终端设备类': { icon: 'monitor', darkColor: 'text-gray-700', lightColor: 'text-gray-500' }
};

// 默认颜色映射
const defaultColors: Record<IconType, string> = {
  server: 'text-blue-600',
  folder: 'text-yellow-600',
  folderOpen: 'text-yellow-600',
  monitor: 'text-gray-600',
  database: 'text-orange-600',
  shield: 'text-red-600',
  printer: 'text-purple-600',
  smartphone: 'text-blue-500',
  hardDrive: 'text-gray-600',
  laptop: 'text-indigo-600',
  building2: 'text-blue-600',
  building: 'text-yellow-600',
  users: 'text-green-600',
  user: 'text-gray-600',
  usb: 'text-green-700',
  calculator: 'text-violet-600',
  speaker: 'text-pink-600',
  cpu: 'text-gray-700',
  briefcase: 'text-amber-600',
  // 新增图标默认颜色
  wrench: 'text-gray-600',
  hammer: 'text-orange-600',
  settings: 'text-gray-600',
  cog: 'text-gray-600',
  wifi: 'text-blue-500',
  radio: 'text-green-600',
  bluetooth: 'text-blue-500',
  signal: 'text-green-500',
  battery: 'text-green-600',
  zap: 'text-yellow-500',
  power: 'text-red-600',
  plug: 'text-gray-600',
  archive: 'text-brown-600',
  save: 'text-blue-600',
  download: 'text-green-600',
  upload: 'text-blue-600',
  lock: 'text-red-600',
  key: 'text-yellow-600',
  shieldAlert: 'text-red-600',
  eye: 'text-blue-600',
  eyeOff: 'text-gray-600',
  tv: 'text-purple-600',
  camera: 'text-gray-700',
  video: 'text-red-600',
  image: 'text-green-600',
  globe: 'text-blue-600',
  link: 'text-blue-500',
  gamepad2: 'text-purple-600',
  headphones: 'text-gray-700',
  mic: 'text-red-600',
  volume2: 'text-blue-600',
  clock: 'text-gray-600',
  timer: 'text-orange-600',
  thermometer: 'text-red-500',
  activity: 'text-green-600',
  // 新增设备相关图标默认颜色 - 只保留存在的图标
  router: 'text-blue-600',
  tablet: 'text-indigo-600',
  watch: 'text-purple-600',
  mousePointer: 'text-gray-600',
  keyboard: 'text-gray-700',
  scanLine: 'text-green-600',
  fingerprint: 'text-blue-700',
  lightbulb: 'text-yellow-500',
  fan: 'text-cyan-600',
  car: 'text-red-600',
  truck: 'text-orange-600',
  plane: 'text-blue-600',
  ship: 'text-blue-700',
  bike: 'text-green-500',
  fuel: 'text-orange-500',
  gauge: 'text-gray-600',
  compass: 'text-red-600',
  mapPin: 'text-red-500',
  navigation: 'text-blue-600',
  satellite: 'text-blue-600',
  rss: 'text-orange-600',
  cast: 'text-purple-600',
  monitorSpeaker: 'text-gray-700',
  disc: 'text-purple-500',
  memoryStick: 'text-green-700',
  circuitBoard: 'text-green-600',
  batteryCharging: 'text-green-600',
  batteryLow: 'text-red-600',
  powerOff: 'text-red-600',
  plugZap: 'text-yellow-600',
  cable: 'text-gray-600',
  wifiOff: 'text-red-600'
};

// 图标分类配置
export interface IconCategoryConfig {
  name: string;
  description: string;
  icons: IconType[];
}

// 图标分类
export const ICON_CATEGORIES: IconCategoryConfig[] = [
  {
    name: 'device',
    description: '设备类',
    icons: ['laptop', 'monitor', 'smartphone', 'printer', 'server', 'hardDrive', 'database']
  },
  {
    name: 'tool',
    description: '工具类',
    icons: ['wrench', 'hammer', 'settings', 'cog', 'calculator']
  },
  {
    name: 'communication',
    description: '通信类',
    icons: ['wifi', 'radio', 'bluetooth', 'signal', 'globe', 'link']
  },
  {
    name: 'power',
    description: '电源类',
    icons: ['battery', 'zap', 'power', 'plug']
  },
  {
    name: 'storage',
    description: '存储类',
    icons: ['archive', 'save', 'download', 'upload', 'folder', 'folderOpen']
  },
  {
    name: 'security',
    description: '安全类',
    icons: ['lock', 'key', 'shield', 'shieldAlert', 'eye', 'eyeOff']
  },
  {
    name: 'display',
    description: '显示类',
    icons: ['tv', 'camera', 'video', 'image']
  },
  {
    name: 'audio',
    description: '音频类',
    icons: ['speaker', 'headphones', 'mic', 'volume2']
  },
  {
    name: 'network',
    description: '网络类',
    icons: ['usb', 'server', 'globe']
  },
  {
    name: 'other',
    description: '其他类',
    icons: ['gamepad2', 'clock', 'timer', 'thermometer', 'activity', 'briefcase', 'building', 'building2', 'users', 'user', 'cpu']
  }
];

// 图标描述映射
export const ICON_DESCRIPTIONS: Record<IconType, string> = {
  server: '服务器',
  folder: '文件夹',
  folderOpen: '打开文件夹',
  monitor: '显示器',
  database: '数据库',
  shield: '盾牌',
  printer: '打印机',
  smartphone: '智能手机',
  hardDrive: '硬盘',
  laptop: '笔记本电脑',
  building2: '建筑',
  building: '办公楼',
  users: '用户组',
  user: '用户',
  usb: 'USB',
  calculator: '计算器',
  speaker: '扬声器',
  cpu: '处理器',
  briefcase: '公文包',
  wrench: '扳手',
  hammer: '锤子',
  settings: '设置',
  cog: '齿轮',
  wifi: 'WiFi',
  radio: '无线电',
  bluetooth: '蓝牙',
  signal: '信号',
  battery: '电池',
  zap: '闪电',
  power: '电源',
  plug: '插头',
  archive: '归档',
  save: '保存',
  download: '下载',
  upload: '上传',
  lock: '锁',
  key: '钥匙',
  shieldAlert: '安全警告',
  eye: '眼睛',
  eyeOff: '隐藏',
  tv: '电视',
  camera: '摄像头',
  video: '视频',
  image: '图像',
  globe: '地球',
  link: '链接',
  gamepad2: '游戏手柄',
  headphones: '耳机',
  mic: '麦克风',
  volume2: '音量',
  clock: '时钟',
  timer: '计时器',
  thermometer: '温度计',
  activity: '活动',
  // 新增设备相关图标描述 - 只保留存在的图标
  router: '路由器',
  tablet: '平板电脑',
  watch: '智能手表',
  mousePointer: '鼠标',
  keyboard: '键盘',
  scanLine: '扫描仪',
  fingerprint: '指纹识别',
  lightbulb: '灯泡',
  fan: '风扇',
  car: '汽车',
  truck: '卡车',
  plane: '飞机',
  ship: '轮船',
  bike: '自行车',
  fuel: '燃料',
  gauge: '仪表',
  compass: '指南针',
  mapPin: '地图标记',
  navigation: '导航',
  satellite: '卫星',
  rss: 'RSS订阅',
  cast: '投屏',
  monitorSpeaker: '显示器音响',
  disc: '光盘',
  memoryStick: '内存条',
  circuitBoard: '电路板',
  batteryCharging: '电池充电',
  batteryLow: '电池低电量',
  powerOff: '关机',
  plugZap: '电源插头',
  cable: '数据线',
  wifiOff: 'WiFi关闭'
};

/**
 * 根据分类名称或ID检测父级分类
 * @param categoryName 分类名称
 * @param categoryId 分类ID
 * @param categories 分类树数据
 * @returns 父级分类名称
 */
export function detectParentCategory(
  categoryName: string,
  categoryId: string,
  categories?: (DeviceCategory | DepartmentCategory)[]
): string | null {
  // 如果是根节点，返回null
  if (categoryId === 'all' || categoryId === 'all-dept') {
    return null;
  }

  // 如果是一级分类（parent-xxx格式），直接返回分类名称
  if (categoryId.startsWith('parent-') && !categoryId.includes('-', 7)) {
    return categoryName;
  }

  // 如果是二级分类（parent-xxx-yyy格式），从分类树中查找父级
  if (categoryId.startsWith('parent-') && categoryId.includes('-', 7)) {
    if (categories) {
      // 从分类树中查找父级分类
      for (const rootCategory of categories) {
        if (rootCategory.children) {
          for (const parentCategory of rootCategory.children) {
            if (parentCategory.children) {
              for (const subCategory of parentCategory.children) {
                if (subCategory.id === categoryId) {
                  return parentCategory.name;
                }
              }
            }
          }
        }
      }
    }

    // 如果找不到，尝试从ID中解析父级ID
    const parts = categoryId.split('-');
    if (parts.length >= 3) {
      const parentId = `parent-${parts[1]}`;
      // 这里可以进一步查找，但为了简化，我们使用名称匹配
    }
  }

  // 通过名称关键词匹配父级分类
  for (const [parentName] of Object.entries(CATEGORY_ICON_CONFIG)) {
    if (categoryName.includes(parentName.replace('类', '')) ||
        categoryName.includes(parentName)) {
      return parentName;
    }
  }

  return null;
}

/**
 * 获取分类的图标配置
 * @param categoryName 分类名称
 * @param categoryId 分类ID
 * @param depth 分类深度（0=根节点，1=一级分类，2=二级分类）
 * @param categories 分类树数据
 * @param customIcon 自定义图标类型
 * @returns 图标配置
 */
export function getCategoryIconConfig(
  categoryName: string,
  categoryId: string,
  depth: number,
  categories?: (DeviceCategory | DepartmentCategory)[],
  customIcon?: string
): { icon: IconType; color: string } {
  // 优先使用自定义图标
  if (customIcon && customIcon in defaultColors) {
    const iconType = customIcon as IconType;
    const baseColor = defaultColors[iconType];

    // 根据深度调整颜色深浅
    if (depth === 1) {
      // 一级分类使用原色
      return { icon: iconType, color: baseColor };
    } else if (depth === 2) {
      // 二级分类使用浅色版本
      const lightColor = baseColor.replace('-600', '-400').replace('-700', '-500').replace('-500', '-300');
      return { icon: iconType, color: lightColor };
    } else {
      return { icon: iconType, color: baseColor };
    }
  }

  // 根节点
  if (depth === 0 || categoryId === 'all' || categoryId === 'all-dept') {
    return { icon: 'building2', color: 'text-blue-600' };
  }

  // 一级分类
  if (depth === 1) {
    const config = CATEGORY_ICON_CONFIG[categoryName];
    if (config) {
      return { icon: config.icon, color: config.darkColor };
    }
    // 默认一级分类图标
    return { icon: 'folder', color: 'text-yellow-600' };
  }

  // 二级分类
  if (depth === 2) {
    // 检测父级分类
    const parentCategory = detectParentCategory(categoryName, categoryId, categories);
    if (parentCategory) {
      const config = CATEGORY_ICON_CONFIG[parentCategory];
      if (config) {
        return { icon: config.icon, color: config.lightColor };
      }
    }

    // 如果找不到父级配置，使用默认二级分类图标
    return { icon: 'monitor', color: 'text-gray-400' };
  }

  // 默认图标
  return { icon: 'folder', color: 'text-gray-600' };
}

// 钩子参数接口
export interface UseCategoryIconsOptions {
  deviceIconOverrides?: Partial<Record<string, IconConfig>>;
  departmentIconOverrides?: Partial<Record<string, IconConfig>>;
  defaultIconSize?: string;
}

/**
 * 分类图标Hook - 处理分类树的图标逻辑
 * 提供灵活的图标配置和自定义选项
 */
export function useCategoryIcons(options: UseCategoryIconsOptions = {}) {
  const {
    deviceIconOverrides = {},
    departmentIconOverrides = {},
    defaultIconSize = 'w-4 h-4 mr-1'
  } = options;

  /**
   * 创建图标元素
   * @param iconType 图标类型
   * @param className 自定义类名
   * @returns React元素
   */
  const createIcon = (iconType: IconType, className?: string): React.ReactElement => {
    const IconComponent = iconComponents[iconType];
    const defaultColor = defaultColors[iconType];
    const fullClassName = className || `${defaultIconSize} ${defaultColor}`;

    return React.createElement(IconComponent, { className: fullClassName });
  };

  /**
   * 根据分类和层级获取合适的图标
   * @param category 分类对象
   * @param depth 层级深度
   * @param categoryMode 分类模式
   * @param isExpanded 是否展开
   * @returns React元素
   */
  const getIconForCategory = (
    category: DeviceCategory | DepartmentCategory,
    depth: number,
    categoryMode: 'device' | 'department',
    isExpanded: boolean = false
  ): React.ReactElement => {
    // 设备分类模式
    if (categoryMode === 'device') {
      // 检查是否有自定义图标配置
      const customConfig = deviceIconOverrides[category.id];
      if (customConfig) {
        return createIcon(customConfig.icon, customConfig.className);
      }

      // 根节点 - 全部设备
      if (category.id === 'all') {
        return createIcon('building2');
      }

      // 子节点 - 根据设备类型选择不同图标
      if (depth === 0) {
        // 一级分类
        return isExpanded
          ? createIcon('folderOpen')
          : createIcon('folder');
      } else if (depth === 1) {
        // 二级分类
        const name = category.name.toLowerCase();

        // 根据名称选择合适的图标
        if (category.id.startsWith('new-') || (category as DeviceCategory).isNew) {
          return createIcon('cpu');
        } else if (name.includes('计算')) {
          return createIcon('laptop');
        } else if (name.includes('存储')) {
          return createIcon('database');
        } else if (name.includes('网络') || name.includes('路由')) {
          return createIcon('server');
        } else if (name.includes('安全') || name.includes('防火墙')) {
          return createIcon('shield');
        } else if (name.includes('打印')) {
          return createIcon('printer');
        } else if (name.includes('移动') || name.includes('手机')) {
          return createIcon('smartphone');
        } else if (name.includes('外接') || name.includes('外设')) {
          return createIcon('usb');
        } else if (name.includes('办公') || name.includes('自动化')) {
          return createIcon('calculator');
        } else if (name.includes('声像') || name.includes('音频') || name.includes('视频')) {
          return createIcon('speaker');
        } else {
          return createIcon('hardDrive');
        }
      } else {
        // 更深层级 - 具体设备
        return createIcon('monitor');
      }
    }
    // 部门分类模式
    else {
      // 检查是否有自定义图标配置
      const customConfig = departmentIconOverrides[category.id];
      if (customConfig) {
        return createIcon(customConfig.icon, customConfig.className);
      }

      // 根节点 - 全部部门
      if (category.id === 'all-dept') {
        return createIcon('building2');
      }

      // 部门节点
      if (category.id.startsWith('dept-')) {
        // 所有部门节点统一使用同一个图标
        return createIcon('building');
      }

      // 人员节点
      if (category.id.startsWith('person-') || category.id.startsWith('resp-')) {
        // 所有人员节点统一使用同一个图标
        return createIcon('user');
      }

      // 默认图标
      return createIcon('briefcase');
    }
  };

  /**
   * 获取特定类型的图标
   * @param iconType 图标类型
   * @param className 自定义类名
   * @returns React元素
   */
  const getIcon = (iconType: IconType, className?: string): React.ReactElement => {
    return createIcon(iconType, className);
  };

  return {
    getIconForCategory,
    getIcon,
    iconTypes: Object.keys(iconComponents) as IconType[]
  };
}
