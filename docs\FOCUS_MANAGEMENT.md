# 焦点管理系统文档

## 概述

本项目实现了一套完整的焦点管理系统，确保应用程序符合Web无障碍访问标准（WCAG），为键盘用户和屏幕阅读器用户提供良好的体验。

## 核心组件

### 1. 基础焦点管理 (`useFocusManager`)

提供核心的焦点管理功能：

```typescript
import { getFocusManager } from '../hooks/base/useFocusManager';

const focusManager = getFocusManager();

// 保存当前焦点
focusManager.saveFocus();

// 恢复之前保存的焦点
focusManager.restoreFocus();

// 设置焦点到指定元素
focusManager.setFocus(element);

// 设置焦点到第一个可聚焦元素
focusManager.focusFirst(container);

// 获取所有可聚焦元素
const focusableElements = focusManager.getFocusableElements(container);
```

### 2. 对话框焦点管理 (`useDialogFocus`)

专门处理对话框的焦点管理：

```typescript
import { useDialogFocus } from '../hooks/base/useDialogFocus';

const { dialogRef } = useDialogFocus({
  isOpen: true,
  onClose: () => setIsOpen(false),
  autoFocus: true,
  restoreFocus: true
});

// 在JSX中使用
<div ref={dialogRef}>
  {/* 对话框内容 */}
</div>
```

### 3. 页面焦点管理 (`usePageFocus`)

处理页面路由切换时的焦点管理：

```typescript
import { usePageFocus } from '../hooks/base/usePageFocus';
import { PageTitle, MainContent } from '../components/ui/PageComponents';

// 在页面组件中使用
const MyPage = () => {
  usePageFocus({
    title: '页面标题',
    autoFocus: true
  });

  return (
    <MainContent>
      <PageTitle level={1}>页面标题</PageTitle>
      {/* 页面内容 */}
    </MainContent>
  );
};
```

### 4. 表单焦点管理 (`useFormFocus`)

处理表单验证错误时的焦点管理：

```typescript
import { useFormFocus } from '../hooks/base/useFormFocus';
import { FormField, FieldError } from '../components/ui/FormComponents';

const MyForm = () => {
  const { formRef, focusFirstError, validateAndFocus } = useFormFocus({
    autoFocusOnError: true,
    scrollToError: true
  });

  const handleSubmit = () => {
    validateAndFocus(
      () => validateForm(), // 验证函数
      () => console.log('验证通过'),
      (errors) => console.log('验证失败', errors)
    );
  };

  return (
    <form ref={formRef}>
      <FormField
        label="用户名"
        fieldName="username"
        error={errors.username}
        required
      >
        <input name="username" />
      </FormField>
    </form>
  );
};
```

### 5. 列表焦点管理 (`useListFocus`)

处理列表中新增、删除项目时的焦点管理：

```typescript
import { useListFocus } from '../hooks/base/useListFocus';
import { ListContainer, ListItem } from '../components/ui/ListComponents';

const MyList = () => {
  const { listRef, focusItem, handleItemsChange } = useListFocus({
    autoFocusOnAdd: true,
    scrollToNewItem: true
  });

  // 监听数据变化
  useEffect(() => {
    handleItemsChange(items);
  }, [items, handleItemsChange]);

  return (
    <ListContainer ref={listRef}>
      {items.map(item => (
        <ListItem key={item.id} itemId={item.id}>
          {item.name}
        </ListItem>
      ))}
    </ListContainer>
  );
};
```

## 键盘导航支持

### 全局键盘管理 (`useKeyboardManager`)

```typescript
import { getKeyboardManager } from '../hooks/base/useKeyboardManager';

const keyboardManager = getKeyboardManager();

// 注册ESC键处理器
const cleanup = keyboardManager.registerEscHandler((event) => {
  console.log('ESC键被按下');
}, 100); // 优先级

// 清理
cleanup();
```

### 支持的键盘快捷键

- **Tab / Shift+Tab**: 在可聚焦元素间导航
- **Enter**: 激活按钮或链接
- **Escape**: 关闭对话框或取消操作
- **Arrow Keys**: 在列表中导航（上下左右）
- **Home / End**: 跳转到列表首尾
- **Space**: 选择复选框或单选按钮

## 无障碍访问特性

### 1. 跳转链接 (`SkipLinks`)

为键盘用户提供快速导航：

```typescript
import SkipLinks from '../components/ui/SkipLinks';

<SkipLinks
  links={[
    { href: '#main-content', label: '跳转到主要内容' },
    { href: '#navigation', label: '跳转到导航菜单' }
  ]}
/>
```

### 2. ARIA 属性支持

- `aria-label`: 为元素提供可访问的名称
- `aria-live`: 通知屏幕阅读器内容变化
- `role`: 定义元素的语义角色
- `aria-expanded`: 指示可折叠元素的状态

### 3. 焦点指示器

通过CSS提供清晰的焦点指示器：

```css
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
}
```

## 最佳实践

### 1. 对话框实现

```typescript
// ✅ 正确的对话框实现
import DialogBase from '../components/ui/DialogBase';

const MyDialog = ({ isOpen, onClose }) => (
  <DialogBase
    isOpen={isOpen}
    onClose={onClose}
    autoFocus={true}
    restoreFocus={true}
  >
    {/* 对话框内容 */}
  </DialogBase>
);

// ❌ 错误的实现 - 不要自己处理焦点
const BadDialog = ({ isOpen, onClose }) => {
  useEffect(() => {
    if (isOpen) {
      // 不要这样做
      document.addEventListener('keydown', handleEsc);
    }
  }, [isOpen]);
  
  return <div>{/* 内容 */}</div>;
};
```

### 2. 表单验证

```typescript
// ✅ 正确的表单验证
const { formRef, focusFirstError } = useFormFocus();

const handleSubmit = async () => {
  const errors = validateForm();
  if (Object.keys(errors).length > 0) {
    focusFirstError(errors); // 自动聚焦到第一个错误字段
    return;
  }
  // 提交表单
};

// ❌ 错误的实现 - 不处理焦点
const badHandleSubmit = async () => {
  const errors = validateForm();
  if (Object.keys(errors).length > 0) {
    // 只显示错误，不处理焦点
    setErrors(errors);
    return;
  }
};
```

### 3. 页面标题

```typescript
// ✅ 正确的页面标题
import { PageTitle } from '../components/ui/PageComponents';

const MyPage = () => (
  <div>
    <PageTitle level={1}>页面标题</PageTitle>
    {/* 页面内容 */}
  </div>
);

// ❌ 错误的实现 - 普通标题
const BadPage = () => (
  <div>
    <h1>页面标题</h1> {/* 不会自动获得焦点 */}
  </div>
);
```

## 测试指南

### 1. 键盘导航测试

1. 使用Tab键遍历所有可交互元素
2. 确保焦点指示器清晰可见
3. 验证Tab顺序符合逻辑
4. 测试Escape键关闭对话框
5. 验证Enter键激活按钮

### 2. 屏幕阅读器测试

1. 使用NVDA或JAWS测试
2. 验证页面标题正确朗读
3. 确保表单标签与输入框关联
4. 测试错误消息的朗读
5. 验证状态变化的通知

### 3. 焦点管理测试

1. 打开对话框时焦点正确设置
2. 关闭对话框时焦点正确恢复
3. 表单验证失败时焦点跳转到错误字段
4. 新增列表项时焦点跳转到新项目
5. 页面切换时焦点跳转到主要内容

## 故障排除

### 常见问题

1. **焦点管理器未初始化**
   ```
   Error: FocusManager未初始化，请在应用根组件中使用useFocusManager
   ```
   解决方案：确保在App.tsx中使用了KeyboardManager组件

2. **对话框焦点陷阱不工作**
   - 检查是否使用了DialogBase组件
   - 确认autoFocus和restoreFocus属性设置正确

3. **表单错误焦点不工作**
   - 检查表单字段是否有正确的name属性
   - 确认使用了FormField组件或正确的错误标记

4. **页面切换焦点不工作**
   - 确认使用了usePageFocus hook
   - 检查主要内容区域是否有正确的id属性

### 调试技巧

1. 在浏览器开发者工具中使用`document.activeElement`查看当前焦点元素
2. 使用Tab键测试焦点顺序
3. 检查控制台是否有焦点管理相关的错误信息
4. 使用无障碍访问检查工具（如axe-core）验证实现

## 更新日志

### v1.0.0 (2024-12-30)
- 实现基础焦点管理系统
- 添加对话框焦点管理
- 实现页面路由切换焦点管理
- 添加表单验证错误焦点处理
- 实现列表项焦点管理
- 添加键盘导航支持
- 实现跳转链接功能
- 添加ARIA属性支持
- 完善焦点样式系统
