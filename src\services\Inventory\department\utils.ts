import { DepartmentCategory } from './types';
import { CacheManager } from './cacheManager';
import { TreeUtils } from './treeUtils';
import { isResponsibleMatch } from '../../../utils/personUtils';

/**
 * 部门工具类
 * 提供部门树操作的通用工具方法
 * @deprecated 大部分功能已迁移到 TreeUtils，建议直接使用 TreeUtils
 */
export class DepartmentUtils {
  // 使用统一的缓存管理器
  private static cacheManager = CacheManager.getInstance();

  /**
   * 清除所有缓存
   * 在部门树更新时调用
   * @deprecated 请使用 CacheManager.getInstance().clearAllCaches()
   */
  public static clearCaches(): void {
    console.log('DepartmentUtils: 清除部门树相关缓存（通过统一缓存管理器）');
    this.cacheManager.clearAllCaches();
  }



  /**
   * 对部门树中的节点进行排序，确保人员节点排在部门节点前面
   * @param nodes 要排序的节点数组
   * @deprecated 请使用 TreeUtils.sortDepartmentTree()
   */
  public static sortDepartmentTree(nodes: DepartmentCategory[]): void {
    TreeUtils.sortDepartmentTree(nodes);
  }

  /**
   * 查找节点 - 统一的节点查找方法
   * @param categories 部门分类树
   * @param nodeId 节点ID
   * @returns 找到的节点或null
   * @deprecated 请使用 TreeUtils.findNodeById()
   */
  public static findNodeById(categories: DepartmentCategory[], nodeId: string): DepartmentCategory | null {
    return TreeUtils.findNodeById(categories, nodeId);
  }

  /**
   * 通用的父部门查找方法
   * @param categories 部门分类树
   * @param targetId 目标节点ID
   * @returns 父部门节点或null
   */
  public static findParentDepartment(
    categories: DepartmentCategory[],
    targetId: string
  ): DepartmentCategory | null {
    for (const category of categories) {
      if (category.children?.some((child: DepartmentCategory) => child.id === targetId)) {
        return category;
      }
      if (category.children?.length) {
        const found = this.findParentDepartment(category.children, targetId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 获取部门的完整路径（包括所有父部门）
   * @param category 部门节点
   * @param departmentCategories 完整的部门分类树
   * @returns 部门的完整路径，例如 "总部/技术部/开发组"
   * @deprecated 请使用 TreeUtils.getFullPath()
   */
  public static getDepartmentPath(category: DepartmentCategory, departmentCategories: DepartmentCategory[]): string {
    return TreeUtils.getFullPath(category, departmentCategories);
  }

  /**
   * 获取部门及其所有子部门的完整路径列表
   * @param category 部门节点
   * @param departmentCategories 完整的部门分类树
   * @returns 部门及其子部门的完整路径列表
   */
  public static getAllDepartmentPaths(category: DepartmentCategory, departmentCategories: DepartmentCategory[]): string[] {
    // 检查统一缓存
    if (this.cacheManager.hasDepartmentPathsCache(category.id)) {
      return this.cacheManager.getDepartmentPathsCache(category.id)!;
    }

    // 使用Set避免重复路径
    const resultSet = new Set<string>();
    const basePath = this.getDepartmentPath(category, departmentCategories);

    // 如果是部门节点，添加自身的路径
    if (category.id.startsWith('dept-')) {
      resultSet.add(basePath);
    }

    // 递归添加所有子部门的路径
    if (category.children) {
      for (const child of category.children) {
        // 只处理部门节点，不处理人员节点
        if (child.id.startsWith('dept-')) {
          const childPath = `${basePath}/${child.name}`;
          resultSet.add(childPath);

          // 递归获取子部门的子部门路径
          const childPaths = this.getAllDepartmentPaths(child, departmentCategories);
          // 直接添加到Set中，自动去重
          childPaths.forEach(path => resultSet.add(path));
        }
      }
    }

    // 转换为数组
    const result = Array.from(resultSet);

    // 缓存结果到统一缓存管理器
    this.cacheManager.setDepartmentPathsCache(category.id, result);

    return result;
  }

  /**
   * 查找所有同名的部门
   * @param categories 部门分类树
   * @param departmentName 部门名称
   * @returns 所有同名部门的路径列表
   * @deprecated 请使用 TreeUtils.findAllDepartmentsWithName()
   */
  public static findAllDepartmentsWithName(categories: DepartmentCategory[], departmentName: string): string[] {
    return TreeUtils.findAllDepartmentsWithName(categories, departmentName);
  }

  /**
   * 根据路径查找部门节点
   * @param path 部门路径，例如 "总部/技术部/开发组"
   * @param departmentCategories 完整的部门分类树
   * @returns 找到的部门节点或null
   */
  public static findDepartmentByPath(path: string, departmentCategories: DepartmentCategory[]): DepartmentCategory | null {
    // 首先尝试直接通过departmentPath字段查找（新的API格式）
    const findByDepartmentPath = (cats: DepartmentCategory[]): DepartmentCategory | null => {
      for (const cat of cats) {
        // 检查当前节点的departmentPath是否匹配
        if (cat.departmentPath === path) {
          return cat;
        }

        // 递归检查子节点
        if (cat.children?.length) {
          const found = findByDepartmentPath(cat.children);
          if (found) return found;
        }
      }
      return null;
    };

    // 优先使用departmentPath字段查找
    const directMatch = findByDepartmentPath(departmentCategories);
    if (directMatch) {
      return directMatch;
    }

    // 回退到逐级查找方式（兼容性）
    const pathParts = path.split('/');
    let currentCategories = departmentCategories;
    let currentNode: DepartmentCategory | null = null;

    // 逐级查找路径中的每一部分
    for (let i = 0; i < pathParts.length; i++) {
      const partName = pathParts[i];
      let found = false;

      for (const cat of currentCategories) {
        if (cat.name === partName) {
          currentNode = cat;
          currentCategories = cat.children || [];
          found = true;
          break;
        }
      }

      if (!found) {
        return null; // 如果路径中的任何部分找不到，返回null
      }
    }

    return currentNode;
  }

  /**
   * 获取部门下的所有人员名称
   * @param department 部门节点
   * @returns 人员名称列表
   * @deprecated 请使用 TreeUtils.getAllPersonsInDepartment()
   */
  public static getAllPersonsInDepartment(department: DepartmentCategory): string[] {
    return TreeUtils.getAllPersonsInDepartment(department);
  }

  /**
   * 从人员全名中提取名称（不含备注）
   * @param fullName 人员全名，例如 "张三 (小张)"
   * @returns 人员名称，例如 "张三"
   */
  public static extractPersonName(fullName: string): string {
    // 如果名称中包含备注（格式为"姓名 (备注)"或"姓名 （备注）"）
    const nameMatch = fullName.match(/^([^(（]+)/);
    if (nameMatch) {
      return nameMatch[1].trim();
    }

    // 如果没有匹配到，尝试使用更宽松的方法
    const lastOpenBracketIndex = Math.max(fullName.lastIndexOf('('), fullName.lastIndexOf('（'));
    if (lastOpenBracketIndex > 0) {
      return fullName.substring(0, lastOpenBracketIndex).trim();
    }

    // 如果没有括号，返回原始名称
    return fullName;
  }

  /**
   * 从人员全名中提取备注
   * @param fullName 人员全名，例如 "张三 (小张)"
   * @returns 人员备注，例如 "小张"，如果没有备注则返回空字符串
   */
  public static extractPersonAlias(fullName: string): string {
    // 分别匹配英文括号和中文括号中的备注
    const englishAliasMatch = fullName.match(/\(([^)]+)\)/);
    const chineseAliasMatch = fullName.match(/（([^）]+)）/);

    if (englishAliasMatch) {
      return englishAliasMatch[1];
    } else if (chineseAliasMatch) {
      return chineseAliasMatch[1];
    }

    // 如果没有匹配到，尝试使用更宽松的方法
    const lastOpenBracketIndex = Math.max(fullName.lastIndexOf('('), fullName.lastIndexOf('（'));
    if (lastOpenBracketIndex > 0) {
      const bracketContent = fullName.substring(lastOpenBracketIndex + 1);
      const closeBracketIndex = Math.min(
        bracketContent.indexOf(')') >= 0 ? bracketContent.indexOf(')') : Number.MAX_SAFE_INTEGER,
        bracketContent.indexOf('）') >= 0 ? bracketContent.indexOf('）') : Number.MAX_SAFE_INTEGER
      );

      if (closeBracketIndex < Number.MAX_SAFE_INTEGER) {
        return bracketContent.substring(0, closeBracketIndex);
      }
    }

    // 如果没有备注，返回空字符串
    return '';
  }

  /**
   * 从人员节点中提取人员信息
   * @param categories 部门分类树
   * @param personId 人员节点ID
   * @returns 人员信息或null
   */
  public static extractPersonInfo(categories: DepartmentCategory[], personId: string): { name: string, alias?: string } | null {
    const category = this.findNodeById(categories, personId);
    if (!category) return null;

    return {
      name: this.extractPersonName(category.name),
      alias: this.extractPersonAlias(category.name) || undefined
    };
  }

  /**
   * 判断责任人是否匹配
   * @param responsible 设备的责任人
   * @param personFullName 人员全名
   * @returns 是否匹配
   * @deprecated 请使用 personUtils.isResponsibleMatch
   */
  public static isResponsibleMatch(responsible: string, personFullName: string): boolean {
    // 委托给统一的工具函数
    return isResponsibleMatch(responsible, personFullName);
  }

  /**
   * 检查一个节点是否是另一个节点的子节点
   * @param childId 潜在的子节点ID
   * @param parentId 潜在的父节点ID
   * @param departmentCategories 完整的部门分类树
   * @returns 是否是子节点
   * @deprecated 请使用 TreeUtils.isChildOf()
   */
  public static isChildOf(childId: string, parentId: string, departmentCategories: DepartmentCategory[]): boolean {
    return TreeUtils.isChildOf(childId, parentId, departmentCategories);
  }

  /**
   * 获取部门的API路径（不包含根节点）
   * @param category 部门节点
   * @param departmentCategories 完整的部门分类树
   * @returns 部门的API路径，例如 "技术部/开发组/前端组"（不包含根节点）
   * @deprecated 请使用 TreeUtils.getApiPath()
   */
  public static getDepartmentApiPath(category: DepartmentCategory, departmentCategories: DepartmentCategory[]): string {
    return TreeUtils.getApiPath(category, departmentCategories);
  }


}
