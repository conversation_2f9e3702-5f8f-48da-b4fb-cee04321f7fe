import React, { useState, useEffect } from 'react';
import {
  Timer,
  Key,
  ShieldX
} from 'lucide-react';
import logoImage from '../../assets/jma.png';
import GlobalConfigService from '../../services/globalConfigService';

interface StatusBarProps {
  // 保留空接口以兼容现有调用
}

const StatusBar: React.FC<StatusBarProps> = () => {
  // 系统启动时间
  const [startTime] = useState(new Date());

  // 运行时间（秒）
  const [uptime, setUptime] = useState(0);

  // 公司名称状态
  const [companyName, setCompanyName] = useState<string>('');

  // 系统激活状态
  const [isSystemActivated, setIsSystemActivated] = useState<boolean>(false);

  // 获取公司名称和系统激活状态
  useEffect(() => {
    const loadSystemStatus = async () => {
      try {
        // 检查系统激活状态
        const isActivated = GlobalConfigService.isRootNodeValidated() && !GlobalConfigService.isRootNodeValidationFailed();
        setIsSystemActivated(isActivated);

        // 优先使用缓存的公司名称，避免API延迟
        const globalConfigService = GlobalConfigService.getInstance();
        const cachedName = globalConfigService.getCachedCompanyName();

        if (cachedName && cachedName !== '公司名称') {
          // 如果有缓存的名称，立即使用
          setCompanyName(cachedName);
          console.log('StatusBar: 使用缓存的公司名称:', cachedName);
          return;
        }

        // 只在根节点验证成功且没有缓存时才调用API
        if (isActivated) {
          try {
            // 设置较短的超时时间，快速降级
            const timeoutPromise = new Promise<string>((_, reject) => {
              setTimeout(() => reject(new Error('获取公司名称超时')), 5000); // 5秒超时
            });

            const namePromise = globalConfigService.getCompanyName();
            const name = await Promise.race([namePromise, timeoutPromise]);

            setCompanyName(name || '公司名称');
            console.log('StatusBar: API获取公司名称成功:', name);
          } catch (error) {
            console.warn('StatusBar: API获取公司名称失败，使用默认名称:', error);
            setCompanyName('公司名称');
          }
        } else {
          setCompanyName('公司名称');
        }
      } catch (error) {
        console.error('获取系统状态失败:', error);
        setCompanyName('公司名称');
        setIsSystemActivated(false);
      }
    };

    // 只在初始加载时执行一次
    loadSystemStatus();

    // 监听根节点验证成功事件，只在成功时更新一次
    const handleRootNodeValidated = () => {
      console.log('StatusBar: 监听到根节点验证成功，更新公司名称');
      loadSystemStatus();
    };

    // 只监听根节点验证成功事件
    window.addEventListener('system-activation-changed', handleRootNodeValidated);

    return () => {
      window.removeEventListener('system-activation-changed', handleRootNodeValidated);
    };
  }, []);

  // 更新系统运行时间
  useEffect(() => {
    const uptimeTimer = setInterval(() => {
      const now = new Date();
      const uptimeSeconds = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setUptime(uptimeSeconds);
    }, 1000);
    
    return () => clearInterval(uptimeTimer);
  }, [startTime]);

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / (60 * 60));
    const minutes = Math.floor((seconds % (60 * 60)) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 处理授权状态文本
  const getAuthorizationText = (name: string, isActivated: boolean) => {
    if (!isActivated) {
      return '暂未授权使用';
    }

    if (!name || name === '公司名称') {
      return '已授权使用';
    }

    if (name.endsWith('公司')) {
      return `已授权${name}使用`;
    } else {
      return `已授权${name}公司使用`;
    }
  };

  // 获取授权状态的图标和样式
  const getAuthorizationStyle = (isActivated: boolean) => {
    if (isActivated) {
      return {
        icon: Key,
        iconClass: 'h-4.5 w-4.5 text-green-600',
        textClass: 'text-green-600 font-bold text-sm'
      };
    } else {
      return {
        icon: ShieldX,
        iconClass: 'h-4.5 w-4.5 text-red-800',
        textClass: 'text-red-800 font-bold text-sm'
      };
    }
  };
  
  return (
    <div className="bg-white shadow-sm border-t border-gray-200 py-2 px-4 text-xs text-gray-700">
      <div className="flex items-center justify-between">
        {/* 左侧：授权状态 */}
        <div className="flex items-center space-x-2">
          {(() => {
            const { icon: IconComponent, iconClass, textClass } = getAuthorizationStyle(isSystemActivated);
            return (
              <>
                <IconComponent className={iconClass} />
                <span className={textClass}>{getAuthorizationText(companyName, isSystemActivated)}</span>
              </>
            );
          })()}
        </div>

        {/* 中间：运行时间 */}
        <div className="flex items-center space-x-1.5">
          <Timer className="h-4 w-4 text-gray-500" />
          <span className="font-medium">{formatUptime(uptime)}</span>
        </div>

        {/* 右侧：公司名称 */}
        <div className="flex items-center space-x-2">
          <img src={logoImage} alt="公司图标" className="h-5 w-5" />
          <span className="font-bold text-sm text-gray-800">北京军密安信息技术有限公司</span>
        </div>
      </div>
    </div>
  );
};

export default StatusBar; 