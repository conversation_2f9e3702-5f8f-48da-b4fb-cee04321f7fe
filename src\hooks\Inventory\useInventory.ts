import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import InventoryService from '../../services/Inventory/inventoryService';
import DeviceService from '../../services/Inventory/deviceService';
import ExtFieldService from '../../services/Inventory/extFieldService';
import ColumnVisibilityService from '../../services/Inventory/columnVisibilityService';
import DepartmentService from '../../services/Inventory/departmentService';
import TreeCountManager from '../../services/Inventory/treeCountManager';
import DataFlowManager from '../../services/Inventory/dataFlowManager';
import { categoryIconService } from '../../services/Inventory/categoryIconService';
// 不再需要导入 updateCategoryTreeCounts，使用 service.updateCategoryTreeCounts() 代替
import {
  InventoryItem,
  InventoryState,
  FieldDefinition,
  DictionaryItem,
  DictionaryMap,
  ExtFieldDefinition
} from '../../types/inventory';
import { flushSync } from 'react-dom'; // 导入 flushSync

export interface UseInventoryReturn extends InventoryState {
  // 过滤和分页状态
  filteredItems: InventoryItem[];

  // 列可见性
  columnVisibility: Record<string, boolean>;

  // 字段字典和定义
  fieldDictionary: Record<string, string>;
  tableFields: FieldDefinition[];

  // 字段选项
  fieldOptions: Record<string, string[]>;

  // 数据字典
  dataDictionary: DictionaryMap;

  // 方法
  loadInventory: (forceRefresh?: boolean) => Promise<void>;
  filterByCategory: (categoryId: string, forceRefresh?: boolean) => void;
  toggleSelectItem: (id: string) => void;
  toggleSelectAll: () => void;
  setSearchQuery: (query: string) => void;
  addInventoryItem: (item: Omit<InventoryItem, 'id'>) => Promise<InventoryItem>;
  updateInventoryItem: (id: string, updates: Partial<InventoryItem>) => Promise<InventoryItem>;
  deleteInventoryItems: (ids: string[]) => Promise<void>;
  setColumnVisibility: (columnId: string, isVisible: boolean) => void;
  resetColumnVisibility: () => void;
  setFieldDictionary: (dictionary: Record<string, string>) => void;

  // 辅助方法
  isItemSelected: (id: string) => boolean;
  areAllItemsSelected: () => boolean;
  someItemsSelected: () => boolean;
  getFieldTitle: (key: string) => string;
  getUniqueValues: (fieldKey: string) => string[];
  getDictionaryItems: (dictType: string) => DictionaryItem[];
  getDictionaryValues: (dictType: string) => string[];

  // 扩展字段相关
  currentExtFields: ExtFieldDefinition[];
  formExtFields: ExtFieldDefinition[];
  getCategoryExtFields: (categoryType: string, categoryName: string) => Promise<ExtFieldDefinition[]>;
  setCurrentExtFields: (extFields: ExtFieldDefinition[]) => void;
  clearCurrentExtFields: () => void;
  setFormExtFields: (extFields: ExtFieldDefinition[]) => void;
  clearFormExtFields: () => void;
}

export function useInventory(): UseInventoryReturn {
  // 获取服务实例
  const service = useMemo(() => InventoryService.getInstance(), []);
  const deviceService = useMemo(() => DeviceService.getInstance(), []);
  const columnVisibilityService = useMemo(() => ColumnVisibilityService.getInstance(), []);

  // 防止React.StrictMode重复初始化的标志
  const hasInitialized = useRef(false);

  // 获取初始状态
  const [state, setState] = useState<InventoryState>(() => service.getState());

  // 获取列可见性状态
  const [columnVisibility, setColumnVisibilityState] = useState<Record<string, boolean>>(() =>
    columnVisibilityService.getColumnVisibility()
  );

  // 获取字段字典
  const fieldDictionary = useMemo(() => service.getFieldDictionary(), [service]);

  // 直接从状态中获取表格字段，而不是使用useMemo
  // 这样可以确保当表格字段更新时，UI也会更新
  const tableFields = state.tableFields;

  // 获取数据字典
  const dataDictionary = useMemo(() => service.getDataDictionary(), [service]);

  // 从设备列表中获取字段的唯一值
  const fieldOptions = useMemo(() => {
    const options: Record<string, string[]> = {};

    // 需要获取唯一值的字段列表 (添加了 'name' 和 'purpose')
    const fieldsToExtract = ['name', 'type', 'manufacturer', 'model', 'department', 'responsible', 'location', 'purpose'];

    // 遍历所有字段
    fieldsToExtract.forEach(field => {
      // 获取所有非空值
      const allValues = state.inventoryList
        .map(item => item[field] as string)
        .filter(value => value && value.trim() !== '');

      // 去重
      options[field] = Array.from(new Set(allValues)).sort();
    });

    return options;
  }, [state.inventoryList]);

  // 获取特定字段的唯一值列表
  const getUniqueValues = useCallback((fieldKey: string): string[] => {
    return fieldOptions[fieldKey] || [];
  }, [fieldOptions]);

  // 获取指定类型的字典项
  const getDictionaryItems = useCallback((dictType: string): DictionaryItem[] => {
    return service.getDictionaryItems(dictType);
  }, [service]);

  // 获取指定类型的字典值列表
  const getDictionaryValues = useCallback((dictType: string): string[] => {
    return service.getDictionaryItems(dictType).map(item => item.code);
  }, [service]);

  // 根据搜索查询和当前分类过滤项目 - 增强全局搜索，支持多条件
  const filteredItems = useMemo(() => {
    // 首先按分类筛选，不强制刷新，使用缓存提高性能
    let items = service.filterByCategory(state.currentCategory || 'all', false);

    // 然后按搜索关键字筛选
    if (state.searchQuery) {
      // 将搜索查询按中英文逗号分隔成多个条件
      const queries = state.searchQuery
        .replace(/，/g, ',') // 先将中文逗号替换为英文逗号
        .split(',')
        .map(q => q.toLowerCase().trim())
        .filter(q => q !== ''); // 过滤掉空字符串

      // 如果有有效的搜索条件
      if (queries.length > 0) {
        items = items.filter(item => {
          // 对于每个条件，都需要至少有一个字段匹配
          return queries.every(query => {
            // 全局搜索：遍历所有字段进行搜索
            return Object.entries(item).some(([_, value]) => {
              // 跳过非字符串字段
              if (typeof value !== 'string') return false;

              // 将字符串转换为小写进行比较
              return value.toLowerCase().includes(query);
            });
          });
        });
      }
    }

    return items;
  }, [state.inventoryList, state.currentCategory, state.searchQuery, service]);

  // 判断项目是否被选中
  const isItemSelected = useCallback((id: string) => {
    return state.selectedItems.includes(id);
  }, [state.selectedItems]);

  // 判断是否所有项目都被选中
  const areAllItemsSelected = useCallback(() => {
    if (filteredItems.length === 0) return false;
    return filteredItems.every(item => state.selectedItems.includes(item.id));
  }, [state.selectedItems, filteredItems]);

  // 判断是否有部分项目被选中
  const someItemsSelected = useCallback(() => {
    if (filteredItems.length === 0) return false;
    return filteredItems.some(item => state.selectedItems.includes(item.id)) && !areAllItemsSelected();
  }, [state.selectedItems, filteredItems, areAllItemsSelected]);

  // 加载库存数据
  const loadInventory = useCallback(async (forceRefresh: boolean = false) => {
    try {
      // 检查根节点验证状态
      try {
        // 检查全局状态而不是导入服务，避免循环依赖
        if ((globalThis as any).__rootNodeValidationFailed) {
          console.log('公司名称验证失败，停止库存数据加载');
          return;
        }

        if (!(globalThis as any).__rootNodeValidated) {
          console.log('公司名称尚未验证通过，等待系统初始化完成');
          return;
        }
      } catch (error) {
        console.error('检查公司名称验证状态失败:', error);
        return;
      }

      // 如果已经在加载中，不重复加载
      if (state.isLoading) {
        console.log('正在加载中，跳过重复加载');
        return;
      }

      // 如果已经有数据且不是强制刷新，不重复加载
      if (!forceRefresh && state.inventoryList && state.inventoryList.length > 0) {
        console.log('已有数据且不是强制刷新，跳过重复加载');
        return;
      }

      if (forceRefresh) {
        console.log('强制刷新库存数据');
      } else {
        console.log('开始加载库存数据');
      }

      // 使用forceRefresh参数调用service的loadInventoryList方法
      await service.loadInventoryList(forceRefresh);
    } catch (error) {
      console.error('加载库存数据失败:', error);
    }
  }, [service]);

  // 根据分类过滤
  const filterByCategory = useCallback((categoryId: string, forceRefresh: boolean = false) => {
    service.filterByCategory(categoryId, forceRefresh);
  }, [service]);

  // 切换选中状态
  const toggleSelectItem = useCallback((id: string) => {
    service.toggleSelectItem(id);
  }, [service]);

  // 切换全选状态
  const toggleSelectAll = useCallback(() => {
    service.toggleSelectAll(filteredItems);
  }, [service, filteredItems]);

  // 设置搜索查询
  const setSearchQuery = useCallback((query: string) => {
    service.setSearchQuery(query);
  }, [service]);

  // 添加设备 - 使用deviceService
  const addInventoryItem = useCallback(async (item: Omit<InventoryItem, 'id'>) => {
    try {
      // 检查是否有扩展字段
      const hasExtFields = item.extendedFields && Object.keys(item.extendedFields).length > 0;

      // 将InventoryItem转换为DeviceService需要的格式
      const deviceData = deviceService.convertInventoryItemToDeviceData(item as InventoryItem);

      // 如果有扩展字段，确保它们被正确添加到deviceData中
      if (hasExtFields) {
        deviceData.extFieldsData = item.extendedFields;
      }

      // 调用deviceService添加设备
      const result = await deviceService.addDevice(deviceData);

      // 检查返回结果是否包含设备ID
      if (result && result.data && result.data.id) {
        // 获取新设备ID
        const deviceId = result.data.id;

        // 打印API返回结果，查看分类ID
        console.log('API返回结果中的分类ID信息:', {
          sub_category_id: result.data.sub_category_id,
          parent_category_id: result.data.parent_category_id,
          sub_category_name: result.data.sub_category_name,
          parent_category_name: result.data.parent_category_name
        });

        // 构建完整的后端设备数据对象
        const completeDeviceData = {
          id: deviceId,
          device_name_brand: deviceData.deviceManufacturer || '',
          sub_category_name: deviceData.deviceName || '',
          parent_category_name: deviceData.deviceType || '',
          usage_status: deviceData.usage || '',
          model: deviceData.deviceModel || '',
          department_name: deviceData.department_name || deviceData.ext1 || '',
          responsible_person_name: deviceData.personalName || '',
          responsible_person_alias: deviceData.personalAlias || deviceData.responsible_person_alias || '',
          location: deviceData.location || '',
          activation_timestamp: deviceData.activationTime || 0,
          confidentiality_code: deviceData.rfidCode || '',
          confidentiality_level: deviceData.securityLevel || '',
          purpose: deviceData.purpose || '',
          is_networked: typeof deviceData.isNetworked === 'boolean' ? deviceData.isNetworked : false,
          serial_number_asset_number: deviceData.serialNumber || '',
          ip_address: deviceData.ipAddress || '',
          mac_address: deviceData.macAddress || '',
          purchase_timestamp: deviceData.purchaseTime || 0,
          operating_system: deviceData.operatingSystem || '',
          security_code_rfid: deviceData.securityRfid || '',

          // 确保扩展字段被正确添加
          extended_fields: hasExtFields ? item.extendedFields : {},

          // 添加分类ID - 确保从API返回结果中获取正确的分类ID
          sub_category_id: result.data.sub_category_id !== undefined ? result.data.sub_category_id : null,
          parent_category_id: result.data.parent_category_id !== undefined ? result.data.parent_category_id : null,

          // 合并API返回的其他字段，但不覆盖已设置的字段
          ...result.data
        };

        // 在更新本地设备数据前，使用前端缓存的部门信息补充设备数据
        // 这是解决部门路径传输后部门显示为空的关键步骤
        if (item.department && !completeDeviceData.responsible_person_departments) {
          console.log('使用前端缓存的部门信息补充设备数据:', item.department);
          service.enrichDeviceWithCachedDepartmentInfo(completeDeviceData, item.department);
        }

        // 使用inventoryService的转换方法创建前端格式的设备对象
        // 这样可以确保与其他地方的转换逻辑一致
        service.updateLocalDevice(completeDeviceData);

        console.log('设备添加成功，已更新本地数据');

        // 更新树状图计数 - 添加设备后立即更新
        try {
          // 使用统一的计数管理器，立即更新所有树状图计数
          const treeCountManager = TreeCountManager.getInstance();
          treeCountManager.requestUpdate('both', true); // 立即更新设备和部门树计数
        } catch (countError) {
          console.error('更新树状图计数失败:', countError);
          // 计数更新失败不影响添加操作的完成
        }

        // 强制触发状态更新
        service.forceUpdate();

        // 更新本地状态
        setState(service.getState());

        // 从更新后的状态中获取新添加的设备
        // 直接使用设备ID，不再使用DV格式
        const inventoryList = service.getState().inventoryList;
        const newItem = inventoryList.find(device => device.id === deviceId.toString());

        if (!newItem) {
          console.error('查找新添加设备失败，设备ID:', deviceId, '当前设备列表:', inventoryList.map(d => ({ id: d.id, name: d.name })));
          throw new Error('添加设备成功，但无法找到新添加的设备');
        }

        console.log('成功找到新添加的设备:', newItem);

        return newItem;
      } else {
        // 如果返回结果不包含设备ID，回退到全表刷新
        console.warn('添加设备成功，但返回结果不包含设备ID，回退到全表刷新');

        // 使用flushSync确保状态更新同步完成
        flushSync(async () => {
          // 重要：使用forceRefresh=true强制刷新数据，忽略缓存
          await loadInventory(true);

          // 更新树状图计数 - 全表刷新后立即更新
          try {
            // 使用inventoryService的updateCategoryTreeCounts方法更新计数（立即执行）
            service.updateCategoryTreeCounts(true); // 强制更新
          } catch (countError) {
            console.error('更新树状图计数失败:', countError);
            // 计数更新失败不影响添加操作的完成
          }

          // 强制触发状态更新
          service.forceUpdate();
        });

        // 再次强制更新状态，确保 UI 刷新
        setState(service.getState());

        // 查找新添加的设备
        const newItem = service.getState().inventoryList.find(device =>
          device.name === item.name &&
          device.type === item.type &&
          device.model === item.model
        );

        if (!newItem) {
          throw new Error('添加设备成功，但无法找到新添加的设备');
        }

        return newItem;
      }
    } catch (error: any) {
      console.error('添加设备失败:', error);
      throw error;
    }
  }, [deviceService, service, setState, loadInventory]);

  // 更新设备
  const updateInventoryItem = useCallback(async (id: string, updates: Partial<InventoryItem>) => {
    return await service.updateInventoryItem(id, updates);
  }, [service]);

  // 删除设备
  const deleteInventoryItems = useCallback(async (ids: string[]) => {
    await service.deleteInventoryItems(ids);
  }, [service]);

  // 设置列可见性
  const setColumnVisibility = useCallback((columnId: string, isVisible: boolean) => {
    columnVisibilityService.setColumnVisibility(columnId, isVisible);
  }, [columnVisibilityService]);

  // 设置字段字典
  const setFieldDictionary = useCallback((dictionary: Record<string, string>) => {
    service.setFieldDictionary(dictionary);
  }, [service]);

  // 获取字段标题
  const getFieldTitle = useCallback((key: string): string => {
    return fieldDictionary[key] || key;
  }, [fieldDictionary]);

  // 获取扩展字段服务实例
  const extFieldService = useMemo(() => ExtFieldService.getInstance(), []);

  // 获取当前扩展字段
  const [extFieldState, setExtFieldState] = useState(() => extFieldService.getState());

  // 监听扩展字段服务状态变化
  useEffect(() => {
    // 使用防抖函数，避免短时间内多次更新状态
    let debounceTimer: number | null = null;

    const handleExtFieldStateChange = (newState: any) => {
      // 清除之前的定时器
      if (debounceTimer !== null) {
        clearTimeout(debounceTimer);
      }

      // 创建新的定时器，延迟更新状态
      debounceTimer = window.setTimeout(() => {
        console.log('扩展字段状态变化:', newState.currentExtFields);
        setExtFieldState(prevState => {
          // 使用函数形式的setState，确保状态更新
          if (JSON.stringify(prevState) !== JSON.stringify(newState)) {
            return newState;
          }
          return prevState;
        });
        debounceTimer = null;
      }, 50); // 50ms 的防抖延迟
    };

    // 只监听 state-change 事件，不再监听 current-ext-fields-changed 事件
    // 因为 current-ext-fields-changed 事件会触发 state-change 事件
    extFieldService.on('state-change', handleExtFieldStateChange);

    // 监听扩展字段变化事件，当扩展字段变化时重新生成表格字段
    extFieldService.on('ext-fields-changed', (forceUpdate) => {
      // 使用当前选中的分类信息重新生成表格字段
      const currentSelectedCategory = service.getCurrentSelectedCategory();
      service.generateTableFields(currentSelectedCategory);
      if (forceUpdate) {
        service.forceUpdate();
        setState(service.getState());
      }
    });

    // 监听所有扩展字段加载完成事件
    extFieldService.on('all-ext-fields-loaded', () => {
      // 使用当前选中的分类信息重新生成表格字段
      const currentSelectedCategory = service.getCurrentSelectedCategory();
      service.generateTableFields(currentSelectedCategory);
      service.forceUpdate();
      setState(service.getState());
    });

    // 初始化时强制获取一次当前状态
    setExtFieldState(extFieldService.getState());

    // 检查扩展字段是否已经加载完成，如果是，则立即重新生成表格字段
    const extFieldServiceState = extFieldService.getState();
    if (extFieldServiceState.allExtFields && extFieldServiceState.allExtFields.length > 0) {
      setTimeout(() => {
        // 使用当前选中的分类信息重新生成表格字段
        const currentSelectedCategory = service.getCurrentSelectedCategory();
        service.generateTableFields(currentSelectedCategory);
        service.forceUpdate();
        setState(service.getState());
      }, 100);
    }

    return () => {
      extFieldService.off('state-change', handleExtFieldStateChange);
      // 取消对扩展字段变化事件的监听
      extFieldService.off('ext-fields-changed', () => {});
      // 取消对所有扩展字段加载完成事件的监听
      extFieldService.off('all-ext-fields-loaded', () => {});
      if (debounceTimer !== null) {
        clearTimeout(debounceTimer);
      }
    };
  }, [extFieldService]);

  // 使用状态计算当前扩展字段
  const currentExtFields = useMemo(() => {
    return extFieldState.currentExtFields || [];
  }, [extFieldState]);

  // 使用状态计算表单扩展字段
  const formExtFields = useMemo(() => {
    return extFieldState.formExtFields || [];
  }, [extFieldState]);

  // 重置列可见性
  const resetColumnVisibility = useCallback(() => {
    console.log('重置列可见性，当前扩展字段数量:', currentExtFields.length);

    // 获取当前分类的扩展字段键名
    const extFieldKeys = currentExtFields.map(field => `ext_${field.key}`);

    // 始终使用标准重置方法，它会重置所有标准字段为可见，并保留扩展字段的当前状态
    columnVisibilityService.resetColumnVisibility();

    // 如果有当前分类的扩展字段，额外将这些扩展字段设置为可见
    if (extFieldKeys.length > 0) {
      console.log('设置当前分类的扩展字段为可见:', extFieldKeys);
      columnVisibilityService.resetCurrentCategoryExtFieldsVisibility(extFieldKeys);
    }
  }, [columnVisibilityService, currentExtFields]);

  // 获取指定分类的扩展字段定义
  const getCategoryExtFields = useCallback(async (categoryType: string, categoryName: string) => {
    return await extFieldService.getCategoryExtFields(categoryType, categoryName);
  }, [extFieldService]);

  // 设置当前选中分类的扩展字段
  const setCurrentExtFields = useCallback((extFields: ExtFieldDefinition[]) => {
    extFieldService.setCurrentExtFields(extFields);
  }, [extFieldService]);

  // 清除当前扩展字段
  const clearCurrentExtFields = useCallback(() => {
    extFieldService.clearCurrentExtFields();
  }, [extFieldService]);

  // 设置表单扩展字段
  const updateFormExtFields = useCallback((extFields: ExtFieldDefinition[]) => {
    extFieldService.setFormExtFields(extFields);
  }, [extFieldService]);

  // 清除表单扩展字段
  const clearFormExtFields = useCallback(() => {
    extFieldService.clearFormExtFields();
  }, [extFieldService]);

  // 监听列可见性服务状态变化
  useEffect(() => {
    const handleColumnVisibilityChange = (newVisibility: Record<string, boolean>) => {
      console.log('列可见性已变更');
      setColumnVisibilityState(newVisibility);
    };

    // 订阅列可见性变更事件
    columnVisibilityService.on('column-visibility-changed', handleColumnVisibilityChange);

    return () => {
      columnVisibilityService.off('column-visibility-changed', handleColumnVisibilityChange);
    };
  }, [columnVisibilityService]);

  // 监听服务状态变化
  useEffect(() => {
    // 防止React.StrictMode的重复初始化
    if (hasInitialized.current) {
      console.log('useInventory: 已经初始化过事件监听器，跳过重复注册');
      return;
    }

    hasInitialized.current = true;
    console.log('useInventory: 开始注册事件监听器');

    // 使用节流函数处理状态更新，避免频繁更新
    let updateTimer: number | null = null;
    let latestState: InventoryState | null = null;

    const handleStateChange = (newState: InventoryState) => {
      // 存储最新状态
      latestState = newState;

      // 如果已经有定时器，则不再创建新的定时器
      if (updateTimer !== null) return;

      // 创建定时器，在下一帧更新状态
      updateTimer = window.setTimeout(() => {
        if (latestState) {
          setState(latestState);
          latestState = null;
        }
        updateTimer = null;
      }, 0);
    };

    const handleError = (error: string) => {
      console.error('库存管理服务错误:', error);
      // 将错误状态也通过节流函数更新
      setState(prev => ({ ...prev, error, isLoading: false }));
    };

    // 处理设备分类树更新事件
    const handleCategoryTreeUpdated = async (eventData?: any) => {
      console.log('设备分类树已更新，正在刷新UI...', eventData);

      try {
        // 检查是否是修改操作，如果是则刷新总表
        const isUpdateOperation = eventData &&
          (eventData.action === 'update' ||
           eventData.type === 'update' ||
           eventData.operation === 'update');

        // 检查是否是一级分类更新操作
        const isParentCategoryUpdate = isUpdateOperation &&
          eventData &&
          eventData.curParentCategoryName &&
          eventData.newParentCategoryName;

        // 检查是否是二级分类更新操作
        const isSubCategoryUpdate = isUpdateOperation &&
          eventData &&
          eventData.curSubCategoryName &&
          eventData.newSubCategoryName;

        // 检查是否需要只更新本地数据而不请求数据库
        const updateLocalOnly = eventData && eventData.updateLocalOnly === true;

        // 检查是否需要更新扩展字段信息
        const updateExtFields = eventData && eventData.updateExtFields === true;

        // 检查是否是二级分类删除操作
        const isSubCategoryDelete = !isUpdateOperation &&
          eventData &&
          eventData.subCategoryName &&
          (eventData.action === 'delete' || eventData.operation === 'delete');

        // 所有操作都强制重新生成分类树，不使用缓存
        // 这样可以确保分类树始终是最新的
        console.log('重新生成分类树(强制忽略缓存)');
        await service.generateCategoryTree(undefined, true); // 使用当前设备列表，强制忽略缓存

        // 处理不同类型的操作
        let needsStateUpdate = false;

        if (isUpdateOperation) {
          if (isParentCategoryUpdate) {
            // 一级分类更新操作，只刷新树状图，不刷新总表
            console.log('设备分类树一级分类更新操作，只刷新树状图，不刷新总表');
            service.forceUpdate();
            needsStateUpdate = true;
          } else if (isSubCategoryUpdate && updateLocalOnly) {
            // 二级分类更新操作，直接更新总表中的设备类型列，不请求数据库
            console.log('设备分类树二级分类更新操作，直接更新总表中的设备类型列');
            // 调用新方法直接更新总表数据
            service.updateDeviceTypeInInventoryList(
              eventData.curSubCategoryName,
              eventData.newSubCategoryName
            );

            // 立即触发计数更新，确保分类树显示正确的计数
            console.log('触发计数更新，确保分类树计数正确');
            const treeCountManager = TreeCountManager.getInstance();
            treeCountManager.requestUpdate('device', true); // 立即更新

            // 强制刷新当前筛选结果
            service.forceRefreshCurrentFilter();
            needsStateUpdate = true;

            console.log('二级分类更新完成，界面已刷新');
          } else {
            // 其他更新操作，使用统一的刷新函数
            await refreshData('设备分类树已修改，正在刷新总表数据...');
            return; // refreshData 已经更新了状态，直接返回
          }
        } else if (isSubCategoryDelete && updateExtFields) {
          // 二级分类删除操作，需要更新扩展字段信息
          console.log('设备分类树二级分类删除操作，通过前端缓存删除扩展字段');

          // 获取被删除的二级分类名称
          const subCategoryName = eventData.subCategoryName;

          if (subCategoryName) {
            // 立即检查并重置当前分类，避免后续筛选时出现循环错误
            const currentCategoryInfo = service.getCurrentSelectedCategory();
            const currentCategoryId = service.getState().currentCategory; // 获取当前分类ID
            console.log(`当前选中的分类ID: ${currentCategoryId}, 分类信息:`, currentCategoryInfo, `被删除的二级分类: ${subCategoryName}`);

            // 检查当前分类ID是否与被删除的分类相关
            const shouldResetCategory = currentCategoryId &&
              typeof currentCategoryId === 'string' &&
              currentCategoryId !== 'all' && (
                currentCategoryId.includes(subCategoryName) ||
                currentCategoryId === `parent-${subCategoryName}` ||
                currentCategoryId.endsWith(`-${subCategoryName}`) ||
                // 匹配 parent-数字-数字 格式，这些可能是已删除的分类
                /^parent-\d+-\d+$/.test(currentCategoryId)
              );

            // 或者检查分类信息中是否包含被删除的二级分类
            const shouldResetByInfo = currentCategoryInfo &&
              currentCategoryInfo.subCategory === subCategoryName;

            if (shouldResetCategory || shouldResetByInfo) {
              console.log(`检测到当前分类 ${currentCategoryId} 可能已被删除，立即重置为全部设备`);
              service.setCurrentCategory('all');
              needsStateUpdate = true;
            }

            // 获取扩展字段服务实例
            const extFieldService = ExtFieldService.getInstance();

            // 检查该分类是否有扩展字段
            const hasExtFields = extFieldService.state.allExtFields.some(
              cat => cat.sub_category_name.toLowerCase() === subCategoryName.toLowerCase()
            );

            if (hasExtFields) {
              // 有扩展字段的分类，使用前端缓存删除
              const success = extFieldService.deleteSubCategoryExtFields(subCategoryName);

              if (success) {
                console.log(`成功通过前端缓存删除二级分类 ${subCategoryName} 的所有扩展字段`);
              } else {
                console.warn(`通过前端缓存删除二级分类 ${subCategoryName} 的扩展字段失败，回退到传统方式`);

                // 如果前端缓存删除失败，回退到传统方式：清除缓存并从数据库重新获取
                extFieldService.clearExtFieldsCache();
                await extFieldService.getAllExtFields(true);
              }
            } else {
              // 新分类没有扩展字段，跳过扩展字段删除
              console.log(`二级分类 ${subCategoryName} 没有扩展字段，跳过扩展字段删除`);
            }

            // 无论成功还是失败，都需要更新表格字段和UI
            // 重要：显式调用generateTableFields方法重新生成表格字段
            // 这将确保扩展字段的变化能够反映到表格中
            console.log('重新生成表格字段，确保扩展字段变化能够反映到UI中');
            const currentSelectedCategory = service.getCurrentSelectedCategory();
            service.generateTableFields(currentSelectedCategory);

            // 标记需要状态更新
            service.forceUpdate();
            needsStateUpdate = true;

            console.log('扩展字段信息已更新，表格字段已重新生成');
          } else {
            console.error('事件数据中缺少二级分类名称，无法删除扩展字段');
          }
        } else {
          // 其他添加或删除操作，只刷新树状图，不刷新总表
          console.log('设备分类树添加或删除操作，只刷新树状图，不刷新总表');
          service.forceUpdate();
          needsStateUpdate = true;
        }

        // 统一的状态更新逻辑
        if (needsStateUpdate) {
          // 使用flushSync确保状态更新同步完成
          flushSync(() => {
            setState(service.getState());
          });
        }
      } catch (error) {
        console.error('刷新设备分类树失败:', error);
      }
    };

    // 表格字段更新防抖定时器
    let tableFieldsUpdateTimer: number | null = null;

    // 监听表格字段更新事件
    const handleTableFieldsUpdated = (fields: FieldDefinition[]) => {
      console.log('表格字段已更新:', fields);

      // 检查是否有实际变化
      const areFieldsEqual = (a: FieldDefinition[], b: FieldDefinition[]): boolean => {
        if (a.length !== b.length) return false;

        for (let i = 0; i < a.length; i++) {
          if (a[i].key !== b[i].key) return false;
        }

        return true;
      };

      if (areFieldsEqual(state.tableFields, fields)) {
        console.log('表格字段没有变化，跳过状态更新');
        return;
      }

      // 清除之前的定时器
      if (tableFieldsUpdateTimer !== null) {
        clearTimeout(tableFieldsUpdateTimer);
      }

      // 使用防抖延迟更新状态
      tableFieldsUpdateTimer = window.setTimeout(() => {
        setState(prev => ({ ...prev, tableFields: fields }));
        tableFieldsUpdateTimer = null;
      }, 50); // 50ms 的防抖延迟
    };

    // 订阅事件
    service.on('state-change', handleStateChange);
    service.on('error', handleError);
    service.on('table-fields-updated', handleTableFieldsUpdated);

    // 统一的数据刷新函数，避免重复代码
    const refreshData = async (message: string) => {
      console.log(message);
      try {
        // 直接调用service的loadInventoryList方法，强制刷新
        await service.loadInventoryList(true);

        // 强制触发状态更新
        service.forceUpdate();

        // 更新本地状态
        setState(service.getState());

        console.log('数据刷新完成');
      } catch (error) {
        console.error('刷新数据失败:', error);
      }
    };

    // 更新设备分类树计数的通用函数（统一使用TreeCountManager）
    const updateDeviceCategoryTreeCounts = async (eventType: string, immediate: boolean = false) => {
      console.log(`${eventType}事件触发，更新设备分类树计数`);

      try {
        // 统一使用TreeCountManager更新计数
        const treeCountManager = TreeCountManager.getInstance();
        treeCountManager.requestUpdate('device', immediate);
      } catch (countError) {
        console.error('更新设备分类树计数失败:', countError);
      }
    };

    // 更新所有分类树计数（仅用于设备分类变更）
    const updateAllCategoryTreeCountsForCategoryChange = async () => {
      console.log('设备分类变更，同时更新设备分类树和部门分类树计数');

      try {
        // 统一使用TreeCountManager同时更新设备分类树和部门分类树
        const treeCountManager = TreeCountManager.getInstance();

        // 分类变更需要立即更新，确保计数准确
        treeCountManager.requestUpdate('both', true);
      } catch (countError) {
        console.error('更新分类树计数失败:', countError);
        // 回退逻辑也使用TreeCountManager
        try {
          const treeCountManager = TreeCountManager.getInstance();
          treeCountManager.requestUpdate('device', true);
        } catch (fallbackError) {
          console.error('回退更新也失败:', fallbackError);
        }
      }
    };

    // 处理设备添加事件
    const handleDeviceAdded = async (_: any) => {
      console.log('设备添加事件触发，更新本地状态');

      // 直接更新本地状态，因为设备已经在 updateLocalDevice 中处理了分类树计数和筛选结果
      setState(service.getState());
    };

    // 处理设备更新事件
    const handleDeviceUpdated = async (data: any) => {
      console.log('设备更新事件触发，检查更新内容:', data);

      // 检查是否修改了影响分类的字段
      const categoryRelatedFields = [
        'type',                    // 设备类型（前端字段名）
        'parentCategory',          // 设备父分类（前端字段名）
        'sub_category_name',       // 设备子分类（后端字段名）
        'parent_category_name',    // 设备父分类（后端字段名）
        'category'                 // 分类（如果有的话）
      ];

      const hasCategoryChanges = categoryRelatedFields.some(field =>
        data?.updates && data.updates.hasOwnProperty(field)
      );

      if (hasCategoryChanges) {
        console.log('检测到分类相关字段变更，执行完整缓存更新');

        // 强制清除所有筛选缓存
        service.clearAllFilterCache();
        console.log('已强制清除所有筛选缓存');

        // 立即强制重新生成分类树（包含最新的设备数据）
        try {
          console.log('强制重新生成设备分类树');
          const currentState = service.getState();
          const updatedCategories = await service.generateCategoryTree(currentState.inventoryList, true);
          console.log('设备分类树已重新生成，计数已更新');

          // 更新状态
          setState(service.getState());
        } catch (error) {
          console.error('重新生成分类树失败:', error);
        }

        // 获取当前选中的分类
        const currentCategory = service.getState().currentCategory;
        if (currentCategory) {
          console.log('强制刷新当前分类的筛选结果:', currentCategory);

          // 强制刷新筛选结果
          service.filterByCategory(currentCategory, true);

          // 更新本地状态
          setState(service.getState());
        }

        // 如果修改了子分类，还需要更新相关分类的筛选结果
        if (data?.updates?.sub_category_name || data?.updates?.type) {
          console.log('子分类已变更，强制清除所有筛选缓存并重新筛选');

          // 获取原分类和新分类（支持多种字段名）
          const oldSubCategory = data.originalData?.sub_category_name ||
                                 data.originalData?.type ||
                                 data.originalData?.rawData?.sub_category_name;
          const newSubCategory = data.updates.sub_category_name || data.updates.type;

          console.log(`子分类从 "${oldSubCategory}" 变更为 "${newSubCategory}"`);

          // 强制清除所有筛选缓存（不只是特定分类）
          service.clearAllFilterCache();

          // 获取当前选中的分类并强制重新筛选
          const currentCategory = service.getState().currentCategory;
          if (currentCategory) {
            console.log(`强制重新筛选当前分类: ${currentCategory}`);

            // 使用calculateFilteredResult方法强制重新计算，不使用任何缓存
            const currentState = service.getState();
            const newFilteredResult = service.calculateFilteredResult(
              currentCategory,
              currentState.inventoryList,
              currentState.deviceCategories
            );

            console.log(`重新筛选完成，结果数量: ${newFilteredResult.length}`);

            // 强制刷新当前分类
            service.filterByCategory(currentCategory, true);

            // 更新状态
            setState(service.getState());
          }
        }

        // 分类变更后，使用数据流管理器同步计数
        console.log('设备分类变更完成，使用数据流管理器同步计数');
        const dataFlowManager = DataFlowManager.getInstance();
        await dataFlowManager.processOperation({
          type: 'device-update',
          target: 'device',
          data: { hasCategoryChanges: true }
        });
      } else {
        // 普通更新，使用数据流管理器
        console.log('设备普通更新，使用数据流管理器同步计数');
        const dataFlowManager = DataFlowManager.getInstance();
        await dataFlowManager.processOperation({
          type: 'device-update',
          target: 'device',
          data: { hasCategoryChanges: false }
        });
      }
    };

    // 处理部门或人员重命名事件
    const handleNodeRenamed = async (data: { nodeId: string, newName: string }) => {
      console.log('部门或人员重命名事件触发...', data);

      try {
        // 获取部门服务实例
        const deptService = DepartmentService.getInstance();

        // 检查是否是人员重命名
        if (data.nodeId.startsWith('person-')) {
          console.log('人员重命名事件，清空人员缓存以确保下次编辑时获取最新数据');

          // 人员重命名主要影响部门树结构和责任人选项
          // 不需要刷新设备表格数据，只需要清空人员缓存
          // 设备表格中的责任人选项会在编辑时动态加载
          deptService.clearPersonCache();

          console.log('人员重命名处理完成，无需刷新设备表格');
        } else {
          console.log('部门重命名事件，部门树结构已更新');

          // 部门重命名主要影响部门树结构
          // 不需要刷新设备表格数据，设备数据本身没有变化
          // 只是部门筛选条件可能需要更新，但这不影响当前显示的数据
          console.log('部门重命名处理完成，无需刷新设备表格');
        }
      } catch (error) {
        console.error('处理重命名事件失败:', error);
      }
    };

    // 处理部门或人员删除事件
    const handleNodeDeleted = async (data: { nodeId: string }) => {
      console.log('部门或人员删除事件触发...', data);

      try {
        // 获取部门服务实例
        const deptService = DepartmentService.getInstance();

        // 检查是否是人员删除
        if (data.nodeId.startsWith('person-')) {
          console.log('人员删除事件，清空人员缓存以确保下次编辑时获取最新数据');

          // 人员删除主要影响部门树结构和责任人选项
          // 不需要刷新设备表格数据，只需要清空人员缓存
          // 设备表格中的责任人选项会在编辑时动态加载
          deptService.clearPersonCache();

          console.log('人员删除处理完成，无需刷新设备表格');
        } else {
          console.log('部门删除事件，部门树结构已更新');

          // 部门删除主要影响部门树结构
          // 不需要刷新设备表格数据，设备数据本身没有变化
          // 只是部门筛选条件可能需要更新，但这不影响当前显示的数据
          console.log('部门删除处理完成，无需刷新设备表格');
        }
      } catch (error) {
        console.error('处理删除事件失败:', error);
      }
    };

    // 处理部门或人员移动事件
    const handleNodeMoved = async (data: {
      nodeId: string,
      newParentId: string,
      personName?: string,
      personAlias?: string,
      oldDepartmentName?: string,
      newDepartmentName?: string
    }) => {
      console.log('部门或人员移动事件触发...', data);

      try {
        // 获取部门服务实例
        const deptService = DepartmentService.getInstance();

        if (data.nodeId.startsWith('person-') &&
            data.personName &&
            data.oldDepartmentName &&
            data.newDepartmentName) {
          console.log('人员移动事件，直接更新总表中该人员的所属部门...');

          let personDisplay = data.personName;
          if (data.personAlias) {
            personDisplay = personDisplay + ' (' + data.personAlias + ')';
          }
          console.log('人员移动: 人员="' + personDisplay + '", 旧部门="' + data.oldDepartmentName + '", 新部门="' + data.newDepartmentName + '"');

          // 直接更新总表中该人员的所属部门
          service.updatePersonDepartmentInInventoryList(
            data.personName,
            data.oldDepartmentName,
            data.newDepartmentName,
            data.personAlias
          );

          // 强制触发状态更新，确保UI更新
          service.forceUpdate();

          // 更新本地状态
          setState(service.getState());

          // 人员移动后需要更新部门树计数，因为设备的责任人部门信息已经变化
          console.log('人员移动后，更新部门树计数...');
          const updatedInventoryList = service.getState().inventoryList;
          deptService.updateDepartmentTreeCounts(updatedInventoryList);
          console.log('部门树计数更新完成');

          console.log('人员移动成功，已直接更新总表中该人员的所属部门');
        } else if (data.nodeId.startsWith('dept-')) {
          console.log('部门移动事件，只刷新树状图，不刷新总表');

          // 强制触发状态更新，确保UI更新
          deptService.forceUpdate();

          console.log('部门移动完成，树状图已更新');
        } else {
          console.warn('无法处理的移动事件，缺少必要信息:', data);
        }
      } catch (error) {
        console.error('处理移动事件失败:', error);
      }
    };

    // 处理部门添加事件
    const handleDepartmentAdded = async (data: { parentId: string, name: string }) => {
      console.log('部门添加事件触发...', data);

      try {
        // 部门添加通常不会直接影响设备表格内容，但为了保持一致性，可以选择刷新
        console.log('部门添加完成，部门树已更新');

        // 如果需要，可以在这里添加表格刷新逻辑
        // await refreshData('部门添加，正在刷新表格数据...');
      } catch (error) {
        console.error('处理部门添加事件失败:', error);
      }
    };

    // 处理人员添加事件
    const handlePersonAdded = async (data: { departmentId: string, name: string, alias?: string }) => {
      console.log('人员添加事件触发...', data);

      try {
        // 人员添加主要影响部门树结构，不需要刷新设备表格数据
        // 设备表格中的责任人选项会在编辑时动态加载，无需预先刷新
        console.log('人员添加完成，部门树已更新，无需刷新设备表格');

        // 只清空人员缓存，确保下次编辑时能获取到最新的责任人列表
        // 这样可以避免不必要的表格数据刷新，保持用户当前的查看状态
        const deptService = DepartmentService.getInstance();
        deptService.clearPersonCache();
      } catch (error) {
        console.error('处理人员添加事件失败:', error);
      }
    };

    // 获取部门服务实例
    const departmentService = DepartmentService.getInstance();

    // 订阅设备服务的分类树更新事件（统一处理）
    const categoryEvents = [
      'category-tree-updated',
      'parent-category-added',
      'sub-category-added',
      'parent-category-updated',
      'sub-category-updated',
      'parent-category-deleted',
      'sub-category-deleted'
    ];

    categoryEvents.forEach(event => {
      deviceService.on(event, handleCategoryTreeUpdated);
    });

    // 订阅设备添加和更新事件
    deviceService.on('device-added', handleDeviceAdded);
    deviceService.on('device-updated', handleDeviceUpdated);

    // 订阅部门服务的节点重命名、删除、移动和添加事件
    departmentService.on('node-renamed', handleNodeRenamed);
    departmentService.on('node-deleted', handleNodeDeleted);
    departmentService.on('node-moved', handleNodeMoved);
    departmentService.on('department-added', handleDepartmentAdded);
    departmentService.on('person-added', handlePersonAdded);

    // 注意：初始化加载逻辑已移至 useInventoryManagement 中集中处理
    // 避免多处重复加载导致的重复请求
    const loadDataTimer = setTimeout(() => {}, 0); // 保留定时器变量以便清理

    // 清理函数
    return () => {
      console.log('useInventory: 清理事件监听器');
      hasInitialized.current = false; // 重置初始化标志

      service.off('state-change', handleStateChange);
      service.off('error', handleError);
      service.off('table-fields-updated', handleTableFieldsUpdated);

      // 取消订阅设备服务的分类树更新事件
      categoryEvents.forEach(event => {
        deviceService.off(event, handleCategoryTreeUpdated);
      });

      // 取消订阅设备添加和更新事件
      deviceService.off('device-added', handleDeviceAdded);
      deviceService.off('device-updated', handleDeviceUpdated);

      // 取消订阅部门服务的节点重命名、删除、移动和添加事件
      departmentService.off('node-renamed', handleNodeRenamed);
      departmentService.off('node-deleted', handleNodeDeleted);
      departmentService.off('node-moved', handleNodeMoved);
      departmentService.off('department-added', handleDepartmentAdded);
      departmentService.off('person-added', handlePersonAdded);

      // 清除所有定时器
      if (updateTimer !== null) {
        clearTimeout(updateTimer);
      }
      if (loadDataTimer) clearTimeout(loadDataTimer);
      if (tableFieldsUpdateTimer !== null) {
        clearTimeout(tableFieldsUpdateTimer);
      }
    };
  }, [service, deviceService]);

  // 返回钩子结果
  return {
    ...state,
    columnVisibility, // 使用列可见性服务的状态
    filteredItems,
    fieldDictionary,
    tableFields,
    fieldOptions,
    dataDictionary,

    // 方法
    loadInventory,
    filterByCategory,
    toggleSelectItem,
    toggleSelectAll,
    setSearchQuery,
    addInventoryItem,
    updateInventoryItem,
    deleteInventoryItems,
    setColumnVisibility,
    resetColumnVisibility,
    setFieldDictionary,

    // 辅助方法
    isItemSelected,
    areAllItemsSelected,
    someItemsSelected,
    getFieldTitle,
    getUniqueValues,
    getDictionaryItems,
    getDictionaryValues,

    // 扩展字段相关
    currentExtFields,
    formExtFields,
    getCategoryExtFields,
    setCurrentExtFields,
    clearCurrentExtFields,
    setFormExtFields: updateFormExtFields,
    clearFormExtFields,
  };
}