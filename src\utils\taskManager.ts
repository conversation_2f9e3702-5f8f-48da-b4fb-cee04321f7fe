// 使用统一API管理任务
import WebSocketManager from './websocket';
import TaskMonitor from './taskMonitor';
import { parseTaskId, generateFunctionId, generateWebSocketFunctionId, generateCacheKey } from './taskUtils';

export interface TaskInfo {
    id: string;
    status: 'waiting' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    result?: any;
    error?: string;
    priority?: number; // 任务优先级: 数值越低优先级越高
    createdAt: number; // 任务创建时间戳
    attempts: number; // 尝试次数
    maxAttempts: number; // 最大尝试次数
    taskType?: string;
    params?: any; // 任务参数
}



// 添加系统检查状态接口
export interface SystemCheckState {
    isScanning: boolean;
    scanProgress: number;
    currentScanItem?: string;
    systemInfo?: any;
    lastScanTime?: string;
    error?: string;
}

// 添加网络检查状态接口
export interface NetworkCheckState {
    isScanning: boolean;
    scanProgress: number;
    currentScanItem?: string;
    networkInfo?: any;
    lastScanTime?: string;
    error?: string;
}

// 任务参数接口
interface TaskParams {
    dllName: string;
    funcName: string;
    params: any;
    priority?: number;
    maxAttempts?: number;
}

// 统一API响应格式
// 注意: 这里只是为了文档目的展示响应格式，实际使用了any类型
/*
interface ExecuteResponse {
    type: string;
    status: string;
    taskId?: string;
    message?: string;
    result?: any;
}
*/

type TaskCallback = (task: TaskInfo) => void;

class TaskManager {
    private static instance: TaskManager;
    private ws: WebSocketManager;
    private tasks: Map<string, TaskInfo> = new Map();
    private taskCallbacks: Map<string, TaskCallback[]> = new Map();
    private taskMonitor: TaskMonitor;

    // 任务优化相关属性
    private taskQueue: TaskInfo[] = []; // 任务队列
    private isProcessingQueue: boolean = false; // 是否正在处理队列
    private maxConcurrentTasks: number = 5; // 最大并发任务数
    private activeTaskCount: number = 0; // 当前活跃任务数
    private resultCache: Map<string, { result: any, timestamp: number }> = new Map(); // 结果缓存
    private cacheTTL: number = 5 * 60 * 1000; // 缓存有效期，默认5分钟
    // 任务重试策略由后端管理



    // 系统检查状态
    private systemCheckState: SystemCheckState = {
        isScanning: false,
        scanProgress: 0
    };

    // 网络检查状态
    private networkCheckState: NetworkCheckState = {
        isScanning: false,
        scanProgress: 0
    };

    // 添加任务类型映射
    private taskTypeMap: Map<string, string> = new Map();

    private constructor() {
        this.ws = WebSocketManager.getInstance();
        this.taskMonitor = TaskMonitor.getInstance();

        // 定期清理过期缓存
        setInterval(() => this.cleanExpiredCache(), 60000);

        // 订阅任务更新通知
        this.ws.on('task_update', (taskUpdate) => {
            if (taskUpdate && taskUpdate.id) {
                const taskId = taskUpdate.id;
                const task = this.tasks.get(taskId);

                if (task) {
                    // 更新现有任务信息
                    const update: Partial<TaskInfo> = {
                        status: taskUpdate.status,
                        progress: taskUpdate.progress
                    };

                    // 如果是完成状态且有结果，更新结果
                    if (taskUpdate.status === 'completed' && taskUpdate.result) {
                        try {
                            if (typeof taskUpdate.result === 'string') {
                                console.log('TaskManager 原始结果:', taskUpdate.result);

                                // 处理转义的JSON字符串
                                let jsonString = taskUpdate.result.trim();

                                // 如果包含转义的引号，说明是转义的JSON字符串
                                if (jsonString.includes('\\"')) {
                                    console.log('TaskManager 检测到转义的JSON字符串，进行反转义处理');
                                    // 移除可能的外层引号
                                    if (jsonString.startsWith('"') && jsonString.endsWith('"')) {
                                        jsonString = jsonString.slice(1, -1);
                                    }
                                    // 反转义JSON字符串
                                    jsonString = jsonString.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                                    console.log('TaskManager 反转义后的结果:', jsonString);
                                }

                                update.result = JSON.parse(jsonString);
                                console.log('TaskManager 解析后的结果:', update.result);
                            } else {
                                update.result = taskUpdate.result;
                            }
                        } catch (e) {
                            console.error('TaskManager JSON解析失败:', e);
                            console.error('TaskManager 原始结果:', taskUpdate.result);
                            // 如果解析失败，尝试创建一个基本的结果对象
                            if (typeof taskUpdate.result === 'string' && taskUpdate.result.includes('success')) {
                                console.log('TaskManager 尝试创建基本结果对象');
                                update.result = {
                                    success: true,
                                    message: '任务完成',
                                    rawResult: taskUpdate.result
                                };
                            } else {
                                update.result = taskUpdate.result;
                            }
                        }

                        // 缓存完成的任务结果
                        this.cacheTaskResult(taskId, update.result);
                    }

                    // 如果是失败状态且有错误信息，更新错误
                    if (taskUpdate.status === 'failed' && taskUpdate.error) {
                        update.error = taskUpdate.error;
                    }

                    // 更新任务并触发回调
                    this.updateTask(taskId, update);

                    console.log(`[TaskManager] 收到任务${taskId}的推送状态更新，状态为${taskUpdate.status}`);
                }
            }
        });

        // 连接WebSocket
        this.ws.connect().catch(error => {
            console.error('WebSocket连接失败:', error);
        });
    }

    public static getInstance(): TaskManager {
        if (!TaskManager.instance) {
            TaskManager.instance = new TaskManager();
        }
        return TaskManager.instance;
    }

    public onTaskUpdate(taskId: string, callback: TaskCallback): void {
        const callbacks = this.taskCallbacks.get(taskId) || [];
        callbacks.push(callback);
        this.taskCallbacks.set(taskId, callbacks);
    }

    public offTaskUpdate(taskId: string, callback: TaskCallback): void {
        const callbacks = this.taskCallbacks.get(taskId);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index !== -1) {
                callbacks.splice(index, 1);
                this.taskCallbacks.set(taskId, callbacks);
            }
        }
    }

    private updateTask(taskId: string, update: Partial<TaskInfo>): void {
        const task = this.tasks.get(taskId);
        if (task) {
            Object.assign(task, update);
            this.tasks.set(taskId, task);

            // 更新任务监控
            if (task.id) {
                const taskType = this.getTaskTypeInternal(task);
                this.taskMonitor.updateTaskStatus(task, taskType);
            }

            const callbacks = this.taskCallbacks.get(taskId);
            if (callbacks) {
                callbacks.forEach(callback => callback(task));
            }
        }
    }

    // 获取任务类型（统一的获取逻辑）
    private getTaskTypeInternal(task: TaskInfo): string {
        return task.taskType || this.taskTypeMap.get(task.id) || this.getTaskTypeFromId(task.id);
    }

    // 从任务ID中提取任务类型
    private getTaskTypeFromId(taskId: string): string {
        const { taskType } = parseTaskId(taskId);
        return taskType;
    }

    // 检查是否有相同参数的任务已在队列中
    private findSimilarTask(dllName: string, funcName: string, params: any): TaskInfo | undefined {
        // 生成函数标识符
        const functionId = generateFunctionId(dllName, funcName);
        // 使用更稳定的参数比较方法
        const paramsHash = this.generateParamsHash(params);

        for (const task of this.tasks.values()) {
            if (task.status === 'waiting' || task.status === 'running') {
                // 检查任务类型是否匹配
                const taskType = this.getTaskTypeInternal(task);
                if (taskType === functionId) {
                    // 如果任务类型匹配，还需要检查参数是否匹配
                    if (task.params) {
                        const taskParamsHash = this.generateParamsHash(task.params);
                        if (taskParamsHash === paramsHash) {
                            // 参数也匹配，返回该任务
                            console.log(`[参数匹配] 发现相同参数的任务: ${task.id}`);
                            return task;
                        }
                    }
                }
            }
        }
        return undefined;
    }

    // 生成参数的稳定哈希值
    private generateParamsHash(params: any): string {
        try {
            // 深拷贝参数以避免引用问题
            const clonedParams = JSON.parse(JSON.stringify(params));
            // 对对象键进行排序以确保一致性
            const sortedParams = this.sortObjectKeys(clonedParams);
            return JSON.stringify(sortedParams);
        } catch (error) {
            console.warn('参数序列化失败，使用原始字符串:', error);
            return String(params);
        }
    }

    // 递归排序对象键
    private sortObjectKeys(obj: any): any {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        if (Array.isArray(obj)) {
            return obj.map(item => this.sortObjectKeys(item));
        }

        const sortedObj: any = {};
        const keys = Object.keys(obj).sort();
        for (const key of keys) {
            sortedObj[key] = this.sortObjectKeys(obj[key]);
        }
        return sortedObj;
    }

    // 从缓存中获取结果
    private getCachedResult(dllName: string, funcName: string, params: any): any | null {
        const cacheKey = generateCacheKey(dllName, funcName, params);
        const cached = this.resultCache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
            console.log(`[TaskManager] 使用缓存结果: ${cacheKey}`);
            return cached.result;
        }
        return null;
    }

    // 缓存任务结果
    private cacheTaskResult(taskId: string, result: any): void {
        const task = this.tasks.get(taskId);
        if (task && task.status === 'completed') {
            // 从任务ID中提取任务类型信息
            const { dllName, funcName } = parseTaskId(taskId);

            if (task.params) {
                // 使用任务参数生成缓存键
                const cacheKey = generateCacheKey(dllName, funcName, task.params);

                this.resultCache.set(cacheKey, {
                    result,
                    timestamp: Date.now()
                });

                console.log(`[缓存] 使用参数缓存任务结果: ${cacheKey}`);
            } else {
                // 如果没有参数，使用任务ID作为后缀
                const cacheKey = `${dllName}_${funcName}_task_${taskId}`;

                this.resultCache.set(cacheKey, {
                    result,
                    timestamp: Date.now()
                });

                console.log(`[缓存] 使用任务ID缓存结果: ${cacheKey}`);
            }
        }
    }

    // 清理过期缓存
    private cleanExpiredCache(): void {
        const now = Date.now();
        for (const [key, value] of this.resultCache.entries()) {
            if (now - value.timestamp > this.cacheTTL) {
                this.resultCache.delete(key);
            }
        }
    }

    // 处理任务队列
    private async processTaskQueue(): Promise<void> {
        if (this.isProcessingQueue || this.taskQueue.length === 0 || this.activeTaskCount >= this.maxConcurrentTasks) {
            return;
        }

        this.isProcessingQueue = true;

        try {
            // 按优先级和创建时间排序
            this.taskQueue.sort((a, b) => {
                // 先按优先级排序（数值越低优先级越高）
                const priorityDiff = (a.priority || 0) - (b.priority || 0);
                if (priorityDiff !== 0) return priorityDiff;

                // 优先级相同则按创建时间排序
                return a.createdAt - b.createdAt;
            });

            while (this.taskQueue.length > 0 && this.activeTaskCount < this.maxConcurrentTasks) {
                const task = this.taskQueue.shift();
                if (task) {
                    this.activeTaskCount++;
                    this.updateTask(task.id, { status: 'running' });
                }
            }
        } finally {
            this.isProcessingQueue = false;
        }
    }

    public async submitTask(dllName: string, funcName: string, params: any = {}, priority: number = 0): Promise<string> {
        try {
            // 检查根节点验证状态（除了根节点验证本身、激活相关的DLL调用和数据库初始化调用）
            const isRootNodeValidation = dllName === 'ExportDll' && funcName === 'QueryGlobalConfig';
            const isActivationCall = dllName === 'ExportDll' && funcName === 'DecryptAndStoreToDatabaseWithCompany';
            const isDatabaseInitCall = dllName === 'AccountTableDll' && funcName === 'DbFun' &&
                                     params.action === 'set_database_path';

            if (!isRootNodeValidation && !isActivationCall && !isDatabaseInitCall) {
                // 检查全局状态而不是导入服务，避免循环依赖
                if ((globalThis as any).__rootNodeValidationFailed) {
                    throw new Error('公司名称验证失败，系统已停止所有API调用');
                }

                if (!(globalThis as any).__rootNodeValidated) {
                    throw new Error('公司名称尚未验证通过，请等待系统初始化完成');
                }
            }

            // 检查缓存
            const functionId = generateWebSocketFunctionId(dllName, funcName);
            const cachedResult = this.getCachedResult(dllName, funcName, params);
            if (cachedResult !== null) {
                // 创建一个模拟的已完成任务，包含缓存结果
                const cachedTaskId = `cached_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                const cachedTask: TaskInfo = {
                    id: cachedTaskId,
                    status: 'completed',
                    progress: 100,
                    result: cachedResult,
                    createdAt: Date.now(),
                    attempts: 1,
                    maxAttempts: 1,
                    taskType: generateFunctionId(dllName, funcName),
                    params: params // 存储原始参数
                };

                this.tasks.set(cachedTaskId, cachedTask);
                setTimeout(() => {
                    const callbacks = this.taskCallbacks.get(cachedTaskId);
                    if (callbacks) {
                        callbacks.forEach(callback => callback(cachedTask));
                    }
                }, 10);

                return cachedTaskId;
            }

            // 检查是否有相似任务正在执行
            const similarTask = this.findSimilarTask(dllName, funcName, params);
            if (similarTask) {
                console.log(`[TaskManager] 正在使用相似任务: ${similarTask.id}`);
                return similarTask.id;
            }

            // 连接WebSocket
            await this.ws.connect();

            // 使用统一API格式提交任务
            const response = await new Promise<any>((resolve, reject) => {
                let isHandled = false;

                const messageHandler = (response: any) => {
                    try {
                        // 防止重复处理
                        if (isHandled) return;

                        // 处理统一API响应
                        if (response.type === 'execute_response') {
                            isHandled = true;
                            this.ws.off('message', messageHandler);

                            if (response.status === 'success' && response.taskId) {
                                resolve(response);
                            } else {
                                reject(new Error(response.message || '任务提交失败'));
                            }
                            return;
                        }

                        // 如果收到未知格式的消息，记录日志
                        console.warn('收到未知格式的消息:', response);
                    } catch (e) {
                        if (!isHandled) {
                            isHandled = true;
                            this.ws.off('message', messageHandler);
                            reject(e);
                        }
                    }
                };

                // 注册消息处理器
                this.ws.on('message', messageHandler);

                // 尝试使用新的统一API格式
                this.ws.send({
                    type: 'execute',
                    function: functionId,
                    params: JSON.parse(JSON.stringify(params)), // 深拷贝参数避免引用问题
                    priority: priority
                });

                // 设置超时
                const timeoutId = setTimeout(() => {
                    if (!isHandled) {
                        isHandled = true;
                        this.ws.off('message', messageHandler);
                        reject(new Error('任务提交超时'));
                    }
                }, 30000);

                // 确保在处理完成后清理超时
                const originalResolve = resolve;
                const originalReject = reject;

                resolve = (value: any) => {
                    clearTimeout(timeoutId);
                    originalResolve(value);
                };

                reject = (reason: any) => {
                    clearTimeout(timeoutId);
                    originalReject(reason);
                };
            });

            // 处理响应
            if (!response.type || response.type !== 'execute_response' || !response.taskId) {
                throw new Error('无效的任务响应格式');
            }

            const taskId = response.taskId;

            // 创建本地任务记录
            const newTask: TaskInfo = {
                id: taskId,
                status: 'waiting',
                progress: 0,
                createdAt: Date.now(),
                attempts: 0,
                maxAttempts: 3,
                priority: priority,
                taskType: generateFunctionId(dllName, funcName),
                params: JSON.parse(JSON.stringify(params)) // 深拷贝参数避免引用问题
            };

            // 存储任务信息
            this.tasks.set(taskId, newTask);
            this.taskTypeMap.set(taskId, generateFunctionId(dllName, funcName));
            this.taskMonitor.updateTaskStatus(newTask, newTask.taskType);

            console.log(`[TaskManager] 提交任务成功: ${taskId}, 任务类型: ${generateFunctionId(dllName, funcName)}`);

            return taskId;
        } catch (e) {
            console.error('[TaskManager] 提交任务失败:', e);
            throw e;
        }
    }

    // 批量提交任务
    public async submitTasks(tasks: TaskParams[]): Promise<string[]> {
        const taskIds: string[] = [];

        // 按优先级排序批量任务
        tasks.sort((a, b) => (a.priority || 0) - (b.priority || 0));

        for (const task of tasks) {
            try {
                const taskId = await this.submitTask(
                    task.dllName,
                    task.funcName,
                    task.params
                );
                taskIds.push(taskId);
            } catch (error) {
                console.error(`批量任务提交错误:`, error);
                // 继续提交其他任务
            }
        }

        return taskIds;
    }

    public getTaskInfo(taskId: string): TaskInfo | undefined {
        return this.tasks.get(taskId);
    }

    // 等待任务结果
    public async waitForTaskResult(taskId: string, timeout: number = 60000): Promise<string> {
        return new Promise((resolve, reject) => {
            // 检查任务是否已完成
            const taskInfo = this.getTaskInfo(taskId);
            if (taskInfo && taskInfo.status === 'completed' && taskInfo.result) {
                resolve(taskInfo.result);
                return;
            }

            // 设置超时
            const timeoutId = setTimeout(() => {
                this.offTaskUpdate(taskId, handleTaskUpdate);
                reject(new Error(`等待任务结果超时: ${taskId}`));
            }, timeout);

            // 监听任务更新
            const handleTaskUpdate = (taskInfo: TaskInfo) => {
                if (taskInfo.status === 'completed') {
                    clearTimeout(timeoutId);
                    this.offTaskUpdate(taskId, handleTaskUpdate);
                    if (taskInfo.result) {
                        resolve(taskInfo.result);
                    } else {
                        reject(new Error(`任务完成但没有结果: ${taskId}`));
                    }
                } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
                    clearTimeout(timeoutId);
                    this.offTaskUpdate(taskId, handleTaskUpdate);
                    reject(new Error(taskInfo.error || `任务失败: ${taskId}`));
                }
            };

            this.onTaskUpdate(taskId, handleTaskUpdate);
        });
    }

    public async cancelTask(taskId: string): Promise<boolean> {
        try {
            const task = this.tasks.get(taskId);
            if (!task) {
                return false;
            }

            // 只有等待和运行状态的任务可以取消
            if (task.status !== 'waiting' && task.status !== 'running') {
                return false;
            }

            // 发送取消请求
            await new Promise<void>((resolve, reject) => {
                const messageHandler = (response: any) => {
                    try {
                        if (response.type === 'execute_response') {
                            this.ws.off('message', messageHandler);
                            if (response.status === 'success') {
                                resolve();
                            } else {
                                reject(new Error(response.message || '取消任务失败'));
                            }
                        }
                    } catch (e) {
                        reject(e);
                    } finally {
                        this.ws.off('message', messageHandler);
                    }
                };

                this.ws.on('message', messageHandler);
                this.ws.send({
                    type: 'execute',
                    function: 'System::CancelTask',
                    params: { taskId }
                });

                // 设置超时
                setTimeout(() => {
                    this.ws.off('message', messageHandler);
                    reject(new Error('取消任务超时'));
                }, 10000);
            });

            this.updateTask(taskId, {
                status: 'cancelled',
                error: '用户已取消导入'
            });

            return true;
        } catch (error: any) {
            console.error('[TaskManager] 取消任务失败:', error);
            return false;
        }
    }

    // 批量取消任务
    public async cancelTasks(taskIds: string[]): Promise<void> {
        for (const taskId of taskIds) {
            await this.cancelTask(taskId);
        }
    }

    // 优先级控制
    public setTaskPriority(taskId: string, priority: number): void {
        const task = this.tasks.get(taskId);
        if (task && (task.status === 'waiting' || task.status === 'running')) {
            this.updateTask(taskId, { priority });

            // 更新队列中任务的优先级
            const queueTask = this.taskQueue.find(t => t.id === taskId);
            if (queueTask) {
                queueTask.priority = priority;
            }
        }
    }

    // 设置最大并发任务数
    public setMaxConcurrentTasks(max: number): void {
        if (max > 0) {
            this.maxConcurrentTasks = max;
            // 尝试处理队列
            this.processTaskQueue();
        }
    }

    // 设置缓存有效期（毫秒）
    public setCacheTTL(ttl: number): void {
        if (ttl >= 0) {
            this.cacheTTL = ttl;
        }
    }

    // 清除所有缓存
    public clearCache(): void {
        this.resultCache.clear();
    }



    // 获取系统检查状态
    public getSystemCheckState(): SystemCheckState {
        return this.systemCheckState;
    }

    // 更新系统检查状态
    public updateSystemCheckState(state: Partial<SystemCheckState>): void {
        this.systemCheckState = {
            ...this.systemCheckState,
            ...state
        };
    }

    // 清除系统检查状态
    public clearSystemCheckState(): void {
        this.systemCheckState = {
            isScanning: false,
            scanProgress: 0
        };
    }

    // 获取网络检查状态
    public getNetworkCheckState(): NetworkCheckState {
        return this.networkCheckState;
    }

    // 更新网络检查状态
    public updateNetworkCheckState(state: Partial<NetworkCheckState>): void {
        this.networkCheckState = {
            ...this.networkCheckState,
            ...state
        };
    }

    // 清除网络检查状态
    public clearNetworkCheckState(): void {
        this.networkCheckState = {
            isScanning: false,
            scanProgress: 0
        };
    }

    // 获取任务统计信息
    public getTaskStats() {
        return this.taskMonitor.getTaskStats();
    }

    // 获取任务类型统计信息
    public getTaskTypeStats() {
        return this.taskMonitor.getTaskTypeStats();
    }

    // 获取活跃任务
    public getActiveTasks() {
        return this.taskMonitor.getActiveTasks();
    }

    // 获取最近完成的任务
    public getRecentCompletedTasks(limit: number = 20) {
        return this.taskMonitor.getRecentCompletedTasks(limit);
    }

    // 获取最近失败的任务
    public getRecentFailedTasks(limit: number = 20) {
        return this.taskMonitor.getRecentFailedTasks(limit);
    }

    // 获取任务执行时间分布
    public getTaskDurationDistribution() {
        return this.taskMonitor.getTaskDurationDistribution();
    }

    // 获取任务类型（公共接口）
    public getTaskType(taskId: string): string {
        const task = this.tasks.get(taskId);
        if (task) {
            return this.getTaskTypeInternal(task);
        }
        return this.taskTypeMap.get(taskId) || this.getTaskTypeFromId(taskId) || 'unknown';
    }
}

export default TaskManager;