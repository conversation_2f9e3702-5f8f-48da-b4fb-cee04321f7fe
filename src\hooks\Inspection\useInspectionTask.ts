import { useState, useEffect, useCallback } from 'react';
import InspectionTaskService, {
  InspectionTaskServiceState,
  InspectionTaskItem,
  InspectionTaskFilter,
  CreateTaskRequest
} from '../../services/Inspection/inspectionTaskService';

// 重新导出类型，这样UI层可以从Hook导入，而不是直接从Service层导入
export type { InspectionTaskItem, InspectionTaskFilter };

/**
 * 巡检任务Hook返回值接口
 */
interface UseInspectionTaskReturn {
  // 状态
  isLoading: boolean;
  error?: string;
  tasks: InspectionTaskItem[];
  filter: InspectionTaskFilter;
  filteredTasks: InspectionTaskItem[];
  isRefreshing: boolean;
  refreshProgress: number;

  // 分页相关
  currentPage: number;
  pageSize: number;
  totalPages: number;
  paginatedTasks: InspectionTaskItem[];

  // 方法
  refreshTasks: () => Promise<InspectionTaskItem[]>;
  setFilter: (filter: Partial<InspectionTaskFilter>) => void;
  clearFilter: () => void;
  createTask: (taskData: CreateTaskRequest) => Promise<InspectionTaskItem>;
  updateTask: (taskId: string, updates: Partial<InspectionTaskItem>) => Promise<InspectionTaskItem>;
  deleteTask: (taskId: string) => Promise<void>;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;

  // 辅助方法
  getInspectors: () => string[];
  getDepartments: () => string[];
  getTaskTypes: () => string[];
  getStatusStyle: (status: string) => string;
  getStatusText: (status: string) => string;
  getStatusIconType: (status: string) => string;
}

/**
 * 巡检任务Hook
 * 提供巡检任务功能和状态管理
 */
export function useInspectionTask(): UseInspectionTaskReturn {
  // 获取服务实例
  const service = InspectionTaskService.getInstance();

  // 状态
  const [state, setState] = useState<InspectionTaskServiceState>(service.getState());

  // 分页状态
  const [currentPage, setCurrentPage] = useState<number>(0); // 0-based index
  const [pageSize, setPageSize] = useState<number>(10); // 默认每页10条

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: InspectionTaskServiceState) => {
      setState(newState);
    };

    // 订阅状态变化事件
    service.on('state-change', handleStateChange);

    // 初始化数据监听器
    const removeDataListener = service.initializeDataListener();

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
      removeDataListener();
    };
  }, [service]);

  // 获取任务数据（智能缓存）
  const getTasks = useCallback(async (forceRefresh = false) => {
    try {
      return await service.getTasks(forceRefresh);
    } catch (error) {
      console.error('获取任务失败:', error);
      throw error;
    }
  }, [service]);

  // 刷新任务数据（强制刷新）
  const refreshTasks = useCallback(async () => {
    return await service.refreshTasks();
  }, [service]);

  // 设置任务过滤条件
  const setFilter = useCallback((filter: Partial<InspectionTaskFilter>) => {
    service.setFilter(filter);
  }, [service]);

  // 清除所有过滤条件
  const clearFilter = useCallback(() => {
    service.clearFilter();
  }, [service]);

  // 创建任务
  const createTask = useCallback(async (taskData: CreateTaskRequest) => {
    return await service.createTask(taskData);
  }, [service]);

  // 更新任务
  const updateTask = useCallback(async (updateData: {
    task_id: number;
    task_name?: string;
    task_description?: string;
    start_date?: number;
    end_date?: number;
  }) => {
    return await service.updateTask(updateData);
  }, [service]);

  // 删除任务
  const deleteTask = useCallback(async (taskId: string) => {
    try {
      return await service.deleteTask(taskId);
    } catch (error) {
      console.error('删除任务失败:', error);
      throw error;
    }
  }, [service]);

  // 获取所有巡检人员
  const getInspectors = useCallback(() => {
    try {
      // 从当前任务数据中提取巡检人员
      const inspectors = new Set<string>();
      state.tasks.forEach(task => {
        if (task.assignee) {
          inspectors.add(task.assignee);
        }
      });
      return Array.from(inspectors).sort();
    } catch (error) {
      console.error('获取巡检人员失败:', error);
      return [];
    }
  }, [state.tasks]);

  // 获取所有部门
  const getDepartments = useCallback(() => {
    try {
      // 从当前任务数据中提取部门信息
      const departments = new Set<string>();
      state.tasks.forEach(task => {
        if (task.departmentList) {
          // 如果部门列表是逗号分隔的字符串，需要分割
          const deptList = task.departmentList.split(',').map(d => d.trim());
          deptList.forEach(dept => {
            if (dept) {
              departments.add(dept);
            }
          });
        }
      });
      return Array.from(departments).sort();
    } catch (error) {
      console.error('获取部门列表失败:', error);
      return [];
    }
  }, [state.tasks]);

  // 获取所有任务类型
  const getTaskTypes = useCallback(() => {
    try {
      // 从当前任务数据中提取任务类型（如果有的话）
      const taskTypes = new Set<string>();
      state.tasks.forEach(task => {
        // 这里可以根据实际的任务类型字段来提取
        // 目前先返回一些常见的任务类型
        if (task.task_name) {
          // 可以根据任务名称推断类型，或者有专门的类型字段
          taskTypes.add('日常巡检');
          taskTypes.add('专项检查');
          taskTypes.add('应急巡检');
        }
      });

      // 如果没有任务数据，返回默认类型
      if (taskTypes.size === 0) {
        return ['日常巡检', '专项检查', '应急巡检', '维护检查'];
      }

      return Array.from(taskTypes).sort();
    } catch (error) {
      console.error('获取任务类型失败:', error);
      return ['日常巡检', '专项检查', '应急巡检', '维护检查'];
    }
  }, [state.tasks]);

  // 获取状态样式 - 基于后端返回的task_execution_status
  const getExecutionStatusStyle = useCallback((executionStatus: string) => {
    switch (executionStatus) {
      case '已完成':
        return 'status-success';
      case '进行中':
        return 'status-info';
      case '待执行':
        return 'status-warning';
      case '已逾期':
        return 'status-error';
      case '已取消':
        return 'status-error';
      default:
        return 'status-neutral';
    }
  }, []);

  // 获取状态图标类型 - 基于后端返回的task_execution_status
  const getExecutionStatusIconType = useCallback((executionStatus: string) => {
    switch (executionStatus) {
      case '已完成':
        return 'CheckCircle';
      case '进行中':
        return 'Clock';
      case '待执行':
        return 'AlertCircle';
      case '已逾期':
        return 'XCircle';
      case '已取消':
        return 'XCircle';
      default:
        return 'AlertCircle';
    }
  }, []);

  // 保留原有的状态函数用于兼容性
  const getStatusStyle = useCallback((status: string) => {
    switch (status) {
      case 'completed':
        return 'status-success';
      case 'in_progress':
        return 'status-info';
      case 'pending':
        return 'status-warning';
      case 'overdue':
        return 'status-error';
      default:
        return 'status-neutral';
    }
  }, []);

  const getStatusText = useCallback((status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'in_progress':
        return '进行中';
      case 'pending':
        return '待执行';
      case 'overdue':
        return '已逾期';
      default:
        return '未知';
    }
  }, []);

  const getStatusIconType = useCallback((status: string) => {
    switch (status) {
      case 'completed':
        return 'CheckCircle';
      case 'in_progress':
        return 'Clock';
      case 'pending':
        return 'AlertCircle';
      case 'overdue':
        return 'XCircle';
      default:
        return '';
    }
  }, []);

  // 计算分页数据
  const getPaginatedTasks = useCallback(() => {
    // 如果pageSize为-1，表示显示全部
    if (pageSize === -1) {
      return state.filteredTasks;
    }

    const start = currentPage * pageSize;
    const end = start + pageSize;
    return state.filteredTasks.slice(start, end);
  }, [state.filteredTasks, currentPage, pageSize]);

  // 计算总页数
  const getTotalPages = useCallback(() => {
    // 如果pageSize为-1，表示显示全部，总页数为1
    if (pageSize === -1) {
      return 1;
    }

    return Math.ceil(state.filteredTasks.length / pageSize);
  }, [state.filteredTasks.length, pageSize]);

  // 设置页码
  const handleSetPage = useCallback((page: number) => {
    const totalPages = getTotalPages();
    if (page >= 0 && page < totalPages) {
      setCurrentPage(page);
    }
  }, [getTotalPages]);

  // 设置每页显示条数
  const handleSetPageSize = useCallback((size: number) => {
    setPageSize(size);
    // 重置为第一页
    setCurrentPage(0);
  }, []);

  // 当过滤条件变化时，重置为第一页
  useEffect(() => {
    setCurrentPage(0);
  }, [state.filteredTasks.length]);

  // 计算分页后的任务数据
  const paginatedTasks = getPaginatedTasks();
  // 计算总页数
  const totalPages = getTotalPages();

  return {
    // 状态
    isLoading: state.isLoading,
    error: state.error,
    tasks: state.tasks,
    filter: state.filter,
    filteredTasks: state.filteredTasks,
    isRefreshing: state.isRefreshing,
    refreshProgress: state.refreshProgress,

    // 分页相关
    currentPage,
    pageSize,
    totalPages,
    paginatedTasks,

    // 方法
    getTasks,
    refreshTasks,
    setFilter,
    clearFilter,
    createTask,
    updateTask,
    deleteTask,
    setPage: handleSetPage,
    setPageSize: handleSetPageSize,

    // 辅助方法
    getInspectors,
    getDepartments,
    getTaskTypes,

    // 状态处理函数 - 基于后端task_execution_status
    getExecutionStatusStyle,
    getExecutionStatusIconType,

    // 状态处理函数 - 兼容旧版本
    getStatusStyle,
    getStatusText,
    getStatusIconType
  };
}

export default useInspectionTask;
