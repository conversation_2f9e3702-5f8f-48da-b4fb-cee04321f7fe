import React from 'react';

interface SystemActivationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  companyName: string;
  isLoading: boolean;
  isQueryingCompany: boolean;
  isActivating: boolean;
  error: string | null;
  onCompanyNameChange: (name: string) => void;
  onActivate: () => void;
  onClearError: () => void;
}

/**
 * 系统激活窗口组件
 * UI层：只负责渲染界面，不包含业务逻辑
 */
const SystemActivationDialog: React.FC<SystemActivationDialogProps> = ({
  isOpen,
  onClose,
  companyName,
  isLoading,
  isQueryingCompany,
  isActivating,
  error,
  onCompanyNameChange,
  onActivate,
  onClearError
}) => {
  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onActivate();
  };

  const handleCompanyNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCompanyNameChange(e.target.value);
    if (error) {
      onClearError();
    }
  };

  const isProcessing = isLoading || isQueryingCompany || isActivating;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl max-w-md w-full mx-4 relative overflow-hidden">
        {/* 玻璃拟态装饰元素 */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none"></div>
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>

        <div className="relative p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              系统激活
            </h2>
            <button
              onClick={onClose}
              disabled={isProcessing}
              className="text-gray-500/70 hover:text-gray-700 transition-all duration-200 hover:scale-105 p-1 rounded-full hover:bg-white/20 disabled:opacity-50 disabled:hover:scale-100"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 公司名称输入框 */}
            <div>
              <label htmlFor="companyName" className="block text-sm font-medium text-gray-800 mb-3">
                公司名称
              </label>
              <div className="relative">
                <input
                  id="companyName"
                  type="text"
                  value={companyName}
                  onChange={handleCompanyNameChange}
                  disabled={isProcessing}
                  placeholder="请输入注册的公司名称"
                  className="w-full px-4 py-3 bg-white/40 backdrop-blur-sm border border-white/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/50 disabled:bg-gray-100/50 disabled:cursor-not-allowed shadow-inner text-gray-800 placeholder-gray-500"
                  required
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-50/20 to-emerald-50/20 pointer-events-none"></div>
              </div>
            </div>

            {/* 错误信息显示 */}
            {error && (
              <div className="p-4 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-red-50/30 to-pink-50/30 pointer-events-none"></div>
                <div className="relative flex items-center">
                  <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-sm text-red-700 font-medium">{error}</p>
                </div>
              </div>
            )}

            {/* 加载状态显示 */}
            {isProcessing && (
              <div className="p-4 bg-blue-50/80 backdrop-blur-sm border border-blue-200/50 rounded-xl relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 to-indigo-50/30 pointer-events-none"></div>
                <div className="relative flex items-center">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                  </div>
                  <p className="text-sm text-blue-700 font-medium">
                    {isQueryingCompany && '正在查询公司信息...'}
                    {isActivating && '正在激活系统...'}
                    {isLoading && !isQueryingCompany && !isActivating && '处理中...'}
                  </p>
                </div>
              </div>
            )}

            {/* 激活按钮 */}
            <div className="flex space-x-4 pt-4">
              <button
                type="button"
                onClick={onClose}
                disabled={isProcessing}
                className="flex-1 px-6 py-3 bg-white/40 backdrop-blur-sm border border-white/30 text-gray-700 rounded-xl hover:bg-white/60 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-102 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isProcessing || !companyName.trim()}
                className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-102 font-medium backdrop-blur-sm border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              >
                {isProcessing ? '处理中...' : '激活系统'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SystemActivationDialog;
