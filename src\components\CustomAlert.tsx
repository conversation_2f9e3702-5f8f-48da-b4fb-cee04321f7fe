import React, { useEffect, useState } from 'react';
import { AlertCircle, CheckCircle, Info, X } from 'lucide-react';
import DialogBase from './ui/DialogBase';

export type AlertType = 'info' | 'success' | 'warning' | 'error';

interface CustomAlertProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  type?: AlertType;
  confirmText?: string;
  autoClose?: boolean;
  autoCloseTime?: number;
}

/**
 * 自定义提示框组件
 * 用于替代浏览器默认的alert，适配m108内核浏览器
 */
const CustomAlert: React.FC<CustomAlertProps> = ({
  isOpen,
  onClose,
  title,
  message,
  type = 'info',
  confirmText = '确定',
  autoClose = false,
  autoCloseTime = 3000
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // 处理自动关闭
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);

      if (autoClose) {
        const timer = setTimeout(() => {
          setIsVisible(false);
          setTimeout(onClose, 300); // 动画结束后关闭
        }, autoCloseTime);

        return () => clearTimeout(timer);
      }
    } else {
      setIsVisible(false);
    }
  }, [isOpen, autoClose, autoCloseTime, onClose]);

  // 处理关闭
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // 动画结束后关闭
  };

  // 如果不是打开状态，不渲染
  if (!isOpen) return null;

  // 根据类型设置样式
  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-700',
          iconColor: 'text-green-500',
          buttonBgColor: 'bg-green-600 hover:bg-green-700',
          icon: <CheckCircle className="h-6 w-6" />
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-700',
          iconColor: 'text-yellow-500',
          buttonBgColor: 'bg-yellow-600 hover:bg-yellow-700',
          icon: <AlertCircle className="h-6 w-6" />
        };
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-700',
          iconColor: 'text-red-500',
          buttonBgColor: 'bg-red-600 hover:bg-red-700',
          icon: <AlertCircle className="h-6 w-6" />
        };
      default: // info
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-700',
          iconColor: 'text-blue-500',
          buttonBgColor: 'bg-blue-600 hover:bg-blue-700',
          icon: <Info className="h-6 w-6" />
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleClose}
      width="auto"
      maxWidth="28rem"
      bgColor={styles.bgColor}
      className={`transform transition-all duration-300 ${
        isVisible ? 'translate-y-0 scale-100' : 'translate-y-4 scale-95'
      } ${styles.borderColor} border`}
      animation="none"
    >
        <div className="p-4">
          <div className="flex items-start">
            <div className={`mr-3 flex-shrink-0 ${styles.iconColor}`}>
              {styles.icon}
            </div>
            <div className="flex-1">
              {title && (
                <h3 className={`text-lg font-medium ${styles.textColor}`}>
                  {title}
                </h3>
              )}
              <div className={`mt-1 text-sm ${styles.textColor}`}>
                {message}
              </div>
            </div>
            <button
              type="button"
              className="ml-auto flex-shrink-0 rounded p-1 text-gray-400 hover:text-gray-500"
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
          <div className="mt-4 flex justify-end">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium text-white rounded-md ${styles.buttonBgColor}`}
              onClick={handleClose}
            >
              {confirmText}
            </button>
          </div>
        </div>
    </DialogBase>
  );
};

export default CustomAlert;
