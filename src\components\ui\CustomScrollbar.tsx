import React, { useEffect, useRef, useState, useCallback } from 'react';
import '../../styles/customScrollbar.css';

interface CustomScrollbarProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void;
  horizontal?: boolean; // 是否启用水平滚动条
  vertical?: boolean; // 是否启用垂直滚动条
}

/**
 * 自定义滚动条组件
 * 完全自绘实现，兼容m108版本浏览器
 */
const CustomScrollbar: React.FC<CustomScrollbarProps> = ({
  children,
  className = '',
  style = {},
  onScroll,
  horizontal = true,
  vertical = true
}) => {
  // 引用
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const verticalThumbRef = useRef<HTMLDivElement>(null);
  const horizontalThumbRef = useRef<HTMLDivElement>(null);
  const verticalTrackRef = useRef<HTMLDivElement>(null);
  const horizontalTrackRef = useRef<HTMLDivElement>(null);

  // 状态
  const [isDraggingVertical, setIsDraggingVertical] = useState(false);
  const [isDraggingHorizontal, setIsDraggingHorizontal] = useState(false);
  const [startY, setStartY] = useState(0);
  const [startX, setStartX] = useState(0);
  const [startTop, setStartTop] = useState(0);
  const [startLeft, setStartLeft] = useState(0);

  // 更新滚动条位置和大小 - 优化性能
  const updateScrollbars = useCallback(() => {
    if (!contentRef.current || !containerRef.current) return;

    const content = contentRef.current;
    const container = containerRef.current;

    // 计算内容的总高度和可见高度
    const scrollHeight = content.scrollHeight;
    const clientHeight = container.clientHeight;
    const scrollWidth = content.scrollWidth;
    const clientWidth = container.clientWidth;

    // 检查表格内容，确保正确计算宽度
    // 这解决了minibilink初始状态下不显示滚动条的问题
    let actualScrollWidth = scrollWidth;
    if (content.querySelector('table')) {
      const tableElement = content.querySelector('table');
      if (tableElement) {
        // 使用表格的实际宽度，这更准确
        actualScrollWidth = Math.max(scrollWidth, tableElement.offsetWidth);
      }
    }

    // 计算垂直滚动条高度比例
    const verticalRatio = clientHeight / scrollHeight;
    const thumbHeight = Math.max(20, verticalRatio * clientHeight);

    // 计算水平滚动条宽度比例
    const horizontalRatio = clientWidth / actualScrollWidth;
    const thumbWidth = Math.max(20, horizontalRatio * clientWidth);

    // 计算垂直滚动条位置
    const scrollTop = content.scrollTop;
    const verticalThumbTop = (scrollTop / scrollHeight) * clientHeight;

    // 计算水平滚动条位置
    const scrollLeft = content.scrollLeft;
    const horizontalThumbLeft = (scrollLeft / actualScrollWidth) * clientWidth;

    // 更新垂直滚动条
    if (vertical && verticalThumbRef.current && verticalTrackRef.current) {
      verticalThumbRef.current.style.height = `${thumbHeight}px`;
      verticalThumbRef.current.style.top = `${verticalThumbTop}px`;

      // 只有当内容高度大于容器高度时才显示垂直滚动条
      if (scrollHeight > clientHeight) {
        verticalTrackRef.current.style.display = 'block';
      } else {
        verticalTrackRef.current.style.display = 'none';
      }
    }

    // 更新水平滚动条
    if (horizontal && horizontalThumbRef.current && horizontalTrackRef.current) {
      horizontalThumbRef.current.style.width = `${thumbWidth}px`;
      horizontalThumbRef.current.style.left = `${horizontalThumbLeft}px`;

      // 只有当内容宽度大于容器宽度时才显示水平滚动条
      if (actualScrollWidth > clientWidth) {
        horizontalTrackRef.current.style.display = 'block';
      } else {
        horizontalTrackRef.current.style.display = 'none';
      }
    }
  }, [horizontal, vertical]);

  // 处理内容滚动事件 - 添加节流优化
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    // 使用requestAnimationFrame节流滚动条更新
    requestAnimationFrame(() => {
      updateScrollbars();
    });

    // 触发外部滚动事件回调
    if (onScroll) {
      onScroll(e);
    }
  }, [updateScrollbars, onScroll]);

  // 处理垂直滚动条拖动开始
  const handleVerticalDragStart = (e: React.MouseEvent) => {
    if (!contentRef.current || !verticalThumbRef.current) return;

    setIsDraggingVertical(true);
    setStartY(e.clientY);
    setStartTop(verticalThumbRef.current.offsetTop);

    // 添加拖动中的样式
    document.body.classList.add('custom-scrollbar-dragging');

    // 阻止默认行为和事件冒泡
    e.preventDefault();
    e.stopPropagation();
  };

  // 处理水平滚动条拖动开始
  const handleHorizontalDragStart = (e: React.MouseEvent) => {
    if (!contentRef.current || !horizontalThumbRef.current) return;

    setIsDraggingHorizontal(true);
    setStartX(e.clientX);
    setStartLeft(horizontalThumbRef.current.offsetLeft);

    // 添加拖动中的样式
    document.body.classList.add('custom-scrollbar-dragging');

    // 阻止默认行为和事件冒泡
    e.preventDefault();
    e.stopPropagation();
  };

  // 处理垂直滚动条轨道点击
  const handleVerticalTrackClick = (e: React.MouseEvent) => {
    if (!contentRef.current || !verticalTrackRef.current || !verticalThumbRef.current) return;

    const track = verticalTrackRef.current;
    const thumb = verticalThumbRef.current;
    const content = contentRef.current;

    // 获取点击位置相对于轨道的偏移
    const trackRect = track.getBoundingClientRect();
    const clickPosition = e.clientY - trackRect.top;

    // 计算滚动条中心应该在的位置
    const thumbHalfHeight = thumb.offsetHeight / 2;
    const targetTop = clickPosition - thumbHalfHeight;

    // 计算对应的内容滚动位置
    const scrollRatio = content.scrollHeight / track.offsetHeight;
    const targetScrollTop = targetTop * scrollRatio;

    // 设置滚动位置
    content.scrollTop = targetScrollTop;

    // 阻止默认行为和事件冒泡
    e.preventDefault();
    e.stopPropagation();
  };

  // 处理水平滚动条轨道点击
  const handleHorizontalTrackClick = (e: React.MouseEvent) => {
    if (!contentRef.current || !horizontalTrackRef.current || !horizontalThumbRef.current) return;

    const track = horizontalTrackRef.current;
    const thumb = horizontalThumbRef.current;
    const content = contentRef.current;

    // 获取点击位置相对于轨道的偏移
    const trackRect = track.getBoundingClientRect();
    const clickPosition = e.clientX - trackRect.left;

    // 计算滚动条中心应该在的位置
    const thumbHalfWidth = thumb.offsetWidth / 2;
    const targetLeft = clickPosition - thumbHalfWidth;

    // 计算对应的内容滚动位置
    const scrollRatio = content.scrollWidth / track.offsetWidth;
    const targetScrollLeft = targetLeft * scrollRatio;

    // 设置滚动位置
    content.scrollLeft = targetScrollLeft;

    // 阻止默认行为和事件冒泡
    e.preventDefault();
    e.stopPropagation();
  };

  // 添加全局鼠标移动和鼠标松开事件监听
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!contentRef.current || !containerRef.current) return;

      const content = contentRef.current;
      const container = containerRef.current;

      // 处理垂直滚动条拖动
      if (isDraggingVertical && verticalThumbRef.current && verticalTrackRef.current) {
        const deltaY = e.clientY - startY;
        const newTop = startTop + deltaY;

        // 限制滚动条在轨道范围内
        const maxTop = container.clientHeight - verticalThumbRef.current.offsetHeight;
        const boundedTop = Math.max(0, Math.min(newTop, maxTop));

        // 计算对应的内容滚动位置
        const scrollRatio = content.scrollHeight / container.clientHeight;
        const targetScrollTop = boundedTop * scrollRatio;

        // 设置滚动位置
        content.scrollTop = targetScrollTop;
      }

      // 处理水平滚动条拖动
      if (isDraggingHorizontal && horizontalThumbRef.current && horizontalTrackRef.current) {
        const deltaX = e.clientX - startX;
        const newLeft = startLeft + deltaX;

        // 限制滚动条在轨道范围内
        const maxLeft = container.clientWidth - horizontalThumbRef.current.offsetWidth;
        const boundedLeft = Math.max(0, Math.min(newLeft, maxLeft));

        // 计算对应的内容滚动位置
        const scrollRatio = content.scrollWidth / container.clientWidth;
        const targetScrollLeft = boundedLeft * scrollRatio;

        // 设置滚动位置
        content.scrollLeft = targetScrollLeft;
      }
    };

    const handleMouseUp = () => {
      if (isDraggingVertical || isDraggingHorizontal) {
        setIsDraggingVertical(false);
        setIsDraggingHorizontal(false);
        document.body.classList.remove('custom-scrollbar-dragging');
      }
    };

    // 添加事件监听器
    if (isDraggingVertical || isDraggingHorizontal) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    // 清理函数
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDraggingVertical, isDraggingHorizontal, startY, startTop, startX, startLeft]);

  // 初始化和窗口大小变化时更新滚动条
  useEffect(() => {
    // 立即更新一次滚动条
    updateScrollbars();

    // 延迟再次更新滚动条，确保内容完全渲染
    const initialUpdateTimer = setTimeout(() => {
      updateScrollbars();

      // 再次延迟更新，解决某些情况下初始化不正确的问题
      setTimeout(updateScrollbars, 300);
    }, 100);

    // 监听窗口大小变化
    const handleResize = () => {
      updateScrollbars();
    };

    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      clearTimeout(initialUpdateTimer);
      window.removeEventListener('resize', handleResize);
    };
  }, [updateScrollbars]);

  // 确保组件挂载后隐藏所有原生滚动条
  useEffect(() => {
    // 查找所有可能的滚动容器
    if (containerRef.current) {
      const scrollableElements = containerRef.current.querySelectorAll('*');

      // 为每个元素应用滚动条隐藏样式
      scrollableElements.forEach(element => {
        if (element instanceof HTMLElement) {
          // 检查元素是否有滚动条
          const hasScrollbar = element.scrollHeight > element.clientHeight ||
                              element.scrollWidth > element.clientWidth;

          if (hasScrollbar) {
            // 应用样式隐藏滚动条
            element.style.overflow = 'auto';
            element.style.msOverflowStyle = 'none';
            element.style.scrollbarWidth = 'none';
          }
        }
      });
    }

    // 当子元素变化时，延迟更新滚动条
    // 这解决了内容变化后滚动条不更新的问题
    const updateTimer = setTimeout(() => {
      updateScrollbars();
    }, 50);

    return () => {
      clearTimeout(updateTimer);
    };
  }, [children, updateScrollbars]); // 当子元素变化时重新检查

  return (
    <div
      ref={containerRef}
      className={`custom-scrollbar-container ${className}`}
      style={{
        overflow: 'hidden',
        position: 'relative',
        ...style
      }}
    >
      <div
        ref={contentRef}
        className="custom-scrollbar-content"
        onScroll={handleScroll}
        style={{ overflow: 'auto', msOverflowStyle: 'none', scrollbarWidth: 'none' }}
      >
        {children}
      </div>

      {/* 垂直滚动条 */}
      {vertical && (
        <div
          ref={verticalTrackRef}
          className="custom-scrollbar-track-vertical"
          onClick={handleVerticalTrackClick}
        >
          <div
            ref={verticalThumbRef}
            className={`custom-scrollbar-thumb-vertical ${isDraggingVertical ? 'active' : ''}`}
            onMouseDown={handleVerticalDragStart}
          />
        </div>
      )}

      {/* 水平滚动条 */}
      {horizontal && (
        <div
          ref={horizontalTrackRef}
          className="custom-scrollbar-track-horizontal"
          onClick={handleHorizontalTrackClick}
        >
          <div
            ref={horizontalThumbRef}
            className={`custom-scrollbar-thumb-horizontal ${isDraggingHorizontal ? 'active' : ''}`}
            onMouseDown={handleHorizontalDragStart}
          />
        </div>
      )}
    </div>
  );
};

export default CustomScrollbar;
