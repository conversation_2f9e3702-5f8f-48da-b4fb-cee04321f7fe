import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface SecurityLevelOption {
  value: number;
  label: string;
}

interface SecurityLevelSelectProps {
  value: number | undefined;
  onChange: (value: number | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  error?: string;
}

/**
 * 岗位密级专用下拉选择框组件 - 完全自绘，不依赖任何第三方组件
 * 兼容谷歌108m版本浏览器
 */
const SecurityLevelSelect: React.FC<SecurityLevelSelectProps> = ({
  value,
  onChange,
  placeholder = '请选择岗位密级',
  className = '',
  disabled = false,
  required = false,
  error
}) => {
  // 岗位密级选项 - 固定的4个选项
  const securityLevelOptions: SecurityLevelOption[] = [
    { value: 0, label: '非涉密人员' },
    { value: 1, label: '一般涉密人员' },
    { value: 2, label: '重要涉密人员' },
    { value: 3, label: '核心涉密人员' }
  ];

  // 下拉框是否展开
  const [isOpen, setIsOpen] = useState(false);

  // 引用下拉框容器元素
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 下拉列表引用
  const dropdownListRef = useRef<HTMLDivElement>(null);

  // 下拉框位置状态
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    maxHeight: 200,
    direction: 'down' as 'up' | 'down'
  });

  // 获取当前选中的选项
  const selectedOption = securityLevelOptions.find(option => option.value === value);

  // 处理选项点击事件
  const handleOptionClick = (optionValue: number) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  // 处理下拉框切换事件
  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // 计算下拉框位置，避免界面抖动和超出视口
  const calculateDropdownPosition = () => {
    if (!dropdownRef.current) return;

    const rect = dropdownRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    const dropdownHeight = Math.min(200, securityLevelOptions.length * 36 + 16); // 估计下拉框高度

    // 默认向下展开
    let top = rect.bottom + window.scrollY;
    let maxHeight = Math.min(200, spaceBelow - 10); // 最大高度，留10px边距
    let direction: 'up' | 'down' = 'down';

    // 如果下方空间不足且上方空间更多，则向上展开
    if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
      top = rect.top + window.scrollY - Math.min(200, spaceAbove - 10);
      maxHeight = Math.min(200, spaceAbove - 10);
      direction = 'up';
    }

    setDropdownPosition({
      top,
      left: rect.left + window.scrollX,
      width: rect.width,
      maxHeight,
      direction
    });
  };

  // 当下拉框打开时计算位置
  useEffect(() => {
    if (isOpen) {
      calculateDropdownPosition();
    }
  }, [isOpen]);

  // 点击外部关闭下拉框和处理窗口事件
  useEffect(() => {
    // 处理点击外部事件
    const handleClickOutside = (event: MouseEvent) => {
      // 检查点击是否在下拉框容器或下拉列表之外
      const isOutsideDropdown = dropdownRef.current && !dropdownRef.current.contains(event.target as Node);
      const isOutsideDropdownList = !dropdownListRef.current || !dropdownListRef.current.contains(event.target as Node);

      // 如果点击在两者之外，则关闭下拉框
      if (isOutsideDropdown && isOutsideDropdownList) {
        setIsOpen(false);
      }
    };

    // 处理窗口大小变化事件
    const handleResize = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    // 处理页面滚动事件（不包括下拉框内部滚动）
    const handleScroll = (event: Event) => {
      // 如果滚动事件来自下拉列表内部，则不关闭下拉框
      if (dropdownListRef.current && (
          dropdownListRef.current === event.target ||
          dropdownListRef.current.contains(event.target as Node)
      )) {
        return;
      }

      // 如果是页面其他部分的滚动，则关闭下拉框
      if (isOpen) {
        setIsOpen(false);
      }
    };

    // 处理键盘事件
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          setIsOpen(false);
          break;
        case 'ArrowDown':
          event.preventDefault();
          // 可以添加键盘导航逻辑
          break;
        case 'ArrowUp':
          event.preventDefault();
          // 可以添加键盘导航逻辑
          break;
        case 'Enter':
          event.preventDefault();
          // 可以添加选择逻辑
          break;
      }
    };

    // 添加事件监听器
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('scroll', handleScroll, true);
      document.addEventListener('keydown', handleKeyDown);
      window.addEventListener('resize', handleResize);
    }

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('scroll', handleScroll, true);
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  return (
    <div className={`relative ${className}`}>
      {/* 下拉框触发器 */}
      <div
        ref={dropdownRef}
        className={`flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md cursor-pointer transition-colors duration-200 ${
          isOpen ? 'ring-2 ring-blue-500 border-blue-500' : 'hover:border-gray-400'
        } ${disabled ? 'opacity-60 cursor-not-allowed bg-gray-100' : ''} ${
          error ? 'border-red-500' : ''
        }`}
        onClick={toggleDropdown}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-required={required}
        tabIndex={disabled ? -1 : 0}
      >
        <span className={`block truncate text-sm ${selectedOption ? 'text-gray-700' : 'text-gray-400'}`}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <span className="flex items-center ml-2">
          <svg
            className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </span>
      </div>

      {/* 下拉选项列表 - 使用Portal渲染到body */}
      {isOpen && createPortal(
        <div
          ref={dropdownListRef}
          className="fixed z-[9999] bg-white border border-gray-300 rounded-md shadow-lg overflow-auto"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: `${dropdownPosition.maxHeight}px`
          }}
          role="listbox"
        >
          <ul className="py-1">
            {securityLevelOptions.map((option) => (
              <li
                key={option.value}
                className={`px-3 py-2 cursor-pointer text-sm transition-colors duration-150 ${
                  option.value === value
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => handleOptionClick(option.value)}
                role="option"
                aria-selected={option.value === value}
              >
                {option.label}
              </li>
            ))}
          </ul>
        </div>,
        document.body
      )}

      {/* 错误信息 */}
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

export default SecurityLevelSelect;
