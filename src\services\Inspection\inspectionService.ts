import mitt from 'mitt';
import { BaseService, BaseServiceState, BaseServiceEvents } from '../../services/base/baseService';
import WebSocketManager from '../../utils/websocket';
import TaskManager from '../../utils/taskManager';

/**
 * 巡检服务状态接口
 */
export interface InspectionServiceState extends BaseServiceState {
  isInitialized: boolean;
  activeTab: string;
}

/**
 * 巡检服务事件接口
 */
export interface InspectionServiceEvents extends BaseServiceEvents<InspectionServiceState> {
  'tab-changed': string;
  'initialized': boolean;
}

/**
 * 巡检服务类
 * 提供巡检管理的基础功能和状态管理
 */
class InspectionService extends BaseService<InspectionServiceState, InspectionServiceEvents> {
  private static instance: InspectionService;

  /**
   * 获取巡检服务实例
   * @returns 巡检服务实例
   */
  public static getInstance(): InspectionService {
    if (!InspectionService.instance) {
      InspectionService.instance = new InspectionService();
    }
    return InspectionService.instance;
  }

  /**
   * 构造函数
   * 初始化巡检服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      isInitialized: false,
      activeTab: 'task', // 默认激活巡检任务标签页
    });
  }

  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    if (this.state.isInitialized) {
      return;
    }

    try {
      this.updateState({ isLoading: true });

      // 确保WebSocket已连接
      if (!this.ws.isConnected()) {
        await this.ws.connect();
      }

      // 在这里可以添加其他初始化逻辑，如加载配置等

      this.updateState({ 
        isInitialized: true,
        isLoading: false 
      });
      
      this.emitter.emit('initialized', true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '初始化巡检服务失败';
      this.updateState({ 
        error: errorMessage,
        isLoading: false 
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 确保服务已初始化
   */
  public async ensureInitialized(): Promise<void> {
    if (!this.state.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * 设置当前激活的标签页
   * @param tabKey 标签页键名
   */
  public setActiveTab(tabKey: string): void {
    if (this.state.activeTab !== tabKey) {
      this.updateState({ activeTab: tabKey });
      this.emitter.emit('tab-changed', tabKey);
    }
  }

  /**
   * 获取当前激活的标签页
   * @returns 当前激活的标签页键名
   */
  public getActiveTab(): string {
    return this.state.activeTab;
  }

  /**
   * 更新服务状态
   * @param updates 状态更新
   */
  protected updateState(updates: Partial<InspectionServiceState>): void {
    this.state = { ...this.state, ...updates };
    this.emitter.emit('state-change', this.state);
  }
}

export default InspectionService;
