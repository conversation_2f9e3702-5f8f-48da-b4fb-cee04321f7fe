import React, { useCallback } from 'react';
import { useListFocus } from '../../hooks/base/useListFocus';

/**
 * 列表项组件的Props
 */
interface ListItemProps {
  children: React.ReactNode;
  itemId: string | number;
  className?: string;
  onClick?: () => void;
  onDoubleClick?: () => void;
  'aria-label'?: string;
}

/**
 * 列表项组件
 * 提供统一的列表项焦点管理
 */
export const ListItem: React.FC<ListItemProps> = ({
  children,
  itemId,
  className = '',
  onClick,
  onDoubleClick,
  'aria-label': ariaLabel
}) => {
  return (
    <div
      data-item-id={itemId}
      tabIndex={-1}
      className={`focus:outline-none focus-ring cursor-pointer ${className}`}
      onClick={onClick}
      onDoubleClick={onDoubleClick}
      aria-label={ariaLabel}
      role="listitem"
    >
      {children}
    </div>
  );
};

/**
 * 列表容器组件
 * 提供统一的列表焦点管理和键盘导航
 */
interface ListContainerProps {
  children: React.ReactNode;
  className?: string;
  'aria-label'?: string;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

export const ListContainer: React.FC<ListContainerProps> = ({
  children,
  className = '',
  'aria-label': ariaLabel = '列表',
  onKeyDown
}) => {
  const { listRef, handleKeyDown } = useListFocus();
  
  const combinedKeyDown = useCallback((event: React.KeyboardEvent) => {
    handleKeyDown(event);
    onKeyDown?.(event);
  }, [handleKeyDown, onKeyDown]);
  
  return (
    <div
      ref={listRef}
      className={`focus:outline-none ${className}`}
      aria-label={ariaLabel}
      role="list"
      onKeyDown={combinedKeyDown}
    >
      {children}
    </div>
  );
};
