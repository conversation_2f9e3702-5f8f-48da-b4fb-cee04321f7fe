import { useState, useCallback, useEffect } from 'react';

// 通知类型
export type NotificationType = 'info' | 'success' | 'warning' | 'error';

// 通知项接口
export interface NotificationItem {
  id: string;
  type: NotificationType;
  title?: string;
  message: string;
  duration?: number;
  closable?: boolean;
  onClose?: () => void;
}

// 通知选项接口
export interface NotificationOptions {
  title?: string;
  message: string;
  type?: NotificationType;
  duration?: number;
  closable?: boolean;
  onClose?: () => void;
}

// 通知钩子返回值接口
export interface UseNotificationReturn {
  // 通知状态
  notifications: NotificationItem[];
  
  // 通知方法
  notify: (options: NotificationOptions) => string;
  info: (message: string, title?: string, duration?: number) => string;
  success: (message: string, title?: string, duration?: number) => string;
  warning: (message: string, title?: string, duration?: number) => string;
  error: (message: string, title?: string, duration?: number) => string;
  close: (id: string) => void;
  closeAll: () => void;
}

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 默认通知持续时间（毫秒）
const DEFAULT_DURATION = 3000;

/**
 * 通知钩子
 * 提供通知消息的显示和管理
 * @param maxNotifications 最大通知数量
 */
export function useNotification(maxNotifications: number = 5): UseNotificationReturn {
  // 通知列表
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  
  // 添加通知
  const notify = useCallback((options: NotificationOptions): string => {
    const id = generateId();
    const notification: NotificationItem = {
      id,
      type: options.type || 'info',
      title: options.title,
      message: options.message,
      duration: options.duration !== undefined ? options.duration : DEFAULT_DURATION,
      closable: options.closable !== undefined ? options.closable : true,
      onClose: options.onClose
    };
    
    setNotifications(prevNotifications => {
      // 如果超过最大数量，移除最早的通知
      const updatedNotifications = [...prevNotifications, notification];
      if (updatedNotifications.length > maxNotifications) {
        return updatedNotifications.slice(updatedNotifications.length - maxNotifications);
      }
      return updatedNotifications;
    });
    
    return id;
  }, [maxNotifications]);
  
  // 关闭通知
  const close = useCallback((id: string) => {
    setNotifications(prevNotifications => {
      const notification = prevNotifications.find(n => n.id === id);
      if (notification && notification.onClose) {
        notification.onClose();
      }
      return prevNotifications.filter(n => n.id !== id);
    });
  }, []);
  
  // 关闭所有通知
  const closeAll = useCallback(() => {
    setNotifications([]);
  }, []);
  
  // 显示信息通知
  const info = useCallback((message: string, title?: string, duration?: number): string => {
    return notify({
      type: 'info',
      title,
      message,
      duration
    });
  }, [notify]);
  
  // 显示成功通知
  const success = useCallback((message: string, title?: string, duration?: number): string => {
    return notify({
      type: 'success',
      title,
      message,
      duration
    });
  }, [notify]);
  
  // 显示警告通知
  const warning = useCallback((message: string, title?: string, duration?: number): string => {
    return notify({
      type: 'warning',
      title,
      message,
      duration
    });
  }, [notify]);
  
  // 显示错误通知
  const error = useCallback((message: string, title?: string, duration?: number): string => {
    return notify({
      type: 'error',
      title,
      message,
      duration
    });
  }, [notify]);
  
  // 自动关闭通知
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    
    notifications.forEach(notification => {
      if (notification.duration && notification.duration > 0) {
        const timer = setTimeout(() => {
          close(notification.id);
        }, notification.duration);
        
        timers.push(timer);
      }
    });
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [notifications, close]);
  
  return {
    notifications,
    notify,
    info,
    success,
    warning,
    error,
    close,
    closeAll
  };
}
