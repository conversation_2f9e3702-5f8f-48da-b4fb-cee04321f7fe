# 添加人员对话框字段顺序调整

## 修改内容

已成功调整添加人员对话框中的字段显示顺序，将联系方式字段移动到最下面。

### 修改前的字段顺序：
1. 父级部门 *
2. 人员名称 *
3. 备注 (可选)
4. **联系方式 (可选)**
5. 岗位密级 (可选)

### 修改后的字段顺序：
1. 父级部门 *
2. 人员名称 *
3. 备注 (可选)
4. 岗位密级 (可选)
5. **联系方式 (可选)** ← 移动到最下面

## 技术实现

修改了 `src/pages/Inventory/InventoryManagement/Dialogs/AddDepartmentDialog.tsx` 文件中的字段渲染顺序：

- 将联系方式输入框的代码块从岗位密级选择之前移动到岗位密级选择之后
- 保持了所有字段的功能和验证逻辑不变
- 添加了注释说明联系方式已移动到最下面

## 影响范围

此修改仅影响添加人员对话框的UI布局，不影响：
- 数据提交逻辑
- 字段验证规则
- API调用参数
- 其他相关功能

## 测试验证

1. 代码已通过编译测试
2. 字段顺序调整符合用户需求
3. 所有字段功能保持正常

## 修改的文件

- `src/pages/Inventory/InventoryManagement/Dialogs/AddDepartmentDialog.tsx`
  - 调整了人员模式下的字段渲染顺序
  - 将联系方式字段移动到岗位密级字段之后

修改已完成并通过编译测试。
