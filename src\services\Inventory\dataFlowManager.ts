/**
 * 数据流管理器 - 统一管理数据更新和计数同步
 * 解决分类操作后计数不更新的根本问题
 */

import InventoryService from './inventoryService';
import DepartmentService from './departmentService';
import TreeCountManager from './treeCountManager';
import { InventoryDataService } from './services/inventoryDataService';

export interface DataFlowOperation {
  type: 'category-update' | 'category-delete' | 'device-update' | 'device-add' | 'device-delete';
  target: 'parent-category' | 'sub-category' | 'device' | 'department';
  data?: any;
}

class DataFlowManager {
  private static instance: DataFlowManager;
  private isProcessing = false;
  private operationQueue: DataFlowOperation[] = [];

  private constructor() {}

  public static getInstance(): DataFlowManager {
    if (!DataFlowManager.instance) {
      DataFlowManager.instance = new DataFlowManager();
    }
    return DataFlowManager.instance;
  }

  /**
   * 处理数据操作，确保数据和计数同步更新
   */
  public async processOperation(operation: DataFlowOperation): Promise<void> {
    console.log(`DataFlowManager: 处理操作`, operation);

    // 如果正在处理，加入队列
    if (this.isProcessing) {
      this.operationQueue.push(operation);
      return;
    }

    this.isProcessing = true;

    try {
      await this.executeOperation(operation);
      
      // 处理队列中的其他操作
      while (this.operationQueue.length > 0) {
        const nextOperation = this.operationQueue.shift()!;
        await this.executeOperation(nextOperation);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  private async executeOperation(operation: DataFlowOperation): Promise<void> {
    const { type, target } = operation;

    console.log(`DataFlowManager: 执行操作 ${type} - ${target}`);

    // 根据操作类型执行相应的数据同步
    switch (type) {
      case 'category-update':
        await this.handleCategoryUpdate(target);
        break;
      case 'category-delete':
        await this.handleCategoryDelete(target);
        break;
      case 'device-update':
        await this.handleDeviceUpdate();
        break;
      case 'device-add':
      case 'device-delete':
        await this.handleDeviceChange();
        break;
    }
  }

  private async handleCategoryUpdate(target: string): Promise<void> {
    console.log(`DataFlowManager: 处理分类更新 - ${target}`);

    // 等待事件处理完成
    await this.waitForEventProcessing();

    // 立即更新所有计数
    const treeCountManager = TreeCountManager.getInstance();
    treeCountManager.requestUpdate('both', true);

    console.log(`DataFlowManager: 分类更新处理完成`);
  }

  private async handleCategoryDelete(target: string): Promise<void> {
    console.log(`DataFlowManager: 处理分类删除 - ${target}`);

    // 分类删除由事件处理器处理，这里不需要额外操作
    console.log(`DataFlowManager: 分类删除处理完成`);
  }

  private async handleDeviceUpdate(): Promise<void> {
    console.log(`DataFlowManager: 处理设备更新`);

    // 等待数据更新完成
    await this.waitForEventProcessing();

    // 设备更新影响分类计数，立即更新
    const treeCountManager = TreeCountManager.getInstance();
    treeCountManager.requestUpdate('both', true); // 立即更新，确保计数准确

    console.log(`DataFlowManager: 设备更新处理完成`);
  }

  private async handleDeviceChange(): Promise<void> {
    console.log(`DataFlowManager: 处理设备变更`);

    // 设备添加/删除影响计数
    const treeCountManager = TreeCountManager.getInstance();
    treeCountManager.requestUpdate('both', false); // 使用防抖

    console.log(`DataFlowManager: 设备变更处理完成`);
  }

  /**
   * 等待事件处理完成
   */
  private async waitForEventProcessing(): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 150); // 等待事件处理完成
    });
  }

  /**
   * 强制同步所有数据和计数
   */
  public async forceSyncAll(): Promise<void> {
    console.log(`DataFlowManager: 强制同步所有数据`);

    const inventoryService = InventoryService.getInstance();
    const departmentService = DepartmentService.getInstance();
    const treeCountManager = TreeCountManager.getInstance();

    // 获取最新数据
    const inventoryList = inventoryService.getState().inventoryList;
    console.log(`DataFlowManager: 当前设备数量: ${inventoryList.length}`);

    // 确保数据已加载
    if (inventoryList.length === 0) {
      console.log(`DataFlowManager: 设备列表为空，等待数据加载完成...`);
      // 等待数据加载
      await this.waitForDataLoading();
    }

    // 立即更新所有计数
    console.log(`DataFlowManager: 立即更新所有分类树计数`);
    treeCountManager.requestUpdate('both', true);

    // 额外等待确保计数更新完成
    await this.waitForEventProcessing();

    console.log(`DataFlowManager: 强制同步完成`);
  }

  /**
   * 等待数据加载完成（包括设备数据和部门树数据）
   */
  private async waitForDataLoading(): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const timeout = 10000; // 10秒超时

      const checkData = () => {
        const inventoryService = InventoryService.getInstance();
        const departmentService = DepartmentService.getInstance();

        const inventoryList = inventoryService.getState().inventoryList;
        const departmentCategories = departmentService.getState().departmentCategories;

        // 检查设备数据是否已加载完成（包括空数据库的情况）
        // 通过InventoryDataService检查数据库状态，而不仅仅检查数据是否存在
        let hasInventoryDataLoaded = false;
        let isEmptyDatabase = false;
        let dbInitialized = false;

        try {
          const inventoryDataService = InventoryDataService.getInstance();

          // 获取数据库状态信息
          isEmptyDatabase = inventoryDataService.getIsEmptyDatabase();
          dbInitialized = inventoryDataService.getDbInitialized();
          const hasInventoryData = inventoryList.length > 0;

          // 数据加载完成的条件：
          // 1. 有数据且数据库已初始化，或
          // 2. 数据库为空且数据库已初始化
          hasInventoryDataLoaded = (hasInventoryData || isEmptyDatabase) && dbInitialized;

          if (isEmptyDatabase && dbInitialized) {
            console.log(`DataFlowManager: 数据库为空但已初始化，设备数据加载完成`);
          } else if (hasInventoryData && dbInitialized) {
            console.log(`DataFlowManager: 设备数据加载完成，数量: ${inventoryList.length}`);
          }
        } catch (error) {
          // 如果无法获取数据库状态，回退到原来的逻辑
          console.warn('DataFlowManager: 无法获取数据库状态，使用回退逻辑:', error);
          hasInventoryDataLoaded = inventoryList.length > 0;
        }

        const hasDepartmentData = departmentCategories.length > 0;

        if (hasInventoryDataLoaded && hasDepartmentData) {
          const statusMsg = isEmptyDatabase ?
            `数据加载完成，设备数据库为空, 部门节点数量: ${departmentCategories.length}` :
            `数据加载完成，设备数量: ${inventoryList.length}, 部门节点数量: ${departmentCategories.length}`;
          console.log(`DataFlowManager: ${statusMsg}`);
          resolve();
        } else {
          // 检查超时
          const elapsed = Date.now() - startTime;
          if (elapsed > timeout) {
            const inventoryStatus = hasInventoryDataLoaded ? '✓' :
              (isEmptyDatabase ? '✓(空)' : (dbInitialized ? '✗' : '✗(未初始化)'));
            console.warn(`DataFlowManager: 等待数据加载超时 (${elapsed}ms)，设备: ${inventoryStatus}, 部门树: ${hasDepartmentData ? '✓' : '✗'}`);
            // 即使超时也继续执行，但会跳过部门树计数更新
            resolve();
          } else {
            const inventoryStatus = hasInventoryDataLoaded ? '✓' :
              (isEmptyDatabase ? '✓(空)' : (dbInitialized ? '✗' : '✗(未初始化)'));
            console.log(`DataFlowManager: 等待数据加载 (${elapsed}ms)，设备: ${inventoryStatus}, 部门树: ${hasDepartmentData ? '✓' : '✗'}`);
            // 继续等待
            setTimeout(checkData, 100);
          }
        }
      };

      checkData();
    });
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.operationQueue = [];
    this.isProcessing = false;
  }
}

export default DataFlowManager;
