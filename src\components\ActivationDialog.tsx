import React from 'react';

interface AnnouncementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  announcement: string;
  isLoading: boolean;
  error: string | null;
  onActivateSystem: () => void;
  onGoToRegistration: () => void;
  onRetry: () => void;
}

/**
 * 公告窗口组件
 * 当根节点查询失败且错误为"T00表为空"时显示
 */
const AnnouncementDialog: React.FC<AnnouncementDialogProps> = ({
  isOpen,
  onClose,
  announcement,
  isLoading,
  error,
  onActivateSystem,
  onGoToRegistration,
  onRetry
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded shadow-lg max-w-md w-full mx-4">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-blue-600">系统公告</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-4">
            {/* 公告内容区域 */}
            <div className="min-h-[100px] p-4 bg-gray-50 rounded-lg">
              {isLoading && (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
                  <span className="ml-2 text-gray-600">正在获取公告...</span>
                </div>
              )}

              {error && (
                <div className="text-center">
                  <p className="text-red-600 mb-2">获取公告失败</p>
                  <p className="text-sm text-gray-500 mb-3">{error}</p>
                  <button
                    onClick={onRetry}
                    className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  >
                    重试
                  </button>
                </div>
              )}

              {!isLoading && !error && announcement && (
                <div className="text-gray-700">
                  <p className="whitespace-pre-wrap">{announcement}</p>
                </div>
              )}

              {!isLoading && !error && !announcement && (
                <div className="text-center text-gray-500">
                  暂无公告信息
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-3">
              <button
                onClick={onActivateSystem}
                className="flex-1 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 font-medium"
              >
                激活系统
              </button>
              <button
                onClick={onGoToRegistration}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 font-medium"
              >
                前往注册
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementDialog;
