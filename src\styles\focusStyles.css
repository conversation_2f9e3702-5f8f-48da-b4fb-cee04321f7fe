/* 焦点管理样式 */

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 跳转链接样式已禁用 */
/*
.skip-links {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  width: 100%;
  pointer-events: none;
}

.skip-link {
  position: absolute;
  top: -100px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  color: #fff;
  text-decoration: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  pointer-events: auto;
  white-space: nowrap;
}

.skip-link:focus {
  top: 16px;
  outline: 3px solid #fbbf24;
  outline-offset: 2px;
  transform: translateX(-50%) scale(1.05);
}
*/

/* 键盘导航模式 */
body.keyboard-navigation *:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 基础焦点样式 */
.focus-ring:focus,
.focus-ring:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 页面主要内容区域焦点样式 */
main:focus,
[role="main"]:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: -2px !important;
  box-shadow: inset 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 页面标题焦点样式 */
h1:focus,
h2:focus,
h3:focus,
h4:focus,
h5:focus,
h6:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 对话框焦点容器 - 用于对话框内的Tab导航 */
.focus-container {
  outline: none;
}

/* 对话框内的焦点样式 */
.dialog-focus:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 菜单项焦点样式 */
.menu-item:focus-visible {
  background-color: #eff6ff !important;
  outline: 2px solid #3b82f6 !important;
  outline-offset: -2px !important;
}

/* 列表项焦点样式 */
[role="listitem"]:focus,
.list-item:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: -2px !important;
  background-color: rgba(59, 130, 246, 0.05) !important;
}

/* 表单字段错误状态焦点样式 */
.error:focus,
.is-invalid:focus,
[data-error="true"]:focus {
  outline: 2px solid #dc2626 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.25) !important;
}

/* 表单字段焦点样式增强 */
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
  border-color: #3b82f6 !important;
}

/* 按钮焦点样式 */
button:focus,
[role="button"]:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 链接焦点样式 */
a:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
  text-decoration: underline !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .focus-ring:focus,
  .focus-ring:focus-visible,
  body.keyboard-navigation *:focus {
    outline: 3px solid currentColor !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  /* .skip-link {
    transition: none;
  } */
}

/* 表格行焦点样式 */
.table-row:focus-visible {
  outline: 2px solid #3b82f6 !important;
  background-color: #eff6ff !important;
}

/* 树节点焦点样式 */
.tree-node:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: -2px !important;
  background-color: #eff6ff !important;
}

/* 按钮焦点样式 */
.button:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 输入框焦点样式 */
.input:focus-visible {
  outline: 2px solid #3b82f6 !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 下拉框焦点样式 */
.select:focus-visible {
  outline: 2px solid #3b82f6 !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 标签页焦点样式 */
.tab:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: -2px !important;
  background-color: #eff6ff !important;
}

/* 链接焦点样式 */
.link:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  text-decoration: underline !important;
}

/* 隐藏鼠标点击时的焦点样式，但保留键盘导航时的焦点样式 */
*:focus:not(:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
}
