/**
 * 人员相关工具函数
 * 统一处理人员信息的各种操作，避免代码重复
 */

/**
 * 人员信息接口
 */
export interface PersonInfo {
  id?: number | string;
  name: string;
  alias?: string;
  mobile?: string;
  department?: string;
  [key: string]: any;
}

/**
 * 人员姓名和备注解析结果
 */
export interface PersonNameAndAlias {
  personName: string;
  personAlias: string;
}

/**
 * 提取人员姓名和备注
 * @param fullName 完整的人员名称，可能包含备注，如 "张三 (小张)"
 * @returns 解析后的姓名和备注
 */
export function extractPersonNameAndAlias(fullName: string): PersonNameAndAlias {
  if (!fullName) {
    return { personName: '', personAlias: '' };
  }

  const trimmedName = fullName.trim();
  
  // 匹配格式：姓名 (备注) 或 姓名(备注)
  const match = trimmedName.match(/^(.+?)\s*\((.+?)\)\s*$/);
  
  if (match) {
    return {
      personName: match[1].trim(),
      personAlias: match[2].trim()
    };
  }
  
  // 如果没有备注，返回原始姓名
  return {
    personName: trimmedName,
    personAlias: ''
  };
}

/**
 * 构建人员显示名称
 * @param personName 人员姓名
 * @param personAlias 人员备注
 * @returns 格式化的显示名称
 */
export function buildPersonDisplayName(personName: string, personAlias?: string): string {
  if (!personName) return '';
  
  const trimmedName = personName.trim();
  const trimmedAlias = personAlias?.trim();
  
  if (trimmedAlias) {
    return `${trimmedName} (${trimmedAlias})`;
  }
  
  return trimmedName;
}

/**
 * 检查人员唯一性
 * @param personName 人员姓名
 * @param personAlias 人员备注
 * @param existingPersons 已存在的人员列表
 * @param options 检查选项
 * @returns 检查结果
 */
export function checkPersonUniqueness(
  personName: string,
  personAlias: string,
  existingPersons: PersonInfo[],
  options: {
    checkNameOnly?: boolean;      // 只检查姓名，忽略备注
    checkDisplayName?: boolean;   // 检查完整显示名称
    caseSensitive?: boolean;      // 是否区分大小写
  } = {}
): {
  isUnique: boolean;
  duplicatePersons: PersonInfo[];
  conflictType: 'name' | 'displayName' | 'none';
} {
  const {
    checkNameOnly = false,
    checkDisplayName = true,
    caseSensitive = false
  } = options;

  const normalizeString = (str: string) => 
    caseSensitive ? str.trim() : str.trim().toLowerCase();

  const targetName = normalizeString(personName);
  const targetAlias = normalizeString(personAlias || '');
  const targetDisplayName = normalizeString(buildPersonDisplayName(personName, personAlias));

  const duplicatePersons: PersonInfo[] = [];
  let conflictType: 'name' | 'displayName' | 'none' = 'none';

  for (const person of existingPersons) {
    const { personName: existingName, personAlias: existingAlias } = extractPersonNameAndAlias(person.name);
    const normalizedExistingName = normalizeString(existingName);
    const normalizedExistingAlias = normalizeString(existingAlias);
    const normalizedExistingDisplayName = normalizeString(person.name);

    // 检查姓名冲突
    if (checkNameOnly && targetName === normalizedExistingName) {
      duplicatePersons.push(person);
      conflictType = 'name';
      continue;
    }

    // 检查完整显示名称冲突
    if (checkDisplayName && targetDisplayName === normalizedExistingDisplayName) {
      duplicatePersons.push(person);
      conflictType = 'displayName';
      continue;
    }

    // 默认检查：姓名相同且备注相同
    if (!checkNameOnly && !checkDisplayName) {
      if (targetName === normalizedExistingName && targetAlias === normalizedExistingAlias) {
        duplicatePersons.push(person);
        conflictType = 'displayName';
      }
    }
  }

  return {
    isUnique: duplicatePersons.length === 0,
    duplicatePersons,
    conflictType
  };
}

/**
 * 判断责任人是否匹配
 * @param responsible 设备的责任人
 * @param personFullName 人员全名
 * @returns 是否匹配
 */
export function isResponsibleMatch(responsible: string, personFullName: string): boolean {
  if (!responsible || !personFullName) return false;

  const normalizeString = (str: string) => str.trim().toLowerCase();

  // 1. 完全相等（包括备注）
  if (normalizeString(responsible) === normalizeString(personFullName)) {
    return true;
  }

  // 2. 提取姓名和备注进行比较
  const { personName: responsibleName, personAlias: responsibleAlias } = extractPersonNameAndAlias(responsible);
  const { personName: targetName, personAlias: targetAlias } = extractPersonNameAndAlias(personFullName);

  // 3. 人员唯一性由姓名+备注共同决定，必须姓名和备注都匹配
  if (normalizeString(responsibleName) === normalizeString(targetName)) {
    // 如果目标人员有备注，则备注也必须匹配
    if (targetAlias) {
      return normalizeString(responsibleAlias || '') === normalizeString(targetAlias);
    }
    // 如果目标人员没有备注，则责任人也不应该有备注（或备注为空）
    else {
      return !responsibleAlias || responsibleAlias.trim() === '';
    }
  }

  return false;
}

/**
 * 格式化人员信息用于显示
 * @param person 人员信息
 * @param options 格式化选项
 * @returns 格式化后的字符串
 */
export function formatPersonInfo(
  person: PersonInfo,
  options: {
    includeAlias?: boolean;
    includeMobile?: boolean;
    includeDepartment?: boolean;
    separator?: string;
  } = {}
): string {
  const {
    includeAlias = true,
    includeMobile = false,
    includeDepartment = false,
    separator = ' | '
  } = options;

  const parts: string[] = [];

  // 添加姓名（可能包含备注）
  if (includeAlias) {
    parts.push(person.name);
  } else {
    const { personName } = extractPersonNameAndAlias(person.name);
    parts.push(personName);
  }

  // 添加手机号
  if (includeMobile && person.mobile) {
    parts.push(person.mobile);
  }

  // 添加部门
  if (includeDepartment && person.department) {
    parts.push(person.department);
  }

  return parts.join(separator);
}

/**
 * 验证人员信息的完整性
 * @param person 人员信息
 * @param requiredFields 必填字段
 * @returns 验证结果
 */
export function validatePersonInfo(
  person: Partial<PersonInfo>,
  requiredFields: (keyof PersonInfo)[] = ['name']
): {
  isValid: boolean;
  missingFields: string[];
  errors: string[];
} {
  const missingFields: string[] = [];
  const errors: string[] = [];

  // 检查必填字段
  for (const field of requiredFields) {
    if (!person[field] || (typeof person[field] === 'string' && !person[field]?.trim())) {
      missingFields.push(String(field));
    }
  }

  // 检查姓名格式
  if (person.name) {
    const { personName } = extractPersonNameAndAlias(person.name);
    if (!personName) {
      errors.push('人员姓名不能为空');
    }
  }

  // 检查手机号格式
  if (person.mobile && !/^1[3-9]\d{9}$/.test(person.mobile)) {
    errors.push('手机号格式不正确');
  }

  return {
    isValid: missingFields.length === 0 && errors.length === 0,
    missingFields,
    errors
  };
}

/**
 * 搜索人员
 * @param persons 人员列表
 * @param searchTerm 搜索词
 * @param searchFields 搜索字段
 * @returns 匹配的人员列表
 */
export function searchPersons(
  persons: PersonInfo[],
  searchTerm: string,
  searchFields: (keyof PersonInfo)[] = ['name', 'mobile', 'department']
): PersonInfo[] {
  if (!searchTerm.trim()) return persons;

  const normalizedSearchTerm = searchTerm.trim().toLowerCase();

  return persons.filter(person => {
    return searchFields.some(field => {
      const value = person[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(normalizedSearchTerm);
      }
      return false;
    });
  });
}
