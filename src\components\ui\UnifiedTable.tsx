/**
 * 统一表格组件 - 替代DataTable和InspectionTable
 * 基于BaseTable实现，提供简化的API
 */

import React, { useMemo } from 'react';
import BaseTable from './BaseTable';
import { BaseTableColumn, BaseTableProps } from '../../types/table';

/**
 * 简化的表格列定义 - 兼容原有的TableColumn接口
 */
export interface TableColumn {
  key: string;
  title: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

/**
 * 统一表格组件属性
 */
interface UnifiedTableProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  loadingText?: string;
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  className?: string;
  onRowClick?: (record: any, index: number) => void;
  /** 表格大小 */
  size?: 'small' | 'middle' | 'large';
  /** 是否显示边框 */
  bordered?: boolean;
  /** 是否启用排序 */
  sortable?: boolean;
  /** 是否启用分页 */
  pagination?: boolean | {
    pageSize?: number;
    showQuickJumper?: boolean;
    showTotal?: boolean;
  };
  /** 最小宽度 */
  minWidth?: number | string;
  /** 行键值 */
  rowKey?: string | ((record: any) => string);
}

/**
 * 统一表格组件
 */
const UnifiedTable: React.FC<UnifiedTableProps> = ({
  columns,
  data,
  loading = false,
  loadingText = '加载中...',
  emptyText = '暂无数据',
  emptyIcon,
  className = '',
  onRowClick,
  size = 'middle',
  bordered = true,
  sortable = true,
  pagination = false,
  minWidth = 800,
  rowKey = 'id',
}) => {
  // 转换列定义为BaseTable格式
  const baseColumns = useMemo<BaseTableColumn[]>(() => {
    return columns.map(column => ({
      key: column.key,
      title: column.title,
      width: column.width ? (typeof column.width === 'string' ? parseInt(column.width.replace(/\D/g, '')) : column.width) : 150,
      align: column.align || 'left',
      sortable: sortable,
      render: column.render,
    }));
  }, [columns, sortable]);

  // 处理分页配置
  const paginationConfig = useMemo(() => {
    if (pagination === false) {
      return false;
    }
    
    if (pagination === true) {
      return {
        pageIndex: 0,
        pageSize: 10,
        total: data.length,
        showQuickJumper: true,
        showTotal: true,
      };
    }
    
    return {
      pageIndex: 0,
      pageSize: pagination.pageSize || 10,
      total: data.length,
      showQuickJumper: pagination.showQuickJumper ?? true,
      showTotal: pagination.showTotal ?? true,
    };
  }, [pagination, data.length]);

  // 处理事件
  const events = useMemo(() => ({
    onRowClick: onRowClick ? (record: any, index: number) => onRowClick(record, index) : undefined,
  }), [onRowClick]);

  return (
    <BaseTable
      data={data}
      columns={baseColumns}
      features={{
        sorting: sortable,
        pagination: !!pagination,
        selection: false,
        filtering: false,
        columnVisibility: false,
      }}
      pagination={paginationConfig}
      loading={{
        loading,
        loadingText,
      }}
      empty={{
        emptyText,
        emptyIcon,
      }}
      events={events}
      style={{
        size,
        bordered,
        className: `unified-table ${className}`,
      }}
      scroll={{
        x: minWidth,
      }}
      rowKey={rowKey}
    />
  );
};

export default UnifiedTable;

/**
 * 数据表格组件 - 兼容原有的DataTable接口
 */
export const DataTable: React.FC<{
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  className?: string;
}> = (props) => {
  return (
    <div className={`flex-1 px-6 pb-6 ${props.className || ''}`}>
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <UnifiedTable
          {...props}
          size="middle"
          bordered={false}
          sortable={false}
          pagination={false}
          minWidth={800}
        />
      </div>
    </div>
  );
};

/**
 * 巡检表格组件 - 兼容原有的InspectionTable接口
 */
export const InspectionTable: React.FC<{
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  loadingText?: string;
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  className?: string;
  onRowClick?: (record: any, index: number) => void;
}> = (props) => {
  return (
    <UnifiedTable
      {...props}
      size="middle"
      bordered={true}
      sortable={true}
      pagination={false}
      minWidth={800}
      className={`inspection-table ${props.className || ''}`}
    />
  );
};

// 导出类型定义以保持兼容性
export type { TableColumn };
