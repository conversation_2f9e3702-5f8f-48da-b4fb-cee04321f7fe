import { useState, useCallback, useRef } from 'react';

/**
 * 统一的树状态管理Hook
 * 整合重复的展开状态管理和事件处理逻辑
 */
export interface UseTreeStateOptions {
  defaultExpandedNodes?: string[];
  onNodeSelect?: (nodeId: string) => void;
  onNodeToggle?: (nodeId: string, isExpanded: boolean) => void;
}

export interface UseTreeStateReturn {
  // 状态
  expandedNodes: Set<string>;
  selectedNode: string | null;
  
  // 方法
  toggleNode: (nodeId: string, event?: React.MouseEvent) => void;
  expandNode: (nodeId: string) => void;
  collapseNode: (nodeId: string) => void;
  selectNode: (nodeId: string) => void;
  isNodeExpanded: (nodeId: string) => boolean;
  isNodeSelected: (nodeId: string) => boolean;
  
  // 批量操作
  expandAll: (nodeIds: string[]) => void;
  collapseAll: () => void;
  setExpandedNodes: (nodeIds: string[]) => void;
  
  // 重置
  reset: () => void;
}

/**
 * 统一的树状态管理Hook
 */
export function useTreeState(options: UseTreeStateOptions = {}): UseTreeStateReturn {
  const {
    defaultExpandedNodes = [],
    onNodeSelect,
    onNodeToggle
  } = options;

  // 展开的节点集合
  const [expandedNodes, setExpandedNodesState] = useState<Set<string>>(() => {
    return new Set(defaultExpandedNodes);
  });

  // 选中的节点
  const [selectedNode, setSelectedNode] = useState<string | null>(null);

  // 保存初始状态用于重置
  const initialExpandedNodes = useRef(new Set(defaultExpandedNodes));

  // 切换节点展开/收起状态
  const toggleNode = useCallback((nodeId: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation(); // 阻止事件冒泡
    }

    setExpandedNodesState(prev => {
      const newSet = new Set(prev);
      const wasExpanded = newSet.has(nodeId);
      
      if (wasExpanded) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }

      // 触发回调
      if (onNodeToggle) {
        onNodeToggle(nodeId, !wasExpanded);
      }

      return newSet;
    });
  }, [onNodeToggle]);

  // 展开节点
  const expandNode = useCallback((nodeId: string) => {
    setExpandedNodesState(prev => {
      if (prev.has(nodeId)) return prev;
      
      const newSet = new Set(prev);
      newSet.add(nodeId);
      
      // 触发回调
      if (onNodeToggle) {
        onNodeToggle(nodeId, true);
      }
      
      return newSet;
    });
  }, [onNodeToggle]);

  // 收起节点
  const collapseNode = useCallback((nodeId: string) => {
    setExpandedNodesState(prev => {
      if (!prev.has(nodeId)) return prev;
      
      const newSet = new Set(prev);
      newSet.delete(nodeId);
      
      // 触发回调
      if (onNodeToggle) {
        onNodeToggle(nodeId, false);
      }
      
      return newSet;
    });
  }, [onNodeToggle]);

  // 选择节点
  const selectNode = useCallback((nodeId: string) => {
    setSelectedNode(nodeId);
    
    // 触发回调
    if (onNodeSelect) {
      onNodeSelect(nodeId);
    }
  }, [onNodeSelect]);

  // 检查节点是否展开
  const isNodeExpanded = useCallback((nodeId: string): boolean => {
    return expandedNodes.has(nodeId);
  }, [expandedNodes]);

  // 检查节点是否选中
  const isNodeSelected = useCallback((nodeId: string): boolean => {
    return selectedNode === nodeId;
  }, [selectedNode]);

  // 批量展开节点
  const expandAll = useCallback((nodeIds: string[]) => {
    setExpandedNodesState(prev => {
      const newSet = new Set(prev);
      let hasChanges = false;
      
      nodeIds.forEach(nodeId => {
        if (!newSet.has(nodeId)) {
          newSet.add(nodeId);
          hasChanges = true;
          
          // 触发回调
          if (onNodeToggle) {
            onNodeToggle(nodeId, true);
          }
        }
      });
      
      return hasChanges ? newSet : prev;
    });
  }, [onNodeToggle]);

  // 收起所有节点
  const collapseAll = useCallback(() => {
    setExpandedNodesState(prev => {
      if (prev.size === 0) return prev;
      
      // 触发所有节点的收起回调
      if (onNodeToggle) {
        prev.forEach(nodeId => {
          onNodeToggle(nodeId, false);
        });
      }
      
      return new Set();
    });
  }, [onNodeToggle]);

  // 设置展开的节点
  const setExpandedNodes = useCallback((nodeIds: string[]) => {
    setExpandedNodesState(prev => {
      const newSet = new Set(nodeIds);
      
      // 检查是否有变化
      if (prev.size === newSet.size && [...prev].every(id => newSet.has(id))) {
        return prev;
      }
      
      // 触发变化的节点回调
      if (onNodeToggle) {
        // 新展开的节点
        newSet.forEach(nodeId => {
          if (!prev.has(nodeId)) {
            onNodeToggle(nodeId, true);
          }
        });
        
        // 新收起的节点
        prev.forEach(nodeId => {
          if (!newSet.has(nodeId)) {
            onNodeToggle(nodeId, false);
          }
        });
      }
      
      return newSet;
    });
  }, [onNodeToggle]);

  // 重置到初始状态
  const reset = useCallback(() => {
    setExpandedNodesState(new Set(initialExpandedNodes.current));
    setSelectedNode(null);
  }, []);

  return {
    // 状态
    expandedNodes,
    selectedNode,
    
    // 方法
    toggleNode,
    expandNode,
    collapseNode,
    selectNode,
    isNodeExpanded,
    isNodeSelected,
    
    // 批量操作
    expandAll,
    collapseAll,
    setExpandedNodes,
    
    // 重置
    reset
  };
}

/**
 * 树节点渲染辅助Hook
 * 提供常用的节点渲染逻辑
 */
export interface UseTreeRenderOptions {
  categoryMode?: 'device' | 'department';
  draggable?: boolean;
}

export interface UseTreeRenderReturn {
  calculateIndent: (depth: number, categoryMode: string) => string;
  getNodeClasses: (isSelected: boolean, isDragging?: boolean, isDropTarget?: boolean) => string;
  shouldShowExpandIcon: (hasChildren: boolean) => boolean;
}

export function useTreeRender(options: UseTreeRenderOptions = {}): UseTreeRenderReturn {
  const { categoryMode = 'device', draggable = false } = options;

  // 计算缩进
  const calculateIndent = useCallback((depth: number, mode: string): string => {
    const baseIndent = mode === 'department' ? 8 : 12; // 基础缩进
    const indentPerLevel = mode === 'department' ? 12 : 16; // 每级缩进
    return `${baseIndent + depth * indentPerLevel}px`;
  }, []);

  // 获取节点样式类
  const getNodeClasses = useCallback((
    isSelected: boolean, 
    isDragging: boolean = false, 
    isDropTarget: boolean = false
  ): string => {
    const baseClasses = `flex items-center ${categoryMode === 'department' ? 'py-0.5' : 'py-1'} px-1 my-0.5 rounded-sm cursor-pointer hover:bg-blue-50`;
    
    let classes = baseClasses;
    
    if (isSelected) {
      classes += ' bg-blue-100 text-blue-700 font-medium';
    }
    
    if (isDragging) {
      classes += ' opacity-50';
    }
    
    if (isDropTarget) {
      classes += ' bg-blue-100 border border-blue-300 rounded';
    }
    
    return classes;
  }, [categoryMode]);

  // 是否显示展开图标
  const shouldShowExpandIcon = useCallback((hasChildren: boolean): boolean => {
    return hasChildren;
  }, []);

  return {
    calculateIndent,
    getNodeClasses,
    shouldShowExpandIcon
  };
}
