import mitt, { Emitter } from 'mitt';
import AnnouncementWebSocketManager from '../../utils/announcementWebSocket';

/**
 * 公告数据接口
 */
interface AnnouncementData {
  announcement: string;
}

/**
 * 公告响应接口
 */
interface AnnouncementResponse {
  success: boolean;
  timestamp: string;
  message: string;
  type: string;
  data: AnnouncementData;
}

/**未找到匹配的公司名称


 * 公告服务状态接口
 */
interface AnnouncementServiceState {
  isLoading: boolean;
  announcement: string;
  error: string | null;
  lastFetchTime: number;
}

/**
 * 公告服务事件接口
 */
interface AnnouncementServiceEvents {
  'state-change': AnnouncementServiceState;
  'announcement-loaded': string;
  'error': string;
  'loading': boolean;
  [key: string]: any; // 添加索引签名以满足mitt类型约束
}

/**
 * 公告服务类
 * Service层：只负责数据获取和WebSocket通信
 */
class AnnouncementService {
  private static instance: AnnouncementService;
  private emitter: Emitter<AnnouncementServiceEvents> = mitt<AnnouncementServiceEvents>();
  private announcementWS: AnnouncementWebSocketManager = AnnouncementWebSocketManager.getInstance();
  private state: AnnouncementServiceState = {
    isLoading: false,
    announcement: '',
    error: null,
    lastFetchTime: 0
  };

  // 请求超时配置
  private readonly REQUEST_TIMEOUT = 5000; // 5秒请求超时

  /**
   * 获取公告服务实例
   */
  public static getInstance(): AnnouncementService {
    if (!AnnouncementService.instance) {
      AnnouncementService.instance = new AnnouncementService();
    }
    return AnnouncementService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 获取当前状态
   */
  public getState(): AnnouncementServiceState {
    return { ...this.state };
  }

  /**
   * 更新状态
   */
  private updateState(newState: Partial<AnnouncementServiceState>): void {
    this.state = { ...this.state, ...newState };
    this.emitter.emit('state-change', this.state);
    
    if (newState.error) {
      this.emitter.emit('error', newState.error);
    }
    
    if (newState.isLoading !== undefined) {
      this.emitter.emit('loading', newState.isLoading);
    }
  }



  /**
   * 发送请求并等待响应
   */
  private async sendRequest(request: any): Promise<AnnouncementResponse> {
    return await this.announcementWS.sendRequest(request, 'announcement_response', this.REQUEST_TIMEOUT);
  }

  /**
   * 获取公告信息
   */
  public async getAnnouncement(): Promise<string> {
    this.updateState({ isLoading: true, error: null });

    try {
      // 发送获取公告请求
      const request = {
        type: "get_announcement"
      };

      const response = await this.sendRequest(request);

      // 检查响应
      if (!response.success) {
        throw new Error(response.message || '获取公告失败');
      }

      const announcement = response.data?.announcement || '';
      
      // 更新状态
      this.updateState({
        isLoading: false,
        announcement,
        lastFetchTime: Date.now()
      });

      // 触发事件
      this.emitter.emit('announcement-loaded', announcement);

      console.log('公告获取成功:', announcement);
      return announcement;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取公告失败';
      console.error('获取公告失败:', errorMessage);
      
      this.updateState({
        isLoading: false,
        error: errorMessage
      });

      throw error;
    }
  }



  /**
   * 事件订阅
   */
  public on<K extends keyof AnnouncementServiceEvents>(
    event: K, 
    callback: (data: AnnouncementServiceEvents[K]) => void
  ): void {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消事件订阅
   */
  public off<K extends keyof AnnouncementServiceEvents>(
    event: K, 
    callback: (data: AnnouncementServiceEvents[K]) => void
  ): void {
    this.emitter.off(event, callback as any);
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    this.emitter.all.clear();
  }
}

export default AnnouncementService;
