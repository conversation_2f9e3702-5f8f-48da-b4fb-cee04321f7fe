/**
 * 服务构造函数类型
 */
type ServiceConstructor<T> = new (...args: any[]) => T;

/**
 * 服务工厂函数类型
 */
type ServiceFactory<T> = () => T;

/**
 * 服务注册表
 * 管理应用中的所有服务实例
 */
class ServiceRegistry {
  private static instance: ServiceRegistry;
  private services: Map<string, any> = new Map();
  private factories: Map<string, ServiceFactory<any>> = new Map();

  private constructor() {}

  /**
   * 获取服务注册表实例
   * @returns 服务注册表实例
   */
  public static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry();
    }
    return ServiceRegistry.instance;
  }

  /**
   * 注册服务实例
   * @param key 服务键名
   * @param service 服务实例
   */
  public register<T>(key: string, service: T): void {
    this.services.set(key, service);
  }

  /**
   * 注册服务工厂
   * @param key 服务键名
   * @param factory 服务工厂函数
   */
  public registerFactory<T>(key: string, factory: ServiceFactory<T>): void {
    this.factories.set(key, factory);
  }

  /**
   * 注册单例服务
   * @param key 服务键名
   * @param constructor 服务构造函数
   */
  public registerSingleton<T>(key: string, constructor: ServiceConstructor<T>): void {
    if (!this.services.has(key)) {
      const service = new constructor();
      this.register(key, service);
    }
  }

  /**
   * 获取服务实例
   * @param key 服务键名
   * @returns 服务实例
   */
  public get<T>(key: string): T {
    // 检查是否已有实例
    if (this.services.has(key)) {
      return this.services.get(key) as T;
    }

    // 检查是否有工厂函数
    if (this.factories.has(key)) {
      const factory = this.factories.get(key)!;
      const service = factory();
      this.services.set(key, service);
      return service as T;
    }

    throw new Error(`Service not found: ${key}`);
  }

  /**
   * 获取服务实例，如果不存在则尝试使用提供的工厂函数创建
   * @param key 服务键名
   * @param factory 如果服务不存在时用于创建服务的工厂函数
   * @returns 服务实例
   */
  public getOrCreate<T>(key: string, factory: () => T): T {
    try {
      // 尝试获取已注册的服务
      return this.get<T>(key);
    } catch (error) {
      // 如果服务不存在，使用提供的工厂函数创建
      console.log(`服务 ${key} 未注册，正在创建新实例...`);
      const service = factory();
      this.register(key, service);
      return service;
    }
  }

  /**
   * 检查服务是否已注册
   * @param key 服务键名
   * @returns 是否已注册
   */
  public has(key: string): boolean {
    return this.services.has(key) || this.factories.has(key);
  }

  /**
   * 移除服务
   * @param key 服务键名
   */
  public remove(key: string): void {
    this.services.delete(key);
    this.factories.delete(key);
  }

  /**
   * 清除所有服务
   */
  public clear(): void {
    this.services.clear();
    this.factories.clear();
  }
}

export default ServiceRegistry;
