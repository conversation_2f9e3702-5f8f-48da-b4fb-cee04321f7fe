import { useCallback } from 'react';
import { useFieldValidation } from './useFieldValidation';

/**
 * 通用验证字段Hook
 * 简化ValidatedInput的使用模式
 */
export function useValidatedField(
  fieldName: string,
  value: string,
  setValue: (value: string) => void,
  validationConfig: Record<string, string>
) {
  const { fieldErrors, validateAndSetError, clearErrors, clearFieldError } = useFieldValidation(validationConfig);

  // 统一的onChange处理器
  const handleChange = useCallback((newValue: string) => {
    setValue(newValue);
    validateAndSetError(fieldName, newValue);
  }, [fieldName, setValue, validateAndSetError]);

  // 创建稳定的clearFieldError函数引用
  const clearCurrentFieldError = useCallback(() => {
    clearFieldError(fieldName);
  }, [clearFieldError, fieldName]);

  return {
    value,
    onChange: handleChange,
    error: fieldErrors[fieldName],
    clearErrors,
    clearFieldError: clearCurrentFieldError
  };
}

/**
 * 多字段验证Hook
 * 用于处理多个字段的验证
 */
export function useValidatedFields<T extends Record<string, string>>(
  values: T,
  setters: Record<keyof T, (value: string) => void>,
  validationConfig: Record<string, string>
) {
  const { fieldErrors, validateAndSetError, clearErrors, clearFieldError } = useFieldValidation(validationConfig);

  // 创建字段处理器
  const createFieldHandler = useCallback((fieldName: keyof T) => ({
    value: values[fieldName],
    onChange: (newValue: string) => {
      setters[fieldName](newValue);
      validateAndSetError(fieldName as string, newValue);
    },
    error: fieldErrors[fieldName as string]
  }), [values, setters, fieldErrors, validateAndSetError]);

  // 获取所有字段的处理器
  const getFieldProps = useCallback((fieldName: keyof T) =>
    createFieldHandler(fieldName),
    [createFieldHandler]
  );

  return {
    getFieldProps,
    fieldErrors,
    hasErrors: Object.values(fieldErrors).some(error => !!error),
    clearErrors,
    clearFieldError
  };
}
