import React, { useState, useEffect, useCallback } from 'react';
import { X, Calendar, User, MapPin, FileText, Clock, AlertCircle, ClipboardList } from 'lucide-react';
import InspectorSelect from '../../components/Inspection/InspectorSelect';
import DepartmentSelectorDialog from '../../components/Inspection/DepartmentSelectorDialog';
import DatePicker from '../../components/Common/DatePicker';
import { useDepartment } from '../../hooks/Inventory/useDepartment';
import DepartmentService from '../../services/Inventory/departmentService';
import { DictionaryItem } from '../../types/inventory';
import { useDialogEscapeKey } from '../../hooks/base/useEscapeKey';

/**
 * 创建任务表单数据接口
 */
interface CreateTaskFormData {
  taskName: string;
  taskDescription: string;
  personName: string;
  personAlias: string;
  startDate: string;
  endDate: string;
  departmentPaths: string[];
}

/**
 * 创建任务对话框属性接口
 */
interface CreateTaskDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (taskData: CreateTaskFormData) => Promise<void>;
  isLoading?: boolean;
}

/**
 * 创建巡检任务对话框组件
 */
const CreateTaskDialog: React.FC<CreateTaskDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false
}) => {
  // 表单数据状态
  const [formData, setFormData] = useState<CreateTaskFormData>({
    taskName: '',
    taskDescription: '',
    personName: '',
    personAlias: '',
    startDate: '',
    endDate: '',
    departmentPaths: []
  });

  // 表单验证错误状态
  const [errors, setErrors] = useState<Partial<Record<keyof CreateTaskFormData, string>>>({});

  // 使用稳定的空数组引用避免循环依赖
  const emptyInventoryList = React.useMemo(() => [], []);

  // 使用部门Hook获取部门数据
  const { departmentCategories } = useDepartment(emptyInventoryList);

  // 获取部门服务实例
  const departmentService = DepartmentService.getInstance();







  // 选中的部门状态
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);

  // 部门选择弹窗状态
  const [showDepartmentDialog, setShowDepartmentDialog] = useState(false);





  // 获取所有一级部门名称
  const getTopLevelDepartments = (): string[] => {
    const rootNode = departmentCategories.find(item => item.id === 'all-dept');
    return rootNode?.children
      ?.filter(child => child.id.startsWith('dept-') && child.name)
      ?.map(child => child.name) || [];
  };

  // 递归查找部门节点
  const findDepartmentByName = (categories: any[], name: string): any => {
    for (const category of categories) {
      if (category.name === name) return category;
      if (category.children) {
        const found = findDepartmentByName(category.children, name);
        if (found) return found;
      }
    }
    return null;
  };

  // 将部门名称转换为API路径
  const convertDepartmentsToApiPaths = (departmentNames: string[]): string[] => {
    const topLevelDepts = getTopLevelDepartments();

    // 检查是否选择了所有一级部门（表示选择了根节点）
    const isAllTopLevel = topLevelDepts.length > 0 &&
      topLevelDepts.every(dept => departmentNames.includes(dept)) &&
      departmentNames.length === topLevelDepts.length;

    if (isAllTopLevel) {
      console.log('检测到选择了公司名称，API传递空数组');
      return [''];
    }

    // 转换部门名称为API路径
    return departmentNames.map(deptName => {
      const deptNode = findDepartmentByName(departmentCategories, deptName);
      return deptNode
        ? departmentService.createPathResolver().getApiPath(deptNode)
        : deptName;
    });
  };

  // 处理部门选择确认
  const handleDepartmentConfirm = (departments: string[]) => {
    setSelectedDepartments(departments);
    if (errors.departmentPaths) {
      setErrors(prev => ({ ...prev, departmentPaths: undefined }));
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      taskName: '',
      taskDescription: '',
      personName: '',
      personAlias: '',
      startDate: '',
      endDate: '',
      departmentPaths: []
    });
    setErrors({});
    setSelectedDepartments([]);
  };

  // 关闭对话框
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 使用统一的ESC键处理
  useDialogEscapeKey(isOpen, handleClose, {
    debugId: 'CreateTaskDialog',
    priority: 100
  });

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof CreateTaskFormData, string>> = {};

    if (!formData.taskName.trim()) {
      newErrors.taskName = '任务名称不能为空';
    }

    if (!formData.personName.trim()) {
      newErrors.personName = '责任人不能为空';
    }

    if (!formData.startDate) {
      newErrors.startDate = '开始日期不能为空';
    }

    if (!formData.endDate) {
      newErrors.endDate = '结束日期不能为空';
    }

    if (formData.startDate && formData.endDate) {
      const startTime = new Date(formData.startDate).getTime();
      const endTime = new Date(formData.endDate).getTime();
      if (startTime >= endTime) {
        newErrors.endDate = '结束日期必须大于开始日期';
      }
    }

    if (selectedDepartments.length === 0) {
      newErrors.departmentPaths = '至少选择一个部门';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提取人员姓名和备注
  const extractPersonNameAndAlias = (fullName: string): { personName: string; personAlias?: string } => {
    let personName = fullName;
    let personAlias: string | undefined;

    // 如果名称中包含备注（格式为"姓名 (备注)"或"姓名 （备注）"）
    const nameMatch = fullName.match(/^([^(（]+)/);
    if (nameMatch) {
      personName = nameMatch[1].trim();
    }

    // 分别匹配英文括号和中文括号中的备注
    const englishAliasMatch = fullName.match(/\(([^)]+)\)/);
    const chineseAliasMatch = fullName.match(/（([^）]+)）/);

    if (englishAliasMatch) {
      personAlias = englishAliasMatch[1];
    } else if (chineseAliasMatch) {
      personAlias = chineseAliasMatch[1];
    }

    return { personName, personAlias };
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // 提取人员姓名和备注
      const { personName, personAlias } = extractPersonNameAndAlias(formData.personName);

      // 将显示用的部门名称转换为API路径
      const apiDepartmentPaths = convertDepartmentsToApiPaths(selectedDepartments);

      // 构建提交数据
      const submitData: CreateTaskFormData = {
        ...formData,
        personName,
        personAlias: personAlias || '', // 如果没有备注，传递空字符串
        departmentPaths: apiDepartmentPaths
      };



      await onSubmit(submitData);
      handleClose();
    } catch (error) {
      console.error('提交创建任务失败:', error);
    }
  };

  // 处理人员选择变化
  const handlePersonChange = (value: string) => {
    setFormData(prev => ({ ...prev, personName: value }));
    if (errors.personName) {
      setErrors(prev => ({ ...prev, personName: undefined }));
    }
  };

  // 如果对话框未打开，不渲染
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[95vh] overflow-y-auto">
        {/* 对话框标题 - 紧凑设计 */}
        <div className="flex justify-between items-center px-5 py-3 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <h2 className="text-lg font-semibold text-gray-800 flex items-center">
            <ClipboardList className="h-5 w-5 mr-2 text-blue-600" />
            创建巡检任务
          </h2>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="text-gray-500 hover:text-gray-700 hover:bg-white rounded-full p-1 transition-all duration-200 focus:outline-none"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 表单内容 - 紧凑布局 */}
        <div className="p-5 space-y-4">
          {/* 基本信息区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 任务名称 */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                <span className="flex items-center">
                  <FileText className="h-4 w-4 mr-1.5 text-blue-600" />
                  任务名称 <span className="text-red-500 ml-1">*</span>
                </span>
              </label>
              <input
                type="text"
                value={formData.taskName}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, taskName: e.target.value }));
                  if (errors.taskName) {
                    setErrors(prev => ({ ...prev, taskName: undefined }));
                  }
                }}
                placeholder="请输入任务名称"
                className={`w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                  errors.taskName ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                disabled={isLoading}
              />
              {errors.taskName && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.taskName}
                </p>
              )}
            </div>

            {/* 任务描述 */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                <span className="flex items-center">
                  <FileText className="h-4 w-4 mr-1.5 text-blue-600" />
                  任务描述
                </span>
              </label>
              <textarea
                value={formData.taskDescription}
                onChange={(e) => setFormData(prev => ({ ...prev, taskDescription: e.target.value }))}
                placeholder="请输入任务描述（可选）"
                rows={2}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 hover:border-gray-400 transition-all duration-200 resize-none"
                disabled={isLoading}
              />
            </div>
          </div>

          {/* 执行信息区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* 责任人选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                <span className="flex items-center">
                  <User className="h-4 w-4 mr-1.5 text-blue-600" />
                  责任人 <span className="text-red-500 ml-1">*</span>
                </span>
              </label>
              <InspectorSelect
                value={formData.personName}
                onChange={handlePersonChange}
                placeholder="请选择责任人"
                disabled={isLoading}
                required={true}
                error={!!errors.personName}
                className="text-sm"
              />
              {errors.personName && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.personName}
                </p>
              )}
            </div>

            {/* 开始日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                <span className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1.5 text-blue-600" />
                  开始日期 <span className="text-red-500 ml-1">*</span>
                </span>
              </label>
              <DatePicker
                value={formData.startDate}
                onChange={(date) => {
                  setFormData(prev => ({ ...prev, startDate: date }));
                  if (errors.startDate) {
                    setErrors(prev => ({ ...prev, startDate: undefined }));
                  }
                }}
                placeholder="请选择开始日期和时间"
                disabled={isLoading}
                error={!!errors.startDate}
                showTime={true}
                showSeconds={true}
                className="text-sm"
              />
              {errors.startDate && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.startDate}
                </p>
              )}
            </div>

            {/* 结束日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                <span className="flex items-center">
                  <Clock className="h-4 w-4 mr-1.5 text-blue-600" />
                  结束日期 <span className="text-red-500 ml-1">*</span>
                </span>
              </label>
              <DatePicker
                value={formData.endDate}
                onChange={(date) => {
                  setFormData(prev => ({ ...prev, endDate: date }));
                  if (errors.endDate) {
                    setErrors(prev => ({ ...prev, endDate: undefined }));
                  }
                }}
                placeholder="请选择结束日期和时间"
                disabled={isLoading}
                error={!!errors.endDate}
                showTime={true}
                showSeconds={true}
                className="text-sm"
              />
              {errors.endDate && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.endDate}
                </p>
              )}
            </div>
          </div>

          {/* 巡检范围区域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1.5">
              <span className="flex items-center">
                <MapPin className="h-4 w-4 mr-1.5 text-blue-600" />
                巡检部门 <span className="text-red-500 ml-1">*</span>
              </span>
            </label>
            <div className="space-y-3">
              {/* 部门选择按钮 */}
              <button
                type="button"
                onClick={() => setShowDepartmentDialog(true)}
                disabled={isLoading}
                className={`w-full px-3 py-2 text-sm border rounded-md text-left focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 ${
                  errors.departmentPaths
                    ? 'border-red-300 bg-red-50 text-red-700'
                    : 'border-gray-300 hover:border-gray-400 text-gray-700'
                } ${isLoading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-50'}`}
              >
                <div className="flex items-center justify-between">
                  <span className={selectedDepartments.length > 0 ? 'text-gray-900' : 'text-gray-500'}>
                    {selectedDepartments.length > 0
                      ? `已选择 ${selectedDepartments.length} 个部门`
                      : '请选择要巡检的部门'
                    }
                  </span>
                  <MapPin className="h-4 w-4 text-gray-400" />
                </div>
              </button>

              {/* 已选择的部门列表 */}
              {selectedDepartments.length > 0 && (
                <div className="bg-gray-50 rounded-md p-3">
                  <div className="flex flex-wrap gap-1.5">
                    {selectedDepartments.map((dept, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-1 rounded-full text-xs bg-blue-100 text-blue-800 border border-blue-200"
                      >
                        <MapPin className="h-3 w-3 mr-1" />
                        {dept}
                        <button
                          type="button"
                          onClick={() => {
                            const newDepartments = selectedDepartments.filter((_, i) => i !== index);
                            setSelectedDepartments(newDepartments);
                          }}
                          disabled={isLoading}
                          className="ml-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                        >
                          <X className="h-2.5 w-2.5" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
            {errors.departmentPaths && (
              <p className="mt-1 text-xs text-red-600 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                {errors.departmentPaths}
              </p>
            )}
          </div>
        </div>

        {/* 对话框底部按钮 - 紧凑设计 */}
        <div className="flex justify-between items-center px-5 py-3 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-500">
            {selectedDepartments.length > 0 && (
              <span className="inline-flex items-center">
                <MapPin className="h-3 w-3 mr-1" />
                将在 {selectedDepartments.length} 个部门执行巡检
              </span>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isLoading}
              className="px-5 py-2 text-sm bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-md hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
            >
              {isLoading ? (
                <span className="flex items-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  创建中...
                </span>
              ) : (
                <span className="flex items-center">
                  <ClipboardList className="h-4 w-4 mr-1.5" />
                  创建任务
                </span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 部门选择弹窗 */}
      <DepartmentSelectorDialog
        isOpen={showDepartmentDialog}
        onClose={() => setShowDepartmentDialog(false)}
        onConfirm={handleDepartmentConfirm}
        departmentCategories={departmentCategories}
        selectedDepartments={selectedDepartments}
        title="选择巡检部门"
      />
    </div>
  );
};

export default CreateTaskDialog;
