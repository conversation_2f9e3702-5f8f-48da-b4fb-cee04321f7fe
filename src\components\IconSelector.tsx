import React, { useState, useMemo } from 'react';
import { Search } from 'lucide-react'; // 移除了 X, ChevronDown，因为不再需要
import {
  IconType,
  ICON_CATEGORIES,
  ICON_DESCRIPTIONS,
  IconCategoryConfig
} from '../hooks/Inventory/useCategoryIcons';
import { getIconComponent } from '../utils/iconMapping';

// 图标选择器属性
interface IconSelectorProps {
  selectedIcon?: IconType;
  onSelectIcon: (icon: IconType) => void;
  onClose: () => void;
  className?: string;
}

// 图标项组件
interface IconItemProps {
  iconType: IconType;
  isSelected: boolean;
  onClick: (icon: IconType) => void;
}

const IconItem: React.FC<IconItemProps> = ({ iconType, isSelected, onClick }) => {
  const description = ICON_DESCRIPTIONS[iconType] || iconType;

  return (
    <div
      onClick={() => onClick(iconType)}
      className={`
        flex flex-col items-center justify-center p-2 rounded-md cursor-pointer transition-all
        ${isSelected
          ? 'bg-blue-100 ring-2 ring-blue-500 ring-inset'
          : 'bg-white hover:bg-gray-100 border border-gray-200'
        }
      `}
      title={description}
      style={isSelected ? { margin: '1px' } : undefined}
    >
      {getIconComponent(iconType, `w-6 h-6 ${isSelected ? 'text-blue-600' : 'text-gray-700'}`)}
    </div>
  );
};

/**
 * 图标选择器组件
 * 用于在添加分类时选择自定义图标
 */
const IconSelector: React.FC<IconSelectorProps> = ({
  selectedIcon,
  onSelectIcon,
  onClose,
  className = ''
}) => {
  // 暂时注释掉不再使用的状态变量
  // const [searchQuery, setSearchQuery] = useState('');
  // const [selectedCategory, setSelectedCategory] = useState<string>('all');
  // const [recentIcons] = useState<IconType[]>(['laptop', 'server', 'database', 'usb', 'shield']);

  // 过滤图标 - 简化版本，直接显示所有图标
  const filteredIcons = useMemo(() => {
    // 直接显示所有图标，不需要分类和搜索过滤
    return Object.keys(ICON_DESCRIPTIONS) as IconType[];

    /* 原来的过滤逻辑 - 暂时注释掉
    let icons: IconType[] = [];

    if (selectedCategory === 'all') {
      // 显示所有图标
      icons = Object.keys(ICON_DESCRIPTIONS) as IconType[];
    } else if (selectedCategory === 'recent') {
      // 显示最近使用的图标
      icons = recentIcons;
    } else {
      // 显示特定分类的图标
      const category = ICON_CATEGORIES.find(cat => cat.name === selectedCategory);
      icons = category?.icons || [];
    }

    // 根据搜索查询过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      icons = icons.filter(icon => {
        const description = ICON_DESCRIPTIONS[icon] || '';
        return description.toLowerCase().includes(query) ||
               icon.toLowerCase().includes(query);
      });
    }

    return icons;
    */
  }, []); // 移除依赖项，因为现在不需要动态过滤

  return (
    <div className={`w-full max-w-2xl ${className}`}>
      {/* 分类选择和搜索 - 暂时注释掉，图标不多不需要分类和搜索 */}
      {/* <div className="flex items-center mb-4 gap-2">
        <div className="relative">
          <select
            className="
              border border-gray-300 rounded-lg pl-3 pr-10 py-2.5
              bg-white text-gray-700 text-sm font-medium
              hover:border-gray-400 hover:bg-gray-50
              focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white
              transition-all duration-200 ease-in-out
              cursor-pointer appearance-none
              shadow-sm hover:shadow-md
              min-w-[120px]
            "
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="all" className="py-2 text-gray-700">全部图标</option>
            <option value="recent" className="py-2 text-gray-700">最近使用</option>
            {ICON_CATEGORIES.map(category => (
              <option key={category.name} value={category.name} className="py-2 text-gray-700">
                {category.description}
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none" />
        </div>
        <div className="relative flex-1">
          <input
            type="text"
            placeholder="搜索图标..."
            className="w-full pl-9 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
        </div>
      </div> */}

      {/* 图标网格 - 移除了控制区域后调整上边距 */}
      <div className="border rounded-lg p-4 bg-gray-50">
        {filteredIcons.length > 0 ? (
          <div className="grid grid-cols-8 gap-3 max-h-64 overflow-y-auto pr-2 pl-1">
            {filteredIcons.map((icon) => (
              <IconItem
                key={icon}
                iconType={icon}
                isSelected={selectedIcon === icon}
                onClick={onSelectIcon}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Search className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>暂无可用图标</p>
            <p className="text-sm">请稍后再试</p>
          </div>
        )}
      </div>

      {/* 已选图标显示 */}
      {selectedIcon && (
        <div className="mt-4 flex items-center">
          <div className="mr-2 text-sm font-medium">已选择:</div>
          <div className="flex items-center bg-white border rounded-md px-3 py-1.5">
            {getIconComponent(selectedIcon, "w-5 h-5 mr-2 text-gray-700")}
            <span className="text-sm">{ICON_DESCRIPTIONS[selectedIcon]}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default IconSelector;
