﻿import { InventoryItem, PersonDetailInfo } from '../../types/inventory';
import { BaseService, AppError } from '../base/baseService';
import { logError } from '../../utils/errorHandler';

// 导入拆分后的模块
import {
  DepartmentCategory,
  DepartmentServiceState,
  DepartmentServiceEvents,
  DepartmentServiceInterface,
  DepartmentTreeLoader,
  DepartmentCountUpdater,
  DepartmentFilter,
  DepartmentCRUD,
  DepartmentUtils,
  DepartmentPathResolver
} from './department';
import { DataFetchService } from './department/dataFetchService';
import { CacheManager } from './department/cacheManager';
import { IncrementalCacheUpdater } from './department/incrementalCacheUpdater';
import { TreeUtils } from './department/treeUtils';
import { DEFAULT_CACHE_DURATION, LOG_PREFIXES, formatLogMessage } from './department/constants';
import ServiceRegistry from '../serviceRegistry';

/**
 * 部门服务
 * 管理部门树的加载、更新和操作
 */
class DepartmentService extends BaseService<DepartmentServiceState, DepartmentServiceEvents> implements DepartmentServiceInterface {
  private static instance: DepartmentService;
  private treeLoader: DepartmentTreeLoader;
  private crud: DepartmentCRUD;
  private dataFetchService: DataFetchService;
  private cacheManager: CacheManager;

  // 删除操作状态管理
  private deletingNodes: Set<string> = new Set();
  private deleteOperationLock: boolean = false;

  // 保留原有的人员缓存接口以兼容现有代码（使用统一常量）
  private readonly CACHE_DURATION = DEFAULT_CACHE_DURATION;

  private constructor() {
    super('AccountTableDll', {
      departmentCategories: [],
      currentCategory: 'all-dept',
      isLoading: false
    });

    // 初始化拆分后的模块
    this.treeLoader = new DepartmentTreeLoader(this);
    this.crud = new DepartmentCRUD(this);
    this.dataFetchService = DataFetchService.getInstance(this);
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 获取部门服务实例
   * @returns 部门服务实例
   */
  public static getInstance(): DepartmentService {
    if (!DepartmentService.instance) {
      DepartmentService.instance = new DepartmentService();
    }
    return DepartmentService.instance;
  }

  /**
   * 获取部门的完整路径（包括所有父部门）
   * @param category 部门节点
   * @returns 部门的完整路径，例如 "总部/技术部/开发组"
   */
  public getDepartmentPath(category: DepartmentCategory): string {
    return DepartmentUtils.getDepartmentPath(category, this.state.departmentCategories);
  }

  /**
   * 创建部门路径解析器实例
   * @returns 部门路径解析器实例
   */
  public createPathResolver(): DepartmentPathResolver {
    return new DepartmentPathResolver(this.state.departmentCategories);
  }

  /**
   * 从人员节点中提取人员信息
   * @param categories 部门分类树
   * @param personId 人员节点ID
   * @returns 人员信息或null
   */
  public extractPersonInfo(categories: DepartmentCategory[], personId: string): { name: string, alias?: string } | null {
    return DepartmentUtils.extractPersonInfo(categories, personId);
  }

  /**
   * 获取人员详细信息（使用统一缓存）
   * @param personId 人员ID（数字）
   * @returns 人员详细信息或null
   */
  public async getPersonInfo(personId: number): Promise<PersonDetailInfo | null> {
    // 检查统一缓存是否有效
    const isCacheValid = this.cacheManager.isCacheValid('personCache', this.CACHE_DURATION);

    if (isCacheValid && this.cacheManager.hasPersonCache(personId)) {
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `从统一缓存获取人员信息: ${personId}`));
      return this.cacheManager.getPersonCache(personId) || null;
    }

    // 如果缓存无效或没有该人员信息，重新加载所有人员信息
    if (!isCacheValid || this.cacheManager.getAllPersonCache().size === 0) {
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '人员缓存无效或为空，重新加载所有人员信息'));
      await this.refreshPersonCache();
    }

    // 从统一缓存中获取
    return this.cacheManager.getPersonCache(personId) || null;
  }

  /**
   * 刷新人员缓存（使用统一数据获取服务）
   * @returns 是否成功刷新
   */
  public async refreshPersonCache(): Promise<boolean> {
    try {
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '开始刷新人员缓存（使用统一数据获取服务）...'));

      // 使用统一的数据获取服务
      const result = await this.dataFetchService.getPersons(true); // 强制刷新

      if (result && result.success && Array.isArray(result.data)) {
        // 将人员信息存入统一缓存
        this.cacheManager.setPersonCacheBatch(result.data);

        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `人员缓存刷新成功，缓存了 ${result.data.length} 个人员信息`));
        return true;
      } else {
        console.warn('获取人员信息失败或数据为空');
        return false;
      }
    } catch (error) {
      console.error('刷新人员缓存失败:', error);
      return false;
    }
  }

  /**
   * 获取人员缓存（兼容接口）
   * @returns 人员缓存Map
   */
  public getPersonCache(): Map<number, PersonDetailInfo> {
    return this.cacheManager.getAllPersonCache();
  }

  /**
   * 清空人员缓存（使用统一缓存管理器）
   */
  public clearPersonCache(): void {
    this.cacheManager.clearCache('personCache');
    console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '人员缓存已清空（通过统一缓存管理器）'));
  }

  /**
   * 更新人员缓存中的特定人员信息（使用统一缓存管理器）
   * @param personId 人员ID
   * @param updatedPersonInfo 更新后的人员信息
   */
  public updatePersonInCache(personId: number, updatedPersonInfo: PersonDetailInfo): void {
    this.cacheManager.setPersonCache(personId, updatedPersonInfo);
    console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `人员缓存已更新: ID=${personId}, 姓名=${updatedPersonInfo.user_name}`));
  }

  /**
   * 更新临时部门下的人员缓存
   * @param tempDepartmentId 临时部门ID
   * @param realDepartmentId 真实部门ID
   */
  public async updatePersonCacheForDepartment(tempDepartmentId: string, realDepartmentId: number): Promise<void> {
    try {
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `更新临时部门下的人员缓存: ${tempDepartmentId} -> dept-${realDepartmentId}`));

      // 查找该部门下的所有人员节点
      const departmentNode = TreeUtils.findNodeById(this.state.departmentCategories, `dept-${realDepartmentId}`);
      if (!departmentNode || !departmentNode.children) {
        return;
      }

      // 获取部门路径
      const pathResolver = this.createPathResolver();
      const departmentPath = pathResolver.getFullPath(departmentNode);

      // 为该部门下的每个人员更新缓存
      for (const child of departmentNode.children) {
        if (child.id.startsWith('person-') && child.originalPersonId) {
          const personId = child.originalPersonId;

          // 检查缓存中是否已有该人员信息
          const existingPersonInfo = this.cacheManager.getPersonCache(personId);

          if (existingPersonInfo) {
            // 更新部门信息
            const updatedDepartments = existingPersonInfo.departments?.map((dept: any) => {
              // 如果部门名称匹配，更新ID
              if (dept.name === departmentNode.name) {
                return {
                  ...dept,
                  id: realDepartmentId,
                  path_name: departmentPath
                };
              }
              return dept;
            }) || [];

            // 如果没有找到匹配的部门，添加新的部门信息
            if (!updatedDepartments.some((dept: any) => dept.id === realDepartmentId)) {
              updatedDepartments.push({
                id: realDepartmentId,
                name: departmentNode.name,
                path_name: departmentPath,
                is_primary: 1
              });
            }

            const updatedPersonInfo = {
              ...existingPersonInfo,
              departments: updatedDepartments
            };

            this.cacheManager.setPersonCache(personId, updatedPersonInfo);
            console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `已更新人员缓存: ID=${personId}, 部门=${departmentNode.name}`));
          }
        }
      }
    } catch (error) {
      console.error(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '更新人员缓存失败'), error);
    }
  }

  /**
   * 更新部门树中的人员节点信息（增量更新）
   * @param personId 人员ID
   * @param updatedPersonInfo 更新后的人员信息
   */
  public updatePersonNodeInTree(personId: number, updatedPersonInfo: any): void {
    try {
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `增量更新部门树中的人员节点: ID=${personId}`));

      // 使用增量更新器更新人员节点
      const updatedCategories = IncrementalCacheUpdater.updatePersonNode(
        this.state.departmentCategories,
        personId,
        updatedPersonInfo
      );

      // 更新状态（不触发计数更新）
      this.updateState({ departmentCategories: updatedCategories });

      // 同时更新人员缓存
      this.updatePersonInCache(personId, updatedPersonInfo);

      // 触发结构变化事件，通知其他组件这是结构变化而非数据变化
      this.emitter.emit('tree-structure-changed', {
        operation: 'update-person',
        nodeId: `person-${personId}`,
        skipCountUpdate: true
      });

      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '人员节点更新完成（增量更新）'));
    } catch (error) {
      console.error('更新部门树中的人员节点失败:', error);
      // 如果增量更新失败，至少更新缓存
      this.updatePersonInCache(personId, updatedPersonInfo);
    }
  }

  /**
   * 从数据库加载部门树
   * 使用get_department和get_person API获取部门和人员数据
   * @param forceRefresh 是否强制刷新，即使已有数据也重新加载
   */
  public async loadDepartmentTree(forceRefresh: boolean = false): Promise<DepartmentCategory[]> {
    const result = await this.treeLoader.loadDepartmentTree(forceRefresh, this.state.departmentCategories);

    // 更新状态
    this.updateState({
      departmentCategories: result,
      currentCategory: 'all-dept',
      isLoading: false
    });

    // 触发树加载完成事件
    this.emitter.emit('tree-loaded', result);

    return result;
  }

  /**
   * 更新部门树的计数
   * @param inventoryList 设备列表
   * @param skipStructuralChanges 是否跳过结构变化时的计数更新（默认false）
   * @returns 是否成功更新
   */
  public updateDepartmentTreeCounts(inventoryList: InventoryItem[], skipStructuralChanges: boolean = false): boolean {
    // 如果跳过结构变化时的计数更新，直接返回false
    if (skipStructuralChanges) {
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '跳过结构变化时的计数更新'));
      return false;
    }

    const hasChanges = DepartmentCountUpdater.updateDepartmentTreeCounts(inventoryList, this.state.departmentCategories);

    if (hasChanges) {
      console.log('部门树计数有变化，触发状态更新事件');

      // 重要：调用updateState触发state-change事件，确保UI更新
      this.updateState({ departmentCategories: this.state.departmentCategories });

      // 触发树更新事件
      this.emitter.emit('tree-updated', this.state.departmentCategories);
    } else {
      console.log('部门树计数无变化，不触发状态更新');
    }

    return hasChanges;
  }

  /**
   * 根据部门ID筛选设备列表
   * @param inventoryList 完整的设备列表
   * @param departmentCategories 部门分类树
   * @param categoryId 选中的部门ID
   * @returns 筛选后的设备列表
   */
  public filterByDepartment(
    inventoryList: InventoryItem[],
    departmentCategories: DepartmentCategory[],
    categoryId: string
  ): InventoryItem[] {
    // 更新当前选中的分类
    this.updateState({ currentCategory: categoryId });

    return DepartmentFilter.filterByDepartment(inventoryList, departmentCategories, categoryId);
  }

  /**
   * 添加部门
   * @param parentId 父级部门ID
   * @param departmentName 部门名称
   */
  public async addDepartment(parentId: string, departmentName: string): Promise<DepartmentCategory[]> {
    try {
      // 执行API调用并获取返回的部门ID
      const apiResult = await this.crud.addDepartment(parentId, departmentName, this.state.departmentCategories);

      // 使用增量更新而不是全量刷新
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '使用增量更新添加部门节点'));

      // 创建新部门数据（模拟API返回的数据结构）
      const newDepartmentData = {
        name: departmentName,
        department_path: this.buildDepartmentPath(parentId, departmentName)
      };

      // 先使用临时ID增量添加部门节点到树中
      const { updatedCategories, newNodeId } = IncrementalCacheUpdater.addDepartmentNode(
        this.state.departmentCategories,
        parentId,
        newDepartmentData
      );

      // 更新状态（不触发计数更新）
      this.updateState({ departmentCategories: updatedCategories });

      // 直接使用API返回的部门ID
      if (apiResult && apiResult.data && apiResult.data.id) {
        const realDepartmentId = apiResult.data.id;
        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `使用API返回的部门ID: ${realDepartmentId}`));

        // 更新临时ID为真实ID
        const finalCategories = IncrementalCacheUpdater.updateTemporaryDepartmentId(
          this.state.departmentCategories,
          newNodeId,
          realDepartmentId
        );

        // 更新状态
        this.updateState({ departmentCategories: finalCategories });

        // 更新该部门下人员的缓存信息
        await this.updatePersonCacheForDepartment(newNodeId, realDepartmentId);

        // 触发部门添加事件 - 使用真实ID
        this.emitter.emit('department-added', { parentId, name: departmentName, realId: realDepartmentId });

        // 触发结构变化事件，通知其他组件这是结构变化而非数据变化
        this.emitter.emit('tree-structure-changed', {
          operation: 'add-department',
          nodeId: `dept-${realDepartmentId}`,
          skipCountUpdate: true
        });
      } else {
        console.warn(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, 'API未返回有效的部门ID，使用临时ID'));

        // 触发部门添加事件 - 使用临时ID
        this.emitter.emit('department-added', { parentId, name: departmentName });

        // 触发结构变化事件
        this.emitter.emit('tree-structure-changed', {
          operation: 'add-department',
          nodeId: newNodeId,
          skipCountUpdate: true
        });
      }

      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '部门添加完成（增量更新）'));
      return this.state.departmentCategories;
    } catch (error) {
      logError(error, 'DepartmentService.addDepartment');
      throw this.handleError(error);
    }
  }

  /**
   * 添加人员
   * @param departmentId 所属部门ID
   * @param personName 人员名称
   * @param alias 备注（可选）
   * @param mobileNumber 联系方式（可选）
   * @param positionSecurityLevel 岗位密级（可选）
   */
  public async addPerson(departmentId: string, personName: string, alias?: string, mobileNumber?: string, positionSecurityLevel?: number): Promise<DepartmentCategory[]> {
    try {
      // 执行API调用并获取响应
      const apiResult = await this.crud.addPerson(departmentId, personName, alias, this.state.departmentCategories, mobileNumber, positionSecurityLevel);

      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '使用增量更新添加人员节点'));

      // 检查API响应中是否包含新人员的ID和操作类型
      let realPersonId: number | null = null;
      let isExistingPersonAssociation = false;

      if (apiResult && apiResult.data && apiResult.data.id) {
        realPersonId = apiResult.data.id;
        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `获取到新人员的真实ID: ${realPersonId}`));

        // 检查是否是现有人员的部门关联
        if (apiResult.data.operation === 'department_association_added') {
          isExistingPersonAssociation = true;
          console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '检测到现有人员部门关联操作'));
        }
      } else {
        console.warn(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, 'API响应中未包含新人员ID，将刷新人员缓存获取'));
      }

      // 如果没有获取到真实ID，先刷新人员缓存
      if (!realPersonId) {
        await this.refreshPersonCache();

        // 尝试从缓存中找到新添加的人员
        const allPersons = this.cacheManager.getAllPersonCache();
        for (const [personId, personInfo] of allPersons) {
          if (personInfo.user_name === personName &&
              (personInfo.alias || '') === (alias || '')) {
            realPersonId = personId;
            console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `从缓存中找到新人员ID: ${realPersonId}`));
            break;
          }
        }
      }

      // 创建新人员数据
      const newPersonData = {
        id: realPersonId || Date.now(), // 使用真实ID或临时ID
        user_name: personName,
        alias: alias || ''
      };

      // 增量添加人员节点到树中
      const updatedCategories = IncrementalCacheUpdater.addPersonNode(
        this.state.departmentCategories,
        departmentId,
        newPersonData
      );

      // 更新状态（不触发计数更新）
      this.updateState({ departmentCategories: updatedCategories });

      // 如果获取到了真实的人员ID，将人员信息加入缓存
      if (realPersonId) {
        // 获取部门信息
        const departmentNode = TreeUtils.findNodeById(this.state.departmentCategories, departmentId);

        // 使用路径解析器获取正确的部门路径
        const pathResolver = this.createPathResolver();
        const departmentPath = departmentNode ? pathResolver.getFullPath(departmentNode) : '';

        // 处理部门ID：如果是临时部门，不添加到缓存中，等待真实ID
        let shouldAddToCache = true;
        let departmentInfo: any = null;

        if (departmentId.startsWith('dept-temp-')) {
          // 临时部门，暂不添加到缓存
          console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `部门为临时ID，暂不添加人员到缓存: ${departmentId}`));
          shouldAddToCache = false;
        } else {
          // 真实部门ID
          const deptIdStr = departmentId.replace('dept-', '');
          const deptIdNum = parseInt(deptIdStr);

          if (!isNaN(deptIdNum)) {
            departmentInfo = {
              id: deptIdNum,
              name: departmentNode ? departmentNode.name : '',
              path_name: departmentPath, // 使用完整的部门路径
              is_primary: 1 // 新添加的人员，这是主要部门
            };
          } else {
            console.warn(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `无效的部门ID格式: ${departmentId}`));
            shouldAddToCache = false;
          }
        }

        if (shouldAddToCache && departmentInfo) {
          const personInfo = {
            id: realPersonId,
            user_name: personName,
            alias: alias || '',
            mobile_number: mobileNumber || '',
            position_security_level: typeof positionSecurityLevel === 'number' ? positionSecurityLevel : 0,
            departments: [departmentInfo] // 完整的部门信息
          };
          this.cacheManager.setPersonCache(realPersonId, personInfo);
          console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `新人员信息已加入缓存: ID=${realPersonId}, 部门=${departmentInfo.name}, 路径=${departmentPath}`));
        } else {
          console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `人员信息暂不加入缓存，等待部门ID确定: 人员ID=${realPersonId}`));
        }
      } else {
        // 如果没有真实ID，清空人员缓存以便重新加载
        this.clearPersonCache();
      }

      // 触发人员添加事件 - 只刷新树，不刷新总表
      this.emitter.emit('person-added', {
        departmentId,
        name: personName,
        alias,
        personId: realPersonId, // 添加真实的人员ID
        isExistingPersonAssociation // 传递是否为现有人员关联的信息
      });

      // 触发结构变化事件，通知其他组件这是结构变化而非数据变化
      this.emitter.emit('tree-structure-changed', {
        operation: 'add-person',
        nodeId: `person-${newPersonData.id}`,
        skipCountUpdate: true
      });

      // 如果是现有人员关联，触发特殊提示事件
      if (isExistingPersonAssociation) {
        this.emitter.emit('existing-person-associated', {
          personName,
          alias,
          departmentId,
          message: apiResult.data.message
        });
      }

      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '人员添加完成（增量更新）'));
      return this.state.departmentCategories;
    } catch (error) {
      logError(error, 'DepartmentService.addPerson');
      throw this.handleError(error);
    }
  }

  /**
   * 重命名部门或人员
   * @param nodeId 节点ID
   * @param newName 新名称
   * @returns 更新后的部门树
   */
  public async renameDepartmentOrPerson(nodeId: string, newName: string): Promise<DepartmentCategory[]> {
    return this.withLoadingState(async () => {
      // 执行API调用
      await this.crud.renameDepartmentOrPerson(nodeId, newName, this.state.departmentCategories);

      // 使用增量更新而不是全量刷新
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '使用增量更新重命名节点'));

      // 增量重命名节点
      const updatedCategories = IncrementalCacheUpdater.renameNode(
        this.state.departmentCategories,
        nodeId,
        newName
      );

      // 更新状态（不触发计数更新）
      this.updateState({ departmentCategories: updatedCategories });

      // 如果是人员节点，清空人员缓存
      if (nodeId.startsWith('person-')) {
        this.clearPersonCache();
      }

      // 触发节点重命名事件
      this.emitter.emit('node-renamed', { nodeId, newName });

      // 触发结构变化事件，通知其他组件这是结构变化而非数据变化
      this.emitter.emit('tree-structure-changed', {
        operation: 'rename',
        nodeId,
        skipCountUpdate: true
      });

      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '节点重命名完成（增量更新）'));
      return this.state.departmentCategories;
    }, 'renameDepartmentOrPerson');
  }

  /**
   * 删除部门或人员（增强版本，带错误处理、回滚机制和操作锁）
   * @param nodeId 节点ID
   * @returns 更新后的部门树
   */
  public async deleteDepartmentOrPerson(nodeId: string): Promise<DepartmentCategory[]> {
    // 检查节点是否正在删除中
    if (this.deletingNodes.has(nodeId)) {
      console.warn(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `节点 ${nodeId} 正在删除中，忽略重复请求`));
      return this.state.departmentCategories;
    }

    // 检查全局删除锁
    if (this.deleteOperationLock) {
      console.warn(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `有其他删除操作正在进行，请稍后再试`));
      throw new Error('有其他删除操作正在进行，请稍后再试');
    }

    // 设置删除锁
    this.deleteOperationLock = true;
    this.deletingNodes.add(nodeId);

    return this.withLoadingState(async () => {
      // 保存原始状态用于回滚
      const originalCategories = JSON.parse(JSON.stringify(this.state.departmentCategories));

      try {
        // 预检查：验证节点存在性
        const nodeToDelete = TreeUtils.findNodeById(this.state.departmentCategories, nodeId);
        if (!nodeToDelete) {
          throw new Error(`未找到要删除的节点: ${nodeId}`);
        }

        // 预检查：扩展字段关联检查
        const preCheckResult = await this.preCheckDeletion(nodeId, nodeToDelete);
        if (!preCheckResult.canDelete) {
          throw new Error(preCheckResult.reason);
        }

        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `开始删除节点: ${nodeId} (${nodeToDelete.name})`));

        // 执行API调用
        await this.crud.deleteDepartmentOrPerson(nodeId, this.state.departmentCategories);

        // 使用增量更新而不是全量刷新
        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '使用增量更新删除节点'));

        // 增量删除节点
        const updatedCategories = IncrementalCacheUpdater.removeNode(
          this.state.departmentCategories,
          nodeId
        );

        // 更新状态（不触发计数更新）
        this.updateState({ departmentCategories: updatedCategories });

        if (nodeId.startsWith('person-')) {
          // 清空人员缓存
          this.clearPersonCache();
          console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '人员删除完成（增量更新）'));
        } else {
          // 触发节点删除事件（部门删除可能影响设备分类）
          this.emitter.emit('node-deleted', { nodeId });
          console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '部门删除完成（增量更新）'));
        }

        // 触发结构变化事件，通知其他组件这是结构变化而非数据变化
        this.emitter.emit('tree-structure-changed', {
          operation: 'delete',
          nodeId,
          skipCountUpdate: true
        });

        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `节点删除成功: ${nodeId}`));
        return this.state.departmentCategories;

      } catch (error) {
        console.error(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `删除节点失败: ${nodeId}`), error);

        // 回滚状态
        this.updateState({ departmentCategories: originalCategories });
        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '已回滚到删除前的状态'));

        // 重新抛出错误
        throw error;
      } finally {
        // 无论成功还是失败，都释放锁
        this.deleteOperationLock = false;
        this.deletingNodes.delete(nodeId);
        console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `释放节点 ${nodeId} 的删除锁`));
      }
    }, 'deleteDepartmentOrPerson');
  }

  /**
   * 删除前预检查
   * @param nodeId 节点ID
   * @param node 节点对象
   * @returns 预检查结果
   */
  private async preCheckDeletion(nodeId: string, node: DepartmentCategory): Promise<{ canDelete: boolean; reason?: string }> {
    try {
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `执行删除前预检查: ${nodeId}`));

      // 检查是否是部门节点
      if (nodeId.startsWith('dept-')) {
        // 检查是否是二级分类
        const isSecondLevel = this.isSecondLevelDepartment(node);

        if (isSecondLevel) {
          console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `检测到二级分类删除: ${node.name}`));

          // 检查扩展字段关联
          try {
            const extFieldService = ServiceRegistry.getInstance().get('extFieldService');
            if (extFieldService) {
              // 获取父级分类名称
              const parentNode = this.findParentDepartment(node);
              if (!parentNode) {
                return { canDelete: true }; // 找不到父级，不进行扩展字段检查
              }

              // 检查扩展字段缓存状态
              const cacheStatus = await this.checkExtFieldsCacheStatus(parentNode.name, node.name);
              if (!cacheStatus.isValid) {
                console.warn(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE,
                  `扩展字段缓存状态异常: ${cacheStatus.reason}，尝试修复`));

                // 尝试修复缓存
                const repairResult = extFieldService.repairExtFieldsCache();
                if (!repairResult.success) {
                  return {
                    canDelete: false,
                    reason: `删除前检查失败: 扩展字段缓存状态异常，无法安全删除。请刷新页面后重试。`
                  };
                }
              }
            }
          } catch (serviceError) {
            console.warn(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE,
              `无法获取扩展字段服务: ${serviceError.message}`));
            // 服务不可用时不阻止删除操作
          }
        }
      }

      return { canDelete: true };
    } catch (error) {
      console.error(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, `删除前预检查失败`), error);
      return {
        canDelete: true, // 预检查失败不阻止删除，但记录错误
        reason: `预检查失败: ${error.message}`
      };
    }
  }

  /**
   * 检查是否是二级部门
   */
  private isSecondLevelDepartment(node: DepartmentCategory): boolean {
    // 查找节点的父节点
    const parentNode = this.findParentDepartment(node);
    if (!parentNode) return false;

    // 检查父节点是否是根节点
    return parentNode.id === 'all-dept';
  }

  /**
   * 查找节点的父部门
   */
  private findParentDepartment(node: DepartmentCategory): DepartmentCategory | null {
    const findParent = (nodes: DepartmentCategory[], targetId: string): DepartmentCategory | null => {
      for (const current of nodes) {
        if (current.children) {
          const isParent = current.children.some(child => child.id === targetId);
          if (isParent) return current;

          const found = findParent(current.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    return findParent(this.state.departmentCategories, node.id);
  }

  /**
   * 检查扩展字段缓存状态
   */
  private async checkExtFieldsCacheStatus(
    parentCategoryName: string,
    subCategoryName: string
  ): Promise<{ isValid: boolean; reason?: string }> {
    try {
      try {
        const extFieldService = ServiceRegistry.getInstance().get('extFieldService');
        if (!extFieldService) {
          return { isValid: true }; // 找不到服务，不进行检查
        }

      // 检查缓存键是否存在（使用内部方法构建缓存键）
      const cacheKey = `${parentCategoryName.toLowerCase()}::${subCategoryName.toLowerCase()}`;
      const categoryExtFields = extFieldService.state.categoryExtFields;

      if (!categoryExtFields[cacheKey]) {
        // 缓存键不存在，可能是正常的（没有扩展字段）
        return { isValid: true };
      }

      // 检查 allExtFields 中是否存在对应的分类
      const allExtFields = extFieldService.state.allExtFields;
      const categoryExists = allExtFields.some(
        cat => cat.parent_category_name.toLowerCase() === parentCategoryName.toLowerCase() &&
               cat.sub_category_name.toLowerCase() === subCategoryName.toLowerCase()
      );

      if (!categoryExists) {
        return {
          isValid: false,
          reason: `缓存不一致: 缓存键 ${cacheKey} 存在，但在 allExtFields 中找不到对应的分类`
        };
      }

        return { isValid: true };
      } catch (serviceError) {
        console.warn('无法获取扩展字段服务:', serviceError.message);
        return { isValid: true }; // 服务不可用时不进行检查
      }
    } catch (error) {
      console.error('检查扩展字段缓存状态失败:', error);
      return { isValid: false, reason: `检查失败: ${error.message}` };
    }
  }

  /**
   * 更新部门位置
   * @param departmentId 部门ID
   * @param newParentId 新的父部门ID
   */
  public async updateDepartmentPosition(departmentId: string, newParentId: string): Promise<DepartmentCategory[]> {
    return this.withLoadingState(async () => {
      // 执行API调用
      await this.crud.updateDepartmentPosition(departmentId, newParentId, this.state.departmentCategories);

      // 使用增量更新：先删除原位置的节点，再添加到新位置
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '使用增量更新移动部门节点'));

      // 查找要移动的节点
      const nodeToMove = TreeUtils.findNodeById(this.state.departmentCategories, departmentId);
      if (!nodeToMove) {
        throw new Error(`未找到要移动的部门: ${departmentId}`);
      }

      // 先从原位置删除
      let updatedCategories = IncrementalCacheUpdater.removeNode(
        this.state.departmentCategories,
        departmentId
      );

      // 再添加到新位置
      const departmentData = {
        name: nodeToMove.name,
        department_path: this.buildDepartmentPath(newParentId, nodeToMove.name)
      };

      updatedCategories = IncrementalCacheUpdater.addDepartmentNode(
        updatedCategories,
        newParentId,
        departmentData
      );

      // 更新状态（不触发计数更新）
      this.updateState({ departmentCategories: updatedCategories });

      // 触发节点移动事件
      this.emitter.emit('node-moved', { nodeId: departmentId, newParentId });

      // 触发结构变化事件
      this.emitter.emit('tree-structure-changed', {
        operation: 'move',
        nodeId: departmentId,
        skipCountUpdate: true
      });

      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '部门位置更新完成（增量更新）'));
      return this.state.departmentCategories;
    }, 'updateDepartmentPosition');
  }

  /**
   * 更新人员位置
   * @param personId 人员ID
   * @param newDepartmentId 新的部门ID
   */
  public async updatePersonPosition(personId: string, newDepartmentId: string): Promise<DepartmentCategory[]> {
    return this.withLoadingState(async () => {
      // 获取人员信息用于事件触发
      const personInfo = DepartmentUtils.extractPersonInfo(this.state.departmentCategories, personId);
      if (!personInfo) {
        throw new Error(`未找到人员: ${personId}`);
      }

      // 获取旧部门信息
      const oldDepartmentNode = DepartmentUtils.findParentDepartment(this.state.departmentCategories, personId);
      if (!oldDepartmentNode) {
        throw new Error(`未找到人员的所属部门: ${personId}`);
      }
      const oldDepartmentName = oldDepartmentNode.name;

      // 获取新部门名称
      const newDepartmentNode = DepartmentUtils.findNodeById(this.state.departmentCategories, newDepartmentId);
      if (!newDepartmentNode) {
        throw new Error(`未找到新的部门: ${newDepartmentId}`);
      }
      const newDepartmentName = newDepartmentNode.name;

      // 执行API调用
      await this.crud.updatePersonPosition(personId, newDepartmentId, this.state.departmentCategories);

      // 使用增量更新：先删除原位置的人员节点，再添加到新位置
      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '使用增量更新移动人员节点'));

      // 先从原位置删除
      let updatedCategories = IncrementalCacheUpdater.removeNode(
        this.state.departmentCategories,
        personId
      );

      // 再添加到新位置
      const personData = {
        id: personInfo.originalPersonId || personId.replace('person-', ''),
        user_name: personInfo.name,
        alias: personInfo.alias || ''
      };

      updatedCategories = IncrementalCacheUpdater.addPersonNode(
        updatedCategories,
        newDepartmentId,
        personData
      );

      // 更新状态（不触发计数更新）
      this.updateState({ departmentCategories: updatedCategories });

      // 清空人员缓存，因为人员部门信息发生了变化
      this.clearPersonCache();

      // 触发节点移动事件，同时传递旧部门和新部门的信息
      this.emitter.emit('node-moved', {
        nodeId: personId,
        newParentId: newDepartmentId,
        personName: personInfo.name,
        personAlias: personInfo.alias,
        oldDepartmentName,
        newDepartmentName
      });

      // 人员移动需要更新计数，因为会影响设备统计
      // 触发结构变化事件，但不跳过计数更新
      this.emitter.emit('tree-structure-changed', {
        operation: 'move',
        nodeId: personId,
        skipCountUpdate: false  // 人员移动需要更新计数
      });

      console.log(formatLogMessage(LOG_PREFIXES.DEPARTMENT_SERVICE, '人员位置更新完成（增量更新）'));
      return this.state.departmentCategories;
    }, 'updatePersonPosition');
  }

  /**
   * 统一的部门树刷新方法
   * @param operation 操作描述
   */
  private async refreshDepartmentTree(operation: string): Promise<void> {
    console.log(`${operation}，刷新部门树`);
    await this.loadDepartmentTree(true);
  }

  /**
   * 构建部门路径
   * @param parentId 父部门ID
   * @param departmentName 部门名称
   * @returns 部门路径
   */
  private buildDepartmentPath(parentId: string, departmentName: string): string {
    if (parentId === 'all-dept') {
      return departmentName;
    }

    // 查找父部门
    const parentNode = DepartmentUtils.findNodeById(this.state.departmentCategories, parentId);
    if (!parentNode) {
      return departmentName;
    }

    // 总是基于树结构动态计算路径，确保数据一致性
    const parentPath = TreeUtils.getApiPath(parentNode, this.state.departmentCategories);
    return parentPath ? `${parentPath}/${departmentName}` : departmentName;
  }

  /**
   * 统一的加载状态管理
   * @param operation 操作函数
   * @param operationName 操作名称
   */
  private async withLoadingState<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
    try {
      this.updateState({ isLoading: true });
      return await operation();
    } catch (error) {
      logError(error, `DepartmentService.${operationName}`);
      throw this.handleError(error);
    } finally {
      this.updateState({ isLoading: false });
    }
  }

  /**
   * 从部门树中移除人员节点（内存操作）
   * @param categories 部门分类树
   * @param personId 人员节点ID
   * @returns 更新后的部门树
   */
  private removePersonNodeFromTree(categories: DepartmentCategory[], personId: string): DepartmentCategory[] {
    const removePersonFromCategory = (category: DepartmentCategory): DepartmentCategory => {
      if (!category.children) {
        return category;
      }

      // 过滤掉要删除的人员节点
      const filteredChildren = category.children
        .filter(child => child.id !== personId)
        .map(child => removePersonFromCategory(child));

      // 计数不需要改动，因为人员只有在没有设备时才能删除
      // 删除人员不影响设备计数
      return {
        ...category,
        children: filteredChildren,
        count: category.count // 保持原有计数
      };
    };

    return categories.map(category => removePersonFromCategory(category));
  }

  /**
   * 查找节点 - 统一的节点查找方法
   * @param categories 部门分类树
   * @param nodeId 节点ID
   * @returns 找到的节点或null
   */
  public findNodeById(categories: DepartmentCategory[], nodeId: string): DepartmentCategory | null {
    return DepartmentUtils.findNodeById(categories, nodeId);
  }

  /**
   * 根据部门名称或路径获取该部门的直属人员
   * @param departmentNameOrPath 部门名称或完整路径
   * @param allPersonOptions 所有人员选项
   * @returns 该部门的人员列表
   */
  public getPersonsByDepartment(departmentNameOrPath: string, allPersonOptions: any[]): any[] {
    if (!departmentNameOrPath) return [];

    console.log(`查找部门人员，输入: "${departmentNameOrPath}"`);
    console.log('所有人员选项:', allPersonOptions);

    // 过滤出该部门的直属人员
    const directPersons = allPersonOptions
      .filter(person => {
        // 优先匹配完整路径
        if (person.departmentPath && person.departmentPath === departmentNameOrPath) {
          return true;
        }

        // 兜底匹配部门名称（兼容性）
        if (person.departmentName && person.departmentName === departmentNameOrPath) {
          return true;
        }

        // 如果输入的是路径，尝试提取最后一级部门名称进行匹配
        if (departmentNameOrPath.includes('/')) {
          const lastDeptName = departmentNameOrPath.split('/').pop();
          if (person.departmentName === lastDeptName) {
            return true;
          }
        }

        return false;
      })
      .map(person => ({
        code: person.fullName,
        value: person.fullName
      }));

    console.log(`部门 "${departmentNameOrPath}" 的人员:`, directPersons);
    return directPersons;
  }

  /**
   * 处理部门数据，构建树选项和人员选项
   * @param departmentCategories 部门分类数据
   * @returns 处理后的树选项和人员选项
   */
  public processDepartmentData(departmentCategories: any[]): {
    treeOptions: any[];
    allPersons: any[];
  } {
    // 构建部门树选项
    const convertToTreeOptions = (categories: any[]): any[] => {
      const options: any[] = [];

      categories.forEach(category => {
        // 跳过根节点（公司名称），直接处理其子部门
        if (category.id === 'all-dept') {
          // 如果是根节点，直接处理其子部门
          if (category.children && category.children.length > 0) {
            const childOptions = convertToTreeOptions(category.children);
            options.push(...childOptions);
          }
        } else if (category.id.startsWith('dept-')) {
          // 处理普通部门节点
          const option: any = {
            id: category.id,
            value: category.departmentPath || category.name, // 优先使用完整部门路径，兜底使用部门名称
            label: category.name,
            children: []
          };

          // 递归处理子部门
          if (category.children && category.children.length > 0) {
            option.children = convertToTreeOptions(category.children);
          }

          options.push(option);
        }
      });

      return options;
    };

    const treeOptions = convertToTreeOptions(departmentCategories);

    // 构建所有人员选项
    const allPersons: any[] = [];

    const extractPersons = (categories: any[], parentDeptPath: string = '', parentDeptName: string = '') => {
      categories.forEach(category => {
        if (category.id.startsWith('person-')) {
          // 这是人员节点
          allPersons.push({
            personName: category.name,
            departmentId: category.departmentId || '',
            departmentName: parentDeptName, // 保持单个部门名称（兼容性）
            departmentPath: parentDeptPath, // 新增：完整部门路径
            personId: category.id,
            fullName: category.name
          });
        } else if (category.children && category.children.length > 0) {
          // 递归处理子节点
          if (category.id === 'all-dept') {
            // 根节点，保持原有路径和名称
            extractPersons(category.children, parentDeptPath, parentDeptName);
          } else {
            // 普通部门节点，构建新的路径和名称
            const newDeptPath = category.departmentPath || (parentDeptPath ? `${parentDeptPath}/${category.name}` : category.name);
            const newDeptName = category.name;
            extractPersons(category.children, newDeptPath, newDeptName);
          }
        }
      });
    };

    extractPersons(departmentCategories);

    return {
      treeOptions,
      allPersons
    };
  }

  /**
   * 重写错误处理方法，增加日志记录
   * @param error 错误对象
   * @returns 标准化的AppError
   */
  protected handleError(error: unknown): AppError {
    // 记录错误
    logError(error, `DepartmentService`);

    // 调用父类的错误处理方法
    return super.handleError(error);
  }
}

export default DepartmentService;
