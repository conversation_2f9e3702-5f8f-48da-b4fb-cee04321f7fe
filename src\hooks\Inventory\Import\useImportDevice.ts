import { useState, useEffect, useCallback } from 'react';
import { useService } from '../../base/useService';
import ImportDeviceService, { ImportResult } from '../../../services/Inventory/Import/importDeviceService';

// 导入设备钩子返回类型
interface UseImportDeviceReturn {
  // 状态
  isImporting: boolean;
  lastImportResult: ImportResult | null;
  error?: string;

  // 方法
  importDevicesFromExcel: () => Promise<ImportResult>;
}

/**
 * 导入设备钩子
 * 用于连接导入设备服务和UI组件
 */
export function useImportDevice(): UseImportDeviceReturn {
  // 使用服务
  const [state, service] = useService<
    ImportDeviceService['state'],
    any,
    ImportDeviceService
  >(() => ImportDeviceService.getInstance());

  // 导入设备台账数据
  const importDevicesFromExcel = useCallback(async (): Promise<ImportResult> => {
    try {
      return await service.importDevicesFromExcel();
    } catch (error: any) {
      console.error('导入设备台账数据失败:', error);
      throw error;
    }
  }, [service]);

  // 返回钩子结果
  return {
    isImporting: state.isImporting,
    lastImportResult: state.lastImportResult,
    error: state.error,
    importDevicesFromExcel
  };
}

export default useImportDevice;
