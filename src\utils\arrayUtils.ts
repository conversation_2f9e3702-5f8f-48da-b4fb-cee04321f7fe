/**
 * 通用数组工具函数
 * 统一处理各种数组操作需求，避免代码重复
 */

/**
 * 通用去重函数
 * @param array 要去重的数组
 * @param keySelector 键选择器函数，用于确定去重的依据
 * @returns 去重后的数组
 */
export function removeDuplicates<T>(
  array: T[], 
  keySelector: (item: T) => string | number
): T[] {
  if (!array || array.length === 0) return [];
  
  const seen = new Set<string | number>();
  return array.filter(item => {
    const key = keySelector(item);
    if (seen.has(key)) return false;
    seen.add(key);
    return true;
  });
}

/**
 * 根据对象属性去重
 * @param array 要去重的数组
 * @param property 用于去重的属性名
 * @returns 去重后的数组
 */
export function removeDuplicatesByProperty<T>(
  array: T[], 
  property: keyof T
): T[] {
  return removeDuplicates(array, item => String(item[property]));
}

/**
 * 根据多个属性组合去重
 * @param array 要去重的数组
 * @param properties 用于去重的属性名数组
 * @returns 去重后的数组
 */
export function removeDuplicatesByProperties<T>(
  array: T[], 
  properties: (keyof T)[]
): T[] {
  return removeDuplicates(array, item => 
    properties.map(prop => String(item[prop])).join('|')
  );
}

/**
 * 简单数组去重（基本类型）
 * @param array 要去重的数组
 * @returns 去重后的数组
 */
export function removeDuplicatesSimple<T extends string | number>(array: T[]): T[] {
  return Array.from(new Set(array));
}

/**
 * 移除被上级覆盖的下级项目（用于层级结构去重）
 * @param items 项目数组
 * @param getParentPath 获取父级路径的函数
 * @param isParentOf 判断是否为父级的函数
 * @returns 去重后的数组
 */
export function removeRedundantHierarchical<T>(
  items: T[],
  getParentPath: (item: T) => string[],
  isParentOf: (parent: T, child: T) => boolean
): T[] {
  return items.filter(item => {
    // 检查是否有其他项目是这个项目的上级
    return !items.some(otherItem => {
      if (otherItem === item) return false;
      return isParentOf(otherItem, item);
    });
  });
}

/**
 * 数组交集
 * @param array1 第一个数组
 * @param array2 第二个数组
 * @param keySelector 键选择器函数
 * @returns 交集数组
 */
export function intersection<T>(
  array1: T[],
  array2: T[],
  keySelector?: (item: T) => string | number
): T[] {
  if (!keySelector) {
    const set2 = new Set(array2);
    return array1.filter(item => set2.has(item));
  }
  
  const keys2 = new Set(array2.map(keySelector));
  return array1.filter(item => keys2.has(keySelector(item)));
}

/**
 * 数组差集
 * @param array1 第一个数组
 * @param array2 第二个数组
 * @param keySelector 键选择器函数
 * @returns 差集数组（在array1中但不在array2中的元素）
 */
export function difference<T>(
  array1: T[],
  array2: T[],
  keySelector?: (item: T) => string | number
): T[] {
  if (!keySelector) {
    const set2 = new Set(array2);
    return array1.filter(item => !set2.has(item));
  }
  
  const keys2 = new Set(array2.map(keySelector));
  return array1.filter(item => !keys2.has(keySelector(item)));
}

/**
 * 数组分组
 * @param array 要分组的数组
 * @param keySelector 分组键选择器
 * @returns 分组后的Map对象
 */
export function groupBy<T, K extends string | number>(
  array: T[],
  keySelector: (item: T) => K
): Map<K, T[]> {
  const groups = new Map<K, T[]>();
  
  for (const item of array) {
    const key = keySelector(item);
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key)!.push(item);
  }
  
  return groups;
}

/**
 * 数组分块
 * @param array 要分块的数组
 * @param size 每块的大小
 * @returns 分块后的二维数组
 */
export function chunk<T>(array: T[], size: number): T[][] {
  if (size <= 0) return [];
  
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * 数组扁平化（一层）
 * @param array 要扁平化的二维数组
 * @returns 扁平化后的一维数组
 */
export function flatten<T>(array: T[][]): T[] {
  return array.reduce((acc, val) => acc.concat(val), []);
}

/**
 * 安全的数组访问
 * @param array 数组
 * @param index 索引
 * @param defaultValue 默认值
 * @returns 数组元素或默认值
 */
export function safeGet<T>(array: T[], index: number, defaultValue?: T): T | undefined {
  if (index < 0 || index >= array.length) {
    return defaultValue;
  }
  return array[index];
}
