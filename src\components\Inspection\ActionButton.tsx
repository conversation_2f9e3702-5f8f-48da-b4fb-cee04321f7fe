import React from 'react';
import { LucideIcon } from 'lucide-react';

/**
 * 按钮类型
 */
export type ButtonVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'warning';

/**
 * 操作按钮组件的属性接口
 */
interface ActionButtonProps {
  variant?: ButtonVariant;
  icon?: LucideIcon;
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  title?: string;
}

/**
 * 操作按钮组件
 * 提供统一的Vben风格按钮，消除重复代码
 */
const ActionButton: React.FC<ActionButtonProps> = ({
  variant = 'primary',
  icon: Icon,
  children,
  onClick,
  disabled = false,
  loading = false,
  className = '',
  title
}) => {
  // 获取按钮样式
  const getButtonStyles = (variant: ButtonVariant) => {
    const baseStyles = 'inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200';
    
    switch (variant) {
      case 'primary':
        return `${baseStyles} border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500`;
      case 'secondary':
        return `${baseStyles} border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500`;
      case 'success':
        return `${baseStyles} border-transparent text-white bg-green-600 hover:bg-green-700 focus:ring-green-500`;
      case 'danger':
        return `${baseStyles} border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500`;
      case 'warning':
        return `${baseStyles} border-transparent text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500`;
      default:
        return baseStyles;
    }
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`${getButtonStyles(variant)} ${className}`}
      title={title}
    >
      {Icon && (
        <Icon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
      )}
      {children}
    </button>
  );
};

export default ActionButton;
