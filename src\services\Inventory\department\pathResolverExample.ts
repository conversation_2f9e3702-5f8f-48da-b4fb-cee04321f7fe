/**
 * 统一部门路径解析器使用示例
 * 展示如何使用DepartmentPathResolver来获取各种格式的部门路径
 */

import { DepartmentCategory } from './types';
import { DepartmentUtils } from './utils';

/**
 * 部门路径解析器使用示例
 */
export class DepartmentPathResolverExample {
  private pathResolver: DepartmentUtils.DepartmentPathResolver;

  constructor(departmentCategories: DepartmentCategory[]) {
    this.pathResolver = new DepartmentUtils.DepartmentPathResolver(departmentCategories);
  }

  /**
   * 示例1: 获取API调用路径
   * 用于添加、删除、更新部门的API调用
   */
  public getApiPathExample(department: DepartmentCategory): string {
    // 获取用于API调用的路径（不包含根节点）
    const apiPath = this.pathResolver.getApiPath(department);
    console.log(`部门 "${department.name}" 的API路径: "${apiPath}"`);
    return apiPath;
  }

  /**
   * 示例2: 获取完整显示路径
   * 用于UI显示、日志记录等
   */
  public getFullPathExample(department: DepartmentCategory): string {
    // 获取完整路径（包含根节点）
    const fullPath = this.pathResolver.getFullPath(department);
    console.log(`部门 "${department.name}" 的完整路径: "${fullPath}"`);
    return fullPath;
  }

  /**
   * 示例3: 获取导出路径
   * 用于Excel、PDF、Word导出
   */
  public getExportPathExample(department: DepartmentCategory): string {
    // 获取导出路径（不包含根节点）
    const exportPath = this.pathResolver.getExportPath(department);
    console.log(`部门 "${department.name}" 的导出路径: "${exportPath}"`);
    return exportPath;
  }

  /**
   * 示例4: 根据ID获取API路径
   * 当只有部门ID时使用
   */
  public getApiPathByIdExample(departmentId: string): string {
    const apiPath = this.pathResolver.getApiPathById(departmentId);
    console.log(`部门ID "${departmentId}" 的API路径: "${apiPath}"`);
    return apiPath;
  }

  /**
   * 示例5: 根据名称获取所有匹配的API路径
   * 处理同名部门的情况
   */
  public getApiPathsByNameExample(departmentName: string): string[] {
    const apiPaths = this.pathResolver.getApiPathsByName(departmentName);
    console.log(`名为 "${departmentName}" 的所有部门API路径:`, apiPaths);
    return apiPaths;
  }

  /**
   * 示例6: 构建新部门路径
   * 用于添加新部门时构建路径
   */
  public buildNewDepartmentPathExample(parentId: string, departmentName: string): string {
    try {
      const newPath = this.pathResolver.buildNewDepartmentPath(parentId, departmentName);
      console.log(`在父部门 "${parentId}" 下添加 "${departmentName}" 的路径: "${newPath}"`);
      return newPath;
    } catch (error) {
      console.error('构建新部门路径失败:', error);
      throw error;
    }
  }

  /**
   * 示例7: 验证路径有效性
   * 检查路径格式是否正确
   */
  public validatePathExample(departmentPath: string): boolean {
    const isValid = this.pathResolver.isValidPath(departmentPath);
    console.log(`路径 "${departmentPath}" ${isValid ? '有效' : '无效'}`);
    return isValid;
  }

  /**
   * 综合示例: 完整的部门操作流程
   */
  public comprehensiveExample(department: DepartmentCategory): void {
    console.log('\n=== 部门路径解析器综合示例 ===');
    console.log(`处理部门: ${department.name} (ID: ${department.id})`);

    // 1. 获取各种格式的路径
    const apiPath = this.getApiPathExample(department);
    const fullPath = this.getFullPathExample(department);
    const exportPath = this.getExportPathExample(department);

    // 2. 验证路径
    this.validatePathExample(apiPath);
    this.validatePathExample(fullPath);

    // 3. 模拟API调用
    console.log('\n--- 模拟API调用 ---');
    console.log('添加子部门API参数:', {
      action: 'add_department',
      action_params: {
        department_path: `${apiPath}/新子部门`
      }
    });

    console.log('删除部门API参数:', {
      action: 'delete_department',
      action_params: {
        department_path: apiPath
      }
    });

    // 4. 导出格式
    console.log('\n--- 导出格式 ---');
    console.log('Excel导出路径:', exportPath);
    console.log('PDF导出路径:', exportPath);
    console.log('Word导出路径:', exportPath);

    console.log('\n=== 示例完成 ===\n');
  }
}

/**
 * 快捷工具函数 - 直接从DepartmentService创建路径解析器
 */
export function createPathResolver(departmentService: any): DepartmentUtils.DepartmentPathResolver {
  return departmentService.createPathResolver();
}

/**
 * 快捷工具函数 - 获取API路径
 */
export function getApiPath(departmentService: any, department: DepartmentCategory): string {
  const resolver = createPathResolver(departmentService);
  return resolver.getApiPath(department);
}

/**
 * 快捷工具函数 - 根据ID获取API路径
 */
export function getApiPathById(departmentService: any, departmentId: string): string {
  const resolver = createPathResolver(departmentService);
  return resolver.getApiPathById(departmentId);
}

/**
 * 快捷工具函数 - 构建新部门路径
 */
export function buildNewDepartmentPath(
  departmentService: any, 
  parentId: string, 
  departmentName: string
): string {
  const resolver = createPathResolver(departmentService);
  return resolver.buildNewDepartmentPath(parentId, departmentName);
}

/**
 * 快捷工具函数 - 验证路径
 */
export function validatePath(departmentService: any, departmentPath: string): boolean {
  const resolver = createPathResolver(departmentService);
  return resolver.isValidPath(departmentPath);
}
