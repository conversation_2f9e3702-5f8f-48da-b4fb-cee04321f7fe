import React, { useMemo, useCallback, useEffect, useRef, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
  ColumnDef
} from '@tanstack/react-table';
import {
  ChevronsLeft,
  ChevronLeft,
  ChevronRight,
  ChevronsRight,
  Pencil,
  Trash,
  RefreshCw,
  Tag,
  Printer
} from 'lucide-react';
import { InventoryItem, FieldDefinition } from '../../../types/inventory';
import CustomScrollbar from '../../../components/ui/CustomScrollbar';
import { isTimeField, formatTimeValue } from '../../../utils/fieldUtils';
import InspectionTooltip from '../../../components/InspectionTooltip';
import CustomTooltip from '../../../components/ui/CustomTooltip';
import HardDriveSerialDisplay from '../../../components/ui/HardDriveSerialDisplay';

// 在文件开头添加全局样式
// 添加全局样式
const globalStyles = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* 操作列样式 */
  td[data-column-id="actions"] {
    border-bottom: 1px solid #e5e7eb !important;
  }

  /* 操作列悬停样式 */
  tr.group:hover td[data-column-id="actions"] {
    background-color: #f9fafb !important;
  }

  /* 最后一行的操作列没有下边框 */

  /* 列宽调整拖拽手柄样式 */
  .column-resizer {
    position: absolute;
    right: -1px;
    top: 0;
    height: 100%;
    width: 3px;
    background: transparent;
    cursor: col-resize;
    user-select: none;
    touch-action: none;
    z-index: 30;
    opacity: 0;
    transition: all 0.2s ease;
    border-radius: 1px;
  }

  /* 表头悬停时显示拖拽手柄 */
  .resizable-header:hover .column-resizer {
    opacity: 1;
    background: #3b82f6;
    width: 4px;
    right: -2px;
  }

  /* 拖拽时保持显示 */
  .column-resizer.resizing {
    opacity: 1;
    background: #3b82f6;
    width: 4px;
    right: -2px;
  }

  /* 表头单元格相对定位以支持拖拽手柄 */
  .resizable-header {
    position: relative;
  }
  tbody tr:last-child td[data-column-id="actions"],
  tr.empty-row td {
    border-bottom: none !important;
  }
`;

// 自定义下拉框组件
interface CustomDropdownProps {
  value: number;
  options: number[];
  onChange: (value: number) => void;
  showAllOption?: boolean; // 是否显示"全部"选项
  showCustomOption?: boolean; // 是否显示"自定义"选项
  totalItems?: number; // 总数据条数，用于判断是否显示"全部"
  customRange?: [number, number]; // 自定义值的范围限制
}

// 简化版的自绘下拉框组件，确保在旧版浏览器中正常工作
const CustomDropdown: React.FC<CustomDropdownProps> = ({
  value,
  options,
  onChange,
  showAllOption = false,
  showCustomOption = false,
  totalItems = 0,
  customRange = [1, 1000]
}) => {
  // 下拉框状态
  const [isOpen, setIsOpen] = React.useState(false);
  // 下拉框引用
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // 自定义输入相关状态
  const [isCustomMode, setIsCustomMode] = React.useState(false);
  const [customValue, setCustomValue] = React.useState('');
  const [customError, setCustomError] = React.useState('');
  const customInputRef = React.useRef<HTMLInputElement>(null);

  // 特殊值常量
  const ALL_OPTION_VALUE = -1;

  // 判断当前值是否为自定义值
  const isCustomValue = !options.includes(value) && value !== ALL_OPTION_VALUE && value > 0;

  // 验证输入值
  const validateInput = (inputValue: string) => {
    if (!inputValue.trim()) return { valid: false, error: '' };

    const num = parseInt(inputValue, 10);
    const [min, max] = customRange;

    if (isNaN(num) || num < min || num > max) {
      return { valid: false, error: '数值超出范围' };
    }

    return { valid: true, error: '', value: num };
  };

  // 重置自定义输入状态
  const resetCustomInput = () => {
    setIsCustomMode(false);
    setCustomValue('');
    setCustomError('');
  };

  // 聚焦并选中输入框
  const focusInput = () => {
    setTimeout(() => {
      if (customInputRef.current) {
        customInputRef.current.focus();
        customInputRef.current.select();
      }
    }, 50);
  };

  // 开始自定义输入
  const startCustomInput = () => {
    setIsCustomMode(true);
    setCustomValue('');
    setCustomError('');
    focusInput();
  };

  // 智能处理输入完成
  const handleInputComplete = () => {
    const trimmedValue = customValue.trim();

    if (!trimmedValue) {
      resetCustomInput();
      return;
    }

    const validation = validateInput(trimmedValue);
    if (!validation.valid) {
      setCustomError(validation.error);
      focusInput();
      return;
    }

    // 输入有效，保存并退出
    setCustomError('');
    setIsCustomMode(false);
    setIsOpen(false);
    onChange(validation.value!);
  };

  // 取消自定义输入（仅在明确取消时使用）
  const cancelCustomInput = () => {
    resetCustomInput();
  };

  // 添加动画样式
  React.useEffect(() => {
    // 检查是否已经添加了动画样式
    if (!document.getElementById('dropdown-animation-style')) {
      const style = document.createElement('style');
      style.id = 'dropdown-animation-style';
      style.innerHTML = globalStyles;
      document.head.appendChild(style);
    }

    return () => {
      // 清理函数，组件卸载时移除样式
      const styleElement = document.getElementById('dropdown-animation-style');
      if (styleElement) {
        styleElement.parentNode?.removeChild(styleElement);
      }
    };
  }, []);

  // 处理点击外部关闭下拉菜单或完成自定义输入
  React.useEffect(() => {
    if (!isOpen && !isCustomMode) return;

    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        if (isCustomMode) {
          // 在自定义输入模式下，点击外部智能处理输入
          handleInputComplete();
        } else {
          // 在下拉模式下，点击外部关闭下拉框
          setIsOpen(false);
        }
      }
    }

    // 使用setTimeout确保事件监听器在当前点击事件之后添加
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 0);

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isOpen, isCustomMode, customValue]); // 添加依赖

  // 切换下拉菜单的显示/隐藏
  const toggleDropdown = (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止立即触发点击外部事件
    e.stopPropagation();

    // 如果要打开下拉菜单，先计算可用空间
    if (!isOpen && dropdownRef.current) {
      // 获取下拉框的位置信息
      const rect = dropdownRef.current.getBoundingClientRect();
    }

    setIsOpen(!isOpen);
  };

  // 选择选项
  const selectOption = (option: number, e: React.MouseEvent) => {
    // 阻止事件冒泡，防止触发点击外部事件
    e.stopPropagation();
    onChange(option);
    setIsOpen(false);
  };

  return (
    <div
      ref={dropdownRef}
      style={{
        position: 'relative',
        display: 'inline-block',
        margin: '0 8px',
        width: '70px' // 增加宽度，确保"全部"选项能够完整显示
      }}
    >
      {/* 当前选中值显示框 */}
      {isCustomMode ? (
        // 自定义输入模式
        <div style={{
          display: 'flex',
          alignItems: 'center',
          border: customError ? '1px solid #ef4444' : '1px solid #3b82f6',
          borderRadius: '4px',
          backgroundColor: 'white',
          height: '24px',
          overflow: 'hidden',
          boxShadow: '0 0 0 1px rgba(59, 130, 246, 0.1)'
        }}>
          <input
            ref={customInputRef}
            type="text"
            value={customValue}
            onChange={(e) => {
              const newValue = e.target.value.replace(/[^0-9]/g, '');
              setCustomValue(newValue);

              // 实时验证
              const validation = validateInput(newValue);
              setCustomError(validation.error);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleInputComplete();
              } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelCustomInput();
              }
            }}
            onBlur={() => {
              // 延迟处理，避免与点击外部事件冲突
              // 如果用户点击的是下拉选项，不要触发完成逻辑
              setTimeout(() => {
                if (isCustomMode && !isOpen) {
                  handleInputComplete();
                }
              }, 150);
            }}
            placeholder="输入条数"
            style={{
              width: '70px',
              border: 'none',
              outline: 'none',
              padding: '0 8px',
              fontSize: '14px',
              textAlign: 'center',
              backgroundColor: 'transparent',
              height: '100%'
            }}
          />
        </div>
      ) : (
        // 正常显示模式
        <div
          onClick={toggleDropdown}
          style={{
            padding: '2px 24px 2px 8px',
            fontSize: '14px',
            border: '1px solid #d1d5db',
            borderRadius: '4px',
            backgroundColor: 'white',
            cursor: 'pointer',
            position: 'relative',
            textAlign: 'center',
            height: '24px',
            lineHeight: '24px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            boxShadow: isOpen ? '0 0 0 2px rgba(59, 130, 246, 0.2)' : 'none',
            transition: 'all 0.2s ease',
            minWidth: '60px'
          }}
        >
          <span style={{
            color: value === ALL_OPTION_VALUE || (value > 100 && value === totalItems) || isCustomValue ? '#0369a1' : '#374151',
            fontWeight: 'normal'
          }}>
            {value === ALL_OPTION_VALUE || (value > 100 && value === totalItems) ? '全部' : value}
          </span>
          {/* 下拉箭头 - 使用SVG图标 */}
          <span style={{
            position: 'absolute',
            right: '8px',
            top: '50%',
            transform: `translateY(-50%) rotate(${isOpen ? '180deg' : '0deg'})`,
            fontSize: '10px',
            color: '#6b7280',
            transition: 'transform 0.2s ease'
          }}>
            ▼
          </span>
        </div>
      )}

      {/* 下拉选项列表 - 向上展开 */}
      {isOpen && (
        <div
          style={{
            position: 'absolute',
            left: '0',
            bottom: '28px', // 放置在触发元素上方
            width: '100%',
            minWidth: '80px', // 确保最小宽度
            backgroundColor: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '6px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            zIndex: 1000,
            maxHeight: '200px',
            overflowY: 'auto',
            marginBottom: '2px', // 与触发元素保持一定距离
            overflow: 'hidden' // 确保圆角效果
          }}
          onClick={(e) => e.stopPropagation()} // 阻止事件冒泡
        >
          {/* 按顺序显示选项：数字选项 -> 自定义 -> 全部 */}
          {options.map(option => (
            <div
              key={option}
              onClick={(e) => selectOption(option, e)}
              style={{
                padding: '5px 12px',
                cursor: 'pointer',
                backgroundColor: value === option ? '#f0f9ff' : 'white',
                color: value === option ? '#0369a1' : '#374151',
                fontWeight: value === option ? 'bold' : 'normal',
                textAlign: 'center',
                height: '28px',
                lineHeight: '18px',
                borderBottom: '1px solid #f1f5f9',
                transition: 'all 0.1s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = value === option ? '#f0f9ff' : '#f9fafb';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = value === option ? '#f0f9ff' : 'white';
              }}
            >
              {option}
            </div>
          ))}

          {/* 自定义选项 */}
          {showCustomOption && (
            <div
              key="custom"
              onClick={(e) => {
                e.stopPropagation();
                setIsOpen(false);
                startCustomInput();
              }}
              style={{
                padding: '5px 12px',
                cursor: 'pointer',
                backgroundColor: isCustomValue ? '#f0f9ff' : 'white',
                color: isCustomValue ? '#0369a1' : '#374151',
                fontWeight: isCustomValue ? 'bold' : 'normal',
                textAlign: 'center',
                height: '28px',
                lineHeight: '18px',
                borderBottom: '1px solid #f1f5f9',
                transition: 'all 0.1s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = isCustomValue ? '#f0f9ff' : '#f9fafb';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = isCustomValue ? '#f0f9ff' : 'white';
              }}
            >
              自定义
            </div>
          )}

          {/* 全部选项 */}
          {showAllOption && (
            <div
              key="all"
              onClick={(e) => selectOption(ALL_OPTION_VALUE, e)}
              style={{
                padding: '5px 12px',
                cursor: 'pointer',
                backgroundColor: (value === ALL_OPTION_VALUE || (value > 100 && value === totalItems)) ? '#f0f9ff' : 'white',
                color: (value === ALL_OPTION_VALUE || (value > 100 && value === totalItems)) ? '#0369a1' : '#374151',
                fontWeight: (value === ALL_OPTION_VALUE || (value > 100 && value === totalItems)) ? 'bold' : 'normal',
                textAlign: 'center',
                height: '28px',
                lineHeight: '18px',
                transition: 'all 0.1s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = (value === ALL_OPTION_VALUE || (value > 100 && value === totalItems)) ? '#f0f9ff' : '#f9fafb';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = (value === ALL_OPTION_VALUE || (value > 100 && value === totalItems)) ? '#f0f9ff' : 'white';
              }}
            >
              全部
            </div>
          )}
        </div>
      )}

      {/* 错误提示 */}
      {customError && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '0',
          right: '0',
          backgroundColor: '#fee2e2',
          border: '1px solid #fca5a5',
          borderRadius: '3px',
          padding: '2px 6px',
          fontSize: '11px',
          color: '#dc2626',
          zIndex: 10000,
          marginTop: '1px',
          textAlign: 'center',
          whiteSpace: 'nowrap'
        }}>
          {customError}
        </div>
      )}
    </div>
  );
};

interface InventoryTableProps {
  items: InventoryItem[];
  columnVisibility: Record<string, boolean>;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onSelect: (id: string) => void;
  onSelectAll: () => void;
  isSelected: (id: string) => boolean;
  isAllSelected: () => boolean;
  someSelected: () => boolean;
  tableFields?: FieldDefinition[]; // 动态表格字段定义
  fieldDictionary?: Record<string, string>; // 字段字典
  isLoading?: boolean; // 添加加载状态
  onRfidManagement?: () => void; // RFID管理回调

  // 表格位置记忆相关属性
  rememberPosition?: boolean; // 是否记住位置
  initialPageIndex?: number; // 初始页码
  onPageChange?: (pageIndex: number) => void; // 页码变化回调
  lastOperatedItemId?: string; // 最后操作的项目ID
}



const InventoryTable: React.FC<InventoryTableProps> = ({
  items,
  columnVisibility,
  onEdit,
  onDelete,
  onSelect,
  onSelectAll,
  isSelected,
  isAllSelected,
  someSelected,
  tableFields = [],
  isLoading = false,
  onRfidManagement,
  rememberPosition = true,
  initialPageIndex = 0,
  onPageChange,
  lastOperatedItemId
}) => {


  const [sorting, setSorting] = React.useState<SortingState>([]);
  // 添加页面大小状态，默认为10
  const [pageSize, setPageSize] = React.useState<number>(10);

  // 列宽拖拽状态
  const [isResizing, setIsResizing] = useState(false);
  const [resizingColumn, setResizingColumn] = useState<string | null>(null);
  const [columnSizes, setColumnSizes] = useState<Record<string, number>>({});

  const columnHelper = createColumnHelper<InventoryItem>();

  // 列宽拖拽处理函数
  const handleMouseDown = useCallback((e: React.MouseEvent, columnId: string) => {
    e.preventDefault();
    setIsResizing(true);
    setResizingColumn(columnId);

    const startX = e.clientX;
    const startWidth = columnSizes[columnId] || 120; // 默认宽度120px

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(50, startWidth + deltaX); // 最小宽度50px

      setColumnSizes(prev => ({
        ...prev,
        [columnId]: newWidth
      }));
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setResizingColumn(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [columnSizes]);

  // 获取列宽度
  const getColumnWidth = useCallback((columnId: string, defaultWidth: number = 120) => {
    return columnSizes[columnId] || defaultWidth;
  }, [columnSizes]);

  // 创建通用的表头渲染函数
  const renderColumnHeader = useCallback(({ column }: { column: any }, title: string) => {
    return (
      <div className="flex items-center space-x-1 group relative w-full">
        <button
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="font-medium text-gray-900 flex items-center flex-1"
          style={{ whiteSpace: 'nowrap', fontSize: '0.95rem' }}
        >
          {title}
        </button>
        {/* 列宽调整拖拽手柄 */}
        {column.id !== 'actions' && (
          <div
            className={`column-resizer ${resizingColumn === column.id ? 'resizing' : ''}`}
            onMouseDown={(e) => handleMouseDown(e, column.id)}
          />
        )}
      </div>
    );
  }, [handleMouseDown, resizingColumn]);



  const columns = useMemo<ColumnDef<InventoryItem, any>[]>(() => {
    // 使用动态字段定义
    return tableFields.map(field => {
      if (field.key === 'select') {
        return columnHelper.display({
          id: 'select',
          header: () => (
            <input
              type="checkbox"
              checked={isAllSelected()}
              ref={(input) => {
                if (input) {
                  input.indeterminate = someSelected() && !isAllSelected();
                }
              }}
              onChange={onSelectAll}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={isLoading}
            />
          ),
          cell: ({ row }) => (
            <input
              type="checkbox"
              checked={isSelected(row.original.id)}
              onChange={() => onSelect(row.original.id)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={isLoading}
            />
          ),
          enableSorting: false,
          size: 40
        });
      } else if (field.key === 'actions') {
        return columnHelper.display({
          id: 'actions',
          header: () => (
            <div className="w-full text-center">
              <span className="font-medium text-gray-900" style={{ whiteSpace: 'nowrap', fontSize: '0.95rem' }}>
                操作
              </span>
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex justify-center space-x-0.5">
              <button
                onClick={() => onEdit(row.original.id)}
                className="p-0.5 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
                title="编辑"
                disabled={isLoading}
              >
                <Pencil className="h-3.5 w-3.5" />
              </button>
              <button
                onClick={() => onDelete(row.original.id)}
                className="p-0.5 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed"
                title="删除"
                disabled={isLoading}
              >
                <Trash className="h-3.5 w-3.5" />
              </button>
            </div>
          ),
          enableSorting: false,
          size: 50, // 进一步减小宽度，使操作列更紧凑
          // 添加固定列配置
          meta: {
            fixed: 'right'
          }
        });
      } else {
        // 动态扩展字段（以ext_开头）
        if (field.key.startsWith('ext_')) {
          const fieldKey = field.key.substring(4); // 移除"ext_"前缀



          return columnHelper.accessor(
            row => {
              // 从扩展字段中获取值，如果没有则返回空字符串
              if (row.extendedFields && row.extendedFields[fieldKey] !== undefined) {
                return row.extendedFields[fieldKey];
              }
              return '';
            },
            {
              id: field.key,
              header: ({ column }) => renderColumnHeader({ column }, field.title),
              cell: info => {
                const value = info.getValue();

                // 硬盘序列号字段特殊处理
                if (field.title === '硬盘序列号' || fieldKey === 'hard_drive_serial' || field.title.includes('硬盘序列号')) {
                  return <HardDriveSerialDisplay value={value || ''} />;
                }

                // 如果是时间字段或字段类型是date，进行时间格式化 - 扩展字段显示时分秒
                if ((isTimeField(field.title) || isTimeField(fieldKey) || field.type === 'date') && value) {
                  return formatTimeValue(value, true, true); // showTime=true, showSeconds=true
                }

                return value;
              },
              enableSorting: field.sortable !== false,
              size: field.width || 150
            }
          );
        }

        // 常规标准字段
        return columnHelper.accessor(
          (row) => row[field.key as keyof InventoryItem] as any,
          {
            id: field.key,
            header: ({ column }) => renderColumnHeader({ column }, field.title),
            cell: info => {
              const value = info.getValue();

              // 巡检状态字段特殊渲染
              if (field.key === 'inspectionStatus') {
                // 如果值为空，直接返回空内容
                if (!value || value.trim() === '') {
                  return '';
                }

                const statusBadge = (
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      value === '正常'
                        ? 'bg-green-50 text-green-700 border border-green-200'
                        : value === '异常'
                        ? 'bg-red-50 text-red-700 border border-red-200'
                        : value === '未知'
                        ? 'bg-gray-50 text-gray-700 border border-gray-200'
                        : 'bg-gray-50 text-gray-700 border border-gray-200'
                    }`}
                  >
                    {value}
                  </span>
                );

                // 如果是异常状态，添加悬浮提示（即使没有任务信息也显示）
                const rowData = info.row.original;
                if (value === '异常' && rowData.inspection_status === 1) {
                  // 使用实际的任务信息，如果没有则使用默认空值
                  const taskInfo = rowData.inspection_task_info || {
                    task_name: '',
                    inspection_start_date: 0
                  };

                  return (
                    <InspectionTooltip inspectionTaskInfo={taskInfo}>
                      {statusBadge}
                    </InspectionTooltip>
                  );
                }

                return statusBadge;
              }

              return value;
            },
            enableSorting: field.sortable !== false,
            size: field.width || 150
          }
        );
      }
    });
  }, [
    tableFields,
    renderColumnHeader,
    isLoading,
    isSelected,
    isAllSelected,
    someSelected,
    onSelect,
    onSelectAll,
    onEdit,
    onDelete
  ]);

  // 表格容器引用，用于滚动操作
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // 记录上一次的页码
  const prevPageIndexRef = useRef<number>(initialPageIndex);

  // 记录上一次操作的项目ID
  const prevOperatedItemIdRef = useRef<string | undefined>(lastOperatedItemId);

  // 页码输入框引用
  const pageInputRef = useRef<HTMLInputElement>(null);



  // 创建表格实例
  const table = useReactTable({
    data: items,
    columns,
    state: {
      sorting,
      columnVisibility,
      pagination: {
        pageIndex: initialPageIndex,
        pageSize: pageSize,
      }
    },
    onSortingChange: setSorting,
    onPaginationChange: (updater) => {
      const newState = typeof updater === 'function'
        ? updater(table.getState().pagination)
        : updater;

      // 调用页码变化回调
      if (onPageChange && newState.pageIndex !== prevPageIndexRef.current) {
        onPageChange(newState.pageIndex);
        prevPageIndexRef.current = newState.pageIndex;
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: false,

    // 禁用自动重置页码，确保数据变化时不会重置页码
    autoResetPageIndex: false,
  });

  // 确保在数据变化时保持页码
  useEffect(() => {
    // 如果有初始页码，设置表格页码
    if (initialPageIndex !== undefined && initialPageIndex !== table.getState().pagination.pageIndex) {
      table.setPageIndex(initialPageIndex);
    }
  }, [initialPageIndex, table, items.length]);

  // 当页码变化时，更新页码输入框的值
  useEffect(() => {
    const currentPageIndex = table.getState().pagination.pageIndex;
    // 更新页码输入框的值
    if (pageInputRef.current && pageInputRef.current.value !== (currentPageIndex + 1).toString()) {
      pageInputRef.current.value = (currentPageIndex + 1).toString();
    }
  }, [table.getState().pagination.pageIndex]);

  // 当页面大小变化时，确保表格状态更新
  useEffect(() => {
    if (table.getState().pagination.pageSize !== pageSize) {
      table.setPageSize(pageSize);
    }
  }, [pageSize, table]);

  // 当最后操作的项目ID变化时，查找并滚动到该项目
  useEffect(() => {
    if (!rememberPosition || !lastOperatedItemId || !tableContainerRef.current) return;

    // 如果操作的项目ID没有变化，不执行滚动
    if (lastOperatedItemId === prevOperatedItemIdRef.current) return;



    // 更新上一次操作的项目ID
    prevOperatedItemIdRef.current = lastOperatedItemId;

    // 查找操作的项目在当前页面中的索引
    const rowIndex = table.getRowModel().rows.findIndex(
      row => row.original.id === lastOperatedItemId
    );

    // 如果找到了项目
    if (rowIndex >= 0) {


      // 获取对应的DOM元素
      const rowElement = tableContainerRef.current.querySelector(`tbody tr:nth-child(${rowIndex + 1})`);

      // 如果找到了DOM元素，滚动到该元素
      if (rowElement) {
        setTimeout(() => {
          rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

        }, 100);
      }
    } else {
      // 如果在当前页面中没有找到项目，尝试在所有数据中查找
      const allRowIndex = items.findIndex(item => item.id === lastOperatedItemId);

      if (allRowIndex >= 0) {


        // 计算项目所在的页码
        const pageSize = table.getState().pagination.pageSize;
        const pageIndex = Math.floor(allRowIndex / pageSize);



        // 如果页码不同，切换到对应页码
        if (pageIndex !== table.getState().pagination.pageIndex) {

          table.setPageIndex(pageIndex);

          // 如果有页码变化回调，调用它
          if (onPageChange) {
            onPageChange(pageIndex);
          }

          // 切换页码后，需要等待DOM更新后再滚动
          setTimeout(() => {
            const rowElement = tableContainerRef.current?.querySelector(
              `tbody tr:nth-child(${allRowIndex % pageSize + 1})`
            );

            if (rowElement) {
              rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

            }
          }, 100);
        }
      } else {

      }
    }
  }, [lastOperatedItemId, items, rememberPosition, table, onPageChange]);

  // 添加自定义滚动条样式
  useEffect(() => {
    // 确保表格容器有足够的高度
    if (tableContainerRef.current) {
      // 计算表格容器应该的高度
      const updateTableHeight = () => {
        if (!tableContainerRef.current) return;

        // 获取表格容器的父元素
        const parentElement = tableContainerRef.current.parentElement;
        if (!parentElement) return;

        // 获取分页组件的高度
        const paginationElement = parentElement.querySelector('.pagination-container');
        const paginationHeight = paginationElement ? paginationElement.clientHeight : 0;

        // 获取提示栏的高度
        const tipElement = parentElement.querySelector('.tip-container');
        const tipHeight = tipElement ? tipElement.clientHeight : 0;

        // 计算表格容器应该的高度
        const parentHeight = parentElement.clientHeight;
        const tableHeight = parentHeight - paginationHeight - tipHeight;

        // 设置表格容器的高度
        tableContainerRef.current.style.height = `${tableHeight}px`;
      };

      // 初始化时更新高度
      updateTableHeight();

      // 监听窗口大小变化
      window.addEventListener('resize', updateTableHeight);

      // 清理函数
      return () => {
        window.removeEventListener('resize', updateTableHeight);
      };
    }
  }, []);

  // 初始化列宽度
  useEffect(() => {
    const initialSizes: Record<string, number> = {};
    tableFields.forEach(field => {
      if (field.width) {
        initialSizes[field.key] = field.width;
      }
    });
    setColumnSizes(initialSizes);
  }, [tableFields]);

  return (
    <div className="flex flex-col h-full relative">
      {/* 加载指示器 */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-20">
          <div className="flex flex-col items-center">
            <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
            <span className="mt-2 text-sm text-gray-600">正在加载数据...</span>
          </div>
        </div>
      )}

      {/* 双击编辑提示 */}
      <div className="px-4 bg-gray-50 text-gray-600 text-sm border-b tip-container flex items-center justify-between" style={{ paddingTop: '4px', paddingBottom: '4px' }}>
        <span>双击表格内容单元格可以快速编辑该条记录（勾选列和操作列除外）</span>
        <div className="flex items-center">
          {/* RFID码管理 - 优化响应速度 */}
          <button
            className="flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 hover:text-blue-800 hover:from-blue-100 hover:to-indigo-100 border border-blue-200 hover:border-blue-300 rounded-md shadow-sm hover:shadow-md transition-colors duration-100"
            onClick={onRfidManagement}
          >
            <Tag className="h-5 w-5" />
            <span className="text-sm font-medium">RFID码管理</span>
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-hidden" ref={tableContainerRef}>
        <CustomScrollbar
          className="table-custom-scrollbar h-full"
          horizontal={true}
          vertical={true}
          style={{ overflow: 'hidden' }}
        >
          <table className="min-w-full divide-y divide-gray-200 table-fixed">
            <thead className="bg-gray-50 sticky top-0 z-30">
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      scope="col"
                      className={`px-2 py-2 ${header.column.id === 'actions' ? 'text-center' : 'text-left'} text-xs font-medium text-gray-600 uppercase tracking-wider resizable-header ${
                        header.column.columnDef.meta?.fixed === 'right' ? 'table-fixed-column-right table-header' : ''
                      }`}
                      style={{
                        whiteSpace: 'nowrap',
                        width: `${getColumnWidth(header.column.id, header.column.getSize())}px`,
                        minWidth: `${getColumnWidth(header.column.id, header.column.getSize())}px`,
                        maxWidth: `${getColumnWidth(header.column.id, header.column.getSize())}px`,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}
                    >
                      {header.isPlaceholder ? null : (
                        <div className="flex items-center">
                          <div
                            className={`flex-1 flex items-center ${header.column.getCanSort() ? 'cursor-pointer select-none' : ''}`}
                            onClick={header.column.id !== 'actions' ? header.column.getToggleSortingHandler() : undefined}
                            style={{
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                          </div>

                        </div>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(() => {
                const rows = table.getRowModel().rows;
                return rows.length > 0 ? (
                  rows.map(row => (
                    <tr key={row.id} className="hover:bg-gray-50 group table-row">
                      {row.getVisibleCells().map(cell => (
                        <td
                          key={cell.id}
                          className={`px-2 py-1.5 whitespace-nowrap text-sm text-gray-500 ${
                            cell.column.columnDef.meta?.fixed === 'right' ? 'table-fixed-column-right' : ''
                          }`}
                          data-column-id={cell.column.id}
                          style={{
                            width: `${getColumnWidth(cell.column.id, cell.column.getSize())}px`,
                            minWidth: `${getColumnWidth(cell.column.id, cell.column.getSize())}px`,
                            maxWidth: `${getColumnWidth(cell.column.id, cell.column.getSize())}px`,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}
                          onDoubleClick={(e) => {
                            // 只有当单元格不是勾选列和操作列时才触发编辑
                            if (cell.column.id !== 'select' && cell.column.id !== 'actions') {
                              onEdit(row.original.id);
                            }
                            // 阻止事件冒泡
                            e.stopPropagation();
                          }}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </td>
                      ))}
                    </tr>
                  ))
                ) : (
                  <tr className="empty-row">
                    <td colSpan={table.getAllColumns().length} className="px-3 py-4 text-center text-sm text-gray-500">
                      没有匹配的数据
                    </td>
                  </tr>
                );
              })()}
            </tbody>
          </table>
        </CustomScrollbar>
      </div>

      {/* 分页组件 - 兼容旧版浏览器，降低高度与左边框一致 */}
      <div className="flex items-center justify-between px-4 py-1.5 bg-white border-t border-gray-200 mt-auto pagination-container" style={{ height: '40px' }}>
        <div className="flex items-center text-sm text-gray-700">
          <span>显示</span>
          {/* 使用完全自绘的下拉框组件 */}
          <CustomDropdown
            value={table.getState().pagination.pageSize}
            options={[20, 50, 100]}
            onChange={(newSize) => {
              // 处理"全部"选项的特殊情况
              if (newSize === -1) {
                // 设置为实际传入数据的总行数，显示所有筛选后的数据
                const totalRows = items.length || 1; // 至少设为1，避免除以0错误
                setPageSize(totalRows);
                table.setPageSize(totalRows);
              } else {
                // 普通页面大小或自定义大小
                setPageSize(newSize);
                table.setPageSize(newSize);
              }
            }}
            showAllOption={true}
            showCustomOption={true}
            totalItems={items.length}
            customRange={[1, 1000]}
          />
          <span>条，共 {items.length} 条</span>
        </div>

        <div className="flex items-center" style={{ gap: '8px' }}>
          {/* 首页按钮 */}
          <button
            style={{
              padding: '4px 8px',
              fontSize: '14px',
              borderRadius: '4px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              color: (table.getCanPreviousPage() && pageSize !== -1) ? '#374151' : '#9ca3af',
              cursor: (table.getCanPreviousPage() && pageSize !== -1) ? 'pointer' : 'not-allowed',
              opacity: (table.getCanPreviousPage() && pageSize !== -1) ? 1 : 0.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage() || pageSize === -1}
            title="首页"
          >
            <span style={{ fontSize: '16px', fontWeight: 'bold' }}>«</span>
          </button>

          {/* 上一页按钮 */}
          <button
            style={{
              padding: '4px 8px',
              fontSize: '14px',
              borderRadius: '4px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              color: (table.getCanPreviousPage() && pageSize !== -1) ? '#374151' : '#9ca3af',
              cursor: (table.getCanPreviousPage() && pageSize !== -1) ? 'pointer' : 'not-allowed',
              opacity: (table.getCanPreviousPage() && pageSize !== -1) ? 1 : 0.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage() || pageSize === -1}
            title="上一页"
          >
            <span style={{ fontSize: '16px', fontWeight: 'bold' }}>‹</span>
          </button>

          {/* 页码信息和页码输入框 */}
          <span className="text-sm text-gray-700" style={{ whiteSpace: 'nowrap', display: 'flex', alignItems: 'center' }}>
            第
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (pageInputRef.current) {
                  const value = parseInt(pageInputRef.current.value, 10);
                  // 当显示全部数据时（pageSize等于items.length且大于100），只允许输入1
                  if (table.getState().pagination.pageSize > 100 && table.getState().pagination.pageSize === items.length) {
                    pageInputRef.current.value = "1";
                    return;
                  }

                  if (!isNaN(value) && value > 0 && value <= table.getPageCount()) {
                    const pageIndex = value - 1;
                    table.setPageIndex(pageIndex);
                    if (onPageChange) {
                      onPageChange(pageIndex);
                    }
                  } else {
                    // 如果输入无效，重置为当前页码
                    pageInputRef.current.value = (table.getState().pagination.pageIndex + 1).toString();
                  }
                }
              }}
              style={{ display: 'inline-block', margin: '0 4px' }}
            >
              <input
                ref={pageInputRef}
                type="text"
                defaultValue={table.getState().pagination.pageIndex + 1}
                style={{
                  width: '40px',
                  padding: '2px 4px',
                  fontSize: '14px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  textAlign: 'center',
                  fontWeight: 'bold'
                }}
                onChange={(e) => {
                  // 只允许输入数字
                  const value = e.target.value.replace(/[^0-9]/g, '');
                  if (value !== e.target.value) {
                    e.target.value = value;
                  }
                }}
                onBlur={(e) => {
                  const value = parseInt(e.target.value, 10);
                  if (isNaN(value) || value < 1 || value > table.getPageCount()) {
                    e.target.value = (table.getState().pagination.pageIndex + 1).toString();
                  }
                }}
                title="输入页码后按回车键跳转"
              />
            </form>
            页，共 <span style={{ fontWeight: 'bold' }}>{pageSize === -1 ? 1 : table.getPageCount()}</span> 页
          </span>

          {/* 下一页按钮 */}
          <button
            style={{
              padding: '4px 8px',
              fontSize: '14px',
              borderRadius: '4px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              color: (table.getCanNextPage() && pageSize !== -1) ? '#374151' : '#9ca3af',
              cursor: (table.getCanNextPage() && pageSize !== -1) ? 'pointer' : 'not-allowed',
              opacity: (table.getCanNextPage() && pageSize !== -1) ? 1 : 0.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage() || pageSize === -1}
            title="下一页"
          >
            <span style={{ fontSize: '16px', fontWeight: 'bold' }}>›</span>
          </button>

          {/* 末页按钮 */}
          <button
            style={{
              padding: '4px 8px',
              fontSize: '14px',
              borderRadius: '4px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              color: (table.getCanNextPage() && pageSize !== -1) ? '#374151' : '#9ca3af',
              cursor: (table.getCanNextPage() && pageSize !== -1) ? 'pointer' : 'not-allowed',
              opacity: (table.getCanNextPage() && pageSize !== -1) ? 1 : 0.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage() || pageSize === -1}
            title="末页"
          >
            <span style={{ fontSize: '16px', fontWeight: 'bold' }}>»</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default InventoryTable;