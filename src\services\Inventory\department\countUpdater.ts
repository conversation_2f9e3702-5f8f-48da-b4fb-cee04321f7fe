import { InventoryItem } from '../../../types/inventory';
import { DepartmentCategory } from './types';
import { DepartmentUtils } from './utils';

/**
 * 部门计数更新器
 * 负责更新部门树中各节点的设备计数
 */
export class DepartmentCountUpdater {
  /**
   * 更新部门树的计数
   * @param inventoryList 设备列表
   * @param departmentCategories 部门分类树
   * @returns 是否成功更新
   */
  public static updateDepartmentTreeCounts(
    inventoryList: InventoryItem[],
    departmentCategories: DepartmentCategory[]
  ): boolean {
    // 检查部门树是否存在
    if (!departmentCategories.length) {
      console.log('部门树尚未加载，跳过计数更新');
      return false;
    }

    // 输出所有部门名称以便调试
    const allDepartmentNames: string[] = [];
    const departmentPersonMap: Record<string, string[]> = {};

    const collectDepartmentNames = (nodes: DepartmentCategory[]) => {
      for (const node of nodes) {
        if (node.id === 'all-dept' || node.id.startsWith('dept-')) {
          allDepartmentNames.push(node.name);
          departmentPersonMap[node.name] = [];

          // 收集该部门下的所有人员
          if (node.children) {
            for (const child of node.children) {
              if (child.id.startsWith('person-')) {
                departmentPersonMap[node.name].push(child.name);
                console.log(`收集到部门 ${node.name} 下的人员: ${child.name}, ID: ${child.id}`);
              }
            }
          }
        }
        if (node.children && node.children.length > 0) {
          collectDepartmentNames(node.children);
        }
      }
    };
    collectDepartmentNames(departmentCategories);
    console.log('部门树中的所有部门名称:', allDepartmentNames);
    console.log('部门下的人员节点:', departmentPersonMap);

    // 输出每个部门下的人员节点详情
    for (const [deptName, persons] of Object.entries(departmentPersonMap)) {
      console.log(`部门 ${deptName} 下的人员节点:`, persons);
    }

    // 输出设备数据的详细信息，用于调试
    console.log('设备列表详细信息:');
    inventoryList.forEach((item, index) => {
      console.log(`设备 ${index + 1}: ID=${item.id}, 名称=${item.name}, 部门=${item.department}, 责任人=${item.responsible}`);
    });

    // 输出设备列表中的所有部门名称和责任人
    const deviceDepartments = new Set<string>();
    const deviceResponsibles = new Map<string, Set<string>>();

    inventoryList.forEach(item => {
      if (item.department) {
        deviceDepartments.add(item.department);

        // 收集每个部门下的责任人
        if (!deviceResponsibles.has(item.department)) {
          deviceResponsibles.set(item.department, new Set<string>());
        }

        if (item.responsible) {
          deviceResponsibles.get(item.department)!.add(item.responsible);
        }
      }
    });

    console.log('设备列表中的所有部门名称:', Array.from(deviceDepartments));

    // 转换为对象以便于输出
    const deviceResponsiblesObj: Record<string, string[]> = {};
    deviceResponsibles.forEach((responsibles, department) => {
      deviceResponsiblesObj[department] = Array.from(responsibles);
    });
    console.log('设备列表中的部门和责任人:', deviceResponsiblesObj);

    // 检查是否有责任人不在部门树中
    const missingPersons: {department: string, responsible: string}[] = [];

    deviceResponsibles.forEach((responsibles, department) => {
      if (departmentPersonMap[department]) {
        // 部门存在于部门树中
        responsibles.forEach(responsible => {
          if (!departmentPersonMap[department].includes(responsible)) {
            // 责任人不在该部门的人员节点中
            missingPersons.push({department, responsible});
          }
        });
      }
    });

    if (missingPersons.length > 0) {
      console.log('以下责任人不在部门树中:', missingPersons);
    }

    try {
      // 深拷贝当前部门树
      const clonedCategories = JSON.parse(JSON.stringify(departmentCategories)) as DepartmentCategory[];

      // 缓存当前计数状态，用于比较是否有变化
      const currentCountsMap = new Map<string, number>();

      // 收集当前计数
      const collectCurrentCounts = (nodes: DepartmentCategory[]) => {
        for (const node of nodes) {
          currentCountsMap.set(node.id, node.count);
          if (node.children && node.children.length > 0) {
            collectCurrentCounts(node.children);
          }
        }
      };

      collectCurrentCounts(clonedCategories);

      // 初始化所有节点的计数为0
      const resetCounts = (nodes: DepartmentCategory[]) => {
        for (const node of nodes) {
          node.count = 0;
          if (node.children && node.children.length > 0) {
            resetCounts(node.children);
          }
        }
      };

      resetCounts(clonedCategories);

      // 按部门和责任人分组设备 - 使用Map而非Object来提高性能
      const departmentMap = new Map<string, InventoryItem[]>();
      const personMap = new Map<string, InventoryItem[]>();

      // 预处理设备列表，只遍历一次
      for (const item of inventoryList) {
        const department = item.department || '未分配部门';
        const responsible = item.responsible || '未分配责任人';
        const key = `${department}-${responsible}`;

        // 按部门分组
        if (!departmentMap.has(department)) {
          departmentMap.set(department, []);
        }
        departmentMap.get(department)!.push(item);

        // 按部门+责任人分组
        if (!personMap.has(key)) {
          personMap.set(key, []);
        }
        personMap.get(key)!.push(item);
      }

      // 缓存父节点查找结果，避免重复查找
      const parentCache = new Map<string, DepartmentCategory | undefined>();

      // 查找父节点的函数 - 使用原始部门树而不是深拷贝的部门树
      const findParentDepartment = (categories: DepartmentCategory[], targetId: string): DepartmentCategory | undefined => {
        // 如果缓存中存在，直接返回
        if (parentCache.has(targetId)) {
          return parentCache.get(targetId);
        }

        // 先在原始部门树中查找节点ID
        const findInOriginalTree = (cats: DepartmentCategory[], id: string): DepartmentCategory | undefined => {
          for (const cat of cats) {
            if (cat.children?.some((child: DepartmentCategory) => child.id === id)) {
              return cat;
            }
            if (cat.children?.length) {
              const found = findInOriginalTree(cat.children, id);
              if (found) return found;
            }
          }
          return undefined;
        };

        // 在原始部门树中查找
        const originalParent = findInOriginalTree(departmentCategories, targetId);
        if (originalParent) {
          // 找到原始父节点后，在深拷贝的树中查找对应的节点
          const findNodeInClonedTree = (cats: DepartmentCategory[], id: string): DepartmentCategory | undefined => {
            for (const cat of cats) {
              if (cat.id === id) {
                return cat;
              }
              if (cat.children?.length) {
                const found = findNodeInClonedTree(cat.children, id);
                if (found) return found;
              }
            }
            return undefined;
          };

          // 在深拷贝的树中查找对应的父节点
          const clonedParent = findNodeInClonedTree(clonedCategories, originalParent.id);
          if (clonedParent) {
            parentCache.set(targetId, clonedParent);
            return clonedParent;
          }
        }

        // 如果在原始树中找不到，尝试在深拷贝的树中直接查找
        for (const category of clonedCategories) {
          if (category.children?.some((child: DepartmentCategory) => child.id === targetId)) {
            parentCache.set(targetId, category);
            return category;
          }
          if (category.children?.length) {
            const found = findParentDepartment(category.children, targetId);
            if (found) {
              parentCache.set(targetId, found);
              return found;
            }
          }
        }

        parentCache.set(targetId, undefined);
        return undefined;
      };

      // 计算部门及其子部门的设备数量
      const calculateDepartmentCount = (category: DepartmentCategory): number => {
        // 如果不是部门节点，返回0
        if (!category.id.startsWith('dept-') && category.id !== 'all-dept') {
          return 0;
        }

        // 如果是根节点，计数为所有设备数量
        if (category.id === 'all-dept') {
          return inventoryList.length;
        }

        // 获取当前部门的完整路径
        const departmentPath = DepartmentUtils.getDepartmentPath(category, departmentCategories);
        console.log(`计算部门计数: ${category.name}, 路径: ${departmentPath}, ID: ${category.id}`);

        // 检查是否有同名部门
        const allSameNameDepartments = DepartmentUtils.findAllDepartmentsWithName(clonedCategories, category.name);
        console.log(`同名部门数量: ${allSameNameDepartments.length}`);

        // 获取当前部门及其所有子部门的完整路径列表
        const departmentPaths = DepartmentUtils.getAllDepartmentPaths(category, departmentCategories);
        console.log(`部门路径列表:`, departmentPaths);

        // 获取当前部门下的所有人员
        const departmentPersons = DepartmentUtils.getAllPersonsInDepartment(category);

        // 计算设备数量
        let count = 0;

        // 筛选属于当前部门的设备
        for (const item of inventoryList) {
          let matched = false;

          // 检查设备部门是否匹配当前部门（支持多部门显示）
          let departmentDirectMatch = false;
          if (item.department) {
            // 如果设备部门字段包含多个部门（用逗号分隔）
            const deviceDepartments = item.department.split(',').map(dept => dept.trim());
            departmentDirectMatch = deviceDepartments.includes(category.name);
          }

          if (departmentDirectMatch) {
            // 如果有多个同名部门，需要进一步判断
            if (allSameNameDepartments.length > 1) {
              // 检查设备的责任人是否属于当前部门
              if (departmentPersons.length > 0) {
                // 如果部门有人员，检查设备的责任人是否属于当前部门
                if (!item.responsible || departmentPersons.some(person => {
                  // 提取人员名称（不包含备注）
                  const personName = person.split(' (')[0];
                  return item.responsible === person || item.responsible!.startsWith(personName);
                })) {
                  matched = true;
                }
              } else {
                // 如果部门没有人员，检查设备的责任人是否也为空
                if (!item.responsible) {
                  // 检查设备的路径是否属于当前部门
                  // 如果设备没有责任人，且当前部门没有人员，则需要检查设备的路径
                  // 获取设备所属部门的所有可能路径
                  const possiblePaths = DepartmentUtils.findAllDepartmentsWithName(clonedCategories, item.department!);

                  // 检查设备所属部门的路径是否包含当前部门的路径
                  if (possiblePaths.some(path => path.startsWith(departmentPath) || departmentPath.startsWith(path))) {
                    matched = true;
                  }
                }
              }
            } else {
              // 如果只有一个同名部门，直接匹配
              matched = true;
            }
          }

          // 如果还没有匹配，检查设备部门名称是否与任何子部门名称匹配
          if (!matched) {
            // 遍历所有子部门路径
            for (const path of departmentPaths) {
              if (path === departmentPath) continue; // 跳过当前部门自身

              const pathParts = path.split('/');
              const subDeptName = pathParts[pathParts.length - 1]; // 获取路径中的最后一个部门名称

              // 检查设备部门是否匹配子部门（支持多部门显示）
              let subDeptMatch = false;
              if (item.department) {
                const deviceDepartments = item.department.split(',').map(dept => dept.trim());
                subDeptMatch = deviceDepartments.includes(subDeptName);
              }

              if (subDeptMatch) {
                // 如果有多个同名部门，需要进一步判断
                const sameDeptPaths = DepartmentUtils.findAllDepartmentsWithName(clonedCategories, subDeptName);

                if (sameDeptPaths.length > 1) {
                  // 找到当前路径对应的部门节点
                  const deptNode = DepartmentUtils.findDepartmentByPath(path, departmentCategories);
                  if (!deptNode) continue;

                  // 获取该部门下的所有人员
                  const subDeptPersons = DepartmentUtils.getAllPersonsInDepartment(deptNode);

                  if (subDeptPersons.length > 0) {
                    // 如果部门有人员，检查设备的责任人是否属于当前部门
                    if (!item.responsible || subDeptPersons.some(person => {
                      // 提取人员名称（不包含备注）
                      const personName = person.split(' (')[0];
                      return item.responsible === person || item.responsible!.startsWith(personName);
                    })) {
                      matched = true;
                      break;
                    }
                  } else {
                    // 如果部门没有人员，检查设备的责任人是否也为空
                    if (!item.responsible) {
                      // 检查设备的路径是否属于当前部门
                      if (path.includes(departmentPath)) {
                        matched = true;
                        break;
                      }
                    }
                  }
                } else {
                  // 如果只有一个同名部门，直接匹配
                  matched = true;
                  break;
                }
              }
            }
          }

          if (matched) {
            count++;
          }
        }

        console.log(`部门 ${category.name} 的计数结果: ${count}`);
        return count;
      };

      // 更新计数
      const updateCounts = (nodes: DepartmentCategory[]) => {
        for (const node of nodes) {
          if (node.id === 'all-dept' || node.id.startsWith('dept-')) {
            // 部门节点计数为该部门及其所有子部门的设备数量
            node.count = calculateDepartmentCount(node);

            // 部门节点计数已更新
          } else if (node.id.startsWith('person-')) {
            // 人员节点计数：该人员在当前部门下负责的设备数量
            const parentDept = DepartmentUtils.findParentDepartment(clonedCategories, node.id);
            const personName = node.name;

            if (!parentDept) {
              console.warn(`未找到人员节点 ${node.name} 的父部门，ID: ${node.id}`);
              node.count = 0;
              continue;
            }

            // 提取人员名称和备注
            let personBaseName = personName;
            let personAlias = '';

            const nameMatch = personName.match(/^([^(]+)/);
            if (nameMatch) {
              personBaseName = nameMatch[1].trim();
            }

            const aliasMatch = personName.match(/\(([^)]+)\)/);
            if (aliasMatch) {
              personAlias = aliasMatch[1];
            }

            // 获取当前部门名称
            const departmentName = parentDept.name;

            // 检查是否有同名部门
            const allSameNameDepartments = DepartmentUtils.findAllDepartmentsWithName(clonedCategories, departmentName);

            let count = 0;

            // 关键修复：只计算该人员在当前部门下负责的设备
            for (const item of inventoryList) {
              // 检查部门是否匹配（支持多部门显示）
              let departmentMatch = false;

              if (item.department) {
                // 如果设备部门字段包含多个部门（用逗号分隔）
                const deviceDepartments = item.department.split(',').map(dept => dept.trim());
                departmentMatch = deviceDepartments.includes(departmentName);
              }

              if (!departmentMatch) {
                continue; // 设备不属于当前部门，跳过
              }

              // 检查责任人是否匹配 - 使用统一的匹配逻辑
              const isResponsibleMatch = DepartmentUtils.isResponsibleMatch(item.responsible || '', personName);

              // 如果责任人匹配，且设备属于当前部门，则计数
              if (isResponsibleMatch) {
                count++;
                console.log(`人员计数匹配: 设备${item.id}, 责任人: ${item.responsible}, 部门: ${item.department}`);
              }
            }

            node.count = count;

            // 调试日志：显示人员在特定部门下的设备计数
            console.log(`人员 ${personName} 在部门 ${departmentName} 下负责的设备数量: ${count}`);
          } else {
            console.warn(`未知节点类型: ${node.id}, 名称: ${node.name}`);
          }

          // 递归处理子节点
          if (node.children && node.children.length > 0) {
            updateCounts(node.children);
          }
        }
      };

      updateCounts(clonedCategories);

      // 对部门树进行排序，确保人员节点排在部门节点前面
      DepartmentUtils.sortDepartmentTree(clonedCategories);

      // 检查计数是否有变化
      let hasChanges = false;

      const checkForChanges = (nodes: DepartmentCategory[]) => {
        for (const node of nodes) {
          const prevCount = currentCountsMap.get(node.id) || 0;
          if (prevCount !== node.count) {
            hasChanges = true;
            return; // 发现变化就可以提前返回
          }

          if (node.children && node.children.length > 0 && !hasChanges) {
            checkForChanges(node.children);
          }
        }
      };

      checkForChanges(clonedCategories);

      // 如果设备列表为空，强制更新部门树计数为0
      if (inventoryList.length === 0) {
        console.log('设备列表为空，强制更新部门树计数为0');
        hasChanges = true;
      }

      // 当计数有变化时或设备列表为空时更新状态
      if (hasChanges) {
        console.log('部门树计数更新完成');
        // 将更新后的分类树复制回原数组
        departmentCategories.splice(0, departmentCategories.length, ...clonedCategories);
        return true;
      } else {
        console.log('部门树计数无变化，跳过更新');
        return false;
      }
    } catch (error) {
      console.error('更新部门树计数失败:', error);
      return false;
    }
  }
}
