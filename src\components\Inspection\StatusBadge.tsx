import React from 'react';
import { LucideIcon } from 'lucide-react';

/**
 * 状态标签配置接口
 */
export interface StatusConfig {
  color: 'green' | 'blue' | 'yellow' | 'red' | 'gray';
  icon?: LucideIcon;
  text: string;
}

/**
 * 状态标签属性接口
 */
interface StatusBadgeProps {
  status: string;
  statusConfig: Record<string, StatusConfig>;
  className?: string;
}

/**
 * 巡检管理通用状态标签组件
 * 统一的状态显示样式
 */
const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  statusConfig,
  className = ''
}) => {
  const config = statusConfig[status] || {
    color: 'gray' as const,
    text: status
  };

  // 获取状态颜色样式
  const getColorClass = (color: StatusConfig['color']) => {
    switch (color) {
      case 'green':
        return 'bg-green-100 text-green-800';
      case 'blue':
        return 'bg-blue-100 text-blue-800';
      case 'yellow':
        return 'bg-yellow-100 text-yellow-800';
      case 'red':
        return 'bg-red-100 text-red-800';
      case 'gray':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const IconComponent = config.icon;

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getColorClass(config.color)} ${className}`}>
      {IconComponent && <IconComponent className="h-3 w-3 mr-1" />}
      {config.text}
    </span>
  );
};

export default StatusBadge;
