import mitt from 'mitt';
import { BaseService, BaseServiceEvents, BaseServiceState } from '../base/baseService';
import ServiceRegistry from '../serviceRegistry';

/**
 * 列可见性服务状态接口
 */
export interface ColumnVisibilityServiceState extends BaseServiceState {
  // 列可见性字典
  columnVisibility: Record<string, boolean>;
}

/**
 * 列可见性服务事件接口
 */
export interface ColumnVisibilityServiceEvents extends BaseServiceEvents<ColumnVisibilityServiceState> {
  'column-visibility-changed': Record<string, boolean>;
}

/**
 * 列可见性服务
 * 负责管理表格列的显示与隐藏，支持持久化存储
 */
class ColumnVisibilityService extends BaseService<ColumnVisibilityServiceState, ColumnVisibilityServiceEvents> {
  private static instance: ColumnVisibilityService;
  private readonly STORAGE_KEY = 'column_visibility_settings';

  /**
   * 构造函数
   */
  constructor() {
    // 从本地存储加载列可见性设置
    const savedSettings = ColumnVisibilityService.loadFromStorage();

    super('AccountTableDll', {
      isLoading: false,
      columnVisibility: savedSettings || {
        // 默认列可见性设置
        id: true,
        type: true,
        securityCode: true,
        securityLevel: true,
        responsible: true,
        name: true,
        model: true,
        department: true,
        location: true,
        startTime: true,
        status: true,
        purpose: true,
        securityRfid: true, // 安全码字段显示
        parentCategory: false // 设备类型分类（一级分类）默认不可见
      }
    });

    console.log('列可见性服务初始化，加载的设置:', this.state.columnVisibility);
  }

  /**
   * 从本地存储加载列可见性设置
   * @returns 保存的列可见性设置，如果没有则返回null
   */
  private static loadFromStorage(): Record<string, boolean> | null {
    try {
      const saved = localStorage.getItem('column_visibility_settings');
      if (saved) {
        const parsed = JSON.parse(saved);
        console.log('从本地存储加载列可见性设置:', parsed);
        return parsed;
      }
    } catch (error) {
      console.error('加载列可见性设置失败:', error);
    }
    return null;
  }

  /**
   * 保存列可见性设置到本地存储
   * @param settings 列可见性设置
   */
  private saveToStorage(settings: Record<string, boolean>): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(settings));
      console.log('列可见性设置已保存到本地存储:', settings);
    } catch (error) {
      console.error('保存列可见性设置失败:', error);
    }
  }

  /**
   * 获取单例实例
   * @returns ColumnVisibilityService实例
   */
  public static getInstance(): ColumnVisibilityService {
    if (!ColumnVisibilityService.instance) {
      ColumnVisibilityService.instance = new ColumnVisibilityService();
    }
    return ColumnVisibilityService.instance;
  }

  /**
   * 设置列可见性
   * @param columnId 列ID
   * @param isVisible 是否可见
   */
  public setColumnVisibility(columnId: string, isVisible: boolean): void {
    console.log(`设置列可见性: ${columnId} = ${isVisible}`);

    // 获取当前可见性状态，如果是undefined则视为true（默认可见）
    const currentVisibility = this.state.columnVisibility[columnId] === undefined ? true : this.state.columnVisibility[columnId];

    // 检查当前状态是否已经是目标状态
    if (currentVisibility === isVisible) {
      console.log(`列 ${columnId} 的可见性已经是 ${isVisible}，跳过更新`);
      return;
    }

    const columnVisibility = {
      ...this.state.columnVisibility,
      [columnId]: isVisible
    };

    this.updateState({ columnVisibility });

    // 保存到本地存储
    this.saveToStorage(columnVisibility);

    // 触发列可见性变更事件
    this.emitter.emit('column-visibility-changed', columnVisibility);
  }

  /**
   * 批量设置列可见性
   * @param visibilityMap 列可见性映射
   */
  public setMultipleColumnVisibility(visibilityMap: Record<string, boolean>): void {
    console.log('批量设置列可见性:', visibilityMap);

    // 检查是否有实际变化
    let hasChanges = false;
    for (const [key, value] of Object.entries(visibilityMap)) {
      if (this.state.columnVisibility[key] !== value) {
        hasChanges = true;
        break;
      }
    }

    if (!hasChanges) {
      console.log('列可见性没有变化，跳过更新');
      return;
    }

    const columnVisibility = {
      ...this.state.columnVisibility,
      ...visibilityMap
    };

    this.updateState({ columnVisibility });

    // 保存到本地存储
    this.saveToStorage(columnVisibility);

    // 触发列可见性变更事件
    this.emitter.emit('column-visibility-changed', columnVisibility);
  }

  /**
   * 重置列可见性
   * 将所有标准字段设置为可见，当前分类的扩展字段也设置为可见，其他扩展字段保持当前状态
   */
  public resetColumnVisibility(): void {
    console.log('重置列可见性');

    // 获取当前的列可见性
    const currentVisibility = { ...this.state.columnVisibility };

    // 将所有标准字段设置为可见，除了特殊字段
    const standardFields = [
      'id', 'type', 'securityCode', 'securityLevel', 'responsible',
      'name', 'model', 'department', 'location', 'startTime',
      'status', 'purpose'
    ];

    // 特殊处理的字段（隐藏的字段）
    const specialFields = {
      'parentCategory': false, // 设备类型分类（一级分类）始终保持隐藏
      'securityRfid': true     // 安全码字段显示
    };

    const resetVisibility: Record<string, boolean> = {};

    // 设置所有标准字段为可见
    standardFields.forEach(field => {
      resetVisibility[field] = true;
    });

    // 应用特殊字段设置
    Object.entries(specialFields).forEach(([field, visible]) => {
      resetVisibility[field] = visible;
    });

    // 保留扩展字段的当前可见性
    Object.entries(currentVisibility).forEach(([key, value]) => {
      if (key.startsWith('ext_')) {
        resetVisibility[key] = value;
      }
    });

    // 检查是否有实际变化
    let hasChanges = false;
    for (const [key, value] of Object.entries(resetVisibility)) {
      if (currentVisibility[key] !== value) {
        hasChanges = true;
        break;
      }
    }

    if (!hasChanges) {
      console.log('列可见性没有变化，跳过重置');
      return;
    }

    // 更新状态
    this.updateState({ columnVisibility: resetVisibility });

    // 保存到本地存储
    this.saveToStorage(resetVisibility);

    // 触发列可见性变更事件
    this.emitter.emit('column-visibility-changed', resetVisibility);

    console.log('列可见性已重置');
  }

  /**
   * 重置所有列可见性（包括扩展字段）
   * 将所有字段设置为可见
   */
  public resetAllColumnVisibility(): void {
    console.log('重置所有列可见性');

    // 获取当前的列可见性
    const currentVisibility = { ...this.state.columnVisibility };

    // 创建新的可见性对象，将所有字段设置为可见
    const resetVisibility: Record<string, boolean> = {};

    Object.keys(currentVisibility).forEach(key => {
      resetVisibility[key] = true;
    });

    // 检查是否有实际变化
    let hasChanges = false;
    for (const [key, value] of Object.entries(resetVisibility)) {
      if (currentVisibility[key] !== value) {
        hasChanges = true;
        break;
      }
    }

    if (!hasChanges) {
      console.log('所有列可见性已经是可见状态，跳过重置');
      return;
    }

    // 更新状态
    this.updateState({ columnVisibility: resetVisibility });

    // 保存到本地存储
    this.saveToStorage(resetVisibility);

    // 触发列可见性变更事件
    this.emitter.emit('column-visibility-changed', resetVisibility);

    console.log('所有列可见性已重置为可见');
  }

  /**
   * 重置当前分类的扩展字段列可见性
   * 将当前分类的扩展字段设置为可见，标准字段和其他分类的扩展字段保持当前状态
   * @param currentExtFieldKeys 当前分类的扩展字段键名数组
   */
  public resetCurrentCategoryExtFieldsVisibility(currentExtFieldKeys: string[]): void {
    if (!currentExtFieldKeys || currentExtFieldKeys.length === 0) {
      console.log('当前分类没有扩展字段，跳过重置');
      return;
    }

    console.log(`重置当前分类的 ${currentExtFieldKeys.length} 个扩展字段列可见性`);

    // 获取当前的列可见性
    const currentVisibility = { ...this.state.columnVisibility };

    // 创建新的可见性对象，将当前分类的扩展字段设置为可见
    const resetVisibility: Record<string, boolean> = { ...currentVisibility };

    // 设置当前分类的扩展字段为可见
    currentExtFieldKeys.forEach(key => {
      resetVisibility[key] = true;
    });

    // 检查是否有实际变化
    let hasChanges = false;
    for (const [key, value] of Object.entries(resetVisibility)) {
      if (currentVisibility[key] !== value) {
        hasChanges = true;
        break;
      }
    }

    if (!hasChanges) {
      console.log('当前分类的扩展字段列可见性没有变化，跳过重置');
      return;
    }

    // 更新状态
    this.updateState({ columnVisibility: resetVisibility });

    // 保存到本地存储
    this.saveToStorage(resetVisibility);

    // 触发列可见性变更事件
    this.emitter.emit('column-visibility-changed', resetVisibility);

    console.log('当前分类的扩展字段列可见性已重置');
  }

  /**
   * 初始化扩展字段的列可见性
   * @param extFieldKey 扩展字段键名
   * @param defaultVisible 默认是否可见
   */
  public initExtFieldColumnVisibility(extFieldKey: string, defaultVisible: boolean = true): void {
    // 如果该扩展字段的列可见性尚未定义，则初始化
    if (this.state.columnVisibility[extFieldKey] === undefined) {
      console.log(`初始化扩展字段 ${extFieldKey} 的列可见性为 ${defaultVisible}`);

      const columnVisibility = {
        ...this.state.columnVisibility,
        [extFieldKey]: defaultVisible
      };

      this.updateState({ columnVisibility });
    }
  }

  /**
   * 批量初始化扩展字段的列可见性
   * @param extFieldKeys 扩展字段键名数组
   * @param defaultVisible 默认是否可见
   */
  public initMultipleExtFieldColumnVisibility(extFieldKeys: string[], defaultVisible: boolean = true): void {
    const updates: Record<string, boolean> = {};

    extFieldKeys.forEach(key => {
      // 如果该扩展字段的列可见性尚未定义，则初始化
      if (this.state.columnVisibility[key] === undefined) {
        updates[key] = defaultVisible;
      }
    });

    // 如果有需要更新的字段，则更新状态
    if (Object.keys(updates).length > 0) {
      console.log(`批量初始化 ${Object.keys(updates).length} 个扩展字段的列可见性`);

      const columnVisibility = {
        ...this.state.columnVisibility,
        ...updates
      };

      this.updateState({ columnVisibility });
    }
  }

  /**
   * 获取列可见性
   * @returns 列可见性字典
   */
  public getColumnVisibility(): Record<string, boolean> {
    return { ...this.state.columnVisibility };
  }

  /**
   * 检查列是否可见
   * @param columnId 列ID
   * @returns 是否可见
   */
  public isColumnVisible(columnId: string): boolean {
    return this.state.columnVisibility[columnId] !== false;
  }

  /**
   * 清除本地存储的列可见性设置
   * 用于重置到默认状态
   */
  public clearStoredSettings(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('已清除本地存储的列可见性设置');
    } catch (error) {
      console.error('清除列可见性设置失败:', error);
    }
  }

  /**
   * 重置到默认设置并清除本地存储
   */
  public resetToDefaults(): void {
    console.log('重置列可见性到默认设置');

    const defaultSettings = {
      id: true,
      type: true,
      securityCode: true,
      securityLevel: true,
      responsible: true,
      name: true,
      model: true,
      department: true,
      location: true,
      startTime: true,
      status: true,
      purpose: true,
      securityRfid: true, // 安全码字段显示
      parentCategory: false
    };

    this.updateState({ columnVisibility: defaultSettings });
    this.saveToStorage(defaultSettings);
    this.emitter.emit('column-visibility-changed', defaultSettings);

    console.log('列可见性已重置到默认设置');
  }
}

// 注册服务
ServiceRegistry.getInstance().registerFactory('columnVisibilityService', () => ColumnVisibilityService.getInstance());

export default ColumnVisibilityService;
