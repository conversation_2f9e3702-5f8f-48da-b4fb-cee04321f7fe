import React from 'react';
import { RefreshCw } from 'lucide-react';
import CustomScrollbar from '../ui/CustomScrollbar';

/**
 * 表格数据行的基础接口
 */
export interface InspectionTableRecord {
  [key: string]: unknown;
}

/**
 * 表格列配置接口
 */
export interface InspectionTableColumn<T extends InspectionTableRecord = InspectionTableRecord> {
  key: string;
  title: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: unknown, record: T, index: number) => React.ReactNode;
}

/**
 * 表格属性接口
 */
interface InspectionTableProps<T extends InspectionTableRecord = InspectionTableRecord> {
  columns: InspectionTableColumn<T>[];
  data: T[];
  loading?: boolean;
  loadingText?: string;
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  className?: string;
  onRowClick?: (record: T, index: number) => void;
}

/**
 * 巡检管理通用表格组件
 * 统一的表格样式和交互
 */
const InspectionTable = <T extends InspectionTableRecord = InspectionTableRecord>({
  columns,
  data,
  loading = false,
  loadingText = '加载中...',
  emptyText = '暂无数据',
  emptyIcon,
  className = '',
  onRowClick
}: InspectionTableProps<T>) => {
  // 渲染表头
  const renderHeader = () => (
    <thead>
      <tr>
        {columns.map((column) => (
          <th
            key={column.key}
            className={`table-header-cell table-cell-${column.align || 'left'} ${column.width || ''}`}
          >
            {column.title}
          </th>
        ))}
      </tr>
    </thead>
  );

  // 渲染空状态
  const renderEmpty = () => (
    <tr className="adaptive-table-empty">
      <td colSpan={columns.length} className="px-4 py-12 text-center">
        <div className="flex flex-col items-center justify-center h-full min-h-[200px]">
          {emptyIcon || (
            <RefreshCw className={`h-8 w-8 mb-3 ${
              loading ? 'text-blue-600 animate-spin' : 'text-gray-400'
            }`} />
          )}
          <p className="text-gray-500 text-sm font-medium">
            {loading ? loadingText : emptyText}
          </p>
        </div>
      </td>
    </tr>
  );

  // 渲染数据行
  const renderRows = () => {
    if (data.length === 0) {
      return renderEmpty();
    }

    return data.map((record, index) => (
      <tr
        key={`${record.id || 'item'}-${index}-${record.inspector || record.task_name || ''}`}
        className={`relative ${onRowClick ? 'cursor-pointer' : ''}`}
        onClick={() => onRowClick?.(record, index)}
      >
        {columns.map((column) => (
          <td
            key={column.key}
            className={`table-cell table-cell-${column.align || 'left'} ${
              column.align === 'left' ? '' : 'whitespace-nowrap'
            }`}
          >
            {column.render
              ? column.render(record[column.key], record, index)
              : record[column.key] || '-'
            }
          </td>
        ))}
      </tr>
    ));
  };

  return (
    <div className={`flex-1 min-h-0 bg-white shadow-sm adaptive-table-container ${className}`}>
      <div className="adaptive-table-content">
        <CustomScrollbar
          className="h-full"
          horizontal={true}
          vertical={true}
        >
          <table className="w-full adaptive-table" style={{ minWidth: '800px' }}>
            {renderHeader()}
            <tbody className="bg-white">
              {renderRows()}
            </tbody>
          </table>
        </CustomScrollbar>
      </div>
    </div>
  );
};

export default InspectionTable;
