import React from 'react';
import { AlertCircle } from 'lucide-react';

/**
 * 巡检页面布局组件的属性接口
 */
interface InspectionPageLayoutProps {
  title: string;
  children: React.ReactNode;
  error?: string;
  toolbar?: React.ReactNode;
  className?: string;
}

/**
 * 巡检页面布局组件
 * 提供统一的Vben风格布局，消除重复代码
 */
const InspectionPageLayout: React.FC<InspectionPageLayoutProps> = ({
  title,
  children,
  error,
  toolbar,
  className = ''
}) => {
  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* 工具栏区域 */}
      {toolbar && (
        <div className="p-6 border-b border-gray-100">
          {toolbar}
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="mx-6 mt-4 border border-red-200 rounded-md bg-red-50 shadow-sm">
          <div className="p-4 flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-3" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
};

export default InspectionPageLayout;
