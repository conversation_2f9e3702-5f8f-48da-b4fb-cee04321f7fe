// 触发TypeScript重新检测该文件
// 确保导入正确识别

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useInventory } from './useInventory';
import { useDepartment } from './useDepartment';
import useDeviceService from './useDeviceService';
import useExtFieldService from './useExtFieldService';
import { DeviceCategory, InventoryItem } from '../../types/inventory';
import { DepartmentCategory } from '../../services/Inventory/departmentService';
import InventoryService from '../../services/Inventory/inventoryService';
import ExtFieldService from '../../services/Inventory/extFieldService';
import TreeCountManager from '../../services/Inventory/treeCountManager';
import DataFlowManager from '../../services/Inventory/dataFlowManager';
import { isDepartmentTreeEmpty } from '../../utils/departmentValidation';

/**
 * 自定义Hook - 库存管理
 * 连接服务层与UI组件，处理业务逻辑和状态管理
 */
export default function useInventoryManagement() {
  // 初始化标志，避免重复执行初始化逻辑
  const [isInitialized, setIsInitialized] = useState(false);

  // 设备分类相关状态和方法
  const {
    inventoryList,
    deviceCategories,
    currentCategory,
    columnVisibility,
    selectedItems,
    filteredItems: deviceFilteredItems,
    isLoading,
    filterByCategory,
    toggleSelectItem,
    setSearchQuery,
    addInventoryItem,
    updateInventoryItem,
    deleteInventoryItems,
    setColumnVisibility,
    resetColumnVisibility,
    isItemSelected,
    tableFields,
    fieldDictionary,
    loadInventory,
    currentExtFields,
    // getCategoryExtFields, // 未使用
    // setCurrentExtFields, // 未使用
    // clearCurrentExtFields, // 未使用
  } = useInventory();

  // 部门分类相关状态和方法
  const {
    departmentCategories,
    currentDepartmentCategory,
    filteredItems: departmentFilteredItems,
    filterByDepartment,
    // addDepartmentOrPerson, // 未使用
    addDepartment,
    addPerson,
    renameDepartmentOrPerson,
    deleteDepartmentOrPerson,
    // updateDepartmentPosition, // 未使用
    // updatePersonPosition, // 未使用
    handleDrop,
    confirmMove,
    cancelMove,
    showMoveConfirmDialog,
    moveConfirmDraggedNode,
    moveConfirmTargetNode,
    canDragNode,
    canDropNode
  } = useDepartment(inventoryList);

  // 设备服务相关状态和方法
  const {
    // isLoading: isServiceLoading, // 未使用
    // progress, // 未使用
    // addInventoryItemWithExt, // 未使用
    addDeviceType,
    addDeviceSubtype,
    renameCategory,
    deleteCategory,
    // 新增的方法
    updateParentCategory,
    deleteParentCategory,
    updateSubCategory,
    deleteSubCategory
  } = useDeviceService();

  // 分类模式：'device'表示设备分类，'department'表示部门分类
  const [categoryMode, setCategoryMode] = useState<'device' | 'department'>('department');

  // 表格位置记忆相关状态
  const [currentPageIndex, setCurrentPageIndex] = useState<number>(0);
  const [lastOperatedItemId, setLastOperatedItemId] = useState<string | undefined>(undefined);

  // 部门搜索查询状态
  const [departmentSearchQuery, setDepartmentSearchQuery] = useState<string>('');

  // 根据当前分类模式获取过滤后的设备列表
  const filteredItems = useMemo(() => {
    console.log(`useInventoryManagement.filteredItems 计算开始，categoryMode: ${categoryMode}`);

    if (categoryMode === 'device') {
      console.log(`设备分类模式，返回设备筛选结果: ${deviceFilteredItems.length}个`);
      return deviceFilteredItems;
    } else {
      console.log(`部门分类模式，departmentFilteredItems长度: ${departmentFilteredItems.length}`);
      console.log(`部门搜索查询: "${departmentSearchQuery}"`);

      // 部门分类模式下，如果有搜索查询，则进一步过滤
      if (!departmentSearchQuery) {
        console.log(`无搜索查询，直接返回部门筛选结果: ${departmentFilteredItems.length}个`);
        return departmentFilteredItems;
      }

      // 将搜索查询按中英文逗号分隔成多个条件
      const queries = departmentSearchQuery
        .replace(/，/g, ',') // 先将中文逗号替换为英文逗号
        .split(',')
        .map(q => q.toLowerCase().trim())
        .filter(q => q !== ''); // 过滤掉空字符串

      // 如果有有效的搜索条件
      if (queries.length > 0) {
        return departmentFilteredItems.filter(item => {
          // 对于每个条件，都需要至少有一个字段匹配
          return queries.every(query => {
            // 全局搜索：遍历所有字段进行搜索
            return Object.entries(item).some(([_, value]) => {
              // 跳过非字符串字段
              if (typeof value !== 'string') return false;

              // 将字符串转换为小写进行比较
              return value.toLowerCase().includes(query);
            });
          });
        });
      }

      return departmentFilteredItems;
    }
  }, [categoryMode, deviceFilteredItems, departmentFilteredItems, departmentSearchQuery]);

  // 通用的查找分类节点及其父节点函数
  const findCategoryById = useCallback(<T extends DeviceCategory | DepartmentCategory>(
    categories: T[],
    id: string
  ): { category: T, parent?: T } | null => {
    for (const category of categories) {
      if (category.id === id) {
        return { category };
      }

      if (category.children?.length) {
        for (const child of category.children) {
          if (child.id === id) {
            return { category: child as T, parent: category };
          }
        }

        const result = findCategoryById(category.children as T[], id);
        if (result) return result;
      }
    }
    return null;
  }, []);

  // UI状态
  const [showColumnSettings, setShowColumnSettings] = useState(false);
  const [searchInputValue, setSearchInputValue] = useState('');
  const [categorySearchValue, setCategorySearchValue] = useState('');

  // 对话框状态
  const [showAddDialog, _setShowAddDialog] = useState(false);
  const [showEditDialog, _setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDepartmentRequiredDialog, setShowDepartmentRequiredDialog] = useState(false);
  const [editItem, setEditItem] = useState<InventoryItem | null>(null);

  // 当前选中的分类信息
  const [selectedCategoryInfo, setSelectedCategoryInfo] = useState<{
    type: string,
    name: string,
    department?: string,
    responsible?: string,
    parentCategory?: string
  } | null>(null);

  // 分类管理对话框状态
  const [showAddCategoryDialog, setShowAddCategoryDialog] = useState(false);
  const [categoryDialogMode, setCategoryDialogMode] = useState<'type' | 'device'>('type');

  // 部门管理对话框状态
  const [showAddDepartmentDialog, setShowAddDepartmentDialog] = useState(false);
  const [departmentDialogMode, setDepartmentDialogMode] = useState<'department' | 'person'>('department');

  // 过滤分类树（支持设备分类和部门分类）
  const filteredCategories = useMemo(() => {
    // 获取当前分类模式下的分类树
    const currentCategories = categoryMode === 'device' ? deviceCategories : departmentCategories;

    if (!categorySearchValue.trim()) {
      return currentCategories;
    }

    // 搜索关键词
    const keyword = categorySearchValue.toLowerCase().trim();

    // 深拷贝分类树，以便进行过滤
    const deepCloneCategories = <T extends DeviceCategory | DepartmentCategory>(categories: T[]): T[] => {
      return categories.map(category => ({
        ...category,
        children: category.children ? deepCloneCategories(category.children) : undefined
      })) as T[];
    };

    // 递归检查是否匹配，匹配则返回true
    const checkCategoryMatch = <T extends DeviceCategory | DepartmentCategory>(category: T, keyword: string): boolean => {
      if (category.name.toLowerCase().includes(keyword)) {
        return true;
      }

      if (category.children && category.children.length > 0) {
        return category.children.some(child => checkCategoryMatch(child, keyword));
      }

      return false;
    };

    // 递归过滤分类树
    const filterCategoryTree = <T extends DeviceCategory | DepartmentCategory>(categories: T[]): T[] => {
      const result: T[] = [];

      for (const category of categories) {
        const clone = { ...category } as T;

        // 检查节点是否匹配
        const nodeMatches = category.name.toLowerCase().includes(keyword);

        // 递归过滤子节点
        if (category.children && category.children.length > 0) {
          clone.children = filterCategoryTree(category.children) as typeof category.children;
        }

        // 如果当前节点匹配或有匹配的子节点，则保留
        if (nodeMatches || (clone.children && clone.children.length > 0)) {
          result.push(clone);
        }
      }

      return result;
    };

    return filterCategoryTree(deepCloneCategories(currentCategories));
  }, [deviceCategories, departmentCategories, categoryMode, categorySearchValue]);

  // 判断是否搜索过滤状态
  const isFiltered = useMemo(() => categorySearchValue.trim() !== '', [categorySearchValue]);

  // 注意：handleSearchInputChange 函数未在组件中使用，已移除
  // 如果需要处理搜索输入变化，可以直接使用 setSearchInputValue 和 setSearchQuery

  // 处理页码变化
  const handlePageChange = useCallback((pageIndex: number) => {
    console.log('页码变化:', pageIndex);

    // 使用函数形式的setState，确保状态更新
    setCurrentPageIndex(prevPageIndex => {
      // 如果页码没有变化，不更新状态
      if (prevPageIndex === pageIndex) {
        return prevPageIndex;
      }

      console.log(`页码从 ${prevPageIndex} 变为 ${pageIndex}`);
      return pageIndex;
    });
  }, []);

  // 用于存储单行删除的ID
  const [singleDeleteId, setSingleDeleteId] = useState<string | null>(null);

  // 处理单行删除（右侧操作列中的删除按钮）
  const handleDelete = useCallback((id: string) => {
    // 如果id为空，关闭对话框
    if (!id) {
      setShowDeleteDialog(false);
      setSingleDeleteId(null);
      return;
    }

    // 存储要删除的单行ID，而不是修改选中状态
    setSingleDeleteId(id);
    setShowDeleteDialog(true);

    console.log(`准备删除单行数据: ${id}，不影响表格中已勾选的项目`);
  }, []);

  // 设置选中的项目
  const setSelectedItems = useCallback((ids: string[]) => {
    // 清除现有的选中项
    selectedItems.forEach(id => toggleSelectItem(id));

    // 添加新的选中项
    ids.forEach(id => {
      if (!isItemSelected(id)) {
        toggleSelectItem(id);
      }
    });
  }, [selectedItems, toggleSelectItem, isItemSelected]);

  // 处理批量删除
  const handleBatchDelete = useCallback(() => {
    // 如果对话框已经打开，则关闭
    if (showDeleteDialog) {
      setShowDeleteDialog(false);
      return;
    }

    if (selectedItems.length === 0) {
      alert('请先选择要删除的设备');
      return;
    }

    setShowDeleteDialog(true);
  }, [selectedItems.length, showDeleteDialog]);

  // 执行删除操作
  const executeDelete = useCallback(async () => {
    try {
      // 确定要删除的ID列表
      const idsToDelete = singleDeleteId
        ? [singleDeleteId] // 如果是单行删除，只删除该行
        : selectedItems;   // 如果是批量删除，删除所有选中项

      console.log('开始删除设备:', idsToDelete);

      // 记录删除前的相邻项目，用于删除后定位
      const findAdjacentItem = () => {
        if (idsToDelete.length === 0 || filteredItems.length === 0) return null;

        // 获取第一个要删除项的索引
        const firstId = idsToDelete[0];
        const selectedIndex = filteredItems.findIndex(item => item.id === firstId);

        if (selectedIndex === -1) return null;

        // 尝试获取下一个项目，如果没有则获取上一个项目
        const nextItem = filteredItems[selectedIndex + 1];
        const prevItem = selectedIndex > 0 ? filteredItems[selectedIndex - 1] : null;

        return nextItem || prevItem;
      };

      // 找到相邻项目
      const adjacentItem = findAdjacentItem();

      // 删除设备
      await deleteInventoryItems(idsToDelete);

      // 关闭对话框
      setShowDeleteDialog(false);

      // 重置单行删除ID
      if (singleDeleteId) {
        setSingleDeleteId(null);
      }

      // 如果有相邻项目，记录其ID用于表格位置记忆
      if (adjacentItem) {
        console.log('记录删除后的相邻项目ID:', adjacentItem.id);
        setLastOperatedItemId(adjacentItem.id);
      }

      // 不再在这里刷新，依赖事件系统触发的刷新逻辑
      console.log('设备删除成功，等待事件系统触发刷新...');
    } catch (error) {
      console.error('删除设备失败:', error);
      throw error;
    }
  }, [deleteInventoryItems, selectedItems, filteredItems, setLastOperatedItemId, singleDeleteId]);

  // 执行更新操作
  const executeUpdate = useCallback(async (id: string, updates: Partial<InventoryItem>) => {
    try {
      // 更新设备
      await updateInventoryItem(id, updates);

      // 关闭对话框
      setShowEditDialog(false);

      // 记录最后操作的项目ID，用于表格位置记忆
      console.log('记录最后操作的项目ID:', id);
      setLastOperatedItemId(id);

      // 不再在这里刷新，依赖事件系统触发的刷新逻辑
      console.log('设备更新成功，等待事件系统触发刷新...');
    } catch (error) {
      console.error('更新设备失败:', error);
      throw error;
    }
  }, [updateInventoryItem, setLastOperatedItemId]);

  // 处理设备分类选择
  const handleDeviceCategorySelect = useCallback((categoryId: string) => {
    // 获取当前selectedCategoryInfo的部门和责任人信息（如果有）
    const currentDepartment = selectedCategoryInfo?.department || '';
    const currentResponsible = selectedCategoryInfo?.responsible || '';

    // 获取InventoryService实例
    const inventoryService = InventoryService.getInstance();

    // 准备新的分类信息（保留部门和责任人信息）
    const newCategoryInfo = {
      type: '',
      name: '',
      department: currentDepartment,
      responsible: currentResponsible
    };

    // 重置页码到首页
    setCurrentPageIndex(0);
    console.log('切换设备分类节点，重置页码到首页');

    // 根据分类ID类型处理不同情况
    if (categoryId === 'all') {
      // 根节点 - 显示所有扩展字段
      console.log('选择根节点，显示所有扩展字段');

      // 更新分类信息
      setSelectedCategoryInfo(newCategoryInfo);

      // 调用过滤方法并更新表格字段，显示所有扩展字段
      // 注意：filterByCategory内部会调用inventoryService.filterByCategory，
      // 但不会更新扩展字段，所以我们需要手动更新
      filterByCategory(categoryId);

      // 清除当前选中的分类信息
      inventoryService.clearCurrentSelectedCategory();

      // 更新表格字段，显示所有扩展字段
      inventoryService.generateTableFields();

      // 更新树状图选中的节点信息
      inventoryService.updateTreeSelectedNode({
        id: 'all',
        name: '全部设备',
        type: 'device',
        level: 0
      });

      return;
    }

    // 使用通用的查找分类函数
    const result = findCategoryById(deviceCategories, categoryId);
    if (!result) return;

    const { category, parent } = result;

    // 判断节点类型并处理
    if (category.id.startsWith('parent-')) {
      if (!category.id.includes('-', 'parent-'.length)) {
        // 一级分类 - 显示该一级分类下所有二级分类的扩展字段
        console.log(`选择一级分类: ${category.name}`);

        // 一级分类不进行联动，清除设备类型信息
        newCategoryInfo.type = '';
        console.log('一级分类处理，设置newCategoryInfo.type为空字符串');

        // 先调用过滤方法
        filterByCategory(categoryId);

        // 然后更新表格字段，显示该一级分类下所有扩展字段
        inventoryService.generateTableFields({
          parentCategory: category.name
        });

        // 更新树状图选中的节点信息
        inventoryService.updateTreeSelectedNode({
          id: category.id,
          name: category.name,
          type: 'device',
          level: 1
        });
      }
      else {
        // 二级分类 - 显示该二级分类的扩展字段
        console.log(`选择二级分类: ${category.name}`);

        // 获取父分类名称
        const parentName = parent ? parent.name : '';
        const parentId = parent ? parent.id : '';

        if (parentName) {
          // 先调用过滤方法
          filterByCategory(categoryId);

          // 然后更新表格字段，只显示当前分类的扩展字段
          inventoryService.generateTableFields({
            parentCategory: parentName,
            subCategory: category.name
          });

          // 更新树状图选中的节点信息
          inventoryService.updateTreeSelectedNode({
            id: category.id,
            name: category.name,
            type: 'device',
            level: 2,
            parentId: parentId,
            parentName: parentName
          });
        }
      }
    }
    else {
      // 叶子节点（三级分类）- 设置分类信息
      console.log(`选择叶子节点: ${category.name}`);
      newCategoryInfo.type = category.name;

      // 调用过滤方法
      filterByCategory(categoryId);

      // 更新树状图选中的节点信息
      inventoryService.updateTreeSelectedNode({
        id: category.id,
        name: category.name,
        type: 'device',
        level: 3,
        parentId: parent ? parent.id : undefined,
        parentName: parent ? parent.name : undefined
      });
    }

    // 更新分类信息
    console.log('handleDeviceCategorySelect设置selectedCategoryInfo为:', newCategoryInfo);
    setSelectedCategoryInfo(newCategoryInfo);
  }, [filterByCategory, deviceCategories, selectedCategoryInfo]);

  // 处理部门分类选择
  const handleDepartmentCategorySelect = useCallback((categoryId: string) => {
    // 调用部门过滤方法
    filterByDepartment(categoryId);

    // 获取InventoryService实例
    const inventoryService = InventoryService.getInstance();

    // 重置页码到首页
    setCurrentPageIndex(0);
    console.log('切换部门分类节点，重置页码到首页');

    // 根据选中的部门ID不同来设置不同的预填信息
    if (categoryId === 'all-dept') {
      // 如果选择的是根节点，清空预填信息
      setSelectedCategoryInfo(null);

      // 更新树状图选中的节点信息
      inventoryService.updateTreeSelectedNode({
        id: 'all-dept',
        name: '全部部门',
        type: 'department',
        level: 0
      });
    } else {
      // 使用通用的查找分类函数获取选中的部门节点
      const result = findCategoryById(departmentCategories, categoryId);
      if (result && result.category) {
        const selectedDepartment = result.category;
        const { parent } = result;

        // 如果选择的是部门节点（以dept-开头）
        if (categoryId.startsWith('dept-')) {
          // 设置部门信息预填充
          setSelectedCategoryInfo({
            type: '', // 设备分类保持为空
            name: '', // 设备名称保持为空
            department: selectedDepartment.name, // 预填部门名称
            responsible: '' // 责任人保持为空
          });

          // 更新树状图选中的节点信息
          inventoryService.updateTreeSelectedNode({
            id: categoryId,
            name: selectedDepartment.name,
            type: 'department',
            level: 1,
            parentId: parent ? parent.id : undefined,
            parentName: parent ? parent.name : undefined
          });
        }
        // 如果选择的是责任人节点（以resp-开头）
        else if (categoryId.startsWith('resp-')) {
          // 从ID中提取部门和责任人名称
          const parts = categoryId.split('-');
          if (parts.length >= 3) {
            const departmentName = parts[1];
            const responsibleName = parts.slice(2).join('-'); // 处理责任人名称中可能包含连字符的情况

            // 同时预填部门和责任人
            setSelectedCategoryInfo({
              type: '', // 设备分类保持为空
              name: '', // 设备名称保持为空
              department: departmentName, // 预填部门名称
              responsible: responsibleName // 预填责任人名称
            });

            // 更新树状图选中的节点信息
            inventoryService.updateTreeSelectedNode({
              id: categoryId,
              name: responsibleName,
              type: 'department',
              level: 2,
              parentId: `dept-${departmentName}`,
              parentName: departmentName
            });
          }
        }
      }
    }
  }, [filterByDepartment, departmentCategories]);

  // 根据当前分类模式选择合适的处理函数
  const handleCategorySelect = useCallback((categoryId: string) => {
    if (categoryMode === 'device') {
      handleDeviceCategorySelect(categoryId);
    } else {
      handleDepartmentCategorySelect(categoryId);
    }
  }, [categoryMode, handleDeviceCategorySelect, handleDepartmentCategorySelect]);

  // 查找人员所属的部门
  const findParentDepartment = useCallback((categories: DepartmentCategory[], targetId: string): DepartmentCategory | undefined => {
    for (const cat of categories) {
      if (cat.children?.some(child => child.id === targetId)) {
        return cat;
      }
      if (cat.children?.length) {
        const found = findParentDepartment(cat.children, targetId);
        if (found) return found;
      }
    }
    return undefined;
  }, []);

  // 设置是否来自右键菜单的标志
  const setFromContextMenu = useCallback((fromContextMenu: boolean) => {
    setSelectedCategoryInfo(prev => ({
      ...prev,
      fromContextMenu
    }));
  }, []);

  // 处理添加
  const handleAdd = useCallback((fromContextMenu = false) => {

    // 检查部门分类树是否为空（只有在从总表添加时才检查）
    if (!fromContextMenu && isDepartmentTreeEmpty(departmentCategories)) {
      setShowDepartmentRequiredDialog(true);
      return;
    }

    // 构建初始分类信息
    let initialCategoryInfo: {
      type: string;
      name: string;
      department?: string;
      responsible?: string;
      parentCategory?: string;
      fromContextMenu?: boolean;
    } | null = null;

    // 如果当前选中了设备分类，并且不是根节点
    if (categoryMode === 'device' && currentCategory !== 'all') {
      // 使用通用的查找分类函数
      const result = findCategoryById(deviceCategories, currentCategory);

      if (result) {
        const {category, parent} = result;

        // 使用分类ID格式来判断是否为一级分类
        // 一级分类ID格式：parent-1, parent-2 等
        // 二级分类ID格式：parent-1-2, parent-1-3 等
        const isFirstLevelCategory = category.id.startsWith('parent-') && !category.id.includes('-', 'parent-'.length);

        console.log('分类判断:', {
          categoryId: category.id,
          categoryName: category.name,
          isFirstLevelCategory,
          parentName: parent?.name
        });

        if (isFirstLevelCategory) {
          // 一级分类 - 不进行联动，不设置任何分类信息
          console.log('检测到一级分类，不进行联动，设置initialCategoryInfo为null');
          initialCategoryInfo = null;
        } else {
          // 二级分类 - 进行联动
          console.log('检测到二级分类，进行联动');
          initialCategoryInfo = {
            type: category.name, // 设备类型应该是二级分类
            name: '', // 设备名称留空，由用户自定义输入
            parentCategory: parent?.name || '', // 保存一级分类信息
            fromContextMenu
          };
        }
      }
    } else if (categoryMode === 'department' && currentDepartmentCategory !== 'all') {
      // 如果选中了部门分类
      // 使用通用的查找分类函数
      const result = findCategoryById(departmentCategories, currentDepartmentCategory);

      if (result) {
        const {category, parent} = result;

        // 如果选中的是人员节点（以person-开头）
        if (category.id.startsWith('person-')) {
          // 找到人员所属的部门
          const parentDept = findParentDepartment(departmentCategories, category.id);

          if (parentDept) {
            // 设置部门和责任人信息
            initialCategoryInfo = {
              type: '',
              name: '',
              department: parentDept.name,
              responsible: category.name,
              fromContextMenu
            };
          } else {
            // 如果找不到部门，只设置责任人信息
            initialCategoryInfo = {
              type: '',
              name: '',
              responsible: category.name,
              fromContextMenu
            };
          }
        }
        // 如果选中的是部门节点（以dept-开头）
        else if (category.id.startsWith('dept-')) {
          initialCategoryInfo = {
            type: '',
            name: '',
            department: category.name,
            responsible: '',
            fromContextMenu
          };
        }
        // 如果是其他情况（可能是旧版的责任人节点，以resp-开头）
        else if (parent) {
          initialCategoryInfo = {
            type: '',
            name: '',
            department: parent.name,
            responsible: category.name,
            fromContextMenu
          };
        } else {
          // 一级分类或其他情况
          initialCategoryInfo = {
            type: '',
            name: '',
            department: category.name,
            responsible: '',
            fromContextMenu
          };
        }
      }
    }

    // 设置选中的分类信息，用于预填充表单
    setSelectedCategoryInfo(initialCategoryInfo);

    // 显示添加对话框
    setShowAddDialog(true);
  }, [
    categoryMode,
    currentCategory,
    currentDepartmentCategory,
    deviceCategories,
    departmentCategories,
    findParentDepartment
  ]);

  // 处理部门必需对话框确认
  const handleDepartmentRequiredConfirm = useCallback(() => {
    // 切换到部门分类树模式
    if (categoryMode !== 'department') {
      setCategoryMode('department');
    }
  }, [categoryMode]);

  // 使用扩展字段服务
  const {
    validateExtFields,
    convertExtFieldsToBackendFormat,
    getAllExtFields,
    formExtFields,
    // setFormExtFields: updateFormExtFields, // 未使用
    clearFormExtFields
  } = useExtFieldService();

  // 扩展字段值状态
  const [extFieldValues, setExtFieldValues] = useState<Record<string, any>>({});

  // 处理扩展字段变更
  const handleExtFieldChange = useCallback((key: string, value: any) => {
    setExtFieldValues(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // 重置扩展字段值
  const resetExtFieldValues = useCallback(() => {
    setExtFieldValues({});
  }, []);

  // 通用的对话框关闭处理函数
  const handleDialogClose = useCallback((show: boolean, dialogType: string, setDialogState: (show: boolean) => void) => {
    setDialogState(show);

    // 如果关闭对话框，清除表单扩展字段和扩展字段值
    if (!show) {
      console.log(`关闭${dialogType}对话框，清除表单扩展字段和扩展字段值`);
      resetExtFieldValues();
      clearFormExtFields();
    }
  }, [resetExtFieldValues, clearFormExtFields]);

  // 包装 setShowAddDialog，在关闭对话框时清除表单扩展字段
  const setShowAddDialog = useCallback((show: boolean) => {
    handleDialogClose(show, '添加', _setShowAddDialog);
  }, [handleDialogClose, _setShowAddDialog]);

  // 包装 setShowEditDialog，在关闭对话框时清除表单扩展字段
  const setShowEditDialog = useCallback((show: boolean) => {
    handleDialogClose(show, '编辑', _setShowEditDialog);
  }, [handleDialogClose, _setShowEditDialog]);

  // 处理编辑
  const handleEdit = useCallback((id: string) => {
    // 如果id为空，关闭对话框
    if (!id) {
      setShowEditDialog(false);
      return;
    }

    const item = inventoryList.find(item => item.id === id);
    if (item) {
      // 设置编辑项
      setEditItem(item);

      // 如果有扩展字段，设置扩展字段值
      if (item.extendedFields && Object.keys(item.extendedFields).length > 0) {
        console.log('设置扩展字段值:', item.extendedFields);

        // 处理扩展字段值，确保日期字段格式正确
        const processedExtFields: Record<string, any> = {};
        Object.entries(item.extendedFields).forEach(([key, value]) => {
          // 如果值不为空，则保留
          if (value !== null && value !== undefined && value !== '') {
            processedExtFields[key] = value;
            console.log(`扩展字段 ${key}:`, value, typeof value);
          }
        });

        setExtFieldValues(processedExtFields);
      } else {
        // 如果没有扩展字段，重置扩展字段值
        console.log('没有扩展字段，重置扩展字段值');
        resetExtFieldValues();
      }

      setShowEditDialog(true);
    }
  }, [inventoryList, resetExtFieldValues, setShowEditDialog, setEditItem]);

  // 执行添加操作（支持扩展字段）
  const executeAdd = useCallback(async (item: Omit<InventoryItem, 'id'>) => {
    try {
      // 验证扩展字段
      if (Object.keys(extFieldValues).length > 0 && formExtFields.length > 0) {
        const { isValid, errors } = validateExtFields(extFieldValues, formExtFields);
        if (!isValid) {
          // 显示验证错误
          const errorMessages = Object.values(errors).join('\n');
          alert(`扩展字段验证失败:\n${errorMessages}`);
          return;
        }
      }

      // 创建包含扩展字段的完整项目
      const completeItem: InventoryItem = {
        ...(item as InventoryItem),
        extendedFields: Object.keys(extFieldValues).length > 0
          ? convertExtFieldsToBackendFormat(extFieldValues)
          : {}
      };

      // 统一使用addInventoryItem方法，确保一致的处理流程
      const newItem = await addInventoryItem(completeItem);

      // 记录最后操作的项目ID，用于表格位置记忆
      if (newItem && newItem.id) {
        setLastOperatedItemId(newItem.id);
      }

      setShowAddDialog(false);
      resetExtFieldValues(); // 重置扩展字段值
      clearFormExtFields(); // 清除表单扩展字段
    } catch (error) {
      console.error('添加设备失败:', error);
    }
  }, [addInventoryItem, extFieldValues, resetExtFieldValues, setShowAddDialog, formExtFields, validateExtFields, convertExtFieldsToBackendFormat, clearFormExtFields, setLastOperatedItemId]);

  // 处理添加分类 - 添加一级分类
  const handleAddType = useCallback((fromContextMenu = false) => {
    // 如果对话框已经打开，则关闭
    if (showAddCategoryDialog) {
      setShowAddCategoryDialog(false);
      return;
    }

    setCategoryDialogMode('type');
    // 保存是否来自右键菜单的标志
    setFromContextMenu(fromContextMenu);
    setShowAddCategoryDialog(true);
  }, [showAddCategoryDialog, setFromContextMenu]);

  // 处理添加类型 - 添加二级分类
  const handleAddDevice = useCallback((fromContextMenu = false) => {
    setCategoryDialogMode('device');
    // 保存是否来自右键菜单的标志
    setFromContextMenu(fromContextMenu);
    setShowAddCategoryDialog(true);
  }, [setFromContextMenu]);

  // 添加分类或类型（新方法，根据类型调用不同API）
  const executeAddCategory = useCallback(async (parentId: string, categoryName: string, categoryType: 'type' | 'device', customIcon?: string) => {
    try {
      if (categoryType === 'type') {
        // 添加分类（一级分类）
        // 注意：新API不需要parentId，直接使用categoryName作为parent_category_name
        await addDeviceType(categoryName, customIcon);
      } else {
        // 添加类型（二级分类）
        // 获取父分类的名称
        let parentCategoryName = '';

        // 如果父ID不是'all'，则查找其名称
        if (parentId !== 'all') {
          // 使用通用的查找分类函数获取父分类
          const result = findCategoryById(deviceCategories, parentId);
          if (result && result.category) {
            parentCategoryName = result.category.name;
          }
        }

        // 如果找不到父分类名称，则使用ID作为名称
        if (!parentCategoryName) {
          parentCategoryName = parentId;
        }

        await addDeviceSubtype(parentCategoryName, categoryName, customIcon);
      }

      // 成功添加后关闭对话框，不刷新总表
      // 树状图的刷新由事件处理函数负责
      console.log('分类添加成功，关闭对话框');
      setShowAddCategoryDialog(false);
    } catch (error) {
      console.error('添加分类失败:', error);
      throw error;
    }
  }, [addDeviceType, addDeviceSubtype, deviceCategories]);

  // 处理添加部门
  const handleAddDepartment = useCallback((fromContextMenu = false) => {
    // 如果对话框已经打开，则关闭
    if (showAddDepartmentDialog) {
      setShowAddDepartmentDialog(false);
      return;
    }

    setDepartmentDialogMode('department');
    // 保存是否来自右键菜单的标志
    setFromContextMenu(fromContextMenu);
    setShowAddDepartmentDialog(true);
  }, [showAddDepartmentDialog, setFromContextMenu]);

  // 处理添加人员
  const handleAddPerson = useCallback((fromContextMenu = false) => {
    // 如果对话框已经打开，则关闭
    if (showAddDepartmentDialog) {
      setShowAddDepartmentDialog(false);
      return;
    }

    setDepartmentDialogMode('person');
    // 保存是否来自右键菜单的标志
    setFromContextMenu(fromContextMenu);
    setShowAddDepartmentDialog(true);
  }, [showAddDepartmentDialog, setFromContextMenu]);

  // 添加部门或人员（新方法，根据类型调用不同API）
  const executeAddDepartmentOrPerson = useCallback(async (parentId: string, name: string, itemType: 'department' | 'person', alias?: string, mobileNumber?: string, positionSecurityLevel?: number) => {
    try {
      if (itemType === 'department') {
        // 添加部门
        await addDepartment(parentId, name);
      } else {
        // 添加人员
        await addPerson(parentId, name, alias, mobileNumber, positionSecurityLevel);
      }

      setShowAddDepartmentDialog(false);
    } catch (error) {
      console.error(`添加${itemType === 'department' ? '部门' : '人员'}失败:`, error);
      throw error;
    }
  }, [addDepartment, addPerson]);

  // 切换列显示
  const toggleColumn = useCallback((columnId: string) => {
    // 获取当前可见性状态，如果是undefined则视为true（默认可见）
    const currentVisibility = columnVisibility[columnId] === undefined ? true : columnVisibility[columnId];
    console.log(`切换列可见性: ${columnId}, 当前状态: ${currentVisibility}`);

    // 切换为相反的状态
    setColumnVisibility(columnId, !currentVisibility);

    // 判断是否是全部设备总表
    const isAllDevices = !currentCategory || currentCategory === 'all';

    // 如果是全部设备总表，恢复全部设备总表的扩展字段
    if (isAllDevices) {
      // 使用setTimeout延迟执行，确保在列可见性更新后执行
      setTimeout(() => {
        const extFieldSvc = ExtFieldService.getInstance();
        extFieldSvc.restoreAllDevicesExtFields();
      }, 100);
    }
  }, [columnVisibility, setColumnVisibility, currentCategory]);

  // 通用的错误处理包装函数
  const withErrorHandling = useCallback(async <T extends any[], R>(
    operation: (...args: T) => Promise<R>,
    errorMessage: string,
    ...args: T
  ): Promise<R> => {
    try {
      // 执行操作
      const result = await operation(...args);
      // 操作成功后刷新设备列表
      await loadInventory();
      return result;
    } catch (error) {
      console.error(`${errorMessage}:`, error);
      throw error;
    }
  }, [loadInventory]);

  // 不重新加载数据的错误处理包装器（用于删除分类等操作）
  const withErrorHandlingNoReload = useCallback(async <T extends any[], R>(
    operation: (...args: T) => Promise<R>,
    errorMessage: string,
    ...args: T
  ): Promise<R> => {
    try {
      const result = await operation(...args);
      // 操作成功后不重新加载数据，避免不必要的网络请求
      console.log(`${errorMessage.replace('失败', '成功')}，跳过数据重新加载`);
      return result;
    } catch (error) {
      console.error(`${errorMessage}:`, error);
      throw error;
    }
  }, []);

  // 处理重命名设备分类
  const handleRenameCategory = useCallback(async (categoryId: string, newName: string) => {
    return withErrorHandling(
      renameCategory,
      '重命名分类失败',
      categoryId,
      newName
    );
  }, [renameCategory, withErrorHandling]);

  // 处理删除设备分类
  const handleDeleteCategory = useCallback(async (categoryId: string) => {
    return withErrorHandling(
      deleteCategory,
      '删除分类失败',
      categoryId
    );
  }, [deleteCategory, withErrorHandling]);

  // 统一的分类操作处理器 - 使用数据流管理器确保数据同步
  const handleCategoryOperation = useCallback(async <T extends any[], R>(
    operation: (...args: T) => Promise<R>,
    operationType: 'update' | 'delete',
    operationName: string,
    target: 'parent-category' | 'sub-category',
    ...args: T
  ): Promise<R> => {
    try {
      console.log(`开始执行${operationName}操作`);

      // 执行操作
      const result = await operation(...args);

      // 使用数据流管理器处理后续同步
      const dataFlowManager = DataFlowManager.getInstance();
      await dataFlowManager.processOperation({
        type: operationType === 'update' ? 'category-update' : 'category-delete',
        target,
        data: { operationName, args, result }
      });

      console.log(`${operationName}操作完成，数据流已同步`);
      return result;
    } catch (error) {
      console.error(`${operationName}失败:`, error);
      throw error;
    }
  }, []);

  // 处理重命名设备父类（一级分类）
  const handleUpdateParentCategory = useCallback(async (curParentCategoryName: string, newParentCategoryName: string) => {
    return handleCategoryOperation(
      updateParentCategory,
      'update',
      '重命名设备父类',
      'parent-category',
      curParentCategoryName,
      newParentCategoryName
    );
  }, [updateParentCategory, handleCategoryOperation]);

  // 处理删除设备父类（一级分类）
  const handleDeleteParentCategory = useCallback(async (parentCategoryName: string) => {
    return handleCategoryOperation(
      deleteParentCategory,
      'delete',
      '删除设备父类',
      'parent-category',
      parentCategoryName
    );
  }, [deleteParentCategory, handleCategoryOperation]);

  // 处理重命名设备子类（二级分类）
  const handleUpdateSubCategory = useCallback(async (curSubCategoryName: string, newSubCategoryName: string) => {
    return handleCategoryOperation(
      updateSubCategory,
      'update',
      '重命名设备子类',
      'sub-category',
      curSubCategoryName,
      newSubCategoryName
    );
  }, [updateSubCategory, handleCategoryOperation]);

  // 处理删除设备子类（二级分类）
  const handleDeleteSubCategory = useCallback(async (subCategoryName: string) => {
    return withErrorHandlingNoReload(
      deleteSubCategory,
      '删除设备子类失败',
      subCategoryName
    );
  }, [deleteSubCategory, withErrorHandlingNoReload]);

  // 处理重命名部门或人员
  const handleRenameDepartmentOrPerson = useCallback(async (categoryId: string, newName: string) => {
    return withErrorHandling(
      renameDepartmentOrPerson,
      '重命名部门或人员失败',
      categoryId,
      newName
    );
  }, [renameDepartmentOrPerson, withErrorHandling]);

  // 处理删除部门或人员
  const handleDeleteDepartmentOrPerson = useCallback(async (categoryId: string) => {
    return withErrorHandling(
      deleteDepartmentOrPerson,
      '删除部门或人员失败',
      categoryId
    );
  }, [deleteDepartmentOrPerson, withErrorHandling]);

  // 获取分类树和扩展字段
  useEffect(() => {
    // 避免重复初始化
    if (isInitialized) {
      return;
    }

    // 初始加载数据，使用延迟执行避免多次调用
    const initializeData = async () => {
      // 检查根节点验证状态
      try {
        // 检查全局状态而不是导入服务，避免循环依赖
        if ((globalThis as any).__rootNodeValidationFailed) {
          console.log('公司名称验证失败，停止数据初始化');
          return;
        }

        if (!(globalThis as any).__rootNodeValidated) {
          console.log('公司名称尚未验证通过，等待系统初始化完成');
          return;
        }
      } catch (error) {
        console.error('检查公司名称验证状态失败:', error);
        return;
      }

      // 如果已经有数据或正在加载，不重复加载
      if (inventoryList.length > 0 || isLoading) {
        console.log('已有数据或正在加载，跳过初始化加载');

        // 如果已有数据，检查是否需要更新分类树计数
        if (inventoryList.length > 0 && deviceCategories.length > 0) {
          console.log('已有数据，检查并更新分类树计数');
          const inventoryService = InventoryService.getInstance();
          // 立即更新计数，确保界面显示正确
          inventoryService.updateCategoryTreeCounts(true);
        }

        // 标记为已初始化
        setIsInitialized(true);
      } else {
        console.log('执行初始化加载数据');
        // 直接调用InventoryService的方法，避免依赖loadInventory函数
        const inventoryService = InventoryService.getInstance();

        // 开始加载数据
        await inventoryService.loadInventoryList();

        // 等待状态更新完成，确保数据已同步到服务状态
        await new Promise(resolve => setTimeout(resolve, 100));

        // 数据加载完成后，使用数据流管理器确保正确同步
        console.log('数据加载完成，使用数据流管理器同步所有计数');

        const dataFlowManager = DataFlowManager.getInstance();
        await dataFlowManager.forceSyncAll();

        // 标记为已初始化
        setIsInitialized(true);
        console.log('初始化完成，所有数据和计数已同步');
      }

      // 不再在这里加载扩展字段，而是在 inventoryService 中处理
      console.log('扩展字段已在 inventoryService 中加载，跳过重复加载');
    };

    // 使用setTimeout延迟执行，确保在渲染周期外调用
    const timer = setTimeout(initializeData, 500);

    return () => clearTimeout(timer);
  }, [inventoryList.length, isLoading, deviceCategories.length]);

  // 监听数据变化，自动更新分类树计数（避免与初始化冲突）
  useEffect(() => {
    // 在数据已存在且不在加载状态时更新，但避免初始化阶段的重复更新
    if (inventoryList.length > 0 && deviceCategories.length > 0 && !isLoading) {
      // 添加短延迟，避免与初始化逻辑冲突
      const timer = setTimeout(() => {
        console.log(`数据变化检测（后续更新）：设备数量=${inventoryList.length}, 分类树节点数量=${deviceCategories.length}`);

        // 使用统一的计数管理器，防抖更新
        const treeCountManager = TreeCountManager.getInstance();
        treeCountManager.requestUpdate('device', false); // 防抖更新设备分类树计数
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [inventoryList.length, deviceCategories.length, isLoading]);

  // 当分类模式切换时，重置搜索和清除另一个分类树的选中状态
  useEffect(() => {
    // 重置搜索
    setCategorySearchValue('');
    setSearchInputValue('');
    setSearchQuery('');
    setDepartmentSearchQuery('');

    // 获取服务实例
    const inventoryService = InventoryService.getInstance();

    // 清除另一个分类树的选中状态
    if (categoryMode === 'device') {
      // 切换到设备分类树，清除部门分类树的选中状态
      console.log('切换到设备分类树，清除部门分类树的选中状态');
      filterByDepartment('all-dept');
    } else {
      // 切换到部门分类树，清除设备分类树的选中状态
      console.log('切换到部门分类树，清除设备分类树的选中状态');
      filterByCategory('all');
      // 清除当前选中的分类信息
      inventoryService.clearCurrentSelectedCategory();
      // 更新表格字段，显示所有扩展字段
      inventoryService.generateTableFields();
    }
  }, [categoryMode, filterByCategory, filterByDepartment]);

  // 自定义的全选/取消全选方法，确保使用当前分类模式下的筛选结果
  const customToggleSelectAll = useCallback(() => {
    // 获取InventoryService实例
    const inventoryService = InventoryService.getInstance();

    // 使用当前的filteredItems（包含了部门分类树的筛选结果）
    console.log(`自定义全选/取消全选，当前分类模式: ${categoryMode}, 筛选结果数量: ${filteredItems.length}`);
    inventoryService.toggleSelectAll(filteredItems);
  }, [categoryMode, filteredItems]);

  // 自定义的判断是否所有项目都被选中的方法，确保使用当前分类模式下的筛选结果
  const customAreAllItemsSelected = useCallback(() => {
    if (filteredItems.length === 0) return false;
    return filteredItems.every(item => selectedItems.includes(item.id));
  }, [selectedItems, filteredItems]);

  // 自定义的判断是否有部分项目被选中的方法，确保使用当前分类模式下的筛选结果
  const customSomeItemsSelected = useCallback(() => {
    if (filteredItems.length === 0) return false;
    return filteredItems.some(item => selectedItems.includes(item.id)) && !customAreAllItemsSelected();
  }, [selectedItems, filteredItems, customAreAllItemsSelected]);

  return {
    // 基础状态
    inventoryList,
    deviceCategories,
    departmentCategories,
    currentCategory,
    currentDepartmentCategory,
    filteredItems,
    columnVisibility,
    selectedItems,
    isLoading,

    // UI状态
    categoryMode,
    showColumnSettings,
    searchInputValue,
    categorySearchValue,
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showDepartmentRequiredDialog,
    editItem,
    selectedCategoryInfo,
    showAddCategoryDialog,
    categoryDialogMode,
    showAddDepartmentDialog,
    departmentDialogMode,
    filteredCategories,
    isFiltered,

    // 表格位置记忆相关
    currentPageIndex,
    lastOperatedItemId,
    handlePageChange,

    // 方法
    setCategoryMode,
    toggleSelectItem,
    toggleSelectAll: customToggleSelectAll, // 使用自定义的全选方法
    setSearchQuery,
    setDepartmentSearchQuery,
    setSearchInputValue,
    setCategorySearchValue,
    setShowColumnSettings,
    setShowAddDialog,
    setShowEditDialog,
    setShowDepartmentRequiredDialog,
    toggleColumn,
    resetColumnVisibility,
    handleCategorySelect,
    handleAdd,
    handleDepartmentRequiredConfirm,
    handleEdit,
    handleDelete,
    handleBatchDelete,
    executeAdd,
    executeUpdate,
    executeDelete,
    handleAddType,
    handleAddDevice,
    executeAddCategory,
    handleAddDepartment,
    handleAddPerson,
    executeAddDepartmentOrPerson,
    handleRenameCategory,
    handleDeleteCategory,
    handleUpdateParentCategory,
    handleDeleteParentCategory,
    handleUpdateSubCategory,
    handleDeleteSubCategory,
    handleRenameDepartmentOrPerson,
    handleDeleteDepartmentOrPerson,
    handleDrop,
    confirmMove,
    cancelMove,
    showMoveConfirmDialog,
    moveConfirmDraggedNode,
    moveConfirmTargetNode,
    canDragNode,
    canDropNode,
    isItemSelected,
    areAllItemsSelected: customAreAllItemsSelected, // 使用自定义的判断方法
    someItemsSelected: customSomeItemsSelected, // 使用自定义的判断方法
    tableFields,
    fieldDictionary,
    loadInventory,

    // 扩展字段相关
    currentExtFields,
    extFieldValues,
    handleExtFieldChange,
    resetExtFieldValues,

    // 单行删除相关
    singleDeleteId
  };
}