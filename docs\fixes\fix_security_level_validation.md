# 岗位密级验证问题修复

## 问题描述
用户选择了岗位密级（如"重要涉密人员"），但系统仍然提示"岗位密级不能为空"。

## 根本原因
岗位密级的验证逻辑有缺陷，当用户选择"非涉密人员"（值为0）时，验证逻辑错误地认为这是空值。

### 问题代码
```typescript
// 错误的验证逻辑
if (formData.position_security_level === undefined || formData.position_security_level === null) {
  setError('岗位密级不能为空');
  return;
}

// 错误的验证逻辑
if (mode === 'person' && positionSecurityLevel === undefined) {
  return '岗位密级不能为空';
}
```

### 问题分析
- 岗位密级的有效值为：0（非涉密人员）、1（一般涉密人员）、2（重要涉密人员）、3（核心涉密人员）
- 当用户选择"非涉密人员"时，值为0
- JavaScript中，`0 === undefined` 为false，但在某些逻辑判断中可能被误判为空值

## 修复方案

### 1. 修复EditPersonDialog中的验证逻辑
**文件**: `src/pages/Inventory/InventoryManagement/Dialogs/EditPersonDialog.tsx`

```typescript
// 修复前
if (formData.position_security_level === undefined || formData.position_security_level === null) {
  setError('岗位密级不能为空');
  return;
}

// 修复后
if (typeof formData.position_security_level !== 'number') {
  setError('岗位密级不能为空');
  return;
}
```

### 2. 修复AddDepartmentDialog中的验证逻辑
**文件**: `src/hooks/Inventory/useAddDepartmentDialog.ts`

```typescript
// 修复前
if (mode === 'person' && positionSecurityLevel === undefined) {
  return '岗位密级不能为空';
}

// 修复后
if (mode === 'person' && typeof positionSecurityLevel !== 'number') {
  return '岗位密级不能为空';
}
```

### 3. 修复API参数传递逻辑
**文件**: `src/services/Inventory/department/crud.ts`

```typescript
// 修复前
params.action_params.position_security_level = positionSecurityLevel || 0;

// 修复后
params.action_params.position_security_level = typeof positionSecurityLevel === 'number' ? positionSecurityLevel : 0;
```

**文件**: `src/services/Inventory/departmentService.ts`

```typescript
// 修复前
position_security_level: positionSecurityLevel || 0,

// 修复后
position_security_level: typeof positionSecurityLevel === 'number' ? positionSecurityLevel : 0,
```

## 修复原理

### 类型检查方式
使用 `typeof value === 'number'` 来检查是否为有效的数字值：
- `typeof 0 === 'number'` → true（有效）
- `typeof 1 === 'number'` → true（有效）
- `typeof 2 === 'number'` → true（有效）
- `typeof 3 === 'number'` → true（有效）
- `typeof undefined === 'number'` → false（无效）
- `typeof null === 'number'` → false（无效）

### 优势
1. **准确性**：正确识别0作为有效值
2. **类型安全**：确保值是数字类型
3. **一致性**：所有相关代码使用统一的验证逻辑

## 测试验证

### 测试场景1：选择"非涉密人员"
1. 打开添加人员或编辑人员对话框
2. 选择岗位密级为"非涉密人员"
3. 填写其他必填信息
4. 点击保存
5. **预期结果**：不应该提示"岗位密级不能为空"

### 测试场景2：选择其他密级
1. 分别选择"一般涉密人员"、"重要涉密人员"、"核心涉密人员"
2. 填写其他必填信息
3. 点击保存
4. **预期结果**：正常保存，不提示验证错误

### 测试场景3：不选择密级
1. 不选择任何岗位密级（保持默认状态）
2. 填写其他必填信息
3. 点击保存
4. **预期结果**：应该提示"岗位密级不能为空"

## 相关组件

### SecurityLevelSelect组件
**文件**: `src/components/ui/SecurityLevelSelect.tsx`
- 组件本身工作正常
- 正确传递选中的数字值
- 支持0-3的有效值范围

### 岗位密级选项映射
```typescript
const securityLevelOptions = [
  { value: 0, label: '非涉密人员' },
  { value: 1, label: '一般涉密人员' },
  { value: 2, label: '重要涉密人员' },
  { value: 3, label: '核心涉密人员' }
];
```

## 总结
这个修复解决了岗位密级验证的核心问题，确保：
1. 所有有效的岗位密级值（0-3）都能正确通过验证
2. 只有真正未选择的情况才会触发验证错误
3. 代码逻辑更加清晰和类型安全
