import { useState, useCallback } from 'react';
import RegistrationService from '../../Registration/services/registrationService';

/**
 * 公告弹窗Hook状态接口
 */
interface UseAnnouncementDialogState {
  isAnnouncementDialogOpen: boolean;
  isActivationDialogOpen: boolean;
}

/**
 * 公告弹窗Hook返回值接口
 */
interface UseAnnouncementDialogReturn extends UseAnnouncementDialogState {
  // 弹窗控制方法
  openAnnouncementDialog: () => void;
  closeAnnouncementDialog: () => void;
  openActivationDialog: () => void;
  closeActivationDialog: () => void;
  
  // 操作方法
  handleActivateSystem: () => void;
  handleGoToRegistration: () => void;
}

/**
 * 公告弹窗Hook
 * Hook层：只负责弹窗控制和用户交互逻辑
 */
export const useAnnouncementDialog = (): UseAnnouncementDialogReturn => {
  // 状态管理
  const [state, setState] = useState<UseAnnouncementDialogState>({
    isAnnouncementDialogOpen: false,
    isActivationDialogOpen: false
  });

  // 获取服务实例
  const registrationService = RegistrationService.getInstance();

  /**
   * 更新状态的辅助函数
   */
  const updateState = useCallback((newState: Partial<UseAnnouncementDialogState>) => {
    setState(prev => ({ ...prev, ...newState }));
  }, []);

  /**
   * 打开公告弹窗
   */
  const openAnnouncementDialog = useCallback(() => {
    console.log('打开公告弹窗');
    updateState({ isAnnouncementDialogOpen: true });
  }, [updateState]);

  /**
   * 关闭公告弹窗
   */
  const closeAnnouncementDialog = useCallback(() => {
    console.log('关闭公告弹窗');
    updateState({ isAnnouncementDialogOpen: false });
  }, [updateState]);

  /**
   * 打开激活弹窗
   */
  const openActivationDialog = useCallback(() => {
    console.log('打开激活弹窗');
    updateState({ 
      isActivationDialogOpen: true,
      isAnnouncementDialogOpen: false // 关闭公告弹窗
    });
  }, [updateState]);

  /**
   * 关闭激活弹窗
   */
  const closeActivationDialog = useCallback(() => {
    console.log('关闭激活弹窗');
    updateState({ isActivationDialogOpen: false });
  }, [updateState]);

  /**
   * 处理激活系统按钮点击
   */
  const handleActivateSystem = useCallback(() => {
    console.log('用户点击激活系统');
    openActivationDialog();
  }, [openActivationDialog]);

  /**
   * 处理前往注册按钮点击
   */
  const handleGoToRegistration = useCallback(() => {
    console.log('用户点击前往注册');
    registrationService.openRegistrationUrl();
  }, [registrationService]);

  return {
    // 状态
    ...state,
    
    // 弹窗控制方法
    openAnnouncementDialog,
    closeAnnouncementDialog,
    openActivationDialog,
    closeActivationDialog,
    
    // 操作方法
    handleActivateSystem,
    handleGoToRegistration
  };
};
