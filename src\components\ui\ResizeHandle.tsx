import React, { useState, useEffect, useRef, useCallback } from 'react';

interface ResizeHandleProps {
  /**
   * 方向，horizontal表示水平拖动（调整宽度），vertical表示垂直拖动（调整高度）
   */
  direction?: 'horizontal' | 'vertical';

  /**
   * 拖动时的回调函数，返回拖动的距离
   */
  onResize: (delta: number) => void;

  /**
   * 拖动开始时的回调函数
   */
  onResizeStart?: () => void;

  /**
   * 拖动结束时的回调函数
   */
  onResizeEnd?: () => void;

  /**
   * 自定义样式
   */
  className?: string;

  /**
   * 最小尺寸限制
   */
  minSize?: number;

  /**
   * 最大尺寸限制
   */
  maxSize?: number;
}

/**
 * 可调整大小的拖拽手柄组件
 * 用于实现面板大小调整功能
 */
const ResizeHandle: React.FC<ResizeHandleProps> = ({
  direction = 'horizontal',
  onResize,
  onResizeStart,
  onResizeEnd,
  className = '',
  minSize,
  maxSize
}) => {
  // 是否正在拖拽
  const [isDragging, setIsDragging] = useState(false);
  // 是否悬停
  const [isHovering, setIsHovering] = useState(false);
  // 上一次鼠标位置
  const lastPositionRef = useRef<{ x: number; y: number } | null>(null);
  // 组件引用
  const handleRef = useRef<HTMLDivElement>(null);

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 记录初始鼠标位置
    lastPositionRef.current = { x: e.clientX, y: e.clientY };
    setIsDragging(true);

    // 调用拖动开始回调
    onResizeStart?.();

    // 添加鼠标样式
    if (direction === 'horizontal') {
      document.body.style.cursor = 'col-resize';
    } else {
      document.body.style.cursor = 'row-resize';
    }
  }, [direction, onResizeStart]);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !lastPositionRef.current) return;

    e.preventDefault();

    // 计算移动距离
    const deltaX = e.clientX - lastPositionRef.current.x;
    const deltaY = e.clientY - lastPositionRef.current.y;

    // 根据方向调用回调
    if (direction === 'horizontal') {
      onResize(deltaX);
    } else {
      onResize(deltaY);
    }

    // 更新上一次位置
    lastPositionRef.current = { x: e.clientX, y: e.clientY };
  }, [isDragging, direction, onResize]);

  // 处理鼠标释放事件
  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;

    setIsDragging(false);
    lastPositionRef.current = null;

    // 调用拖动结束回调
    onResizeEnd?.();

    // 恢复鼠标样式
    document.body.style.cursor = '';
  }, [isDragging, onResizeEnd]);

  // 添加和移除事件监听器
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 默认样式
  const defaultClassName = direction === 'horizontal'
    ? 'w-1 cursor-col-resize hover:bg-blue-300/30 active:bg-blue-400/30 transition-colors'
    : 'h-1 cursor-row-resize hover:bg-blue-300/30 active:bg-blue-400/30 transition-colors';

  return (
    <div
      ref={handleRef}
      className={`resize-handle-enhanced ${isDragging ? 'dragging' : ''} ${className} transition-all duration-150 ease-in-out`}
      style={{
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: direction === 'horizontal' ? 0 : 'auto',
        right: direction === 'horizontal' ? 'auto' : 0,
        zIndex: 15, // 提高z-index确保在滚动条之上
        // 优化拖拽区域的宽度，保持合适的抓取区域
        width: direction === 'horizontal' ? '6px' : '100%', // 从10px减小到6px
        height: direction === 'vertical' ? '6px' : '100%', // 从10px减小到6px
        cursor: direction === 'horizontal' ? 'col-resize' : 'row-resize',
        // 添加拖拽时的背景效果 - 使用更简洁的样式
        background: isDragging
          ? 'rgba(59, 130, 246, 0.3)'
          : isHovering
            ? 'rgba(59, 130, 246, 0.15)'
            : 'transparent',
        // 添加拖拽时的阴影效果
        boxShadow: isDragging
          ? '0 0 8px rgba(59, 130, 246, 0.5)'
          : isHovering
            ? '0 0 4px rgba(59, 130, 246, 0.3)'
            : 'none',
        // 添加边框效果 - 使用中心线样式
        borderLeft: (isDragging || isHovering) ? '1px solid rgba(59, 130, 246, 0.6)' : 'none',
        borderRight: (isDragging || isHovering) ? '1px solid rgba(59, 130, 246, 0.6)' : 'none',
        // 添加过渡效果
        transition: 'all 0.2s ease-in-out'
      }}
      onMouseDown={handleMouseDown}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      title={direction === 'horizontal' ? '拖动调整宽度' : '拖动调整高度'}
    >
      {/* 添加拖拽指示器 - 使用简洁的双竖线样式 */}
      {(isHovering || isDragging) && direction === 'horizontal' && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            display: 'flex',
            gap: '1px',
            pointerEvents: 'none'
          }}
        >
          <div style={{
            width: '1px',
            height: '20px',
            backgroundColor: isDragging ? '#3b82f6' : '#9ca3af',
            borderRadius: '0.5px'
          }} />
          <div style={{
            width: '1px',
            height: '20px',
            backgroundColor: isDragging ? '#3b82f6' : '#9ca3af',
            borderRadius: '0.5px'
          }} />
        </div>
      )}
    </div>
  );
};

export default ResizeHandle;
