import React, { createContext, useContext, useState, useCallback } from 'react';

interface CacheContextType {
  clearCache: () => void;
  clearPathCache: (path: string) => void;
  cache: Map<string, React.ReactNode>;
  expandedItems: string[];
  toggleExpanded: (path: string) => void;
}

const CacheContext = createContext<CacheContextType | null>(null);

export const CacheProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cache] = useState(() => new Map<string, React.ReactNode>());
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const clearCache = useCallback(() => {
    cache.clear();
  }, [cache]);

  const clearPathCache = useCallback((path: string) => {
    cache.delete(path);
  }, [cache]);

  const toggleExpanded = useCallback((path: string) => {
    setExpandedItems(prev => 
      prev.includes(path) 
        ? prev.filter(item => item !== path)
        : [...prev, path]
    );
  }, []);

  return (
    <CacheContext.Provider value={{ 
      cache, 
      clearCache, 
      clearPathCache,
      expandedItems,
      toggleExpanded
    }}>
      {children}
    </CacheContext.Provider>
  );
};

export const useCache = () => {
  const context = useContext(CacheContext);
  if (!context) {
    throw new Error('useCache must be used within a CacheProvider');
  }
  return context;
}; 