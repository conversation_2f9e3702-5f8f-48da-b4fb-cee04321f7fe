import React from 'react';
import WebSocketManager from '../utils/websocket';
import TaskManager from '../utils/taskManager';

interface ConnectionState {
    wsStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
    error?: string;
}

interface ConnectionContextValue {
    state: ConnectionState;
    ws: WebSocketManager;
    task: TaskManager;
    connect: () => Promise<void>;
    reconnect: () => Promise<void>;
}

const ConnectionContext = React.createContext<ConnectionContextValue | null>(null);

export const ConnectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [state, setState] = React.useState<ConnectionState>({
        wsStatus: 'disconnected'
    });

    const ws = React.useMemo(() => WebSocketManager.getInstance(), []);
    const task = React.useMemo(() => TaskManager.getInstance(), []);

    const connect = React.useCallback(async () => {
        try {
            setState(prev => ({ ...prev, wsStatus: 'connecting' }));
            await ws.connect();
            setState({
                wsStatus: 'connected'
            });
        } catch (error: any) {
            setState({
                wsStatus: 'error',
                error: error.message
            });
            throw error;
        }
    }, [ws]);

    const reconnect = React.useCallback(async () => {
        setState(prev => ({ ...prev, wsStatus: 'connecting' }));
        try {
            await ws.connect();
            setState(prev => ({ ...prev, wsStatus: 'connected', error: undefined }));
        } catch (error: any) {
            setState(prev => ({
                ...prev,
                wsStatus: 'error',
                error: error.message
            }));
            throw error;
        }
    }, [ws]);

    // 监听WebSocket状态变化
    React.useEffect(() => {
        const handleOpen = () => {
            setState(prev => ({ ...prev, wsStatus: 'connected', error: undefined }));
        };

        const handleClose = () => {
            setState(prev => ({ ...prev, wsStatus: 'disconnected' }));
        };

        const handleError = (error: any) => {
            setState(prev => ({
                ...prev,
                wsStatus: 'error',
                error: error.message || '连接错误'
            }));
        };

        ws.on('open', handleOpen);
        ws.on('close', handleClose);
        ws.on('error', handleError);

        // 初始化连接
        if (state.wsStatus === 'disconnected') {
            connect().catch(console.error);
        }

        return () => {
            ws.off('open', handleOpen);
            ws.off('close', handleClose);
            ws.off('error', handleError);
        };
    }, [ws, connect, state.wsStatus]);

    const value = React.useMemo(() => ({
        state,
        ws,
        task,
        connect,
        reconnect
    }), [state, ws, task, connect, reconnect]);

    return (
        <ConnectionContext.Provider value={value}>
            {children}
        </ConnectionContext.Provider>
    );
};

export const useConnection = () => {
    const context = React.useContext(ConnectionContext);
    if (!context) {
        throw new Error('useConnection must be used within a ConnectionProvider');
    }
    return context;
};

export default ConnectionContext; 