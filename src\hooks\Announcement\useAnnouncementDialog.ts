import { useState, useCallback } from 'react';
import RegistrationService from '../../services/Announcement/registrationService';
import SystemInitService from '../../services/systemInitService';
import { useAnnouncement } from './useAnnouncement';

/**
 * 公告弹窗Hook状态接口
 */
interface UseAnnouncementDialogState {
  isAnnouncementDialogOpen: boolean;
  isActivationDialogOpen: boolean;
}

/**
 * 公告弹窗Hook返回值接口
 */
interface UseAnnouncementDialogReturn extends UseAnnouncementDialogState {
  // 弹窗控制方法
  openAnnouncementDialog: () => void;
  closeAnnouncementDialog: () => void;
  openActivationDialog: () => void;
  closeActivationDialog: () => void;

  // 操作方法
  handleActivateSystem: () => void;
  handleGoToRegistration: () => void;
  handleActivationSuccess: () => void;
  handleActivationErrorRetry: () => void;

  // 公告相关状态和方法
  announcement: string;
  isAnnouncementLoading: boolean;
  announcementError: string | null;
  retryLoadAnnouncement: () => void;
}

/**
 * 公告弹窗Hook
 * Hook层：只负责弹窗控制和用户交互逻辑
 */
export const useAnnouncementDialog = (): UseAnnouncementDialogReturn => {
  // 状态管理
  const [state, setState] = useState<UseAnnouncementDialogState>({
    isAnnouncementDialogOpen: false,
    isActivationDialogOpen: false
  });

  // 获取服务实例
  const registrationService = RegistrationService.getInstance();
  const systemInitService = SystemInitService.getInstance();

  // 使用公告Hook
  const {
    announcement,
    isLoading: isAnnouncementLoading,
    error: announcementError,
    loadAnnouncement,
    retryLoadAnnouncement
  } = useAnnouncement();

  /**
   * 更新状态的辅助函数
   */
  const updateState = useCallback((newState: Partial<UseAnnouncementDialogState>) => {
    setState(prev => ({ ...prev, ...newState }));
  }, []);

  /**
   * 打开公告弹窗
   */
  const openAnnouncementDialog = useCallback(async () => {
    console.log('打开公告弹窗');
    updateState({ isAnnouncementDialogOpen: true });

    // 同时加载公告数据
    try {
      await loadAnnouncement();
    } catch (error) {
      console.error('加载公告失败:', error);
    }
  }, [updateState, loadAnnouncement]);

  /**
   * 关闭公告弹窗
   */
  const closeAnnouncementDialog = useCallback(() => {
    console.log('关闭公告弹窗');
    updateState({ isAnnouncementDialogOpen: false });
  }, [updateState]);

  /**
   * 打开激活弹窗
   */
  const openActivationDialog = useCallback(() => {
    console.log('打开激活弹窗');
    updateState({ 
      isActivationDialogOpen: true,
      isAnnouncementDialogOpen: false // 关闭公告弹窗
    });
  }, [updateState]);

  /**
   * 关闭激活弹窗
   */
  const closeActivationDialog = useCallback(() => {
    console.log('关闭激活弹窗');
    updateState({ isActivationDialogOpen: false });
  }, [updateState]);

  /**
   * 处理激活系统按钮点击
   */
  const handleActivateSystem = useCallback(() => {
    console.log('用户点击激活系统');
    openActivationDialog();
  }, [openActivationDialog]);

  /**
   * 处理前往注册按钮点击
   */
  const handleGoToRegistration = useCallback(() => {
    console.log('用户点击前往注册');
    registrationService.openRegistrationUrl();
  }, [registrationService]);

  /**
   * 处理激活成功后的操作
   */
  const handleActivationSuccess = useCallback(async () => {
    console.log('激活成功，开始重新初始化系统');

    // 关闭所有弹窗
    updateState({
      isAnnouncementDialogOpen: false,
      isActivationDialogOpen: false
    });

    try {
      // 重置系统状态，准备重新初始化
      console.log('重置系统状态...');

      // 重新初始化系统
      console.log('开始重新初始化系统...');
      await systemInitService.initialize();
      console.log('系统重新初始化完成');

      // 刷新页面以确保完全重新加载
      console.log('刷新页面以完全重新加载系统...');
      window.location.reload();

    } catch (error) {
      console.error('系统重新初始化失败:', error);
      // 如果初始化失败，仍然刷新页面
      console.log('初始化失败，强制刷新页面...');
      window.location.reload();
    }
  }, [updateState, systemInitService]);

  /**
   * 处理激活错误重试
   */
  const handleActivationErrorRetry = useCallback(() => {
    console.log('激活错误重试，返回公告页面');

    // 打开公告窗口，关闭激活窗口
    updateState({
      isActivationDialogOpen: false,
      isAnnouncementDialogOpen: true
    });
  }, [updateState]);

  return {
    // 状态
    ...state,

    // 弹窗控制方法
    openAnnouncementDialog,
    closeAnnouncementDialog,
    openActivationDialog,
    closeActivationDialog,

    // 操作方法
    handleActivateSystem,
    handleGoToRegistration,
    handleActivationSuccess,
    handleActivationErrorRetry,

    // 公告相关状态和方法
    announcement,
    isAnnouncementLoading,
    announcementError,
    retryLoadAnnouncement
  };
};
