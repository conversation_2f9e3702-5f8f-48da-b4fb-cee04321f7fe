import mitt, { Emitter } from 'mitt';
import TaskManager from '../../utils/taskManager';
import AnnouncementWebSocketManager from '../../utils/announcementWebSocket';

/**
 * 公司查询响应接口
 */
interface CompanyQueryResponse {
  success: boolean;
  timestamp: string;
  message: string;
  type: string;
  data?: {
    encrypted_company: string;
  };
  error_code?: string;
}

/**
 * DLL响应接口
 */
interface DllResponse {
  success: boolean;
  message: string;
}

/**
 * 激活服务状态接口
 */
interface ActivationServiceState {
  isLoading: boolean;
  isQueryingCompany: boolean;
  isActivating: boolean;
  error: string | null;
  companyName: string;
  encryptedData: string;
  activationSuccess: boolean;
}

/**
 * 激活服务事件接口
 */
interface ActivationServiceEvents {
  'state-change': ActivationServiceState;
  'company-query-success': { companyName: string; encryptedData: string };
  'company-query-failed': string;
  'activation-success': string;
  'activation-failed': string;
  'error': string;
  [key: string]: any;
}

/**
 * 激活服务类
 * Service层：只负责激活相关的数据操作和API调用
 */
class ActivationService {
  private static instance: ActivationService;
  private emitter: Emitter<ActivationServiceEvents> = mitt<ActivationServiceEvents>();
  private taskManager: TaskManager = TaskManager.getInstance();
  private announcementWS: AnnouncementWebSocketManager = AnnouncementWebSocketManager.getInstance();
  private state: ActivationServiceState = {
    isLoading: false,
    isQueryingCompany: false,
    isActivating: false,
    error: null,
    companyName: '',
    encryptedData: '',
    activationSuccess: false
  };

  // 请求超时配置
  private readonly REQUEST_TIMEOUT = 10000; // 10秒请求超时

  /**
   * 获取激活服务实例
   */
  public static getInstance(): ActivationService {
    if (!ActivationService.instance) {
      ActivationService.instance = new ActivationService();
    }
    return ActivationService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 获取当前状态
   */
  public getState(): ActivationServiceState {
    return { ...this.state };
  }

  /**
   * 更新状态
   */
  private updateState(newState: Partial<ActivationServiceState>): void {
    this.state = { ...this.state, ...newState };
    this.emitter.emit('state-change', this.state);

    if (newState.error) {
      this.emitter.emit('error', newState.error);
    }
  }



  /**
   * 发送WebSocket请求并等待响应
   */
  private async sendWebSocketRequest(request: any, expectedResponseType: string): Promise<any> {
    return await this.announcementWS.sendRequest(request, expectedResponseType, this.REQUEST_TIMEOUT);
  }

  /**
   * 查询公司名称
   */
  public async queryCompany(companyName: string): Promise<{ companyName: string; encryptedData: string }> {
    this.updateState({ 
      isQueryingCompany: true, 
      error: null,
      companyName: companyName.trim()
    });

    try {
      // 发送公司查询请求
      const request = {
        type: "company_query",
        company_name: companyName.trim()
      };

      const response: CompanyQueryResponse = await this.sendWebSocketRequest(request, 'company_query_response');

      // 检查响应
      if (!response.success) {
        const errorMessage = response.message || '公司名称查询失败';
        this.updateState({
          isQueryingCompany: false,
          error: errorMessage
        });
        this.emitter.emit('company-query-failed', errorMessage);
        throw new Error(errorMessage);
      }

      // 检查是否匹配成功
      if (response.message !== "公司名称匹配成功并已加密") {
        const errorMessage = response.message || '公司名称匹配失败';
        this.updateState({
          isQueryingCompany: false,
          error: errorMessage
        });
        this.emitter.emit('company-query-failed', errorMessage);
        throw new Error(errorMessage);
      }

      const encryptedData = response.data?.encrypted_company || '';
      if (!encryptedData) {
        throw new Error('未获取到加密数据');
      }

      // 更新状态
      this.updateState({
        isQueryingCompany: false,
        encryptedData,
        error: null
      });

      const result = { companyName: companyName.trim(), encryptedData };
      this.emitter.emit('company-query-success', result);

      console.log('公司查询成功:', result);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '公司查询失败';
      console.error('公司查询失败:', errorMessage);
      
      this.updateState({
        isQueryingCompany: false,
        error: errorMessage
      });

      this.emitter.emit('company-query-failed', errorMessage);
      throw error;
    }
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase(): Promise<void> {
    try {
      console.log('开始激活流程中的数据库初始化...');

      // 准备数据库初始化参数
      const params = {
        "action": "set_database_path",
        "action_params": {
          "path": "AccountTableDll.db"
        }
      };

      console.log('激活流程数据库初始化请求参数:', JSON.stringify(params));

      // 提交数据库初始化任务
      const taskId = await this.taskManager.submitTask('AccountTableDll', 'DbFun', params);

      // 等待任务完成
      await new Promise<void>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            console.log('激活流程数据库初始化成功');
            resolve();
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            this.taskManager.offTaskUpdate(taskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || '激活流程数据库初始化失败'));
          }
        };
        this.taskManager.onTaskUpdate(taskId, handleTaskUpdate);
      });

    } catch (error) {
      console.error('激活流程数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 激活系统（包含数据库初始化）
   */
  public async activateSystemWithDatabaseInit(companyName: string, encryptedData: string): Promise<string> {
    this.updateState({
      isActivating: true,
      error: null
    });

    try {
      console.log('开始激活系统（包含数据库初始化）:', { companyName, encryptedData });

      // 步骤1: 先初始化数据库
      console.log('激活流程 - 步骤1: 初始化数据库');
      await this.initializeDatabase();

      // 步骤2: 再进行系统激活
      console.log('激活流程 - 步骤2: 进行系统激活');
      return await this.activateSystem(companyName, encryptedData);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '激活系统失败';
      console.error('激活系统（包含数据库初始化）失败:', errorMessage);

      this.updateState({
        isActivating: false,
        error: errorMessage
      });

      this.emitter.emit('activation-failed', errorMessage);
      throw error;
    }
  }

  /**
   * 激活系统
   */
  public async activateSystem(companyName: string, encryptedData: string): Promise<string> {
    this.updateState({
      isActivating: true,
      error: null
    });

    try {
      console.log('开始激活系统:', { companyName, encryptedData });

      // 构建DLL请求参数
      const params = {
        companyName,
        encryptedData
      };

      console.log('调用DLL激活参数:', params);

      // 调用DLL函数并等待结果
      const taskId = await this.taskManager.submitTask('ExportDll', 'DecryptAndStoreToDatabaseWithCompany', params);
      console.log('DLL任务提交成功，任务ID:', taskId);

      // 等待任务完成并获取结果
      const result = await this.taskManager.waitForTaskResult(taskId);

      // TaskManager已经解析过JSON，直接使用结果
      const response = result as DllResponse;
      console.log('DLL激活响应:', response);

      // 检查激活结果
      if (!response.success) {
        const errorMessage = response.message || '系统激活失败';
        this.updateState({
          isActivating: false,
          error: errorMessage
        });
        this.emitter.emit('activation-failed', errorMessage);
        throw new Error(errorMessage);
      }

      // 激活成功
      const successMessage = response.message || '系统激活成功';
      this.updateState({
        isActivating: false,
        activationSuccess: true,
        error: null
      });

      this.emitter.emit('activation-success', successMessage);

      console.log('系统激活成功:', successMessage);
      return successMessage;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '系统激活失败';
      console.error('系统激活失败:', errorMessage);
      
      this.updateState({
        isActivating: false,
        error: errorMessage
      });

      this.emitter.emit('activation-failed', errorMessage);
      throw error;
    }
  }



  /**
   * 重置状态
   */
  public resetState(): void {
    this.updateState({
      isLoading: false,
      isQueryingCompany: false,
      isActivating: false,
      error: null,
      companyName: '',
      encryptedData: '',
      activationSuccess: false
    });
  }

  /**
   * 事件订阅
   */
  public on<K extends keyof ActivationServiceEvents>(
    event: K,
    callback: (data: ActivationServiceEvents[K]) => void
  ): void {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消事件订阅
   */
  public off<K extends keyof ActivationServiceEvents>(
    event: K,
    callback: (data: ActivationServiceEvents[K]) => void
  ): void {
    this.emitter.off(event, callback as any);
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    this.emitter.all.clear();
  }
}

export default ActivationService;
