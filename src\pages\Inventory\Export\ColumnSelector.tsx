import React, { useState, useEffect, useCallback } from 'react';
import { GripVertical } from 'lucide-react';
import { FieldDefinition } from '../../../types/inventory';
import { useCustomDrag } from '../../../hooks/base/useCustomDrag';
import '../../../styles/customDrag.css';
import '../../../styles/exportColumnPreview.css';

// 列选择器属性
interface ColumnSelectorProps {
  columns: FieldDefinition[];
  selectedColumns: string[];
  onSelectColumns: (columnIds: string[]) => void;
  onReorderColumns: (columnIds: string[]) => void;
  title?: string;
  onReset?: () => void; // 添加重置函数属性
}

/**
 * 列选择器组件
 * 用于在导出时选择和排序表格列
 */
const ColumnSelector: React.FC<ColumnSelectorProps> = ({
  columns,
  selectedColumns,
  onSelectColumns,
  onReorderColumns,
  title = '选择导出列',
  onReset
}) => {
  // 本地状态 - 用于拖拽排序
  const [orderedColumns, setOrderedColumns] = useState<string[]>([...selectedColumns]);
  // 所有可用列
  const [allColumns, setAllColumns] = useState<FieldDefinition[]>([]);

  // 使用自定义拖拽Hook
  const {
    dragState,
    hoverTargetId,
    canDropOnTarget,
    dragHandlers,
    resetDragState
  } = useCustomDrag({
    enabled: true,
    onDrop: (targetId, draggedId) => {
      // 将ID转换为索引
      const draggedIndex = orderedColumns.indexOf(draggedId);
      const dropIndex = orderedColumns.indexOf(targetId);

      if (draggedIndex !== -1 && dropIndex !== -1 && draggedIndex !== dropIndex) {
        // 创建新的排序数组
        const newOrderedColumns = [...orderedColumns];
        newOrderedColumns.splice(draggedIndex, 1);
        newOrderedColumns.splice(dropIndex, 0, draggedId);

        setOrderedColumns(newOrderedColumns);
        onReorderColumns(newOrderedColumns);
      }
    },
    onDragStart: (_, data) => {
      // 拖拽开始时的处理
      console.log('开始拖拽列:', data?.title);
    },
    onDragEnd: () => {
      // 拖拽结束时的处理
      console.log('列拖拽结束');
    },
    canDrag: (id) => selectedColumns.includes(id),
    canDrop: (draggedId, targetId) => {
      return draggedId !== targetId && selectedColumns.includes(targetId);
    },
    dragImageStyle: {
      position: 'absolute',
      pointerEvents: 'none',
      zIndex: '9999',
      opacity: '0.8',
      backgroundColor: '#dbeafe',
      border: '1px solid #93c5fd',
      borderRadius: '4px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      padding: '4px 8px',
      maxWidth: '200px',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      fontSize: '0.75rem',
      color: '#1e40af'
    },
    dragOpacity: 0.6,
    dragCursor: 'grabbing'
  });

  // 初始化所有可用列
  useEffect(() => {
    const filteredCols = columns.filter(col =>
      col.key !== 'select' && col.key !== 'actions' && col.key !== '_selected'
    );
    setAllColumns(filteredCols);
  }, [columns]);

  // 当selectedColumns变化时更新本地排序
  useEffect(() => {
    // 使用Set确保没有重复的key
    const uniqueSelectedColumns = Array.from(new Set(selectedColumns));

    // 创建一个新的有序列数组
    const newOrderedColumns: string[] = [];

    // 首先添加已经在orderedColumns中且仍在selectedColumns中的列，保持原有顺序
    orderedColumns.forEach(colId => {
      if (uniqueSelectedColumns.includes(colId) && !newOrderedColumns.includes(colId)) {
        newOrderedColumns.push(colId);
      }
    });

    // 然后添加任何不在newOrderedColumns中但在selectedColumns中的列
    uniqueSelectedColumns.forEach(colId => {
      if (!newOrderedColumns.includes(colId)) {
        newOrderedColumns.push(colId);
      }
    });

    setOrderedColumns(newOrderedColumns);
  }, [selectedColumns]);

  // 切换列选择状态
  const toggleColumnSelection = useCallback((columnId: string) => {
    // 如果列已经被选中，则移除它
    if (selectedColumns.includes(columnId)) {
      const newSelectedColumns = selectedColumns.filter(id => id !== columnId);
      onSelectColumns(newSelectedColumns);
    }
    // 如果列未被选中，则添加它（确保不会添加重复的键）
    else {
      // 使用Set确保没有重复的key
      const newSelectedColumnsSet = new Set(selectedColumns);
      newSelectedColumnsSet.add(columnId);
      onSelectColumns(Array.from(newSelectedColumnsSet));
    }
  }, [selectedColumns, onSelectColumns]);

  // 全选/取消全选
  const toggleSelectAll = useCallback(() => {
    // 使用过滤后的列集合而不是原始列集合
    if (selectedColumns.length === allColumns.length) {
      // 如果全部选中，则清空
      onSelectColumns([]);
    } else {
      // 否则全选 - 使用Set确保没有重复的key
      const allColumnKeys = new Set<string>();
      allColumns.forEach(col => allColumnKeys.add(col.key));
      onSelectColumns(Array.from(allColumnKeys));
    }
  }, [allColumns, selectedColumns, onSelectColumns]);

  // 组件卸载时清理拖拽状态
  useEffect(() => {
    return () => {
      resetDragState();
    };
  }, [resetDragState]);

  // 渲染列项
  const renderColumnItem = (column: FieldDefinition, index: number, orderIndex?: number) => {
    const isSelected = selectedColumns.includes(column.key);
    const isBeingDragged = dragState.isDragging && dragState.draggedId === column.key;
    const isDragOver = hoverTargetId === column.key && canDropOnTarget;

    // 显示序号 - 只有选中的项才显示序号，未选中的项不显示序号
    const displayIndex = isSelected && typeof orderIndex === 'number' ? orderIndex + 1 : '';

    return (
      <div
        key={column.key}
        className={`export-column-preview-item ${
          isSelected ? 'selected' : 'unselected'
        } ${isBeingDragged ? 'dragging' : ''
        } ${isDragOver ? 'drop-target' : ''}`}
        onClick={() => toggleColumnSelection(column.key)}
        data-drag-data={JSON.stringify({ index, title: column.title })}
        onMouseDown={(e) => {
          if (isSelected) {
            // 只有点击拖拽手柄或者整个项目时才触发拖拽
            const target = e.target as HTMLElement;
            const isHandle = target.classList.contains('export-column-preview-handle') ||
                            target.closest('.export-column-preview-handle');

            if (isHandle || !target.closest('button')) {
              dragHandlers.onMouseDown(e, column.key, { index, title: column.title });
            }
          }
        }}
        onMouseEnter={(e) => dragHandlers.onMouseEnter(e, column.key)}
        onMouseLeave={(e) => dragHandlers.onMouseLeave(e, column.key)}
      >
        {displayIndex && <span className="column-index">{displayIndex}</span>}
        <span className="flex-1">{column.title}</span>
        {isSelected && (
          <div className="flex items-center" title="拖动排序">
            <GripVertical className="export-column-preview-handle" />
          </div>
        )}
      </div>
    );
  };

  // 不再需要单独获取扩展字段

  return (
    <div className="border rounded-md h-full flex flex-col relative">
      {/* 标题栏 - 响应式紧凑样式 */}
      <div className="bg-gray-100 px-2 py-1.5 flex items-center justify-between border-b flex-shrink-0">
        <h3 className="font-medium text-gray-700 text-xs sm:text-sm truncate flex-1 mr-2">{title}</h3>
        <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
          {/* 重置按钮 */}
          {onReset && (
            <button
              type="button"
              className="text-xs text-gray-600 hover:text-gray-800 px-1"
              onClick={onReset}
            >
              重置
            </button>
          )}
          {/* 全选按钮 */}
          <button
            type="button"
            className="text-xs text-blue-600 hover:text-blue-800 px-1"
            onClick={toggleSelectAll}
          >
            {/* 检查是否所有可选列都已选中 */}
            {selectedColumns.length > 0 && selectedColumns.length >= allColumns.length
              ? '取消全选'
              : '全选'}
          </button>
        </div>
      </div>

      {/* 列表内容 - 选择导出列 - 使用flex-1自动填充剩余高度，确保滚动容器正确工作 */}
      <div
        className="flex-1"
        style={{
          minHeight: 0,
          maxHeight: '100%',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <div
          className="absolute inset-0 overflow-y-auto"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#cbd5e0 #f7fafc'
          }}
        >
          <div className="export-column-preview">
            <div className="export-column-preview-list">
              {/* 所有字段合并显示 */}
              <div className="export-column-section">
                {/* 标准字段和扩展字段合并显示 - 按照orderedColumns的顺序 */}
                <div className="export-column-section-items">
                  {/* 选中的列 - 按照orderedColumns的顺序显示，并传递序号 */}
                  {orderedColumns.map((colId, index) => {
                    const column = allColumns.find(col => col.key === colId);
                    if (!column) return null;
                    return renderColumnItem(column, index, index);
                  })}

                  {/* 未选中的列 - 标准字段和扩展字段，不显示序号 */}
                  {allColumns
                    .filter(column => !selectedColumns.includes(column.key))
                    .map((column, index) => renderColumnItem(column, orderedColumns.length + index))}

                  {/* 不再显示扩展字段提示 */}
                </div>
              </div>

              {/* 无列时显示提示 */}
              {allColumns.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  无可用列
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ColumnSelector;
