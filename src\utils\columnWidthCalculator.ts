/**
 * 列宽度计算工具
 * 基于内容自适应计算表格列的最佳宽度
 */

import React from 'react';

/**
 * 计算字符串的显示宽度（像素）
 * 考虑中文字符和英文字符的不同宽度
 */
export function calculateTextWidth(text: string, fontSize: number = 14): number {
  if (!text) return 0;

  let width = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const charCode = char.charCodeAt(0);

    // 中文字符、全角字符
    if (charCode >= 0x4e00 && charCode <= 0x9fff) {
      width += fontSize * 1.1; // 增加中文字符宽度估算
    }
    // 全角标点符号
    else if (charCode >= 0xff00 && charCode <= 0xffef) {
      width += fontSize * 1.0;
    }
    // 数字字符
    else if (char >= '0' && char <= '9') {
      width += fontSize * 0.6; // 增加数字字符宽度
    }
    // 英文字符、半角标点
    else {
      width += fontSize * 0.6; // 增加英文字符宽度估算
    }
  }

  return Math.ceil(width);
}

/**
 * 计算表格列的最佳宽度
 */
export interface ColumnWidthOptions {
  /** 列标题 */
  title: string;
  /** 列数据 */
  data: any[];
  /** 数据字段键 */
  dataKey: string;
  /** 字体大小 */
  fontSize?: number;
  /** 最小宽度 */
  minWidth?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 额外的内边距 */
  padding?: number;
  /** 是否包含排序图标 */
  hasSortIcon?: boolean;
  /** 自定义渲染函数 */
  render?: (value: any, record: any) => React.ReactNode;
}

/**
 * 计算单列的最佳宽度
 */
export function calculateColumnWidth(options: ColumnWidthOptions): number {
  const {
    title,
    data,
    dataKey,
    fontSize = 14,
    minWidth = 80,
    maxWidth = 350, // 增加默认最大宽度
    padding = 28, // 增加内边距以确保内容不被截断
    hasSortIcon = true,
    render
  } = options;

  // 1. 计算标题宽度
  let titleWidth = calculateTextWidth(title, fontSize);
  
  // 如果有排序图标，增加图标宽度
  if (hasSortIcon) {
    titleWidth += 20; // 图标宽度约20px
  }

  // 2. 计算内容最大宽度
  let maxContentWidth = 0;
  
  for (const record of data) {
    let cellContent = '';
    
    if (render) {
      // 如果有自定义渲染函数，尝试获取渲染后的文本内容
      try {
        const rendered = render(record[dataKey], record);
        if (typeof rendered === 'string') {
          cellContent = rendered;
        } else if (typeof rendered === 'number') {
          cellContent = String(rendered);
        } else {
          // 对于复杂的React元素，使用原始值
          cellContent = String(record[dataKey] || '');
        }
      } catch {
        cellContent = String(record[dataKey] || '');
      }
    } else {
      cellContent = String(record[dataKey] || '');
    }
    
    const contentWidth = calculateTextWidth(cellContent, fontSize);
    maxContentWidth = Math.max(maxContentWidth, contentWidth);
  }

  // 3. 取标题宽度和内容最大宽度的较大值
  const baseWidth = Math.max(titleWidth, maxContentWidth);
  
  // 4. 加上内边距
  const totalWidth = baseWidth + padding;
  
  // 5. 限制在最小和最大宽度范围内
  return Math.max(minWidth, Math.min(maxWidth, totalWidth));
}

/**
 * 批量计算多列的最佳宽度
 */
export interface BatchColumnWidthOptions {
  /** 列配置 */
  columns: Array<{
    key: string;
    title: string;
    render?: (value: any, record: any) => React.ReactNode;
    minWidth?: number;
    maxWidth?: number;
  }>;
  /** 数据 */
  data: any[];
  /** 字体大小 */
  fontSize?: number;
  /** 默认内边距 */
  defaultPadding?: number;
  /** 是否包含排序图标 */
  hasSortIcon?: boolean;
}

/**
 * 批量计算列宽度
 */
export function calculateColumnsWidth(options: BatchColumnWidthOptions): Record<string, number> {
  const {
    columns,
    data,
    fontSize = 14,
    defaultPadding = 24,
    hasSortIcon = true
  } = options;

  const result: Record<string, number> = {};

  for (const column of columns) {
    const width = calculateColumnWidth({
      title: column.title,
      data,
      dataKey: column.key,
      fontSize,
      minWidth: column.minWidth,
      maxWidth: column.maxWidth,
      padding: defaultPadding,
      hasSortIcon,
      render: column.render
    });
    
    result[column.key] = width;
  }

  return result;
}

/**
 * 动态计算列的推荐最小和最大宽度
 * 根据列标题字节长度自适应最小列宽，根据列内容字节自适应最大列宽
 */
export function getRecommendedWidthByFieldType(fieldKey: string, fieldTitle: string, data?: any[]): { minWidth: number; maxWidth: number } {
  // 1. 根据列标题字节长度计算最小宽度
  const titleWidth = calculateTextWidth(fieldTitle, 14); // 使用14px字体

  // 最小宽度 = 标题宽度+ 内边距(24px) + 缓冲(10px)
  const minWidthFromTitle = Math.ceil(titleWidth + 24 + 10);

  // 设置绝对最小宽度，确保基本可用性
  const absoluteMinWidth = 50;
  const calculatedMinWidth = Math.max(absoluteMinWidth, minWidthFromTitle);

  // 2. 根据列内容字节长度计算最大宽度
  let maxWidthFromContent = calculatedMinWidth;

  if (data && data.length > 0) {
    // 分析前100行数据的内容长度（避免性能问题）
    const sampleData = data.slice(0, 100);
    let maxContentWidth = 0;

    for (const row of sampleData) {
      const cellValue = row[fieldKey];
      if (cellValue !== null && cellValue !== undefined) {
        const valueStr = String(cellValue);
        const contentWidth = calculateTextWidth(valueStr, 14);
        maxContentWidth = Math.max(maxContentWidth, contentWidth);
      }
    }

    // 最大宽度 = 内容最大宽度 + 内边距(24px) + 缓冲(16px)
    if (maxContentWidth > 0) {
      maxWidthFromContent = Math.ceil(maxContentWidth + 24 + 16);
    }
  }

  // 3. 确定最终的最大宽度
  // 最大宽度不能小于最小宽度，增加最大宽度限制以适应更长内容
  const finalMaxWidth = Math.min(Math.max(maxWidthFromContent, calculatedMinWidth), 300);

  return {
    minWidth: calculatedMinWidth,
    maxWidth: finalMaxWidth
  };
}


