
import { BaseService, BaseServiceState, BaseServiceEvents } from '../base/baseService';
import { formatInspectionDateTime } from '../../utils/formatUtils';

/**
 * 账户管理日志项接口 - 直接匹配后端返回的数据结构
 */
export interface AccountLogItem {
  id: number;
  log_type: number;
  log_type_text: string;
  new_data: Record<string, unknown>;
  original_data: Record<string, unknown>;
  target_id: number;
  target_name: string;
  timestamp: number;
  timestamp_str?: string;

  // 前端添加的格式化字段
  operation_time?: string;
}

/**
 * 账户管理日志过滤条件接口
 */
export interface AccountLogFilter {
  startDate?: string;
  endDate?: string;
  logType?: number;
  userName?: string;
  personName?: string;
  searchText?: string;
}

/**
 * 账户管理日志服务状态接口
 */
export interface AccountLogServiceState extends BaseServiceState {
  logs: AccountLogItem[];
  filter: AccountLogFilter;
  filteredLogs: AccountLogItem[];
}

/**
 * 账户管理日志服务事件接口
 */
export interface AccountLogServiceEvents extends BaseServiceEvents<AccountLogServiceState> {
  'logs-loaded': AccountLogItem[];
  'filter-changed': AccountLogFilter;
}

/**
 * 账户管理日志服务类
 * 提供账户管理日志的查询和过滤功能
 */
class AccountLogService extends BaseService<AccountLogServiceState, AccountLogServiceEvents> {
  private static instance: AccountLogService;

  // 防重复调用机制
  private isLoadingLogs = false;
  private lastLoadTime = 0;
  private readonly CACHE_DURATION = 30000; // 30秒缓存

  /**
   * 获取账户管理日志服务实例
   * @returns 账户管理日志服务实例
   */
  public static getInstance(): AccountLogService {
    if (!AccountLogService.instance) {
      AccountLogService.instance = new AccountLogService();
    }
    return AccountLogService.instance;
  }

  /**
   * 构造函数
   * 初始化账户管理日志服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      logs: [],
      filter: {},
      filteredLogs: []
    });
  }

  /**
   * 获取账户管理日志数据
   * @param logType 日志类型，默认53获取账户管理日志。具体类型：50=添加移动端用户，52=删除移动端用户，51=修改移动端用户
   */
  public async getAccountLogs(logType?: number): Promise<AccountLogItem[]> {
    try {
      await this.ensureInitialized();

      // 防重复调用检查
      const now = Date.now();
      if (this.isLoadingLogs) {
        return this.state.logs;
      }

      // 缓存检查（只有在已有数据且在缓存时间内才使用缓存）
      if (this.state.logs.length > 0 &&
          this.lastLoadTime > 0 &&
          (now - this.lastLoadTime) < this.CACHE_DURATION) {
        return this.state.logs;
      }

      this.isLoadingLogs = true;
      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 调用API获取日志数据
      const logs = await this.fetchAccountLogs(logType);
      this.lastLoadTime = now;

      this.updateState({
        logs,
        filteredLogs: this.applyFilter(logs, this.state.filter),
        isLoading: false
      });

      this.emitter.emit('logs-loaded', logs);
      return logs;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取账户管理日志失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    } finally {
      this.isLoadingLogs = false;
    }
  }

  /**
   * 强制刷新账户管理日志数据（忽略缓存）
   * @param logType 日志类型
   */
  public async forceRefreshAccountLogs(logType?: number): Promise<AccountLogItem[]> {
    this.lastLoadTime = 0; // 重置缓存时间
    return await this.getAccountLogs(logType);
  }

  /**
   * 调用获取账户管理日志API
   */
  private async fetchAccountLogs(logType?: number): Promise<AccountLogItem[]> {
    try {
      // 构建API参数 - 按照您提供的正确格式
      const apiParams = {
        action: 'query_logs',
        action_params: {
          log_table: 'inspection',
          log_type: logType !== undefined ? logType : 53 // 默认使用53获取账户管理日志
        }
      };

      console.log('调用获取账户管理日志API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams) as unknown;

      console.log('获取账户管理日志API原始返回结果:', rawResult);

      // 解析result字段（如果是JSON字符串）
      let result: unknown;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
          console.log('解析JSON字符串后的结果:', result);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      console.log('最终处理的结果:', result);

      // 检查API返回结果
      if (this.isValidApiResponse(result)) {
        const logs = result.data.logs as AccountLogItem[];

        // 为每个日志项添加格式化的操作时间
        const processedLogs = logs.map(log => ({
          ...log,
          operation_time: formatInspectionDateTime(log.timestamp)
        }));

        console.log('处理后的账户管理日志数据:', processedLogs);
        return processedLogs;
      } else {
        // 处理错误情况
        const errorMessage = this.getErrorMessage(result) || '获取账户管理日志失败';
        console.error('API返回错误:', errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('获取账户管理日志API调用失败:', error);
      throw error;
    }
  }

  /**
   * 设置过滤条件
   * @param filter 过滤条件
   */
  public setFilter(filter: Partial<AccountLogFilter>): void {
    const newFilter = { ...this.state.filter, ...filter };
    const filteredLogs = this.applyFilter(this.state.logs, newFilter);
    
    this.updateState({
      filter: newFilter,
      filteredLogs
    });

    this.emitter.emit('filter-changed', newFilter);
  }

  /**
   * 清除所有过滤条件
   */
  public clearFilter(): void {
    const emptyFilter: AccountLogFilter = {};
    const filteredLogs = this.applyFilter(this.state.logs, emptyFilter);
    
    this.updateState({
      filter: emptyFilter,
      filteredLogs
    });

    this.emitter.emit('filter-changed', emptyFilter);
  }

  /**
   * 应用过滤条件
   * @param logs 原始日志数据
   * @param filter 过滤条件
   */
  private applyFilter(logs: AccountLogItem[], filter: AccountLogFilter): AccountLogItem[] {
    return logs.filter(log => {
      // 日期范围过滤
      if (filter.startDate || filter.endDate) {
        const logDate = new Date(log.timestamp * 1000);
        const logDateStr = logDate.toISOString().split('T')[0];
        
        if (filter.startDate && logDateStr < filter.startDate) {
          return false;
        }
        if (filter.endDate && logDateStr > filter.endDate) {
          return false;
        }
      }

      // 日志类型过滤
      if (filter.logType !== undefined && log.log_type !== filter.logType) {
        return false;
      }

      // 用户名过滤
      if (filter.userName) {
        const newData = log.new_data || {};
        const originalData = log.original_data || {};
        const userName = newData.user_name || originalData.user_name || '';
        if (!userName.toLowerCase().includes(filter.userName.toLowerCase())) {
          return false;
        }
      }

      // 人员姓名过滤
      if (filter.personName) {
        const newData = log.new_data || {};
        const originalData = log.original_data || {};
        const personName = newData.person_name || originalData.person_name || '';
        if (!personName.toLowerCase().includes(filter.personName.toLowerCase())) {
          return false;
        }
      }

      // 搜索文本过滤（搜索用户名、人员姓名、操作类型）
      if (filter.searchText) {
        const searchText = filter.searchText.toLowerCase();
        const newData = log.new_data || {};
        const originalData = log.original_data || {};
        
        const userName = newData.user_name || originalData.user_name || '';
        const personName = newData.person_name || originalData.person_name || '';
        const logTypeText = log.log_type_text || '';
        
        const searchableText = `${userName} ${personName} ${logTypeText}`.toLowerCase();
        if (!searchableText.includes(searchText)) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * 获取所有日志类型
   */
  public getLogTypes(): string[] {
    const logTypes = new Set<string>();
    this.state.logs.forEach(log => {
      if (log.log_type_text) {
        logTypes.add(log.log_type_text);
      }
    });
    return Array.from(logTypes).sort();
  }

  /**
   * 获取所有用户名
   */
  public getUserNames(): string[] {
    const userNames = new Set<string>();
    this.state.logs.forEach(log => {
      const newData = log.new_data || {};
      const originalData = log.original_data || {};
      const userName = newData.user_name || originalData.user_name;
      if (userName) {
        userNames.add(userName);
      }
    });
    return Array.from(userNames).sort();
  }

  /**
   * 获取所有人员姓名
   */
  public getPersonNames(): string[] {
    const personNames = new Set<string>();
    this.state.logs.forEach(log => {
      const newData = log.new_data || {};
      const originalData = log.original_data || {};
      const personName = newData.person_name || originalData.person_name;
      if (personName) {
        personNames.add(personName);
      }
    });
    return Array.from(personNames).sort();
  }

  /**
   * 检查API响应是否有效
   */
  private isValidApiResponse(result: unknown): result is { success: boolean; data: { logs: AccountLogItem[] } } {
    return (
      typeof result === 'object' &&
      result !== null &&
      'success' in result &&
      'data' in result &&
      typeof (result as Record<string, unknown>).success === 'boolean' &&
      (result as Record<string, unknown>).success === true &&
      typeof (result as Record<string, unknown>).data === 'object' &&
      (result as Record<string, unknown>).data !== null &&
      'logs' in ((result as Record<string, unknown>).data as Record<string, unknown>) &&
      Array.isArray(((result as Record<string, unknown>).data as Record<string, unknown>).logs)
    );
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(result: unknown): string | undefined {
    if (typeof result === 'object' && result !== null && 'message' in result) {
      return typeof (result as Record<string, unknown>).message === 'string'
        ? (result as Record<string, unknown>).message as string
        : undefined;
    }
    return undefined;
  }

  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    // 基础服务初始化逻辑
    if (!this.isInitialized) {
      await this.initialize();
    }
  }
}

export default AccountLogService;
