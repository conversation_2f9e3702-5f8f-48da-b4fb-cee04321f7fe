import mitt from 'mitt';
import TaskManager from '../../utils/taskManager';
import { localizeErrorMessage } from '../../utils/errorHandler';
import WebSocketManager from '../../utils/websocket';

/**
 * 基础服务状态接口
 * 所有服务状态都应继承此接口
 */
export interface BaseServiceState {
  isLoading: boolean;
  error?: string;
}

/**
 * 基础服务事件接口
 * 所有服务事件都应继承此接口
 */
export interface BaseServiceEvents<T extends BaseServiceState> {
  'state-change': T;
  'error': string;
  'loading': boolean;
}

/**
 * 基础服务抽象类
 * 提供状态管理、事件处理和任务提交的通用功能
 */
export abstract class BaseService<T extends BaseServiceState, E extends BaseServiceEvents<T>> {
  protected state: T;
  protected emitter = mitt<E>();
  protected task: TaskManager;
  protected ws: WebSocketManager;
  protected dllName: string;
  protected isInitialized = false;

  /**
   * 构造函数
   * @param dllName DLL名称
   * @param initialState 初始状态
   */
  constructor(dllName: string, initialState: T) {
    this.dllName = dllName;
    this.state = initialState;
    this.task = TaskManager.getInstance();
    this.ws = WebSocketManager.getInstance();
  }

  /**
   * 初始化服务
   * 子类可以重写此方法以执行特定的初始化逻辑
   */
  protected async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // 确保WebSocket已连接
      if (!this.ws.isConnected()) {
        await this.ws.connect();
      }
      
      this.isInitialized = true;
    } catch (error) {
      console.error(`初始化服务 ${this.constructor.name} 失败:`, error);
      throw this.handleError(error);
    }
  }

  /**
   * 确保服务已初始化
   */
  protected async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * 统一的任务提交方法
   * @param funcName 函数名
   * @param params 参数
   * @returns 任务结果
   */
  protected async submitTask<R>(funcName: string, params: any = {}): Promise<R> {
    await this.ensureInitialized();
    
    try {
      this.updateState({ isLoading: true } as Partial<T>);
      
      const taskId = await this.task.submitTask(this.dllName, funcName, params);
      console.log(`[${this.constructor.name}] 提交任务: ${funcName}, taskId: ${taskId}`);
      
      return new Promise<R>((resolve, reject) => {
        let isHandled = false;

        const handleTaskUpdate = (taskInfo: any) => {
          // 防止重复处理
          if (isHandled) return;

          // 处理进度更新
          if (taskInfo.progress !== undefined) {
            this.emitter.emit('loading', true);
          }

          // 处理任务完成
          if (taskInfo.status === 'completed') {
            isHandled = true;
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            this.updateState({ isLoading: false } as Partial<T>);

            // 检查结果中的success字段
            let resultData = taskInfo.result;

            // 如果结果是字符串，尝试解析为JSON
            if (typeof resultData === 'string') {
              try {
                resultData = JSON.parse(resultData);
              } catch (parseError) {
                console.warn(`解析任务结果失败: ${funcName}, taskId: ${taskId}`, parseError);
              }
            }

            // 检查业务逻辑是否成功
            if (resultData && typeof resultData === 'object' && resultData.success === false) {
              const rawErrorMessage = resultData.error_message || resultData.message || '操作失败';
              const errorMessage = localizeErrorMessage(rawErrorMessage);
              console.error(`任务业务逻辑失败: ${funcName}, taskId: ${taskId}, 原始错误: ${rawErrorMessage}, 本地化错误: ${errorMessage}`);
              this.updateState({
                isLoading: false,
                error: errorMessage
              } as Partial<T>);
              reject(new AppError(errorMessage, 'BUSINESS_ERROR'));
              return;
            }

            resolve(taskInfo.result);
          }
          // 处理任务失败
          else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            isHandled = true;
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            const rawErrorMsg = taskInfo.error || '任务执行失败';
            const errorMsg = localizeErrorMessage(rawErrorMsg);
            console.error(`任务执行失败: ${funcName}, taskId: ${taskId}, 原始错误: ${rawErrorMsg}, 本地化错误: ${errorMsg}`);
            this.updateState({
              isLoading: false,
              error: errorMsg
            } as Partial<T>);
            reject(new AppError(errorMsg, 'TASK_ERROR'));
          }
        };

        this.task.onTaskUpdate(taskId, handleTaskUpdate);

        // 设置超时保护，确保回调被清理
        setTimeout(() => {
          if (!isHandled) {
            isHandled = true;
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            this.updateState({ isLoading: false } as Partial<T>);
            reject(new AppError('任务执行超时', 'TIMEOUT_ERROR'));
          }
        }, 120000); // 2分钟超时
      });
    } catch (error) {
      this.updateState({ isLoading: false } as Partial<T>);
      console.error(`[${this.constructor.name}] 执行任务 ${this.dllName}.${funcName} 失败:`, error);
      throw this.handleError(error);
    }
  }

  /**
   * 统一的状态更新方法
   * @param newState 新状态
   */
  protected updateState(newState: Partial<T>): void {
    this.state = { ...this.state, ...newState };
    this.emitter.emit('state-change', this.state);
    
    // 如果有错误，触发错误事件
    if (newState.error) {
      this.emitter.emit('error', newState.error);
    }
    
    // 如果加载状态变化，触发加载事件
    if (newState.isLoading !== undefined) {
      this.emitter.emit('loading', newState.isLoading);
    }
  }

  /**
   * 统一的错误处理方法
   * @param error 错误对象
   * @returns 标准化的AppError
   */
  protected handleError(error: unknown): AppError {
    if (error instanceof AppError) {
      // 对已有的AppError也进行本地化处理
      const localizedMessage = localizeErrorMessage(error.message);
      return new AppError(localizedMessage, error.code, error.details);
    }

    if (error instanceof Error) {
      const localizedMessage = localizeErrorMessage(error.message);
      return new AppError(localizedMessage, 'SERVICE_ERROR', error);
    }

    const errorMessage = typeof error === 'string' ? error : '发生未知错误';
    const localizedMessage = localizeErrorMessage(errorMessage);
    return new AppError(localizedMessage, 'UNKNOWN_ERROR', error);
  }

  /**
   * 事件订阅方法
   * @param event 事件名
   * @param callback 回调函数
   */
  public on<K extends keyof E>(event: K, callback: (data: E[K]) => void): void {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消订阅方法
   * @param event 事件名
   * @param callback 回调函数
   */
  public off<K extends keyof E>(event: K, callback: (data: E[K]) => void): void {
    this.emitter.off(event, callback as any);
  }

  /**
   * 获取当前状态
   * @returns 当前状态的副本
   */
  public getState(): T {
    return { ...this.state };
  }
  
  /**
   * 强制触发状态更新
   * 用于确保UI与最新状态同步
   */
  public forceUpdate(): void {
    this.emitter.emit('state-change', this.state);
  }
}

/**
 * 应用错误类
 * 提供标准化的错误格式
 */
export class AppError extends Error {
  public code: string;
  public details?: any;

  constructor(message: string, code: string = 'UNKNOWN_ERROR', details?: any) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.details = details;
  }
}
