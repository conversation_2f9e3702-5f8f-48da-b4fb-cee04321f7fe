/**
 * 右键菜单位置计算工具
 * 智能计算右键菜单的显示位置，避免菜单被视口边界遮挡
 */

export interface ContextMenuPosition {
  x: number;
  y: number;
}

export interface ContextMenuDimensions {
  width: number;
  height: number;
}

export interface ContextMenuOptions {
  /** 菜单项数量，用于计算菜单高度 */
  menuItemCount?: number;
  /** 每个菜单项的高度（像素） */
  menuItemHeight?: number;
  /** 菜单内边距（像素） */
  menuPadding?: number;
  /** 菜单宽度（像素） */
  menuWidth?: number;
  /** 距离视口边界的最小边距（像素） */
  margin?: number;
  /** 是否优先在鼠标上方显示（当下方空间不足时） */
  preferAbove?: boolean;
  /** 是否优先在鼠标左侧显示（当右侧空间不足时） */
  preferLeft?: boolean;
}

/**
 * 计算右键菜单的智能位置
 * @param clientX 鼠标X坐标（相对于视口）
 * @param clientY 鼠标Y坐标（相对于视口）
 * @param options 菜单选项配置
 * @returns 计算后的菜单位置
 */
export function calculateContextMenuPosition(
  clientX: number,
  clientY: number,
  options: ContextMenuOptions = {}
): ContextMenuPosition {
  const {
    menuItemCount = 4,
    menuItemHeight = 36,
    menuPadding = 8,
    menuWidth = 160,
    margin = 10,
    preferAbove = false,
    preferLeft = false
  } = options;

  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  // 计算菜单尺寸
  const menuHeight = menuItemCount * menuItemHeight + menuPadding;
  
  // 计算初始位置
  let x = clientX;
  let y = clientY;
  
  // 水平位置调整
  if (preferLeft) {
    // 优先在左侧显示
    x = clientX - menuWidth;
    // 如果左侧空间不足，则在右侧显示
    if (x < margin) {
      x = clientX;
    }
  }
  
  // 确保菜单不会超出右边界
  if (x + menuWidth > viewportWidth - margin) {
    x = viewportWidth - menuWidth - margin;
  }
  
  // 确保菜单不会超出左边界
  if (x < margin) {
    x = margin;
  }
  
  // 垂直位置调整
  if (preferAbove) {
    // 优先在上方显示
    y = clientY - menuHeight;
    // 如果上方空间不足，则在下方显示
    if (y < margin) {
      y = clientY;
    }
  }
  
  // 确保菜单不会超出底部
  if (y + menuHeight > viewportHeight - margin) {
    // 如果下方空间不足，尝试在鼠标上方显示
    const spaceAbove = clientY - margin;
    const spaceBelow = viewportHeight - clientY - margin;
    
    if (spaceAbove > spaceBelow && spaceAbove >= menuHeight) {
      // 在鼠标上方显示
      y = clientY - menuHeight;
    } else {
      // 在底部边界上方显示
      y = viewportHeight - menuHeight - margin;
    }
  }
  
  // 确保菜单不会超出顶部
  if (y < margin) {
    y = margin;
  }
  
  return { x, y };
}

/**
 * 根据菜单内容动态计算菜单尺寸
 * @param menuItems 菜单项列表
 * @param options 菜单选项配置
 * @returns 菜单尺寸
 */
export function calculateContextMenuDimensions(
  menuItems: string[] | number,
  options: ContextMenuOptions = {}
): ContextMenuDimensions {
  const {
    menuItemHeight = 36,
    menuPadding = 8,
    menuWidth = 160
  } = options;

  const itemCount = typeof menuItems === 'number' ? menuItems : menuItems.length;
  const height = itemCount * menuItemHeight + menuPadding;
  
  return {
    width: menuWidth,
    height
  };
}

/**
 * 检查指定位置的菜单是否会被视口遮挡
 * @param x 菜单X坐标
 * @param y 菜单Y坐标
 * @param dimensions 菜单尺寸
 * @param margin 边距
 * @returns 是否被遮挡的信息
 */
export function checkContextMenuOverflow(
  x: number,
  y: number,
  dimensions: ContextMenuDimensions,
  margin: number = 10
): {
  overflowRight: boolean;
  overflowLeft: boolean;
  overflowBottom: boolean;
  overflowTop: boolean;
  hasOverflow: boolean;
} {
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  const overflowRight = x + dimensions.width > viewportWidth - margin;
  const overflowLeft = x < margin;
  const overflowBottom = y + dimensions.height > viewportHeight - margin;
  const overflowTop = y < margin;
  
  return {
    overflowRight,
    overflowLeft,
    overflowBottom,
    overflowTop,
    hasOverflow: overflowRight || overflowLeft || overflowBottom || overflowTop
  };
}

/**
 * 为特定类型的菜单预设配置
 */
export const CONTEXT_MENU_PRESETS = {
  /** 标准右键菜单 */
  standard: {
    menuItemHeight: 36,
    menuPadding: 8,
    menuWidth: 160,
    margin: 10
  },
  
  /** 紧凑型菜单 */
  compact: {
    menuItemHeight: 28,
    menuPadding: 4,
    menuWidth: 140,
    margin: 8
  },
  
  /** 大型菜单 */
  large: {
    menuItemHeight: 44,
    menuPadding: 12,
    menuWidth: 200,
    margin: 12
  },
  
  /** 设备分类树菜单 */
  deviceCategory: {
    menuItemHeight: 36,
    menuPadding: 8,
    menuWidth: 160,
    margin: 10
  },
  
  /** 部门分类树菜单 */
  departmentCategory: {
    menuItemHeight: 36,
    menuPadding: 8,
    menuWidth: 160,
    margin: 10
  }
} as const;

/**
 * 使用预设配置计算菜单位置
 * @param clientX 鼠标X坐标
 * @param clientY 鼠标Y坐标
 * @param preset 预设名称
 * @param menuItemCount 菜单项数量
 * @param overrides 覆盖配置
 * @returns 计算后的菜单位置
 */
export function calculateContextMenuPositionWithPreset(
  clientX: number,
  clientY: number,
  preset: keyof typeof CONTEXT_MENU_PRESETS,
  menuItemCount: number,
  overrides: Partial<ContextMenuOptions> = {}
): ContextMenuPosition {
  const presetConfig = CONTEXT_MENU_PRESETS[preset];
  const options = {
    ...presetConfig,
    menuItemCount,
    ...overrides
  };
  
  return calculateContextMenuPosition(clientX, clientY, options);
}
