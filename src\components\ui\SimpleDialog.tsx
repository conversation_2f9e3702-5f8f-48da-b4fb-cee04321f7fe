import React from 'react';
import { X } from 'lucide-react';
import DialogBase from './DialogBase';

interface SimpleDialogProps {
  // 对话框是否打开
  isOpen: boolean;
  // 关闭对话框的回调
  onClose: () => void;
  // 对话框标题
  title: string;
  // 对话框内容
  children: React.ReactNode;
  // 对话框宽度
  width?: string;
  // 对话框最大高度
  maxHeight?: string;
  // 对话框CSS类名
  className?: string;
}

/**
 * 简化的对话框组件
 * 现在使用统一的焦点管理系统
 */
const SimpleDialog: React.FC<SimpleDialogProps> = ({
  isOpen,
  onClose,
  title,
  children,
  width = '800px',
  maxHeight = '80vh',
  className = ''
}) => {
  return (
    <DialogBase
      isOpen={isOpen}
      onClose={onClose}
      width={width}
      maxHeight={maxHeight}
      className={className}
      autoFocus={true}
      restoreFocus={true}
    >
      {/* 对话框标题 */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-800">{title}</h2>
        <button
          onClick={onClose}
          className="text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-full p-1 transition-colors focus-ring"
          aria-label="关闭"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* 对话框内容 */}
      <div className="flex-1 overflow-auto" style={{ maxHeight: `calc(${maxHeight} - 80px)` }}>
        {children}
      </div>
    </DialogBase>
  );
};

export default SimpleDialog;
