import { BaseService, AppError, BaseServiceState, BaseServiceEvents } from './base/baseService';

/**
 * 全局配置数据接口
 */
interface GlobalConfigData {
  id?: number;
  company_name?: string;
  archive_timestamp?: number;
  archive_file_size?: number;
}

/**
 * 全局配置服务状态接口
 */
interface GlobalConfigServiceState extends BaseServiceState {
  companyName: string;
  configData: GlobalConfigData | null;
  lastFetchTime: number;
}

/**
 * 全局配置服务事件接口
 */
interface GlobalConfigServiceEvents extends BaseServiceEvents<GlobalConfigServiceState> {
  'config-loaded': GlobalConfigData;
  'company-name-updated': string;
  'loading-start': void;
  'loading-end': void;
}

/**
 * 全局配置服务
 * 负责管理系统全局配置，包括公司名称等信息
 */
class GlobalConfigService extends BaseService<GlobalConfigServiceState, GlobalConfigServiceEvents> {
  private static instance: GlobalConfigService;
  private cache: {
    data: GlobalConfigData | null;
    timestamp: number;
  } = {
    data: null,
    timestamp: 0
  };

  // 缓存有效期：5分钟
  private readonly CACHE_TTL = 5 * 60 * 1000;

  // 默认公司名称
  private readonly DEFAULT_COMPANY_NAME = '公司名称';

  // 根节点验证状态
  private static rootNodeValidated: boolean = false;
  private static rootNodeValidationFailed: boolean = false;
  private static rootNodeValidationPromise: Promise<boolean> | null = null;
  private static rootNodeValidationError: string | null = null;

  /**
   * 构造函数
   */
  private constructor() {
    super('ExportDll', {
      isLoading: false,
      companyName: '',
      configData: null,
      lastFetchTime: 0
    });
  }

  /**
   * 获取全局配置服务实例
   */
  public static getInstance(): GlobalConfigService {
    if (!GlobalConfigService.instance) {
      GlobalConfigService.instance = new GlobalConfigService();
    }
    return GlobalConfigService.instance;
  }

  /**
   * 检查根节点是否已验证通过
   */
  public static isRootNodeValidated(): boolean {
    return GlobalConfigService.rootNodeValidated;
  }

  /**
   * 检查根节点验证是否失败
   */
  public static isRootNodeValidationFailed(): boolean {
    return GlobalConfigService.rootNodeValidationFailed;
  }

  /**
   * 设置根节点验证状态到全局对象
   */
  private static setGlobalValidationState(): void {
    (globalThis as any).__rootNodeValidated = GlobalConfigService.rootNodeValidated;
    (globalThis as any).__rootNodeValidationFailed = GlobalConfigService.rootNodeValidationFailed;
  }

  /**
   * 获取根节点验证错误信息
   */
  public static getRootNodeValidationError(): string | null {
    return GlobalConfigService.rootNodeValidationError;
  }

  /**
   * 检查是否是T00表为空错误
   */
  public static isT00EmptyError(): boolean {
    return GlobalConfigService.rootNodeValidationError === 'T00表为空';
  }

  /**
   * 验证根节点（系统初始化时调用）
   */
  public static async validateRootNode(): Promise<boolean> {
    // 如果已经有验证过程在进行中，等待结果
    if (GlobalConfigService.rootNodeValidationPromise) {
      return GlobalConfigService.rootNodeValidationPromise;
    }

    // 如果已经验证过，直接返回结果
    if (GlobalConfigService.rootNodeValidated || GlobalConfigService.rootNodeValidationFailed) {
      return GlobalConfigService.rootNodeValidated;
    }

    // 开始验证过程
    GlobalConfigService.rootNodeValidationPromise = GlobalConfigService.performRootNodeValidation();
    return GlobalConfigService.rootNodeValidationPromise;
  }

  /**
   * 执行根节点验证
   */
  private static async performRootNodeValidation(): Promise<boolean> {
    try {
      console.log('开始根节点验证...');
      const service = GlobalConfigService.getInstance();

      // 直接调用API验证根节点
      const result = await service.validateRootNodeInternal();

      if (result.success) {
        GlobalConfigService.rootNodeValidated = true;
        GlobalConfigService.rootNodeValidationFailed = false;
        GlobalConfigService.rootNodeValidationError = null;
        GlobalConfigService.setGlobalValidationState();
        console.log('公司名称验证成功，系统可以正常使用');

        // 立即触发状态栏更新事件
        console.log('根节点验证成功，触发状态栏立即更新');
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('system-activation-changed'));
        }

        return true;
      } else {
        GlobalConfigService.rootNodeValidated = false;
        GlobalConfigService.rootNodeValidationFailed = true;
        GlobalConfigService.rootNodeValidationError = result.error;
        GlobalConfigService.setGlobalValidationState();
        console.error('公司名称验证失败，系统将停止所有API调用，错误:', result.error);
        return false;
      }
    } catch (error) {
      console.error('公司名称验证过程出错:', error);
      GlobalConfigService.rootNodeValidated = false;
      GlobalConfigService.rootNodeValidationFailed = true;
      GlobalConfigService.rootNodeValidationError = error instanceof Error ? error.message : '未知错误';
      GlobalConfigService.setGlobalValidationState();
      return false;
    } finally {
      GlobalConfigService.rootNodeValidationPromise = null;
    }
  }

  /**
   * 获取公司名称（优化版本，根节点获取成功后直接使用缓存）
   * @param forceRefresh 是否强制刷新，忽略缓存
   * @returns 公司名称
   */
  public async getCompanyName(forceRefresh: boolean = false): Promise<string> {
    try {
      // 优先使用缓存，避免重复请求
      const now = Date.now();
      if (!forceRefresh && this.cache.data && (now - this.cache.timestamp < this.CACHE_TTL)) {
        console.log('使用缓存的公司名称:', this.cache.data.company_name);
        return this.cache.data.company_name || this.DEFAULT_COMPANY_NAME;
      }

      // 如果已有缓存数据且不强制刷新，直接返回
      if (!forceRefresh && this.cache.data?.company_name) {
        console.log('使用已有缓存的公司名称:', this.cache.data.company_name);
        return this.cache.data.company_name;
      }

      // 只在必要时从API获取配置
      console.log('从API获取公司名称配置');
      const configData = await this.fetchGlobalConfig();

      // 更新状态和缓存
      const companyName = configData.company_name || this.DEFAULT_COMPANY_NAME;
      this.updateState({
        companyName,
        configData,
        lastFetchTime: now
      });

      console.log('公司名称获取成功:', companyName);
      return companyName;
    } catch (error) {
      console.error('获取公司名称失败:', error);

      // 优雅降级：返回缓存的名称或默认名称
      const fallbackName = this.cache.data?.company_name || this.state.companyName || this.DEFAULT_COMPANY_NAME;
      console.log('使用降级名称:', fallbackName);
      return fallbackName;
    }
  }

  /**
   * 获取完整的全局配置数据
   * @param forceRefresh 是否强制刷新
   * @returns 全局配置数据
   */
  public async getGlobalConfig(forceRefresh: boolean = false): Promise<GlobalConfigData> {
    try {
      // 检查缓存
      const now = Date.now();
      if (!forceRefresh && this.cache.data && (now - this.cache.timestamp < this.CACHE_TTL)) {
        console.log('使用缓存的全局配置数据');
        return this.cache.data;
      }

      // 从API获取配置
      const configData = await this.fetchGlobalConfig();

      // 更新状态
      this.updateState({
        configData,
        companyName: configData.company_name || this.DEFAULT_COMPANY_NAME,
        lastFetchTime: now
      });

      return configData;
    } catch (error) {
      console.error('获取全局配置失败:', error);
      this.emitter.emit('error', error as Error);

      // 返回缓存的数据或空对象
      return this.cache.data || {};
    }
  }

  /**
   * 内部根节点验证方法
   */
  private async validateRootNodeInternal(): Promise<{success: boolean, error?: string}> {
    try {
      console.log('开始内部根节点验证...');

      // 构建请求参数
      const params = {
        action: "get_global_config",
        action_params: {}
      };

      console.log('根节点验证请求参数:', JSON.stringify(params));

      // 直接调用TaskManager，绕过BaseService的验证
      const taskId = await this.task.submitTask('ExportDll', 'QueryGlobalConfig', params);

      const result = await new Promise<any>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            resolve(taskInfo.result);
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);
            reject(new Error(taskInfo.error || '公司名称验证失败'));
          }
        };
        this.task.onTaskUpdate(taskId, handleTaskUpdate);
      });

      console.log('公司名称验证API响应:', result);

      // 检查响应格式
      if (!result || typeof result !== 'object') {
        console.error('公司名称验证：API响应格式无效');
        return false;
      }

      // 检查是否有错误信息
      if (result.error) {
        console.error('公司名称验证失败，错误信息:', result.error);
        return { success: false, error: result.error };
      }

      // 检查data字段是否包含错误信息
      if (typeof result.data === 'string' &&
          (result.data.includes('error') ||
           result.data.includes('UTF-8 encoding error') ||
           result.data.includes('T00表为空'))) {
        console.error('公司名称验证失败，data字段包含错误信息:', result.data);

        // 提取具体的错误信息
        let errorMessage = result.data;
        if (result.data.includes('T00表为空')) {
          errorMessage = 'T00表为空';
        } else if (result.data.includes('UTF-8 encoding error')) {
          errorMessage = 'UTF-8 encoding error';
        }

        return { success: false, error: errorMessage };
      }

      // 验证成功，更新缓存
      let configData: GlobalConfigData = {};
      if (typeof result.data === 'string') {
        configData = { company_name: result.data };
      } else if (result.data && typeof result.data === 'object') {
        configData = result.data;
      }

      this.cache = {
        data: configData,
        timestamp: Date.now()
      };

      console.log('公司名称验证成功，配置数据:', configData);
      return { success: true };
    } catch (error) {
      console.error('公司名称验证过程出错:', error);
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    }
  }

  /**
   * 从API获取全局配置数据
   * @returns 全局配置数据
   */
  private async fetchGlobalConfig(): Promise<GlobalConfigData> {
    this.updateState({ isLoading: true });
    this.emitter.emit('loading-start');

    try {
      console.log('开始获取全局配置...');

      // 构建请求参数
      const params = {
        action: "get_global_config",
        action_params: {}
      };

      console.log('全局配置请求参数:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await this.submitTask('QueryGlobalConfig', params);

      console.log('全局配置API响应:', result);

      // 检查响应格式
      if (!result || typeof result !== 'object') {
        throw new Error('API响应格式无效');
      }

      // 检查是否有错误信息
      if (result.error) {
        console.error('公司名称API返回错误:', result.error);
        // 根据记忆，当根节点API失败时，显示"违规使用"并取消后续参数传递
        const configData: GlobalConfigData = {
          company_name: '违规使用'
        };

        // 更新缓存
        this.cache = {
          data: configData,
          timestamp: Date.now()
        };

        console.log('公司名称API失败，使用违规使用标识:', configData);
        this.emitter.emit('config-loaded', configData);
        return configData;
      }

      // 检查data字段是否包含错误信息
      if (typeof result.data === 'string' &&
          (result.data.includes('error') ||
           result.data.includes('UTF-8 encoding error') ||
           result.data.includes('T00表为空'))) {
        console.error('公司名称API data字段包含错误信息:', result.data);
        // 根据记忆，当根节点API失败时，显示"违规使用"并取消后续参数传递
        const configData: GlobalConfigData = {
          company_name: '违规使用'
        };

        // 更新缓存
        this.cache = {
          data: configData,
          timestamp: Date.now()
        };

        console.log('公司名称API失败，使用违规使用标识:', configData);
        this.emitter.emit('config-loaded', configData);
        return configData;
      }

      // 处理正常的API响应格式：{data: '腾讯科技有限公司'}
      let configData: GlobalConfigData = {};

      if (typeof result.data === 'string') {
        // 如果data是字符串，说明是公司名称
        configData = {
          company_name: result.data
        };
      } else if (result.data && typeof result.data === 'object') {
        // 如果data是对象，直接使用
        configData = result.data;
      } else if (result.success && result.data) {
        // 兼容旧格式
        configData = result.data;
      } else {
        // 如果没有明确的错误标识，但也没有有效数据，使用空对象
        configData = {};
      }

      // 更新缓存
      this.cache = {
        data: configData,
        timestamp: Date.now()
      };

      console.log('全局配置获取成功:', configData);

      // 触发事件
      this.emitter.emit('config-loaded', configData);

      return configData;
    } catch (error) {
      console.error('获取全局配置失败:', error);
      throw this.handleError(error);
    } finally {
      this.updateState({ isLoading: false });
      this.emitter.emit('loading-end');
    }
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache = {
      data: null,
      timestamp: 0
    };
    console.log('全局配置缓存已清除');
  }

  /**
   * 获取当前缓存的公司名称（同步方法）
   * @returns 当前缓存的公司名称或默认名称
   */
  public getCachedCompanyName(): string {
    return this.state.companyName || this.cache.data?.company_name || this.DEFAULT_COMPANY_NAME;
  }

  /**
   * 检查缓存是否有效
   * @returns 缓存是否有效
   */
  public isCacheValid(): boolean {
    const now = Date.now();
    return this.cache.data !== null && (now - this.cache.timestamp < this.CACHE_TTL);
  }
}

export default GlobalConfigService;
