import React, { useState, useRef, useEffect } from 'react';
import { FileDown, ChevronDown, FileText } from 'lucide-react';
import ExportDialog from './ExportDialog';
import { InventoryItem, FieldDefinition, DeviceCategory } from '../../../types/inventory';
import { DepartmentCategory } from '../../../services/Inventory/departmentService';

// 导出组件属性类型
interface ExportProps {
  items: InventoryItem[];
  selectedItems: string[];
  deviceCategories: DeviceCategory[];
  departmentCategories: DepartmentCategory[];
  tableFields: FieldDefinition[];
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  exportMode?: 'selected' | 'all' | 'filtered';
  totalCount?: number;
  initialSelectedCategory?: string; // 初始选中的分类ID
  initialCategoryMode?: 'device' | 'department'; // 初始分类模式
  onExportTemplate?: () => void; // 导出模板回调
}

/**
 * 导出组件
 * 用于导出设备台账数据
 */
const Export: React.FC<ExportProps> = ({
  items,
  selectedItems,
  deviceCategories,
  departmentCategories,
  tableFields,
  isOpen,
  onOpen,
  onClose,
  exportMode = 'filtered',
  totalCount = 0,
  initialSelectedCategory,
  initialCategoryMode,
  onExportTemplate
}) => {
  // 计算选中项数量
  const selectedCount = selectedItems.length;

  // 下拉菜单状态
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理导出设备台账
  const handleExportInventory = () => {
    setIsDropdownOpen(false);
    onOpen();
  };

  // 处理导出导入模板
  const handleExportTemplate = () => {
    setIsDropdownOpen(false);
    if (onExportTemplate) {
      onExportTemplate();
    }
  };

  // 渲染导出下拉菜单
  return (
    <>
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className={`flex items-center px-3 py-1.5 rounded ${
            selectedCount > 0
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          title="导出选项"
        >
          <FileDown className="h-4 w-4 mr-1" />
          {selectedCount > 0 ? `导出所选(${selectedCount})` : '导出'}
          <ChevronDown className="h-4 w-4 ml-1" />
        </button>

        {/* 下拉菜单 */}
        {isDropdownOpen && (
          <div className="absolute right-0 mt-2 w-52 bg-white border border-gray-200 rounded-lg shadow-xl z-50 animate-in fade-in-0 zoom-in-95 duration-200">
            <div className="py-2">
              <div className="px-3 py-1 text-xs font-medium text-gray-500 uppercase tracking-wide border-b border-gray-100 mb-1">
                导出选项
              </div>

              <button
                onClick={handleExportInventory}
                className="flex items-center w-full px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150 group"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-md bg-blue-100 text-blue-600 mr-3 group-hover:bg-blue-200 transition-colors duration-150">
                  <FileDown className="h-4 w-4" />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium">导出设备台账</div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    {selectedCount > 0
                      ? `导出选中的 ${selectedCount} 条记录`
                      : '导出当前筛选的设备数据'
                    }
                  </div>
                </div>
              </button>

              <button
                onClick={handleExportTemplate}
                className="flex items-center w-full px-4 py-2.5 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-colors duration-150 group"
                disabled={!onExportTemplate}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-md bg-green-100 text-green-600 mr-3 group-hover:bg-green-200 transition-colors duration-150 group-disabled:bg-gray-100 group-disabled:text-gray-400">
                  <FileText className="h-4 w-4" />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium group-disabled:text-gray-400">导出导入模板</div>
                  <div className="text-xs text-gray-500 mt-0.5 group-disabled:text-gray-400">
                    下载用于批量导入的Excel模板
                  </div>
                </div>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 导出对话框 */}
      <ExportDialog
        isOpen={isOpen}
        onClose={onClose}
        items={items}
        deviceCategories={deviceCategories}
        departmentCategories={departmentCategories}
        tableFields={tableFields}
        selectedCount={selectedCount}
        totalCount={totalCount || items.length}
        exportMode={exportMode}
        initialSelectedCategory={initialSelectedCategory}
        initialCategoryMode={initialCategoryMode}
        selectedItems={selectedItems}
      />
    </>
  );
};

export default Export;
