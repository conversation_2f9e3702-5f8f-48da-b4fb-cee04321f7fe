/**
 * 批量添加设备台账信息的WebSocket客户端脚本
 * 使用方法：
 * 1. 在浏览器控制台中运行此脚本
 * 2. 或者使用Node.js运行此脚本
 */

// 配置参数
const config = {
    // WebSocket服务器地址
    wsUrl: 'ws://localhost:9002',
    // 批量添加的设备数量
    totalDevices: 1000,
    // 每批次添加的设备数量
    batchSize: 10,
    // 每次请求之间的延迟（毫秒）
    delay: 500,
    // DLL名称
    dllName: 'AccountTableDll'
};

// 设备分类数据 - 使用实际系统中的数据
const categories = [
    { parent_id: 2, parent: '移动存储类', sub_id: 7, sub: 'USBKey' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 13, sub: '一体机' },
    { parent_id: 6, parent: '网络设备类', sub_id: 25, sub: '交换机' },
    { parent_id: 2, parent: '移动存储类', sub_id: 4, sub: '优盘' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 16, sub: '传真机' },
    { parent_id: 1, parent: '计算机类', sub_id: 2, sub: '便携机' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 21, sub: '分断机' },
    { parent_id: 3, parent: '外接设备类', sub_id: 10, sub: '单导盒' },
    { parent_id: 1, parent: '计算机类', sub_id: 1, sub: '台式机' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 15, sub: '复印机' },
    { parent_id: 3, parent: '外接设备类', sub_id: 9, sub: '外置光驱' },
    { parent_id: 2, parent: '移动存储类', sub_id: 6, sub: '存储卡' },
    { parent_id: 5, parent: '声像设备类', sub_id: 23, sub: '录音笔' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 14, sub: '打印机' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 17, sub: '扫描仪' },
    { parent_id: 3, parent: '外接设备类', sub_id: 12, sub: '扫描笔' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 20, sub: '投影仪' },
    { parent_id: 5, parent: '声像设备类', sub_id: 24, sub: '摄像机' },
    { parent_id: 1, parent: '计算机类', sub_id: 3, sub: '服务器' },
    { parent_id: 5, parent: '声像设备类', sub_id: 22, sub: '照相机' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 18, sub: '碎纸机' },
    { parent_id: 2, parent: '移动存储类', sub_id: 5, sub: '移动硬盘' },
    { parent_id: 2, parent: '移动存储类', sub_id: 8, sub: '税控盘' },
    { parent_id: 4, parent: '办公自动化设备类', sub_id: 19, sub: '装订机' },
    { parent_id: 3, parent: '外接设备类', sub_id: 11, sub: '读卡器' },
    { parent_id: 6, parent: '网络设备类', sub_id: 26, sub: '路由器' },
    { parent_id: 8, parent: '飞行类', sub_id: 27, sub: '飞机' }
];

// 部门和人员数据 - 使用实际系统中的数据
const departments = [
    {
        id: 4,
        name: '测试部门3级',
        persons: [
            { id: 1, name: '张三', alias: '', position: '工程师', mobile: '13800000001' },
            { id: 2, name: '李四', alias: '', position: '主管', mobile: '13800000002' }
        ]
    },
    {
        id: 3,
        name: '测试部门2级aaaa',
        persons: [
            { id: 3, name: 'u1', alias: '用户1', position: '技术员', mobile: '13800000003' },
            { id: 4, name: 'u2', alias: '用户2', position: '助理', mobile: '13800000004' }
        ]
    }
];

// 设备品牌和型号
const devices = [
    { brand: '联想', models: ['ThinkPad X1', 'ThinkCentre M720', 'ThinkStation P520', 'Legion Y7000P'] },
    { brand: '戴尔', models: ['Latitude 7420', 'OptiPlex 7080', 'Precision 7920', 'XPS 15'] },
    { brand: '惠普', models: ['EliteBook 840', 'ProDesk 600', 'Z8 G4 工作站', 'Spectre x360'] },
    { brand: '华为', models: ['MateBook X Pro', 'MateStation S', '泰山服务器', 'MateBook D'] }
];

// 位置数据
const locations = [
    '办公楼A座101室', '办公楼A座102室', '办公楼B座101室', '办公楼B座102室',
    '研发中心101室', '研发中心102室', '数据中心A区', '数据中心B区',
    '指挥中心主控室', '指挥中心会议室', '安全保密室A', '安全保密室B'
];

// 用途数据
const purposes = [
    '日常办公', '软件开发', '硬件开发', '系统测试', '数据分析', '网络管理',
    '安全管理', '文档处理', '数据存储', '数据备份', '网络通信', '安全防护'
];

// 密级数据
const securityLevels = [
    { level: '公开', prefix: 'GK' },
    { level: '内部', prefix: 'NB' },
    { level: '秘密', prefix: 'MM' },
    { level: '机密', prefix: 'JM' },
    { level: '绝密', prefix: 'JM' },
    { level: '非密专用', prefix: 'FM' },
    { level: '涉密专用', prefix: 'SM' }
];

// 使用状态数据
const usageStatus = ['在用', '停用', '归库', '维修', '报废'];

// 工具函数
function getRandomItem(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomDate(start, end) {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function dateToTimestamp(date) {
    return Math.floor(date.getTime() / 1000);
}

function generateRandomIP() {
    return `${getRandomInt(1, 255)}.${getRandomInt(0, 255)}.${getRandomInt(0, 255)}.${getRandomInt(0, 255)}`;
}

function generateRandomMAC() {
    return Array.from({length: 6}, () => getRandomInt(0, 255).toString(16).padStart(2, '0')).join(':');
}

// 用于存储已生成的序列号，确保不重复
const generatedSerialNumbers = new Set();

// 生成不重复的随机序列号
function generateRandomSerialNumber() {
    let serialNumber;
    do {
        serialNumber = `SN-${getRandomInt(100000, 999999)}`;
    } while (generatedSerialNumbers.has(serialNumber));

    generatedSerialNumbers.add(serialNumber);
    return serialNumber;
}

// 全局计数器，用于生成唯一序列号
let globalCounter = 0;

// 生成UUID的一部分，用于确保全局唯一性
function generatePartialUUID() {
    return Math.random().toString(36).substring(2, 6);
}

// 获取当前时间的微秒级时间戳（模拟）
function getMicrosecondTimestamp() {
    return Date.now() + Math.floor(Math.random() * 1000);
}

// 用于存储已生成的保密编号，确保不重复
const generatedSecurityCodes = new Set();

// 生成不重复的保密编号 - 增强版规则：
// 密级前缀 + 设备类型代码(2位) + 批次号(4位) + 时间戳(8位) + 序列号(4位)
// 例如：JM-JT-ABCD-************* 表示 机密-计算机类台式机-ABCD批次-2023年5月12日15时-0001号
function generateRandomSecurityCode(prefix, category, subCategory) {
    // 获取当前日期和时间信息
    const now = new Date();
    const year = now.getFullYear().toString().slice(2); // 年份后两位
    const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份，补零
    const day = now.getDate().toString().padStart(2, '0'); // 日期，补零
    const hour = now.getHours().toString().padStart(2, '0'); // 小时，补零
    const dateTimeCode = `${year}${month}${day}${hour}`;

    // 设备类型代码：取分类名称和子分类名称的首字母拼音
    const categoryCode = getCategoryCode(category, subCategory);

    // 生成批次号（使用部分UUID）
    const batchCode = generatePartialUUID().toUpperCase();

    // 递增全局计数器
    globalCounter++;

    // 生成序列号（4位，基于全局计数器）
    const serialNum = globalCounter.toString().padStart(4, '0');

    // 组合生成最终的保密编号
    const code = `${prefix}-${categoryCode}-${batchCode}-${dateTimeCode}-${serialNum}`;

    // 极少数情况下可能仍有重复，使用Set进行最后的检查
    if (generatedSecurityCodes.has(code)) {
        // 如果重复，添加微秒级时间戳作为后缀
        const microsecondSuffix = getMicrosecondTimestamp().toString().slice(-4);
        const uniqueCode = `${code}-${microsecondSuffix}`;
        generatedSecurityCodes.add(uniqueCode);
        return uniqueCode;
    }

    generatedSecurityCodes.add(code);
    return code;
}

// 获取设备类型代码
function getCategoryCode(category, subCategory) {
    // 根据分类和子分类生成2位代码
    // 这里使用简化的逻辑，实际应用中可能需要更复杂的映射
    const categoryFirstChar = getPinyinFirstChar(category);
    const subCategoryFirstChar = getPinyinFirstChar(subCategory);
    return `${categoryFirstChar}${subCategoryFirstChar}`.toUpperCase();
}

// 获取中文拼音首字母
function getPinyinFirstChar(text) {
    // 简化的拼音首字母映射
    const pinyinMap = {
        '计算机类': 'J',
        '移动存储类': 'Y',
        '外接设备类': 'W',
        '办公自动化设备类': 'B',
        '声像设备类': 'S',
        '网络设备类': 'W',
        '飞行类': 'F',
        '台式机': 'T',
        '便携机': 'B',
        '服务器': 'F',
        '优盘': 'Y',
        'USBKey': 'U',
        '存储卡': 'C',
        '移动硬盘': 'Y',
        '税控盘': 'S',
        '外置光驱': 'W',
        '单导盒': 'D',
        '读卡器': 'D',
        '扫描笔': 'S',
        '一体机': 'Y',
        '打印机': 'D',
        '复印机': 'F',
        '传真机': 'C',
        '扫描仪': 'S',
        '碎纸机': 'S',
        '装订机': 'Z',
        '分断机': 'F',
        '投影仪': 'T',
        '录音笔': 'L',
        '照相机': 'Z',
        '摄像机': 'S',
        '交换机': 'J',
        '路由器': 'L',
        '飞机': 'F'
    };

    // 如果在映射中找到，返回对应的首字母，否则返回X
    return pinyinMap[text] || 'X';
}

// 用于存储已生成的RFID编号，确保不重复
const generatedRFIDs = new Set();

// 生成不重复的随机RFID编号
function generateRandomRFID() {
    let rfid;
    do {
        rfid = `RFID-${getRandomInt(100000, 999999)}`;
    } while (generatedRFIDs.has(rfid));

    generatedRFIDs.add(rfid);
    return rfid;
}

// 生成随机设备数据 - 使用实际系统中的数据
function generateRandomDeviceData(index) {
    // 随机选择分类
    const category = getRandomItem(categories);

    // 随机选择部门和人员
    const department = getRandomItem(departments);
    const person = getRandomItem(department.persons);

    // 随机选择设备品牌和型号
    const device = getRandomItem(devices);
    const model = getRandomItem(device.models);

    // 随机选择密级
    const securityLevel = getRandomItem(securityLevels);

    // 随机日期
    const startDate = new Date(2020, 0, 1);
    const endDate = new Date();
    const purchaseDate = getRandomDate(startDate, endDate);
    const activationDate = new Date(purchaseDate.getTime() + getRandomInt(0, 30 * 24 * 60 * 60 * 1000)); // 0-30天后激活

    // 扩展字段 - 根据设备类型生成不同的扩展字段
    const extendedFields = {};

    // 计算机类设备的扩展字段
    if (category.parent_id === 1) {
        if (Math.random() > 0.5) {
            extendedFields['CPU'] = getRandomItem(['Intel i5', 'Intel i7', 'Intel i9', 'AMD Ryzen 5', 'AMD Ryzen 7']);
        }
        if (Math.random() > 0.5) {
            extendedFields['内存'] = getRandomItem(['8GB', '16GB', '32GB', '64GB']);
        }
        if (Math.random() > 0.5) {
            extendedFields['硬盘'] = getRandomItem(['256GB SSD', '512GB SSD', '1TB HDD', '2TB HDD']);
        }
    }
    // 移动存储类设备的扩展字段
    else if (category.parent_id === 2) {
        if (Math.random() > 0.5) {
            extendedFields['容量'] = getRandomItem(['32GB', '64GB', '128GB', '256GB', '512GB', '1TB', '2TB']);
        }
        if (Math.random() > 0.5) {
            extendedFields['接口类型'] = getRandomItem(['USB 2.0', 'USB 3.0', 'USB 3.1', 'USB-C', 'Thunderbolt']);
        }
    }
    // 外接设备类的扩展字段
    else if (category.parent_id === 3) {
        if (Math.random() > 0.5) {
            extendedFields['接口类型'] = getRandomItem(['USB', 'HDMI', 'VGA', 'DisplayPort', '蓝牙']);
        }
    }
    // 办公自动化设备类的扩展字段
    else if (category.parent_id === 4) {
        if (Math.random() > 0.5) {
            extendedFields['纸张尺寸'] = getRandomItem(['A4', 'A3', 'B5', 'Letter']);
        }
        if (Math.random() > 0.5) {
            extendedFields['打印速度'] = `${getRandomInt(10, 50)}页/分钟`;
        }
    }

    // 通用扩展字段
    if (Math.random() > 0.7) {
        extendedFields[`${category.sub}_编号`] = `NO${getRandomInt(1000, 9999)}`;
    }
    if (Math.random() > 0.8) {
        extendedFields['备注'] = `自动生成的测试设备 #${index + 1}`;
    }

    // 构建设备数据 - 使用实际系统中的字段名
    return {
        parent_category_name: category.parent,
        sub_category_name: category.sub,
        device_name_brand: device.brand,
        model: model,
        location: getRandomItem(locations),
        purpose: getRandomItem(purposes),
        department_name: department.name,
        responsible_person_name: person.name,
        responsible_person_alias: person.alias || '',
        confidentiality_level: securityLevel.level,
        confidentiality_code: generateRandomSecurityCode(securityLevel.prefix, category.parent, category.sub),
        usage_status: getRandomItem(usageStatus),
        is_networked: Math.random() > 0.5,
        serial_number_asset_number: generateRandomSerialNumber(),
        ip_address: generateRandomIP(),
        mac_address: generateRandomMAC(),
        purchase_timestamp: dateToTimestamp(purchaseDate),
        activation_timestamp: dateToTimestamp(activationDate),
        security_code_rfid: generateRandomRFID(),
        extended_fields: extendedFields
    };
}

// 生成所有设备数据
function generateAllDevicesData(count) {
    const devices = [];
    for (let i = 0; i < count; i++) {
        devices.push(generateRandomDeviceData(i));
    }
    return devices;
}

// 批量添加设备
async function batchAddDevices() {
    // 生成所有设备数据
    console.log(`正在生成 ${config.totalDevices} 条随机设备数据...`);
    const allDevices = generateAllDevicesData(config.totalDevices);
    console.log(`数据生成完成，开始批量添加...`);

    // 连接WebSocket
    const ws = new WebSocket(config.wsUrl);

    // 统计信息
    let successCount = 0;
    let failedCount = 0;
    let currentIndex = 0;

    // 处理WebSocket事件
    ws.onopen = function() {
        console.log('WebSocket连接成功，开始添加设备...');
        processNextBatch();
    };

    ws.onclose = function() {
        console.log('WebSocket连接已关闭');
        console.log(`批量添加完成: 总计 ${config.totalDevices} 条, 成功 ${successCount} 条, 失败 ${failedCount} 条`);
    };

    ws.onerror = function(error) {
        console.error(`WebSocket错误: ${error.message || '未知错误'}`);
    };

    ws.onmessage = function(event) {
        try {
            const response = JSON.parse(event.data);

            if (response.success && response.data && response.data.id) {
                successCount++;
                console.log(`成功添加设备，ID: ${response.data.id}, 进度: ${currentIndex}/${config.totalDevices}`);
            } else {
                failedCount++;
                console.error(`添加设备失败: ${response.message || '未知错误'}`);
            }

            // 更新进度
            updateProgress();

            // 处理下一批
            if (currentIndex < allDevices.length) {
                setTimeout(processNextBatch, config.delay);
            } else {
                console.log('所有设备已添加完成，正在关闭连接...');
                ws.close();
            }
        } catch (error) {
            console.error(`解析响应失败: ${error.message}`);
            failedCount++;

            // 继续处理下一批
            if (currentIndex < allDevices.length) {
                setTimeout(processNextBatch, config.delay);
            } else {
                console.log('所有设备已添加完成，正在关闭连接...');
                ws.close();
            }
        }
    };

    // 处理下一批设备
    function processNextBatch() {
        const endIndex = Math.min(currentIndex + config.batchSize, allDevices.length);
        const batch = allDevices.slice(currentIndex, endIndex);

        batch.forEach(device => {
            try {
                const request = {
                    type: "execute",
                    function: `${config.dllName}::DbFun`,
                    params: {
                        action: "add_device",
                        action_params: device
                    },
                    priority: 0
                };

                ws.send(JSON.stringify(request));
                currentIndex++;
            } catch (error) {
                console.error(`发送请求失败: ${error.message}`);
                failedCount++;
                currentIndex++;
            }
        });
    }

    // 更新进度
    function updateProgress() {
        const percentage = Math.floor((currentIndex / config.totalDevices) * 100);
        console.log(`进度: ${percentage}%, 成功: ${successCount}, 失败: ${failedCount}`);
    }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    console.log('在浏览器环境中运行批量添加脚本...');
    window.batchAddDevices = batchAddDevices;
    console.log('请调用 window.batchAddDevices() 开始批量添加');
}
// 如果在Node.js环境中运行
else if (typeof process !== 'undefined') {
    console.log('在Node.js环境中运行批量添加脚本...');
    const WebSocket = require('ws');
    batchAddDevices();
}
