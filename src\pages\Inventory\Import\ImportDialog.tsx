import React, { useState, useEffect, useRef } from 'react';
import { X, FileUp, Check, AlertCircle, Download, HelpCircle } from 'lucide-react';
import useImport from '../../../hooks/Inventory/Import/useImport';

// 对话框属性类型
interface ImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: () => void;
}

/**
 * 导入对话框组件
 * 用于导入设备台账数据
 */
const ImportDialog: React.FC<ImportDialogProps> = ({
  isOpen,
  onClose,
  onImportComplete
}) => {
  // 使用导入Hook
  const {
    isImporting,
    progress,
    lastImportCount,
    validationErrors,
    error,
    importInventory,
    validateImportFile,
    generateImportTemplate,
    cancelImport,
    clearValidationErrors
  } = useImport();

  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 本地状态
  const [format, setFormat] = useState<'csv' | 'excel'>('excel');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [localError, setLocalError] = useState<string | undefined>(error);
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [isDownloadingTemplate, setIsDownloadingTemplate] = useState<boolean>(false);
  const [showHelp, setShowHelp] = useState<boolean>(false);

  // 同步错误状态
  useEffect(() => {
    setLocalError(error);
  }, [error]);

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  // 处理文件选择
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // 检查文件类型
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      if ((format === 'csv' && fileExt !== 'csv') ||
          (format === 'excel' && fileExt !== 'xlsx' && fileExt !== 'xls')) {
        setLocalError(`文件类型不匹配，请选择${format === 'csv' ? 'CSV' : 'Excel'}文件`);
        return;
      }

      setSelectedFile(file);
      clearValidationErrors();
    }
  };

  // 触发文件选择对话框
  const handleSelectFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理导入
  const handleImport = async () => {
    try {
      // 重置状态
      setShowSuccess(false);
      setLocalError(undefined);
      clearValidationErrors();

      // 验证文件
      if (!selectedFile) {
        setLocalError('请选择导入文件');
        return;
      }

      // 执行导入
      setIsValidating(true);
      const result = await importInventory(selectedFile);
      setIsValidating(false);

      if (result.success) {
        // 显示成功消息
        setShowSuccess(true);

        // 4秒后关闭对话框，给用户更多时间感知成功状态
        setTimeout(() => {
          onClose();
          onImportComplete(); // 通知父组件导入完成，刷新数据
          setShowSuccess(false);
        }, 4000);
      }
    } catch (err: any) {
      console.error('导入失败:', err);
      setLocalError(err.message || '导入失败');
    }
  };

  // 处理取消
  const handleCancel = () => {
    if (isImporting) {
      cancelImport();
    }
    clearValidationErrors();
    setSelectedFile(null);
    onClose();
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      setIsDownloadingTemplate(true);

      // 生成模板文件名
      const fileName = `设备台账导入模板.${format === 'csv' ? 'csv' : 'xlsx'}`;

      // 生成模板
      const blob = generateImportTemplate(format);

      // 下载文件
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }, 0);
    } catch (err: any) {
      console.error('下载模板失败:', err);
      setLocalError(err.message || '下载模板失败');
    } finally {
      setIsDownloadingTemplate(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-[500px] max-w-full">
        {/* 对话框标题 */}
        <div className="flex items-center justify-between px-4 py-3 border-b">
          <h2 className="text-lg font-semibold flex items-center">
            <FileUp className="h-5 w-5 mr-2" />
            导入设备台账
          </h2>
          <button
            onClick={handleCancel}
            className="text-gray-500 hover:text-gray-700"
            disabled={isImporting}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="p-4">
          {/* 导入格式选择 */}
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">导入格式</label>
            <div className="flex space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="format"
                  value="excel"
                  checked={format === 'excel'}
                  onChange={() => setFormat('excel')}
                  disabled={isImporting}
                />
                <span className="ml-2">Excel (.xlsx)</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="format"
                  value="csv"
                  checked={format === 'csv'}
                  onChange={() => setFormat('csv')}
                  disabled={isImporting}
                />
                <span className="ml-2">CSV (.csv)</span>
              </label>
            </div>
          </div>

          {/* 文件选择 */}
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              导入文件
              <button
                onClick={() => setShowHelp(!showHelp)}
                className="ml-2 text-gray-500 hover:text-gray-700"
                title="查看导入说明"
              >
                <HelpCircle className="h-4 w-4" />
              </button>
            </label>
            <div className="flex">
              <input
                type="text"
                className="flex-1 border rounded-l px-3 py-2 text-gray-700"
                placeholder="选择导入文件..."
                value={selectedFile ? selectedFile.name : ''}
                readOnly
              />
              <button
                onClick={handleSelectFile}
                className="bg-gray-100 text-gray-700 px-3 py-2 rounded-r border border-l-0 hover:bg-gray-200"
                disabled={isImporting}
              >
                浏览...
              </button>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept={format === 'csv' ? '.csv' : '.xlsx,.xls'}
              className="hidden"
              onChange={handleFileChange}
            />
          </div>

          {/* 导入帮助 */}
          {showHelp && (
            <div className="mb-4 p-3 bg-blue-50 text-blue-700 rounded text-sm">
              <h3 className="font-medium mb-1">导入说明：</h3>
              <ul className="list-disc pl-5 space-y-1">
                <li>导入文件必须符合指定格式，请下载模板填写</li>
                <li>必填字段：设备名称、设备类型、责任人</li>
                <li>导入前请确保设备类型已在系统中存在</li>
                <li>责任人必须在部门树中存在</li>
                <li>导入时会自动验证数据格式</li>
              </ul>
              <div className="mt-2">
                <button
                  onClick={handleDownloadTemplate}
                  className="flex items-center text-blue-600 hover:text-blue-800"
                  disabled={isDownloadingTemplate}
                >
                  <Download className="h-4 w-4 mr-1" />
                  {isDownloadingTemplate ? '下载中...' : '下载导入模板'}
                </button>
              </div>
            </div>
          )}

          {/* 进度条 */}
          {isImporting && (
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">导入进度</label>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p className="text-right text-sm text-gray-500 mt-1">{progress}%</p>
            </div>
          )}

          {/* 成功消息 - 优化后的动画效果和样式 */}
          {showSuccess && (
            <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md border border-green-200 shadow-sm flex items-center animate-fadeIn">
              <div className="mr-3 flex-shrink-0 h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <Check className="h-5 w-5 text-green-600 animate-checkmark" />
              </div>
              <div>
                <p className="font-medium">导入成功</p>
                <p className="text-sm">已成功导入 {lastImportCount} 条记录到系统</p>
              </div>
            </div>
          )}

          {/* 验证错误 - 优化后的动画效果和样式 */}
          {validationErrors.length > 0 && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-200 shadow-sm animate-fadeIn">
              <div className="flex items-center mb-2">
                <div className="mr-2 flex-shrink-0 h-6 w-6 rounded-full bg-red-100 flex items-center justify-center">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                </div>
                <span className="font-medium">导入文件验证失败</span>
              </div>
              <div className="ml-8 mt-2">
                <ul className="list-disc pl-5 space-y-1.5 text-sm">
                  {validationErrors.map((err, index) => (
                    <li key={index} className="animate-fadeIn" style={{ animationDelay: `${index * 50}ms` }}>{err}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* 错误消息 - 优化后的动画效果和样式 */}
          {localError && !validationErrors.length && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-200 shadow-sm flex items-center animate-fadeIn">
              <div className="mr-3 flex-shrink-0 h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                <AlertCircle className="h-5 w-5 text-red-600 animate-pulse" />
              </div>
              <div>
                <p className="font-medium">导入失败</p>
                <p className="text-sm">{localError}</p>
              </div>
            </div>
          )}
        </div>

        {/* 对话框按钮 */}
        <div className="px-4 py-3 border-t flex justify-end space-x-3">
          <button
            onClick={handleCancel}
            className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-100"
            disabled={isImporting}
          >
            {isImporting ? '取消导入' : '关闭'}
          </button>
          <button
            onClick={handleImport}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
            disabled={isImporting || isValidating || !selectedFile}
          >
            {isValidating ? '验证中...' : isImporting ? '导入中...' : '开始导入'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImportDialog;
