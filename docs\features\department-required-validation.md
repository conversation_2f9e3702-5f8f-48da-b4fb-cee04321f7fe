# 部门必需验证功能

## 📋 功能概述

当用户在台账管理页面点击"添加设备台账"按钮时，系统会检查部门分类树是否为空。如果部门分类树只有根节点而没有任何部门和人员，系统会阻止添加操作并显示提示对话框，引导用户先建立部门和人员。

## 🎯 触发条件

### **检查条件**
- 用户点击"添加设备台账"按钮
- 操作来源为总表（非右键菜单添加）
- 部门分类树满足以下任一条件：
  - 只有根节点，没有任何子节点
  - 只有部门节点，没有人员节点
  - 既没有部门也没有人员

### **不触发条件**
- 从右键菜单添加设备台账（`fromContextMenu = true`）
- 部门分类树中已有人员节点

## 🔧 技术实现

### **重要改进**
- **递归检查**：验证逻辑使用递归方式检查整个部门树结构，而不仅仅检查根节点的直接子节点
- **深度遍历**：能够正确识别嵌套在部门下的人员节点，确保验证的准确性

### **1. 核心文件**

#### **部门验证工具函数**
`src/utils/departmentValidation.ts`
- `isDepartmentTreeEmpty()` - 检查部门分类树是否为空
- `hasDepartmentsButNoPersons()` - 检查是否只有部门没有人员
- `getDepartmentTreeStats()` - 获取部门分类树统计信息

#### **提示对话框组件**
`src/pages/Inventory/InventoryManagement/Dialogs/DepartmentRequiredDialog.tsx`
- 显示友好的提示信息
- 提供确认按钮
- 支持ESC键关闭

#### **业务逻辑Hook**
`src/hooks/Inventory/useInventoryManagement.ts`
- 在`handleAdd`函数中添加部门检查逻辑
- 新增`handleDepartmentRequiredConfirm`处理确认操作
- 新增`showDepartmentRequiredDialog`状态管理

### **2. 验证逻辑**

```typescript
// 检查部门分类树是否为空
if (!fromContextMenu && isDepartmentTreeEmpty(departmentCategories)) {
  setShowDepartmentRequiredDialog(true);
  return; // 阻止默认的添加对话框弹出
}
```

### **3. 部门树状态判断**

```typescript
// 递归检查树中是否有人员节点
function hasPersonsInTree(node: DepartmentCategory): boolean {
  // 检查当前节点是否是人员
  if (node.id.startsWith('person-')) {
    return true;
  }

  // 递归检查子节点
  if (node.children && node.children.length > 0) {
    return node.children.some(child => hasPersonsInTree(child));
  }

  return false;
}

// 递归检查树中是否有部门节点
function hasDepartmentsInTree(node: DepartmentCategory): boolean {
  // 检查当前节点是否是部门（排除根节点）
  if (node.id !== 'all-dept' && node.id.startsWith('dept-')) {
    return true;
  }

  // 递归检查子节点
  if (node.children && node.children.length > 0) {
    return node.children.some(child => hasDepartmentsInTree(child));
  }

  return false;
}

export function isDepartmentTreeEmpty(departmentCategories: DepartmentCategory[]): boolean {
  // 1. 检查是否有根节点
  const rootNode = departmentCategories.find(cat => cat.id === 'all-dept');
  if (!rootNode || !rootNode.children || rootNode.children.length === 0) {
    return true;
  }

  // 2. 递归检查整个树中是否有部门和人员
  const hasDepartments = hasDepartmentsInTree(rootNode);
  const hasPersons = hasPersonsInTree(rootNode);

  // 3. 判断逻辑
  if (!hasDepartments && !hasPersons) return true;  // 既没有部门也没有人员
  if (hasDepartments && !hasPersons) return true;   // 有部门但没有人员
  return false; // 有人员，认为不为空
}
```

## 🎨 用户体验流程

### **正常流程**
1. 用户点击"添加设备台账"按钮
2. 系统检查部门分类树状态
3. 如果有人员，直接打开添加设备对话框

### **提示流程**
1. 用户点击"添加设备台账"按钮
2. 系统检查发现部门分类树为空
3. 显示提示对话框："请先建立部门和人员后再添加设备台账"
4. 用户点击"确认"按钮
5. 关闭提示对话框
6. 自动切换到"部门分类树"模式（如果当前不是）

## 📱 界面设计

### **提示对话框特点**
- **图标**：使用警告图标（AlertTriangle）突出提示性质
- **标题**：简洁的"提示"标题
- **内容**：清晰的说明文字和补充说明
- **按钮**：单个"确认"按钮，符合用户预期
- **样式**：与项目整体设计风格保持一致

### **视觉层次**
```
┌─────────────────────────────────┐
│ ⚠️  提示                    ✕   │
├─────────────────────────────────┤
│ ⚠️  请先建立部门和人员后再添加   │
│     设备台账                    │
│                                 │
│     系统需要部门和人员信息来    │
│     管理设备的归属关系          │
├─────────────────────────────────┤
│                        [确认]   │
└─────────────────────────────────┘
```

## 🔍 测试场景

### **场景1：空部门树**
- **前置条件**：部门分类树只有根节点
- **操作**：点击"添加设备台账"
- **预期结果**：显示提示对话框

### **场景2：只有部门无人员**
- **前置条件**：部门分类树有部门但无人员
- **操作**：点击"添加设备台账"
- **预期结果**：显示提示对话框

### **场景3：有人员**
- **前置条件**：部门分类树有人员
- **操作**：点击"添加设备台账"
- **预期结果**：直接打开添加设备对话框

### **场景4：右键菜单添加**
- **前置条件**：部门分类树为空
- **操作**：从右键菜单选择"添加台账"
- **预期结果**：直接打开添加设备对话框（不检查部门）

## 🚀 扩展性

### **可配置性**
- 验证逻辑可通过修改`isDepartmentTreeEmpty`函数调整
- 提示文案可在对话框组件中修改
- 可添加更多验证条件

### **国际化支持**
- 提示文案支持多语言
- 图标和布局适配不同语言

### **主题定制**
- 对话框样式支持主题定制
- 图标颜色可配置

## 📝 维护说明

### **关键依赖**
- `isDepartmentTreeEmpty` - 核心验证逻辑
- `DepartmentRequiredDialog` - 提示界面
- `useInventoryManagement` - 业务逻辑集成

### **注意事项**
- 确保部门分类树数据结构的一致性
- 验证逻辑需要与部门管理功能保持同步
- 对话框的焦点管理和键盘导航需要测试

### **性能考虑**
- 验证函数执行效率高，对用户体验无影响
- 对话框组件按需渲染，不影响页面性能
