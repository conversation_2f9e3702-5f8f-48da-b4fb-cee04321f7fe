import React, { useState, useRef, useEffect } from 'react';

interface IPAddressFieldProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  error?: string;
  label: string;
  required?: boolean;
}

/**
 * IP地址专用输入组件
 * 采用4个分段的格式化输入框，支持键盘导航和复制粘贴
 */
const IPAddressField: React.FC<IPAddressFieldProps> = ({
  value,
  onChange,
  disabled = false,
  error,
  label,
  required = false
}) => {
  const [segments, setSegments] = useState<string[]>(['', '', '', '']);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 解析IP地址字符串为分段数组
  const parseIPAddress = (ip: string): string[] => {
    if (!ip) return ['', '', '', ''];
    const parts = ip.split('.');
    const result = ['', '', '', ''];
    for (let i = 0; i < Math.min(parts.length, 4); i++) {
      const num = parseInt(parts[i], 10);
      if (!isNaN(num) && num >= 0 && num <= 255) {
        result[i] = parts[i];
      }
    }
    return result;
  };

  // 将分段数组转换为IP地址字符串
  const formatIPAddress = (segs: string[]): string => {
    return segs.join('.');
  };

  // 初始化和同步外部值
  useEffect(() => {
    const newSegments = parseIPAddress(value);
    setSegments(newSegments);
  }, [value]);

  // 处理单个分段的输入变化
  const handleSegmentChange = (index: number, inputValue: string) => {
    // 只允许数字输入
    const numericValue = inputValue.replace(/[^0-9]/g, '');
    
    // 限制输入长度和数值范围
    let finalValue = numericValue;
    if (numericValue.length > 3) {
      finalValue = numericValue.slice(0, 3);
    }
    
    const num = parseInt(finalValue, 10);
    if (!isNaN(num) && num > 255) {
      finalValue = '255';
    }

    const newSegments = [...segments];
    newSegments[index] = finalValue;
    setSegments(newSegments);

    // 更新外部值
    const newValue = formatIPAddress(newSegments);
    onChange(newValue);

    // 自动跳转到下一个输入框
    if (finalValue.length === 3 || (finalValue.length > 0 && parseInt(finalValue, 10) > 25)) {
      if (index < 3) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  // 处理键盘事件
  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;
    
    switch (e.key) {
      case '.':
        e.preventDefault();
        if (index < 3) {
          inputRefs.current[index + 1]?.focus();
        }
        break;
      case 'ArrowRight':
        if (target.selectionStart === target.value.length && index < 3) {
          e.preventDefault();
          inputRefs.current[index + 1]?.focus();
        }
        break;
      case 'ArrowLeft':
        if (target.selectionStart === 0 && index > 0) {
          e.preventDefault();
          inputRefs.current[index - 1]?.focus();
        }
        break;
      case 'Backspace':
        if (target.value === '' && index > 0) {
          e.preventDefault();
          inputRefs.current[index - 1]?.focus();
        }
        break;
      case 'Tab':
        // 让Tab键正常工作，不做特殊处理
        break;
    }
  };

  // 处理粘贴事件
  const handlePaste = (index: number, e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    
    // 尝试解析为完整IP地址
    const ipMatch = pastedText.match(/^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/);
    if (ipMatch) {
      const newSegments = [ipMatch[1], ipMatch[2], ipMatch[3], ipMatch[4]];
      // 验证每个分段
      const validSegments = newSegments.map(seg => {
        const num = parseInt(seg, 10);
        return (num >= 0 && num <= 255) ? seg : '';
      });
      
      setSegments(validSegments);
      onChange(formatIPAddress(validSegments));
      
      // 聚焦到最后一个非空分段
      let lastIndex = -1;
      for (let i = validSegments.length - 1; i >= 0; i--) {
        if (validSegments[i] !== '') {
          lastIndex = i;
          break;
        }
      }
      if (lastIndex >= 0 && lastIndex < 3) {
        inputRefs.current[lastIndex + 1]?.focus();
      }
    } else {
      // 如果不是完整IP，只在当前分段处理数字
      const numericValue = pastedText.replace(/[^0-9]/g, '').slice(0, 3);
      if (numericValue) {
        const num = parseInt(numericValue, 10);
        const finalValue = num > 255 ? '255' : numericValue;
        handleSegmentChange(index, finalValue);
      }
    }
  };

  return (
    <>
      <label className="block text-sm font-bold text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <div className="flex items-center space-x-1">
        {segments.map((segment, index) => (
          <React.Fragment key={index}>
            <input
              ref={(el) => (inputRefs.current[index] = el)}
              type="text"
              value={segment}
              onChange={(e) => handleSegmentChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={(e) => handlePaste(index, e)}
              placeholder={['192', '168', '1', '1'][index]}
              disabled={disabled}
              className={`w-12 px-1 py-2 text-center border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm placeholder-gray-300 ${
                error
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300'
              }`}
              maxLength={3}
            />
            {index < 3 && (
              <span className="text-gray-400 select-none">.</span>
            )}
          </React.Fragment>
        ))}
      </div>
    </>
  );
};

export default IPAddressField;
