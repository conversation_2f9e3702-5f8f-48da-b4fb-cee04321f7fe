import { useState, useEffect, useCallback } from 'react';
import RfidManagementService, { RfidBindResult, RfidDeleteResult } from '../../services/Inventory/rfidManagementService';
import { InventoryItem } from '../../types/inventory';
import { extractDeviceIds } from '../../utils/deviceUtils';
import { handleRfidError } from '../../utils/errorHandler';

/**
 * 操作类型枚举
 */
type OperationType = 'bind' | 'delete' | null;

/**
 * RFID管理Hook
 * 处理RFID码的绑定和删除逻辑，以及UI状态管理
 */
export default function useRfidManagement(initialSelectedItems: InventoryItem[] = []) {
  // 数据状态
  const [isLoading, setIsLoading] = useState(false);
  const [lastBindResult, setLastBindResult] = useState<RfidBindResult | undefined>();
  const [lastDeleteResult, setLastDeleteResult] = useState<RfidDeleteResult | undefined>();
  const [error, setError] = useState<string | null>(null);

  // UI状态管理
  const [operationType, setOperationType] = useState<OperationType>(null);
  const [showResult, setShowResult] = useState(false);
  const [showResultDialog, setShowResultDialog] = useState(false);
  const [localSelectedItems, setLocalSelectedItems] = useState<InventoryItem[]>([]);

  // 获取服务实例
  const rfidService = RfidManagementService.getInstance();

  // 初始化本地选中项目
  useEffect(() => {
    setLocalSelectedItems([...initialSelectedItems]);
    setOperationType(null);
    setShowResult(false);
  }, [initialSelectedItems]);

  // 监听服务状态变化
  useEffect(() => {
    const handleBindStart = () => {
      setIsLoading(true);
      setError(null);
    };

    const handleBindSuccess = (result: RfidBindResult) => {
      console.log('handleBindSuccess - 接收到绑定结果:', result);
      setIsLoading(false);
      setLastBindResult(result);
      setOperationType('bind'); // 确保操作类型设置为绑定
      setError(null);
      // 绑定成功后显示结果对话框
      setShowResult(true);
      setShowResultDialog(true);
      console.log('handleBindSuccess - 状态已更新，operationType: bind, showResultDialog:', true);
    };

    const handleBindError = (error: Error) => {
      setIsLoading(false);
      setError(error.message);
      setShowResult(false);
    };

    const handleDeleteStart = () => {
      setIsLoading(true);
      setError(null);
    };

    const handleDeleteSuccess = (result: RfidDeleteResult) => {
      console.log('handleDeleteSuccess - 接收到删除结果:', result);
      setIsLoading(false);
      setLastDeleteResult(result);
      setOperationType('delete'); // 确保操作类型设置为删除
      setError(null);
      // 删除成功后显示结果对话框
      setShowResult(true);
      setShowResultDialog(true);
      console.log('handleDeleteSuccess - 状态已更新，operationType: delete, showResultDialog:', true);
    };

    const handleDeleteError = (error: Error) => {
      setIsLoading(false);
      setError(error.message);
      setShowResult(false);
    };

    const handleRfidUpdated = (data: { deviceIds: number[]; type: 'bind' | 'delete' }) => {
      console.log('RFID更新事件:', data);
      // RFID更新后，可以在这里触发界面刷新或其他操作
      // 由于我们已经通过InventoryService更新了数据，这里主要用于日志记录
    };

    // 注册事件监听器
    rfidService.on('bind-start', handleBindStart);
    rfidService.on('bind-success', handleBindSuccess);
    rfidService.on('bind-error', handleBindError);
    rfidService.on('delete-start', handleDeleteStart);
    rfidService.on('delete-success', handleDeleteSuccess);
    rfidService.on('delete-error', handleDeleteError);
    rfidService.on('rfid-updated', handleRfidUpdated);

    // 初始化状态
    setIsLoading(rfidService.getIsLoading());
    setLastBindResult(rfidService.getLastBindResult());
    setLastDeleteResult(rfidService.getLastDeleteResult());

    return () => {
      // 清理事件监听器
      rfidService.off('bind-start', handleBindStart);
      rfidService.off('bind-success', handleBindSuccess);
      rfidService.off('bind-error', handleBindError);
      rfidService.off('delete-start', handleDeleteStart);
      rfidService.off('delete-success', handleDeleteSuccess);
      rfidService.off('delete-error', handleDeleteError);
      rfidService.off('rfid-updated', handleRfidUpdated);
    };
  }, [rfidService]);

  /**
   * 绑定设备RFID码
   * @param selectedItems 选中的设备项目
   */
  const bindDeviceRfid = useCallback(async (selectedItems: InventoryItem[]) => {
    try {
      // 使用工具函数提取设备ID
      const deviceIds = extractDeviceIds(selectedItems);

      if (deviceIds.length === 0) {
        throw new Error('没有有效的设备ID');
      }

      console.log('准备绑定RFID的设备ID:', deviceIds);
      const result = await rfidService.bindDeviceRfid(deviceIds);
      return result;
    } catch (error) {
      throw new Error(handleRfidError('绑定RFID', error));
    }
  }, [rfidService]);

  /**
   * 删除设备RFID码
   * @param selectedItems 选中的设备项目
   */
  const deleteDeviceRfid = useCallback(async (selectedItems: InventoryItem[]) => {
    try {
      // 使用工具函数提取设备ID
      const deviceIds = extractDeviceIds(selectedItems);

      if (deviceIds.length === 0) {
        throw new Error('没有有效的设备ID');
      }

      console.log('准备删除RFID的设备ID:', deviceIds);
      const result = await rfidService.deleteDeviceRfid(deviceIds);
      return result;
    } catch (error) {
      throw new Error(handleRfidError('删除RFID', error));
    }
  }, [rfidService]);

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * 清除结果缓存
   */
  const clearResults = useCallback(() => {
    rfidService.clearResults();
    setLastBindResult(undefined);
    setLastDeleteResult(undefined);
  }, [rfidService]);

  /**
   * 关闭结果对话框
   */
  const closeResultDialog = useCallback(() => {
    setShowResultDialog(false);
  }, []);

  /**
   * 重置所有状态
   */
  const resetStates = useCallback(() => {
    console.log('🔄 resetStates 被调用 - 重置所有状态');
    console.log('调用前状态:', { operationType, lastBindResult, lastDeleteResult, showResultDialog });
    setOperationType(null);
    setShowResult(false);
    setShowResultDialog(false);
    clearError();
    clearResults();
    console.log('🔄 resetStates 完成');
  }, [clearError, clearResults, operationType, lastBindResult, lastDeleteResult, showResultDialog]);

  /**
   * 处理本地项目选择切换
   */
  const handleLocalItemToggle = useCallback((item: InventoryItem) => {
    setLocalSelectedItems(prev => {
      const isSelected = prev.some(selected => selected.id === item.id);
      if (isSelected) {
        return prev.filter(selected => selected.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  }, []);



  /**
   * 执行RFID绑定操作
   */
  const executeBindRfid = useCallback(async () => {
    if (localSelectedItems.length === 0) {
      throw new Error('请选择要绑定RFID的设备');
    }

    try {
      setOperationType('bind');
      setShowResult(false); // 重置结果显示状态
      const result = await bindDeviceRfid(localSelectedItems);
      return result;
    } catch (error) {
      console.error('绑定RFID失败:', error);
      throw error;
    }
  }, [localSelectedItems, bindDeviceRfid]);

  /**
   * 执行RFID删除操作
   */
  const executeDeleteRfid = useCallback(async () => {
    if (localSelectedItems.length === 0) {
      throw new Error('请选择要删除RFID的设备');
    }

    try {
      setOperationType('delete');
      setShowResult(false); // 重置结果显示状态
      const result = await deleteDeviceRfid(localSelectedItems);
      return result;
    } catch (error) {
      console.error('删除RFID失败:', error);
      throw error;
    }
  }, [localSelectedItems, deleteDeviceRfid]);

  // 只在结果对话框显示时输出调试信息
  if (showResultDialog) {
    console.log('=== useRfidManagement 结果对话框状态 ===');
    console.log('operationType:', operationType);
    console.log('lastBindResult:', lastBindResult);
    console.log('lastDeleteResult:', lastDeleteResult);
    console.log('showResultDialog:', showResultDialog);
  }

  return {
    // 数据状态
    isLoading,
    lastBindResult,
    lastDeleteResult,
    error,

    // UI状态
    operationType,
    showResult,
    showResultDialog,
    localSelectedItems,

    // 操作方法
    clearError,
    resetStates,
    closeResultDialog,
    handleLocalItemToggle,
    executeBindRfid,
    executeDeleteRfid
  };
}
