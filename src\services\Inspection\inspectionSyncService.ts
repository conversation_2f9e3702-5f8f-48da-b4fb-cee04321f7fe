import mitt from 'mitt';
import { BaseService, BaseServiceState, BaseServiceEvents } from '../../services/base/baseService';
import WebSocketManager from '../../utils/websocket';
import TaskManager from '../../utils/taskManager';
import InspectionService from './inspectionService';
import { dataManager, DataManagerConfig } from '../Base/dataManager';

/**
 * 巡检数据类型
 */
export type InspectionDataType = 'new' | 'exception';

/**
 * 巡检数据项接口
 */
export interface InspectionDataItem {
  id: string;
  name: string;
  type: string;
  department: string;
  responsible: string;
  scanTime: string;
  dataType: InspectionDataType;
  [key: string]: any; // 允许添加其他属性
}

/**
 * 巡检同步服务状态接口
 */
export interface InspectionSyncServiceState extends BaseServiceState {
  inspectionData: InspectionDataItem[];
  selectedItems: string[];
  isReading: boolean;
  readProgress: number;
  isSaving: boolean;
  saveProgress: number;
  isExporting: boolean;
  exportProgress: number;
}

/**
 * 巡检同步服务事件接口
 */
export interface InspectionSyncServiceEvents extends BaseServiceEvents<InspectionSyncServiceState> {
  'data-loaded': InspectionDataItem[];
  'selection-changed': string[];
  'read-progress': number;
  'save-progress': number;
  'save-completed': { success: boolean; message: string };
  'export-progress': number;
  'export-completed': { success: boolean; message: string; data?: any };
}

/**
 * 数据缓存键常量
 */
const CACHE_KEYS = {
  INSPECTION_SYNC_DATA: 'inspection_sync_data'
} as const;

/**
 * 数据管理器配置（单机版优化）
 */
const DATA_CONFIG: DataManagerConfig = {
  maxRetries: 3,
  retryDelay: 1000
};

/**
 * 巡检同步服务类
 * 提供从手持端读取和保存巡检数据的功能
 */
class InspectionSyncService extends BaseService<InspectionSyncServiceState, InspectionSyncServiceEvents> {
  private static instance: InspectionSyncService;

  // 智能缓存机制
  private isImportingData = false;
  private isExportingData = false;
  private lastImportTime = 0;
  private readonly CACHE_DURATION = 300000; // 5分钟缓存

  /**
   * 获取巡检同步服务实例
   * @returns 巡检同步服务实例
   */
  public static getInstance(): InspectionSyncService {
    if (!InspectionSyncService.instance) {
      InspectionSyncService.instance = new InspectionSyncService();
    }
    return InspectionSyncService.instance;
  }

  /**
   * 构造函数
   * 初始化巡检同步服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      inspectionData: [],
      selectedItems: [],
      isReading: false,
      readProgress: 0,
      isSaving: false,
      saveProgress: 0,
      isExporting: false,
      exportProgress: 0
    });
  }

  /**
   * 从Android设备导入巡检数据
   */
  public async importFromAndroidDevice(): Promise<{
    success: boolean;
    message: string;
    data?: {
      import_source: string;
      local_path: string;
      import_time: number;
      processed_results: number;
      updated_devices: number;
      pending_devices: number;
      new_devices_added: number;
      duplicate_imports: number;
    };
  }> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isReading: true,
        readProgress: 0,
        error: undefined
      });

      // 调用真实的导入API
      const result = await this.callImportInspectionDataAPI();

      this.updateState({
        isReading: false,
        readProgress: 100
      });

      this.emitter.emit('import-completed', result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '从Android设备导入数据失败';

      const failureResult = {
        success: false,
        message: errorMessage
      };

      this.updateState({
        error: errorMessage,
        isReading: false
      });

      this.emitter.emit('import-completed', failureResult);
      return failureResult;
    }
  }

  /**
   * 从手持端读取巡检数据（保留兼容性）
   */
  public async readInspectionData(): Promise<InspectionDataItem[]> {
    try {
      // 调用新的导入方法
      const result = await this.importFromAndroidDevice();

      if (result.success) {
        // 如果导入成功，返回模拟数据用于显示
        const mockData = await this.mockReadInspectionData();
        this.updateState({
          inspectionData: mockData
        });
        this.emitter.emit('data-loaded', mockData);
        return mockData;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '读取巡检数据失败';
      this.updateState({
        error: errorMessage,
        isReading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 保存选中的巡检数据到PC端
   */
  public async saveSelectedData(): Promise<{ success: boolean; message: string }> {
    try {
      await this.ensureInitialized();

      if (this.state.selectedItems.length === 0) {
        throw new Error('未选择任何数据项');
      }

      this.updateState({
        isSaving: true,
        saveProgress: 0,
        error: undefined
      });

      // 获取选中的数据项
      const selectedData = this.state.inspectionData.filter(
        item => this.state.selectedItems.includes(item.id)
      );

      // 模拟保存数据
      // 实际项目中，这里应该调用后端API
      const result = await this.mockSaveInspectionData(selectedData);

      this.updateState({
        isSaving: false,
        saveProgress: 100
      });

      this.emitter.emit('save-completed', result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '保存巡检数据失败';
      this.updateState({
        error: errorMessage,
        isSaving: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 导出巡检数据到Android设备
   */
  public async exportToAndroidDevice(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isExporting: true,
        exportProgress: 0,
        error: undefined
      });

      // 调用真实的导出API
      const result = await this.callExportInspectionDataAPI();

      this.updateState({
        isExporting: false,
        exportProgress: 100
      });

      this.emitter.emit('export-completed', result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '导出到Android设备失败';

      const failureResult = {
        success: false,
        message: errorMessage
      };

      this.updateState({
        error: errorMessage,
        isExporting: false
      });

      this.emitter.emit('export-completed', failureResult);
      return failureResult;
    }
  }

  /**
   * 选择/取消选择数据项
   * @param id 数据项ID
   * @param selected 是否选中
   */
  public toggleSelectItem(id: string, selected?: boolean): void {
    const { selectedItems } = this.state;
    const isSelected = selectedItems.includes(id);

    // 如果未指定selected，则切换选中状态
    const newSelected = selected !== undefined ? selected : !isSelected;

    let newSelectedItems: string[];
    if (newSelected && !isSelected) {
      // 添加到选中列表
      newSelectedItems = [...selectedItems, id];
    } else if (!newSelected && isSelected) {
      // 从选中列表中移除
      newSelectedItems = selectedItems.filter(itemId => itemId !== id);
    } else {
      // 状态未变，不更新
      return;
    }

    this.updateState({ selectedItems: newSelectedItems });
    this.emitter.emit('selection-changed', newSelectedItems);
  }

  /**
   * 选择/取消选择所有数据项
   * @param selected 是否全选
   */
  public toggleSelectAll(selected: boolean): void {
    const newSelectedItems = selected
      ? this.state.inspectionData.map(item => item.id)
      : [];

    this.updateState({ selectedItems: newSelectedItems });
    this.emitter.emit('selection-changed', newSelectedItems);
  }

  /**
   * 检查数据项是否被选中
   * @param id 数据项ID
   * @returns 是否选中
   */
  public isItemSelected(id: string): boolean {
    return this.state.selectedItems.includes(id);
  }

  /**
   * 获取选中的数据项数量
   * @returns 选中的数据项数量
   */
  public getSelectedCount(): number {
    return this.state.selectedItems.length;
  }

  /**
   * 删除选中的巡检数据
   */
  public async deleteSelectedData(): Promise<{ success: boolean; message: string }> {
    try {
      await this.ensureInitialized();

      if (this.state.selectedItems.length === 0) {
        throw new Error('未选择任何数据项');
      }

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 获取选中的数据项
      const selectedData = this.state.inspectionData.filter(
        item => this.state.selectedItems.includes(item.id)
      );

      // 模拟删除数据
      // 实际项目中，这里应该调用后端API
      await new Promise(resolve => setTimeout(resolve, 500));

      // 从数据列表中移除选中的项
      const updatedData = this.state.inspectionData.filter(
        item => !this.state.selectedItems.includes(item.id)
      );

      this.updateState({
        inspectionData: updatedData,
        selectedItems: [],
        isLoading: false
      });

      return {
        success: true,
        message: `成功删除 ${selectedData.length} 条数据`
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除巡检数据失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 保存单个数据项
   * @param id 数据项ID
   */
  public async saveItem(id: string): Promise<{ success: boolean; message: string }> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 获取数据项
      const item = this.state.inspectionData.find(item => item.id === id);
      if (!item) {
        throw new Error('数据项不存在');
      }

      // 模拟保存数据
      // 实际项目中，这里应该调用后端API
      await new Promise(resolve => setTimeout(resolve, 500));

      this.updateState({ isLoading: false });

      return {
        success: true,
        message: `成功保存数据项: ${item.name}`
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '保存数据项失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 删除单个数据项
   * @param id 数据项ID
   */
  public async deleteItem(id: string): Promise<{ success: boolean; message: string }> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 获取数据项
      const item = this.state.inspectionData.find(item => item.id === id);
      if (!item) {
        throw new Error('数据项不存在');
      }

      // 模拟删除数据
      // 实际项目中，这里应该调用后端API
      await new Promise(resolve => setTimeout(resolve, 500));

      // 从数据列表中移除该项
      const updatedData = this.state.inspectionData.filter(item => item.id !== id);

      // 如果该项在选中列表中，也从选中列表中移除
      let updatedSelectedItems = this.state.selectedItems;
      if (this.state.selectedItems.includes(id)) {
        updatedSelectedItems = this.state.selectedItems.filter(itemId => itemId !== id);
      }

      this.updateState({
        inspectionData: updatedData,
        selectedItems: updatedSelectedItems,
        isLoading: false
      });

      return {
        success: true,
        message: `成功删除数据项: ${item.name}`
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除数据项失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    // 确保基础巡检服务已初始化
    const inspectionService = InspectionService.getInstance();
    await inspectionService.ensureInitialized();
  }

  /**
   * 模拟从手持端读取巡检数据
   * 实际项目中应替换为真实API调用
   */
  private async mockReadInspectionData(): Promise<InspectionDataItem[]> {
    // 模拟进度更新
    for (let i = 0; i <= 100; i += 10) {
      this.updateState({ readProgress: i });
      this.emitter.emit('read-progress', i);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 返回模拟数据
    return [
      {
        id: '1',
        name: '计算机A',
        type: '台式机',
        department: '技术部',
        responsible: '张三',
        scanTime: '2023-11-01 10:30:45',
        dataType: 'new'
      },
      {
        id: '2',
        name: '计算机B',
        type: '笔记本',
        department: '财务部',
        responsible: '李四',
        scanTime: '2023-11-01 11:15:22',
        dataType: 'exception'
      },
      {
        id: '3',
        name: '计算机C',
        type: '服务器',
        department: '运维部',
        responsible: '王五',
        scanTime: '2023-11-01 09:45:10',
        dataType: 'exception'
      }
    ];
  }

  /**
   * 调用从Android设备导入巡检数据API
   */
  private async callImportInspectionDataAPI(): Promise<{
    success: boolean;
    message: string;
    data?: {
      import_source: string;
      local_path: string;
      import_time: number;
      processed_results: number;
      updated_devices: number;
      pending_devices: number;
      new_devices_added: number;
      duplicate_imports: number;
    };
  }> {
    try {
      console.log('调用从Android设备导入巡检数据API');

      const requestData = {
        action: 'import_inspection_data',
        action_params: {}
      };

      console.log('导入请求参数:', requestData);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', requestData) as any;

      console.log('导入API原始响应:', rawResult);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
          console.log('解析JSON字符串后的结果:', result);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      console.log('导入API最终响应:', result);

      // 处理API返回结果
      if (result.message === 'success') {
        // 成功 - message为"success"表示成功
        return {
          success: true,
          message: '从Android设备导入数据成功',
          data: result.data
        };
      } else {
        // 失败 - message包含错误信息
        return {
          success: false,
          message: result.message || '导入失败'
        };
      }
    } catch (error) {
      console.error('导入巡检数据API调用失败:', error);
      throw error;
    }
  }

  /**
   * 调用导出巡检数据到Android设备API
   */
  private async callExportInspectionDataAPI(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('调用导出巡检数据到Android设备API');

      const requestData = {
        action: 'export_inspection_data',
        action_params: {}
      };

      console.log('导出请求参数:', requestData);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', requestData) as any;

      console.log('导出API原始响应:', rawResult);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
          console.log('解析JSON字符串后的结果:', result);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      console.log('导出API最终响应:', result);

      // 处理API返回结果
      if (result.message === 'success') {
        // 成功 - message为"success"表示成功
        return {
          success: true,
          message: result.data?.note || '导出成功',
          data: result.data
        };
      } else {
        // 失败 - message包含错误信息
        return {
          success: false,
          message: result.message || '导出失败'
        };
      }
    } catch (error) {
      console.error('导出巡检数据API调用失败:', error);
      throw error;
    }
  }

  /**
   * 模拟保存巡检数据
   * 实际项目中应替换为真实API调用
   */
  private async mockSaveInspectionData(data: InspectionDataItem[]): Promise<{ success: boolean; message: string }> {
    // 模拟进度更新
    for (let i = 0; i <= 100; i += 10) {
      this.updateState({ saveProgress: i });
      this.emitter.emit('save-progress', i);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 模拟保存成功
    return {
      success: true,
      message: `成功保存 ${data.length} 条数据`
    };
  }
}

export default InspectionSyncService;
