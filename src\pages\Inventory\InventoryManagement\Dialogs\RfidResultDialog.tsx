import React from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import SimpleDialog from '../../../../components/ui/SimpleDialog';
import { RfidBindResult, RfidDeleteResult } from '../../../../services/Inventory/rfidManagementService';

/**
 * RFID操作结果对话框属性接口
 */
interface RfidResultDialogProps {
  isOpen: boolean;
  onClose: () => void;
  operationType: 'bind' | 'delete';
  bindResult?: RfidBindResult;
  deleteResult?: RfidDeleteResult;
}

/**
 * RFID操作结果对话框组件
 * 显示详细的RFID操作结果信息
 */
const RfidResultDialog: React.FC<RfidResultDialogProps> = ({
  isOpen,
  onClose,
  operationType,
  bindResult,
  deleteResult
}) => {
  // 调试日志
  console.log('=== RfidResultDialog 渲染 ===');
  console.log('RfidResultDialog props:', {
    isOpen,
    operationType,
    bindResult,
    deleteResult
  });
  console.log('bindResult 详情:', bindResult);
  console.log('deleteResult 详情:', deleteResult);

  // 渲染RFID绑定结果
  const renderBindResult = () => {
    console.log('renderBindResult - bindResult:', bindResult);

    if (!bindResult) {
      console.log('bindResult is null/undefined');
      return <div className="p-4 text-center text-slate-500">没有绑定结果数据</div>;
    }

    if (!bindResult.data) {
      console.log('bindResult.data is null/undefined');
      return <div className="p-4 text-center text-slate-500">绑定结果数据为空</div>;
    }

    const { data } = bindResult;
    const hasFailures = (data.failed_count || 0) > 0;
    const hasUnassigned = data.unassigned_devices && data.unassigned_devices.length > 0;
    const hasUnusedRfid = data.unused_rfid && data.unused_rfid.length > 0;
    const isPartialSuccess = (data.success_count || 0) > 0 && (hasFailures || hasUnassigned);
    const isCompleteFailure = (data.success_count || 0) === 0;

    // 根据结果类型选择样式和图标
    let statusConfig = {
      containerClass: "bg-emerald-50 border-emerald-200",
      iconClass: "h-6 w-6 text-emerald-600",
      titleClass: "text-emerald-900",
      icon: CheckCircle,
      title: "RFID绑定成功"
    };

    if (isCompleteFailure) {
      statusConfig = {
        containerClass: "bg-rose-50 border-rose-200",
        iconClass: "h-6 w-6 text-rose-600",
        titleClass: "text-rose-900",
        icon: AlertCircle,
        title: "RFID绑定失败"
      };
    } else if (isPartialSuccess) {
      statusConfig = {
        containerClass: "bg-orange-50 border-orange-200",
        iconClass: "h-6 w-6 text-orange-600",
        titleClass: "text-orange-900",
        icon: AlertTriangle,
        title: "RFID绑定部分成功"
      };
    }

    const IconComponent = statusConfig.icon;

    return (
      <div className="space-y-6">
        {/* 状态标题 */}
        <div className={`p-4 rounded-lg border ${statusConfig.containerClass}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <IconComponent className={statusConfig.iconClass} />
              <h3 className={`ml-3 text-lg font-semibold ${statusConfig.titleClass}`}>
                {statusConfig.title}
              </h3>
            </div>
            <div className="text-sm text-slate-500 bg-white px-3 py-1 rounded-full border">
              设备列表已自动更新
            </div>
          </div>
        </div>

        {/* 操作统计 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-slate-50 p-4 rounded-lg">
            <div className="text-sm text-slate-600 mb-1">总设备数量</div>
            <div className="text-2xl font-bold text-slate-900">{data.device_count || 0}</div>
          </div>
          <div className="bg-slate-50 p-4 rounded-lg">
            <div className="text-sm text-slate-600 mb-1">RFID数据数量</div>
            <div className="text-2xl font-bold text-slate-900">{data.rfid_count || 0}</div>
          </div>
          <div className="bg-emerald-50 p-4 rounded-lg">
            <div className="text-sm text-emerald-600 mb-1">成功绑定</div>
            <div className="text-2xl font-bold text-emerald-700">{data.success_count || 0}</div>
          </div>
          <div className="bg-rose-50 p-4 rounded-lg">
            <div className="text-sm text-rose-600 mb-1">绑定失败</div>
            <div className="text-2xl font-bold text-rose-700">{data.failed_count || 0}</div>
          </div>
        </div>

        {/* 详细信息 */}
        <div className="space-y-4">
          {/* 未分配RFID的设备 */}
          {hasUnassigned && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <AlertTriangle className="h-5 w-5 text-orange-600 mr-2" />
                <span className="font-medium text-orange-800">未分配RFID的设备</span>
                <span className="ml-2 bg-orange-200 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {data.unassigned_devices.length} 个
                </span>
              </div>
              <div className="text-sm text-orange-700">
                设备ID: {data.unassigned_devices.join(', ')}
              </div>
            </div>
          )}

          {/* 未使用的RFID数据 */}
          {hasUnusedRfid && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Info className="h-5 w-5 text-blue-600 mr-2" />
                <span className="font-medium text-blue-800">未使用的RFID数据</span>
                <span className="ml-2 bg-blue-200 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {data.unused_rfid.length} 个
                </span>
              </div>
              <div className="text-sm text-blue-700">
                {data.unused_rfid.length <= 10 ? (
                  <div>RFID码: {data.unused_rfid.join(', ')}</div>
                ) : (
                  <div>
                    <div>显示前10个: {data.unused_rfid.slice(0, 10).join(', ')}...</div>
                    <div className="mt-1 text-xs">共 {data.unused_rfid.length} 个未使用的RFID码</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 失败项详情 */}
          {data.failed_items && data.failed_items.length > 0 && (
            <div className="bg-rose-50 border border-rose-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <AlertCircle className="h-5 w-5 text-rose-600 mr-2" />
                <span className="font-medium text-rose-800">绑定失败详情</span>
                <span className="ml-2 bg-rose-200 text-rose-800 text-xs px-2 py-1 rounded-full">
                  {data.failed_items.length} 个
                </span>
              </div>
              <div className="space-y-2 text-sm text-rose-700">
                {data.failed_items.map((item: any, index: number) => (
                  <div key={index} className="flex justify-between items-center bg-white p-2 rounded">
                    <span>设备ID: {item.device_id}</span>
                    <span className="text-rose-600">{item.error || '绑定失败'}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 操作建议 */}
        {(hasFailures || hasUnassigned) && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Info className="h-5 w-5 text-blue-600 mr-2" />
              <span className="font-medium text-blue-800">操作建议</span>
            </div>
            <div className="text-sm text-blue-700 space-y-1">
              {hasUnassigned && <div>• 请检查RFID数据文件是否包含足够的记录</div>}
              {hasFailures && <div>• 请检查失败设备的状态，确保设备信息正确</div>}
              <div>• 您可以重新导入RFID数据或手动处理失败的设备</div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 渲染RFID删除结果
  const renderDeleteResult = () => {
    console.log('renderDeleteResult - deleteResult:', deleteResult);
    console.log('renderDeleteResult - operationType:', operationType);

    if (!deleteResult) {
      console.log('deleteResult is null/undefined');
      return <div className="p-4 text-center text-slate-500">没有删除结果数据</div>;
    }

    if (!deleteResult.data) {
      console.log('deleteResult.data is null/undefined');
      return <div className="p-4 text-center text-slate-500">删除结果数据为空</div>;
    }

    const { data } = deleteResult;
    const isSuccess = deleteResult.success;

    console.log('renderDeleteResult - 删除统计数据:', {
      deleted_device_count: data.deleted_device_count,
      deleted_rfid_count: data.deleted_rfid_count,
      device_ids: data.device_ids,
      isSuccess
    });

    const statusConfig = isSuccess ? {
      containerClass: "bg-emerald-50 border-emerald-200",
      iconClass: "h-6 w-6 text-emerald-600",
      titleClass: "text-emerald-900",
      icon: CheckCircle,
      title: "RFID删除成功"
    } : {
      containerClass: "bg-rose-50 border-rose-200",
      iconClass: "h-6 w-6 text-rose-600",
      titleClass: "text-rose-900",
      icon: AlertCircle,
      title: "RFID删除失败"
    };

    const IconComponent = statusConfig.icon;

    return (
      <div className="space-y-6">
        {/* 状态标题 */}
        <div className={`p-4 rounded-lg border ${statusConfig.containerClass}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <IconComponent className={statusConfig.iconClass} />
              <h3 className={`ml-3 text-lg font-semibold ${statusConfig.titleClass}`}>
                {statusConfig.title}
              </h3>
            </div>
            {isSuccess && (
              <div className="text-sm text-slate-500 bg-white px-3 py-1 rounded-full border">
                设备列表已自动更新
              </div>
            )}
          </div>
        </div>

        {/* 删除统计 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-slate-50 p-4 rounded-lg">
            <div className="text-sm text-slate-600 mb-1">删除设备数量</div>
            <div className="text-2xl font-bold text-slate-900">{data.deleted_device_count || 0}</div>
          </div>
          <div className="bg-slate-50 p-4 rounded-lg">
            <div className="text-sm text-slate-600 mb-1">删除RFID记录</div>
            <div className="text-2xl font-bold text-slate-900">{data.deleted_rfid_count || 0}</div>
          </div>
        </div>

        {/* 涉及设备 */}
        {data.device_ids && data.device_ids.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Info className="h-5 w-5 text-blue-600 mr-2" />
              <span className="font-medium text-blue-800">涉及设备</span>
              <span className="ml-2 bg-blue-200 text-blue-800 text-xs px-2 py-1 rounded-full">
                {data.device_ids.length} 个
              </span>
            </div>
            <div className="text-sm text-blue-700">
              设备ID: {data.device_ids.join(', ')}
            </div>
          </div>
        )}

        {/* 删除失败原因 */}
        {!isSuccess && (
          <div className="bg-rose-50 border border-rose-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <AlertCircle className="h-5 w-5 text-rose-600 mr-2" />
              <span className="font-medium text-rose-800">删除失败原因</span>
            </div>
            <div className="text-sm text-rose-700">
              {deleteResult.message || '删除操作失败，请检查设备状态或联系管理员'}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <SimpleDialog
      isOpen={isOpen}
      onClose={onClose}
      title={operationType === 'bind' ? 'RFID绑定结果' : 'RFID删除结果'}
      width="600px"
      maxHeight="80vh"
    >
      <div className="p-6">
        {(() => {
          console.log('RfidResultDialog - 条件渲染检查:', {
            operationType,
            isBindOperation: operationType === 'bind',
            willRenderBind: operationType === 'bind',
            willRenderDelete: operationType !== 'bind'
          });
          return operationType === 'bind' ? renderBindResult() : renderDeleteResult();
        })()}
        
        {/* 底部按钮 */}
        <div className="flex justify-end mt-8 pt-6 border-t border-slate-200">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            确定
          </button>
        </div>
      </div>
    </SimpleDialog>
  );
};

export default RfidResultDialog;
