/**
 * 统一的部门路径获取工具
 * 提供各种场景下的部门路径获取方法，统一调用接口
 */

import { DepartmentCategory } from './types';
import { DepartmentUtils } from './utils';

/**
 * 部门路径解析器 - 统一的路径获取工具
 * 根据不同用途返回相应格式的部门路径
 */
export class DepartmentPathResolver {
  private departmentCategories: DepartmentCategory[];

  constructor(departmentCategories: DepartmentCategory[]) {
    this.departmentCategories = departmentCategories;
  }

  /**
   * 获取用于API调用的部门路径（不包含根节点）
   * 用于：添加部门、删除部门、更新部门等API调用
   * @param category 部门节点
   * @returns API路径，如 "技术部/开发组/前端组"
   */
  public getApiPath(category: DepartmentCategory): string {
    return DepartmentUtils.getDepartmentApiPath(category, this.departmentCategories);
  }

  /**
   * 获取完整的显示路径（包含根节点）
   * 用于：UI显示、日志记录、面包屑导航等
   * @param category 部门节点
   * @returns 完整路径，如 "公司名称/技术部/开发组/前端组"
   */
  public getFullPath(category: DepartmentCategory): string {
    return DepartmentUtils.getDepartmentPath(category, this.departmentCategories);
  }

  /**
   * 获取用于导出的路径（不包含根节点）
   * 用于：Excel导出、PDF导出、Word导出等
   * @param category 部门节点
   * @returns 导出路径，如 "技术部/开发组/前端组"
   */
  public getExportPath(category: DepartmentCategory): string {
    const fullPath = this.getFullPath(category);
    return this.removeRootFromPath(fullPath);
  }

  /**
   * 根据部门ID获取API路径
   * 用于：只有部门ID时获取API路径
   * @param departmentId 部门ID
   * @returns API路径或空字符串
   */
  public getApiPathById(departmentId: string): string {
    const department = DepartmentUtils.findNodeById(this.departmentCategories, departmentId);
    return department ? this.getApiPath(department) : '';
  }

  /**
   * 根据部门ID获取完整路径
   * 用于：只有部门ID时获取完整显示路径
   * @param departmentId 部门ID
   * @returns 完整路径或空字符串
   */
  public getFullPathById(departmentId: string): string {
    const department = DepartmentUtils.findNodeById(this.departmentCategories, departmentId);
    return department ? this.getFullPath(department) : '';
  }

  /**
   * 根据部门名称获取所有匹配的API路径
   * 用于：处理同名部门的情况
   * @param departmentName 部门名称
   * @returns 所有匹配的API路径数组
   */
  public getApiPathsByName(departmentName: string): string[] {
    const fullPaths = DepartmentUtils.findAllDepartmentsWithName(this.departmentCategories, departmentName);
    return fullPaths.map(path => this.removeRootFromPath(path));
  }

  /**
   * 构建新部门的API路径
   * 用于：添加新部门时构建路径
   * @param parentId 父部门ID
   * @param departmentName 新部门名称
   * @returns 新部门的API路径
   */
  public buildNewDepartmentPath(parentId: string, departmentName: string): string {
    if (parentId === 'all-dept') {
      // 添加一级部门
      return departmentName;
    }

    const parentDept = DepartmentUtils.findNodeById(this.departmentCategories, parentId);
    if (!parentDept) {
      throw new Error(`未找到父级部门: ${parentId}`);
    }

    const parentApiPath = this.getApiPath(parentDept);
    return parentApiPath ? `${parentApiPath}/${departmentName}` : `${parentDept.name}/${departmentName}`;
  }

  /**
   * 验证部门路径是否有效
   * 用于：路径格式验证
   * @param departmentPath 部门路径
   * @returns 是否有效
   */
  public isValidPath(departmentPath: string): boolean {
    if (!departmentPath || departmentPath.trim() === '') {
      return false;
    }

    // 检查路径格式
    const pathParts = departmentPath.split('/');
    return pathParts.every(part => part.trim() !== '');
  }

  /**
   * 从路径中移除根节点部分
   * @param fullPath 完整路径
   * @returns 移除根节点后的路径
   */
  private removeRootFromPath(fullPath: string): string {
    if (!fullPath) return '';

    const pathParts = fullPath.split('/');
    if (pathParts.length > 1) {
      // 去除第一个部分（根节点）
      return pathParts.slice(1).join('/');
    }

    // 如果只有一级，直接返回
    return fullPath;
  }
}

/**
 * 快捷工具函数集合
 * 提供便捷的静态方法调用
 */
export class DepartmentPathHelper {
  /**
   * 从DepartmentService创建路径解析器
   * @param departmentService 部门服务实例
   * @returns 路径解析器实例
   */
  public static createResolver(departmentService: any): DepartmentPathResolver {
    return departmentService.createPathResolver();
  }

  /**
   * 快速获取API路径
   * @param departmentService 部门服务实例
   * @param department 部门节点
   * @returns API路径
   */
  public static getApiPath(departmentService: any, department: DepartmentCategory): string {
    const resolver = this.createResolver(departmentService);
    return resolver.getApiPath(department);
  }

  /**
   * 快速根据ID获取API路径
   * @param departmentService 部门服务实例
   * @param departmentId 部门ID
   * @returns API路径
   */
  public static getApiPathById(departmentService: any, departmentId: string): string {
    const resolver = this.createResolver(departmentService);
    return resolver.getApiPathById(departmentId);
  }

  /**
   * 快速获取完整路径
   * @param departmentService 部门服务实例
   * @param department 部门节点
   * @returns 完整路径
   */
  public static getFullPath(departmentService: any, department: DepartmentCategory): string {
    const resolver = this.createResolver(departmentService);
    return resolver.getFullPath(department);
  }

  /**
   * 快速获取导出路径
   * @param departmentService 部门服务实例
   * @param department 部门节点
   * @returns 导出路径
   */
  public static getExportPath(departmentService: any, department: DepartmentCategory): string {
    const resolver = this.createResolver(departmentService);
    return resolver.getExportPath(department);
  }

  /**
   * 快速构建新部门路径
   * @param departmentService 部门服务实例
   * @param parentId 父部门ID
   * @param departmentName 新部门名称
   * @returns 新部门路径
   */
  public static buildNewDepartmentPath(
    departmentService: any,
    parentId: string,
    departmentName: string
  ): string {
    const resolver = this.createResolver(departmentService);
    return resolver.buildNewDepartmentPath(parentId, departmentName);
  }

  /**
   * 快速验证路径
   * @param departmentService 部门服务实例
   * @param departmentPath 部门路径
   * @returns 是否有效
   */
  public static async validatePath(departmentService: any, departmentPath: string): Promise<boolean> {
    const resolver = await this.createResolver(departmentService);
    return resolver.isValidPath(departmentPath);
  }
}

/**
 * 使用示例和说明
 */
export const USAGE_EXAMPLES = {
  // 1. 创建解析器
  createResolver: `
    const departmentService = DepartmentService.getInstance();
    const pathResolver = departmentService.createPathResolver();
    // 或者
    const pathResolver = new DepartmentPathResolver(departmentCategories);
  `,

  // 2. 获取API路径（用于API调用）
  getApiPath: `
    const apiPath = pathResolver.getApiPath(department);
    // 结果: "技术部/开发组/前端组"

    // 用于API调用
    const params = {
      action: "delete_department",
      action_params: {
        department_path: apiPath
      }
    };
  `,

  // 3. 获取完整路径（用于显示）
  getFullPath: `
    const fullPath = pathResolver.getFullPath(department);
    // 结果: "公司名称/技术部/开发组/前端组"

    // 用于UI显示
    console.log('部门路径:', fullPath);
  `,

  // 4. 获取导出路径（用于导出）
  getExportPath: `
    const exportPath = pathResolver.getExportPath(department);
    // 结果: "技术部/开发组/前端组"

    // 用于Excel/PDF/Word导出
    worksheet.cell(row, col).value = exportPath;
  `,

  // 5. 快捷方法使用
  quickMethods: `
    // 快速获取API路径
    const apiPath = DepartmentPathHelper.getApiPath(departmentService, department);

    // 快速根据ID获取路径
    const pathById = DepartmentPathHelper.getApiPathById(departmentService, 'dept-123');

    // 快速构建新部门路径
    const newPath = DepartmentPathHelper.buildNewDepartmentPath(
      departmentService,
      'dept-parent',
      '新部门'
    );
  `
};
