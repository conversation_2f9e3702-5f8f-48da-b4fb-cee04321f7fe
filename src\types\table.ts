/**
 * 统一表格系统类型定义
 * 整合所有表格组件的通用接口
 */

import { ColumnDef, SortingState } from '@tanstack/react-table';
import React from 'react';

/**
 * 表格列配置接口 - 统一所有表格的列定义
 */
export interface BaseTableColumn<T = any> {
  key: string;
  title: string;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  // 扩展属性
  fixed?: 'left' | 'right';
  resizable?: boolean;
  filterable?: boolean;
}

/**
 * 表格功能特性配置
 */
export interface TableFeatures {
  /** 是否启用排序 */
  sorting?: boolean;
  /** 是否启用分页 */
  pagination?: boolean;
  /** 是否启用行选择 */
  selection?: boolean;
  /** 是否启用列筛选 */
  filtering?: boolean;
  /** 是否启用列可见性控制 */
  columnVisibility?: boolean;
  /** 是否启用列拖拽调整 */
  columnReordering?: boolean;
  /** 是否启用行拖拽 */
  rowDragging?: boolean;
  /** 是否启用虚拟滚动 */
  virtualScrolling?: boolean;
  /** 是否启用位置记忆 */
  positionMemory?: boolean;
}

/**
 * 表格分页配置
 */
export interface TablePagination {
  /** 当前页码 */
  pageIndex: number;
  /** 每页大小 */
  pageSize: number;
  /** 总数据量 */
  total?: number;
  /** 可选的每页大小选项 */
  pageSizeOptions?: number[];
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean;
  /** 是否显示总数信息 */
  showTotal?: boolean;
}

/**
 * 表格选择配置
 */
export interface TableSelection<T = any> {
  /** 选中的行键值 */
  selectedRowKeys: string[];
  /** 选择变化回调 */
  onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;
  /** 是否禁用全选 */
  hideSelectAll?: boolean;
  /** 自定义选择列宽度 */
  columnWidth?: number;
  /** 行选择的判断函数 */
  getCheckboxProps?: (record: T) => { disabled?: boolean };
}

/**
 * 表格加载状态
 */
export interface TableLoading {
  /** 是否正在加载 */
  loading: boolean;
  /** 加载文本 */
  loadingText?: string;
  /** 自定义加载组件 */
  loadingComponent?: React.ReactNode;
}

/**
 * 表格空状态
 */
export interface TableEmpty {
  /** 空状态文本 */
  emptyText?: string;
  /** 空状态图标 */
  emptyIcon?: React.ReactNode;
  /** 自定义空状态组件 */
  emptyComponent?: React.ReactNode;
}

/**
 * 表格事件处理
 */
export interface TableEvents<T = any> {
  /** 行点击事件 */
  onRowClick?: (record: T, index: number, event: React.MouseEvent) => void;
  /** 行双击事件 */
  onRowDoubleClick?: (record: T, index: number, event: React.MouseEvent) => void;
  /** 行右键事件 */
  onRowContextMenu?: (record: T, index: number, event: React.MouseEvent) => void;
  /** 单元格点击事件 */
  onCellClick?: (record: T, column: BaseTableColumn<T>, event: React.MouseEvent) => void;
  /** 表头点击事件 */
  onHeaderClick?: (column: BaseTableColumn<T>, event: React.MouseEvent) => void;
}

/**
 * 表格样式配置
 */
export interface TableStyle {
  /** 表格大小 */
  size?: 'small' | 'middle' | 'large';
  /** 是否显示边框 */
  bordered?: boolean;
  /** 是否显示斑马纹 */
  striped?: boolean;
  /** 表格类名 */
  className?: string;
  /** 表格样式 */
  style?: React.CSSProperties;
  /** 行类名函数 */
  rowClassName?: (record: any, index: number) => string;
  /** 行样式函数 */
  rowStyle?: (record: any, index: number) => React.CSSProperties;
}

/**
 * 统一表格组件属性接口
 */
export interface BaseTableProps<T = any> {
  /** 数据源 */
  data: T[];
  /** 列配置 */
  columns: BaseTableColumn<T>[];
  /** 功能特性配置 */
  features?: TableFeatures;
  /** 分页配置 */
  pagination?: TablePagination | false;
  /** 选择配置 */
  selection?: TableSelection<T>;
  /** 加载状态 */
  loading?: TableLoading | boolean;
  /** 空状态 */
  empty?: TableEmpty;
  /** 事件处理 */
  events?: TableEvents<T>;
  /** 样式配置 */
  style?: TableStyle;
  /** 滚动配置 */
  scroll?: {
    x?: number | string;
    y?: number | string;
  };
  /** 行键值获取函数 */
  rowKey?: string | ((record: T) => string);
}

/**
 * 表格实例方法接口
 */
export interface BaseTableRef {
  /** 刷新表格 */
  refresh: () => void;
  /** 重置表格状态 */
  reset: () => void;
  /** 滚动到指定位置 */
  scrollTo: (position: { x?: number; y?: number }) => void;
  /** 获取选中的行 */
  getSelectedRows: () => any[];
  /** 设置选中的行 */
  setSelectedRows: (keys: string[]) => void;
  /** 清空选中 */
  clearSelection: () => void;
}

/**
 * 表格Hook返回值接口
 */
export interface UseTableReturn<T = any> {
  /** 表格数据 */
  data: T[];
  /** 加载状态 */
  loading: boolean;
  /** 分页状态 */
  pagination: TablePagination;
  /** 选择状态 */
  selection: {
    selectedRowKeys: string[];
    selectedRows: T[];
  };
  /** 排序状态 */
  sorting: SortingState;
  /** 操作方法 */
  actions: {
    refresh: () => Promise<void>;
    setPageSize: (size: number) => void;
    setPageIndex: (index: number) => void;
    toggleRowSelection: (key: string) => void;
    toggleAllSelection: () => void;
    setSorting: (sorting: SortingState) => void;
  };
}

/**
 * 扩展字段定义 - 兼容现有的FieldDefinition
 */
export interface ExtendedFieldDefinition {
  key: string;
  title: string;
  type?: 'text' | 'number' | 'date' | 'select' | 'boolean';
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  required?: boolean;
  options?: string[];
  defaultValue?: any;
}

/**
 * 表格配置预设
 */
export const TABLE_PRESETS = {
  /** 基础表格 */
  BASIC: {
    features: {
      sorting: true,
      pagination: true,
    },
    style: {
      size: 'middle' as const,
      bordered: true,
    },
  },
  /** 简单表格 */
  SIMPLE: {
    features: {
      sorting: false,
      pagination: false,
    },
    style: {
      size: 'small' as const,
      bordered: false,
    },
  },
  /** 完整功能表格 */
  FULL: {
    features: {
      sorting: true,
      pagination: true,
      selection: true,
      filtering: true,
      columnVisibility: true,
      positionMemory: true,
    },
    style: {
      size: 'middle' as const,
      bordered: true,
      striped: true,
    },
  },
} as const;
