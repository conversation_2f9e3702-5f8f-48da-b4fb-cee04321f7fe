import React from 'react';
import { RefreshCw, Loader2 } from 'lucide-react';

/**
 * 加载指示器属性接口
 */
interface LoadingOverlayProps {
  visible: boolean;
  text?: string;
  type?: 'spinner' | 'refresh';
  className?: string;
}

/**
 * 巡检管理通用加载指示器组件
 * 统一的加载样式和动画
 */
const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  text = '加载中...',
  type = 'refresh',
  className = ''
}) => {
  if (!visible) {
    return null;
  }

  const IconComponent = type === 'spinner' ? Loader2 : RefreshCw;

  return (
    <div className={`absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-10 ${className}`}>
      <div className="flex flex-col items-center">
        <IconComponent className="h-8 w-8 text-blue-600 animate-spin mb-2" />
        <p className="text-blue-600 text-sm font-medium">{text}</p>
      </div>
    </div>
  );
};

export default LoadingOverlay;
