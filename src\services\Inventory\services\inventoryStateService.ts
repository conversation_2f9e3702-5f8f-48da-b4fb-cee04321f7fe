import mitt from 'mitt';
import { InventoryState, InventoryEvents, TreeSelectedNode } from '../../../types/inventory';

/**
 * 库存状态服务
 * 专门负责状态管理和事件发布
 */
export class InventoryStateService {
  private static instance: InventoryStateService;
  private emitter = mitt<InventoryEvents>();
  
  // 初始状态
  private state: InventoryState = {
    inventoryList: [],
    deviceCategories: [],
    departmentCategories: [],
    currentCategory: 'all',
    currentDepartmentCategory: 'all',
    isLoading: false,
    selectedItems: [],
    searchQuery: '',
    tableFields: [],
    categoryExtFields: {},  // 添加分类扩展字段定义映射
    currentExtFields: []    // 当前选中分类的扩展字段
  };

  private constructor() {}

  public static getInstance(): InventoryStateService {
    if (!InventoryStateService.instance) {
      InventoryStateService.instance = new InventoryStateService();
    }
    return InventoryStateService.instance;
  }

  /**
   * 获取当前状态
   */
  public getState(): InventoryState {
    return { ...this.state };
  }

  /**
   * 更新状态
   */
  public updateState(newState: Partial<InventoryState>): void {
    this.state = { ...this.state, ...newState };
    this.emitter.emit('state-change', this.state);
  }

  /**
   * 订阅状态变化
   */
  public on<K extends keyof InventoryEvents>(event: K, callback: (data: InventoryEvents[K]) => void): void {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消订阅
   */
  public off<K extends keyof InventoryEvents>(event: K, callback: (data: InventoryEvents[K]) => void): void {
    this.emitter.off(event, callback as any);
  }

  /**
   * 触发事件
   */
  public emit<K extends keyof InventoryEvents>(event: K, data: InventoryEvents[K]): void {
    this.emitter.emit(event, data);
  }

  /**
   * 强制触发状态更新
   */
  public forceUpdate(): void {
    this.updateState({ ...this.state });
  }

  /**
   * 更新树状图选中的节点信息
   * @param treeSelectedNode 树状图选中的节点信息
   */
  public updateTreeSelectedNode(treeSelectedNode: TreeSelectedNode): void {
    this.updateState({ treeSelectedNode });
  }

  /**
   * 设置加载状态
   */
  public setLoading(isLoading: boolean): void {
    this.updateState({ isLoading });
  }

  /**
   * 设置错误状态
   */
  public setError(error?: string): void {
    this.updateState({ error });
  }

  /**
   * 清除错误状态
   */
  public clearError(): void {
    this.updateState({ error: undefined });
  }

  /**
   * 设置当前分类
   */
  public setCurrentCategory(categoryId: string): void {
    this.updateState({ currentCategory: categoryId });
  }

  /**
   * 设置当前部门分类
   */
  public setCurrentDepartmentCategory(categoryId: string): void {
    this.updateState({ currentDepartmentCategory: categoryId });
  }

  /**
   * 设置搜索查询
   */
  public setSearchQuery(query: string): void {
    // 如果搜索框为空，自动取消搜索
    const trimmedQuery = query.trim();
    this.updateState({ searchQuery: trimmedQuery });
  }

  /**
   * 设置选中的项目
   */
  public setSelectedItems(selectedItems: string[]): void {
    this.updateState({ selectedItems });
  }

  /**
   * 清除选择
   */
  public clearSelection(): void {
    this.updateState({ selectedItems: [] });
  }

  /**
   * 获取选中的项目
   */
  public getSelectedItems(): string[] {
    return [...this.state.selectedItems];
  }

  /**
   * 切换单个项目的选中状态
   */
  public toggleSelectItem(itemId: string): void {
    const selectedItems = [...this.state.selectedItems];
    const index = selectedItems.indexOf(itemId);
    
    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(itemId);
    }
    
    this.updateState({ selectedItems });
  }

  /**
   * 全选/取消全选
   */
  public toggleSelectAll(allItemIds: string[]): void {
    // 如果当前选中的项目包含所有项目，则取消全选
    if (allItemIds.every(id => this.state.selectedItems.includes(id))) {
      // 取消选中所有项目
      const newSelectedItems = this.state.selectedItems.filter(id => !allItemIds.includes(id));
      this.updateState({ selectedItems: newSelectedItems });
    } else {
      // 选中所有项目
      const newSelectedItems = [...new Set([...this.state.selectedItems, ...allItemIds])];
      this.updateState({ selectedItems: newSelectedItems });
    }
  }

  /**
   * 重置状态到初始值
   */
  public resetState(): void {
    this.state = {
      inventoryList: [],
      deviceCategories: [],
      departmentCategories: [],
      currentCategory: 'all',
      currentDepartmentCategory: 'all',
      isLoading: false,
      selectedItems: [],
      searchQuery: '',
      tableFields: [],
      categoryExtFields: {},
      currentExtFields: []
    };
    this.emitter.emit('state-change', this.state);
  }
}

export default InventoryStateService;
