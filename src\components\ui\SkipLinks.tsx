import React from 'react';

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
  className?: string;
}

/**
 * 跳转链接组件
 * 为键盘用户提供快速导航到页面主要区域的功能
 * 符合WCAG无障碍访问标准
 */
const SkipLinks: React.FC<SkipLinksProps> = ({
  links = [
    { href: '#main-content', label: '跳转到主要内容' },
    { href: '#navigation', label: '跳转到导航菜单' },
    { href: '#search', label: '跳转到搜索' }
  ],
  className = ''
}) => {
  return (
    <div className={`skip-links ${className}`}>
      {links.map((link, index) => (
        <a
          key={index}
          href={link.href}
          className="skip-link"
          onClick={(e) => {
            e.preventDefault();
            const target = document.querySelector(link.href);
            if (target) {
              // 确保目标元素可以接收焦点
              if (!target.hasAttribute('tabindex')) {
                target.setAttribute('tabindex', '-1');
              }
              (target as HTMLElement).focus();
              target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }}
        >
          {link.label}
        </a>
      ))}
    </div>
  );
};

export default SkipLinks;
