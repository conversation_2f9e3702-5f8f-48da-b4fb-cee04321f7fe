import React from 'react';
import DialogBase from '../components/ui/DialogBase';
import { useDialogState } from '../hooks/base/useDialogState';

interface DialogTemplateProps {
  // 对话框是否打开
  isOpen: boolean;
  // 关闭对话框的回调
  onClose: () => void;
  // 确认回调
  onConfirm?: (data?: any) => void | Promise<void>;
  // 标题
  title?: string;
  // 内容
  content?: React.ReactNode;
  // 确认按钮文本
  confirmText?: string;
  // 取消按钮文本
  cancelText?: string;
  // 是否显示加载状态
  isLoading?: boolean;
  // 对话框宽度
  width?: string | number;
  // 对话框最大宽度
  maxWidth?: string | number;
  // 对话框CSS类名
  className?: string;
}

/**
 * 对话框组件模板
 * 用于快速创建新的对话框组件
 */
const DialogTemplate: React.FC<DialogTemplateProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title = '对话框标题',
  content,
  confirmText = '确认',
  cancelText = '取消',
  isLoading = false,
  width = '100%',
  maxWidth = '28rem',
  className = ''
}) => {
  // 处理确认
  const handleConfirm = async () => {
    try {
      if (onConfirm) {
        await onConfirm();
      }
      onClose();
    } catch (error) {
      console.error('确认操作失败:', error);
    }
  };

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={onClose}
      width={width}
      maxWidth={maxWidth}
      className={className}
      animation="fade"
    >
      <div className="p-6">
        {/* 对话框标题 */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
          <button
            onClick={onClose}
            disabled={isLoading}
            className="text-gray-500 hover:text-gray-700 focus-ring rounded-full p-1"
            aria-label="关闭"
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="mb-6">
          {content || (
            <p className="text-gray-700">
              这是一个对话框模板，您可以根据需要自定义内容。
            </p>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isLoading}
            className={`px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus-ring
              ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {cancelText}
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={isLoading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus-ring
              ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? '处理中...' : confirmText}
          </button>
        </div>
      </div>
    </DialogBase>
  );
};

/**
 * 使用示例：
 * 
 * // 1. 使用组件
 * const MyDialog = () => {
 *   const [isOpen, setIsOpen] = useState(false);
 *   
 *   return (
 *     <>
 *       <button onClick={() => setIsOpen(true)}>打开对话框</button>
 *       <DialogTemplate
 *         isOpen={isOpen}
 *         onClose={() => setIsOpen(false)}
 *         title="我的对话框"
 *         onConfirm={() => console.log('确认')}
 *       />
 *     </>
 *   );
 * };
 * 
 * // 2. 使用Hook
 * const MyDialogWithHook = () => {
 *   const dialog = useDialogState();
 *   
 *   return (
 *     <>
 *       <button onClick={() => dialog.open()}>打开对话框</button>
 *       <DialogTemplate
 *         isOpen={dialog.isOpen}
 *         onClose={dialog.close}
 *         title="我的对话框"
 *         isLoading={dialog.isLoading}
 *         onConfirm={async () => {
 *           dialog.setLoading(true);
 *           await someAsyncOperation();
 *           dialog.setLoading(false);
 *         }}
 *       />
 *     </>
 *   );
 * };
 */

export default DialogTemplate;
