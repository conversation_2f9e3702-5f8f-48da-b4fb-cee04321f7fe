import { InventoryItem } from '../../../types/inventory';
import { BaseServiceState, BaseServiceEvents } from '../../base/baseService';

/**
 * 部门分类项目接口
 */
export interface DepartmentCategory {
  id: string;
  name: string;
  count: number;
  children?: DepartmentCategory[];
  // 用于多部门人员节点，存储原始人员ID
  originalPersonId?: number;
  // 部门路径，使用斜杠分隔的层级结构，如: "技术部/开发组/前端组"
  departmentPath?: string;
  // 自定义图标类型
  customIcon?: string;
  // 标记是否为临时节点（等待真实ID）
  isTemporary?: boolean;
}

/**
 * 部门服务状态接口
 */
export interface DepartmentServiceState extends BaseServiceState {
  departmentCategories: DepartmentCategory[];
  currentCategory: string;
}

/**
 * 部门服务事件类型
 */
export interface DepartmentServiceEvents extends BaseServiceEvents<DepartmentServiceState> {
  'tree-loaded': DepartmentCategory[];
  'tree-updated': DepartmentCategory[];
  'department-added': { parentId: string, name: string };
  'person-added': { departmentId: string, name: string, alias?: string };
  'node-renamed': { nodeId: string, newName: string };
  'node-deleted': { nodeId: string };
  'node-moved': {
    nodeId: string,
    newParentId: string,
    personName?: string,
    personAlias?: string,
    oldDepartmentName?: string,
    newDepartmentName?: string
  };
}

/**
 * 部门服务接口
 */
export interface DepartmentServiceInterface {
  loadDepartmentTree(forceRefresh?: boolean): Promise<DepartmentCategory[]>;
  updateDepartmentTreeCounts(inventoryList: InventoryItem[]): boolean;
  filterByDepartment(
    inventoryList: InventoryItem[],
    departmentCategories: DepartmentCategory[],
    categoryId: string
  ): InventoryItem[];
  addDepartment(parentId: string, departmentName: string): Promise<DepartmentCategory[]>;
  addPerson(departmentId: string, personName: string, alias?: string): Promise<DepartmentCategory[]>;
  renameDepartmentOrPerson(nodeId: string, newName: string): Promise<DepartmentCategory[]>;
  deleteDepartmentOrPerson(nodeId: string): Promise<DepartmentCategory[]>;
  updateDepartmentPosition(departmentId: string, newParentId: string): Promise<DepartmentCategory[]>;
  updatePersonPosition(personId: string, newDepartmentId: string): Promise<DepartmentCategory[]>;
  getState(): DepartmentServiceState;
  on<K extends keyof DepartmentServiceEvents>(event: K, callback: (data: DepartmentServiceEvents[K]) => void): void;
  off<K extends keyof DepartmentServiceEvents>(event: K, callback: (data: DepartmentServiceEvents[K]) => void): void;
}
