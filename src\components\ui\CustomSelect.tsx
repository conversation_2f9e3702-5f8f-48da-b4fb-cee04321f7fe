import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  options: Option[];
  placeholder?: string;
  error?: string;
  disabled?: boolean;
}

// 自定义选择器组件，样式与DatePicker保持一致
const CustomSelect: React.FC<CustomSelectProps> = ({
  name,
  value,
  onChange,
  options,
  placeholder = '请选择',
  error,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [displayValue, setDisplayValue] = useState('');
  const inputRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  // 更新显示值
  useEffect(() => {
    const selectedOption = options.find(option => option.value === value);
    setDisplayValue(selectedOption ? selectedOption.label : '');
  }, [value, options]);

  // 计算下拉菜单位置
  useEffect(() => {
    if (isOpen && inputRef.current) {
      try {
        const rect = inputRef.current.getBoundingClientRect();
        
        // 计算可用空间
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;
        const dropdownHeight = Math.min(options.length * 40 + 16, 200); // 最大高度200px

        // 默认向下展开
        let top = rect.bottom + window.scrollY;

        // 如果下方空间不足且上方空间更多，则向上展开
        if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
          top = rect.top + window.scrollY - dropdownHeight;
        }

        setDropdownPosition({
          top: top,
          left: rect.left + window.scrollX,
          width: rect.width
        });
      } catch (error) {
        console.error('CustomSelect: 计算位置时出错:', error);
      }
    }
  }, [isOpen, options.length]);

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && dropdownRef.current) {
        const isOutsideInput = !inputRef.current.contains(event.target as Node);
        const isOutsideDropdown = !dropdownRef.current.contains(event.target as Node);

        if (isOutsideInput && isOutsideDropdown) {
          setIsOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 处理窗口滚动和大小变化
  useEffect(() => {
    if (!isOpen) return;

    const recalculatePosition = () => {
      if (isOpen && inputRef.current) {
        try {
          const rect = inputRef.current.getBoundingClientRect();
          const viewportHeight = window.innerHeight;
          const spaceBelow = viewportHeight - rect.bottom;
          const spaceAbove = rect.top;
          const dropdownHeight = Math.min(options.length * 40 + 16, 200);

          let top = rect.bottom + window.scrollY;
          if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
            top = rect.top + window.scrollY - dropdownHeight;
          }

          setDropdownPosition({
            top: top,
            left: rect.left + window.scrollX,
            width: rect.width
          });
        } catch (error) {
          console.error('CustomSelect: 重新计算位置时出错:', error);
        }
      }
    };

    let debounceTimer: number | null = null;
    const debouncedRecalculate = () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      debounceTimer = window.setTimeout(recalculatePosition, 100);
    };

    window.addEventListener('scroll', debouncedRecalculate, true);
    window.addEventListener('resize', debouncedRecalculate);

    return () => {
      window.removeEventListener('scroll', debouncedRecalculate, true);
      window.removeEventListener('resize', debouncedRecalculate);
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [isOpen, options.length]);

  // 处理选项选择
  const handleOptionSelect = (optionValue: string) => {
    // 创建合成事件
    const syntheticEvent = {
      target: { name, value: optionValue }
    } as React.ChangeEvent<HTMLSelectElement>;

    onChange(syntheticEvent);
    setIsOpen(false);
  };

  // 处理输入框点击
  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className="relative">
      <div
        ref={inputRef}
        className={`w-full px-3 py-1.5 border h-[30px] focus:outline-none cursor-pointer flex justify-between items-center
          ${error ? 'border-red-300' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
        onClick={handleInputClick}
      >
        <span className={`text-sm ${!displayValue ? 'text-gray-400' : 'text-gray-700'}`}>
          {displayValue || placeholder}
        </span>
        <svg
          className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </div>

      {isOpen && !disabled && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[9999] bg-white border border-gray-300 shadow-lg rounded-md max-h-[200px] overflow-y-auto"
          style={{
            top: `${dropdownPosition.top + 2}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {options.map((option) => (
            <div
              key={option.value}
              className={`px-3 py-2 text-sm cursor-pointer hover:bg-blue-50 transition-colors ${
                option.value === value ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
              }`}
              onClick={() => handleOptionSelect(option.value)}
            >
              {option.label}
            </div>
          ))}
        </div>,
        document.body
      )}

      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

export default CustomSelect;
