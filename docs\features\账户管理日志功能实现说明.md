# 账户管理日志功能实现说明

## 功能概述

基于现有的巡检任务日志页面，在巡检日志管理页中实现了账户管理日志功能，用于查看移动端用户的添加、修改、删除等操作记录。

## 实现的文件

### 1. 新创建的文件

#### `src/services/Inspection/accountLogService.ts`
- 账户管理日志服务类
- 实现API调用：`{"action": "query_logs", "action_params": {"log_table": "inspection", "log_type": 53}}`
- 支持的日志类型：
  - 添加移动端用户（log_type: 50）
  - 修改移动端用户（log_type: 51）
  - 删除移动端用户（log_type: 52）
- 提供数据过滤、分页、缓存等功能

#### `src/hooks/Inspection/useAccountLog.ts`
- 账户管理日志Hook
- 封装服务层功能，提供React组件使用的接口
- 包含状态管理、分页逻辑、过滤条件等

#### `src/pages/Inspection/AccountLog.tsx`
- 账户管理日志页面组件
- 参考巡检任务日志页面的UI风格、按键样式、表格布局
- 支持的功能：
  - 数据刷新
  - 筛选（操作类型、用户名、人员姓名）
  - 搜索（用户名、人员姓名、操作类型）
  - 分页显示
  - 响应式表格

### 2. 修改的文件

#### `src/pages/Inspection/InspectionLogManagement.tsx`
- 添加了AccountLog组件的导入
- 将账户管理日志的占位符替换为实际的AccountLog组件

## 页面结构

```
巡检管理
├── 巡检任务
├── 数据同步
├── 巡检结果
├── 账户管理
└── 巡检日志
    ├── 巡检任务日志
    ├── 巡检结果日志 (待实现)
    ├── 数据同步日志 (待实现)
    ├── 账户管理日志 (已实现) ✓
    └── 巡检状态日志 (待实现)
```

## API接口规范

### 账户管理日志API
```json
{
  "action": "query_logs",
  "action_params": {
    "log_table": "inspection", 
    "log_type": 53  // 或具体的日志类型：50(添加)、51(修改)、52(删除)
  }
}
```

### 响应数据格式
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 3,
        "log_type": 50,
        "log_type_text": "添加移动端用户",
        "new_data": {
          "operation": "add_mobile_user",
          "person_alias": "",
          "person_id": 1,
          "person_name": "123",
          "register_timestamp": **********,
          "user_name": "123"
        },
        "original_data": "",
        "target_id": 3,
        "target_name": "",
        "timestamp": **********,
        "timestamp_str": "2025-07-29 19:00:13"
      }
    ],
    "total_count": 3
  }
}
```

## 表格字段说明

| 字段名称 | 数据来源 | 说明 |
|---------|---------|------|
| 操作类型 | log_type_text | 显示操作类型（添加/修改/删除移动端用户） |
| 用户名 | new_data.user_name 或 original_data.user_name | 移动端登录用户名 |
| 人员姓名 | new_data.person_name 或 original_data.person_name | 关联的人员姓名 |
| 人员备注 | new_data.person_alias 或 original_data.person_alias | 人员备注信息 |
| 操作时间 | timestamp | 格式化显示的操作时间 |

## 功能特性

### 1. 数据展示
- 支持分页显示，默认每页10条记录
- 支持全部显示模式
- 自动格式化时间戳显示

### 2. 筛选功能
- **操作类型筛选**：可选择添加、修改、删除操作
- **用户名筛选**：支持模糊搜索用户名
- **人员姓名筛选**：支持模糊搜索人员姓名
- **日期范围筛选**：通过父组件传入的日期参数

### 3. 搜索功能
- 全局搜索：同时搜索用户名、人员姓名、操作类型
- 实时搜索：通过父组件传入的搜索参数

### 4. 数据刷新
- 支持手动刷新数据
- 30秒智能缓存机制
- 响应外部刷新触发器

## 技术实现

### 1. 架构设计
- **服务层**：AccountLogService 负责API调用和数据管理
- **Hook层**：useAccountLog 封装业务逻辑
- **组件层**：AccountLog 负责UI展示和用户交互

### 2. 类型安全
- 完整的TypeScript类型定义
- 严格的类型检查和验证
- 避免使用any类型，使用unknown和类型守卫

### 3. 性能优化
- 智能缓存机制减少API调用
- 分页显示减少DOM渲染压力
- 防重复调用机制

### 4. 用户体验
- 响应式表格设计
- 加载状态指示
- 错误提示和处理
- 键盘导航支持

## 使用方法

1. 进入巡检管理页面
2. 点击"巡检日志"导航项
3. 选择"账户管理日志"标签页
4. 查看账户管理操作记录
5. 使用筛选和搜索功能定位特定记录

## 注意事项

1. 日志数据来源于后端的inspection表，log_type为账户管理相关类型
2. 时间戳自动处理秒级和毫秒级格式
3. 支持空数据和错误状态的友好提示
4. 遵循现有的UI设计规范和交互模式
