import { useState, useEffect, useCallback } from 'react';
import { DepartmentCategory } from '../../services/Inventory/departmentService';
import {
  validateDepartmentName,
  validatePersonName,
  validatePersonAlias,
  validateMobileNumber
} from '../../utils/fieldValidation';
import { checkPersonUniqueness, buildPersonDisplayName } from '../../utils/personUtils';

// 重新导出类型，使UI层可以从Hook层导入
export type { DepartmentCategory };

/**
 * 用于管理添加部门/人员对话框的状态和逻辑
 */
export interface UseAddDepartmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (parentId: string, name: string, itemType: 'department' | 'person', alias?: string, mobileNumber?: string, positionSecurityLevel?: number) => Promise<void>;
  departmentCategories: DepartmentCategory[];
  mode: 'department' | 'person';
  selectedCategory?: string;
  disableParentSelection?: boolean; // 是否禁用父级部门选择，用于从树状图联动添加时
}

export interface UseAddDepartmentDialogReturn {
  // 表单状态
  name: string;
  setName: (value: string) => void;
  parentId: string;
  setParentId: (value: string) => void;
  alias: string;
  setAlias: (value: string) => void;
  mobileNumber: string;
  setMobileNumber: (value: string) => void;
  positionSecurityLevel: number | undefined;
  setPositionSecurityLevel: (value: number | undefined) => void;

  // UI状态
  isLoading: boolean;
  error: string | null;
  disableParentSelection: boolean; // 是否禁用父级部门选择

  // 业务处理方法
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  clearError: () => void;

  // 数据处理
  parentDepartments: DepartmentCategory[];
}

export default function useAddDepartmentDialog({
  isOpen,
  onClose,
  onAdd,
  departmentCategories,
  mode,
  selectedCategory,
  disableParentSelection = false
}: UseAddDepartmentDialogProps): UseAddDepartmentDialogReturn {
  // 简单稳定的状态管理
  const [name, setName] = useState('');
  const [parentId, setParentId] = useState('all-dept');
  const [alias, setAlias] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [positionSecurityLevel, setPositionSecurityLevel] = useState<number | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 查找部门节点
  const findDepartmentById = useCallback((categories: DepartmentCategory[], id: string): DepartmentCategory | null => {
    for (const category of categories) {
      if (category.id === id) {
        return category;
      }
      if (category.children) {
        const found = findDepartmentById(category.children, id);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // 根据名称查找部门
  const findDepartmentByName = useCallback((categories: DepartmentCategory[], name: string): DepartmentCategory | null => {
    for (const category of categories) {
      if (category.id === `dept-${name}`) {
        return category;
      }
      if (category.children) {
        const found = findDepartmentByName(category.children, name);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // 扁平化部门列表
  const flattenDepartments = useCallback((departments: DepartmentCategory[]): DepartmentCategory[] => {
    let result: DepartmentCategory[] = [];

    for (const dept of departments) {
      // 添加当前部门，但不包括人员节点（以person-或resp-开头的ID）
      if (!dept.id.startsWith('person-') && !dept.id.startsWith('resp-')) {
        result.push(dept);
      }

      // 如果有子部门，递归添加
      if (dept.children?.length) {
        // 添加所有子部门，但不包括人员节点（以person-或resp-开头的ID）
        const subDepartments = dept.children.filter(child =>
          !child.id.startsWith('person-') && !child.id.startsWith('resp-')
        );
        result = result.concat(flattenDepartments(subDepartments));
      }
    }

    return result;
  }, []);

  // 获取可选择的父级部门列表
  const getParentDepartments = useCallback((): DepartmentCategory[] => {
    // 获取所有部门（包括根节点和一级部门）
    let allDepartments: DepartmentCategory[] = [...departmentCategories];

    // 获取扁平化的部门列表
    let departments = flattenDepartments(allDepartments);

    // 如果是添加人员模式，过滤掉根节点选项
    if (mode === 'person') {
      departments = departments.filter(dept => dept.id !== 'all-dept');
    }

    return departments;
  }, [departmentCategories, mode, flattenDepartments]);

  // 计算父级部门列表
  const parentDepartments = getParentDepartments();

  // 监听对话框关闭，重置所有状态到初始值
  useEffect(() => {
    if (!isOpen) {
      // 对话框关闭时，重置所有状态到初始值
      setName('');
      setParentId('all-dept');
      setAlias('');
      setMobileNumber('');
      setPositionSecurityLevel(undefined);
      setError(null);
      return;
    }
  }, [isOpen]);

  // 根据选中的部门和模式设置初始父级ID
  useEffect(() => {
    if (!isOpen) return;

    if (selectedCategory) {
      const category = findDepartmentById(departmentCategories, selectedCategory);

      if (category) {
        // 如果模式是添加人员
        if (mode === 'person') {
          // 如果选中的是人员节点（以person-开头），则找到该人员所属的部门
          if (selectedCategory.startsWith('person-')) {
            // 找到人员所属的部门
            const findParentDepartment = (categories: DepartmentCategory[], targetId: string): DepartmentCategory | null => {
              for (const category of categories) {
                if (category.children?.some(child => child.id === targetId)) {
                  return category;
                }
                if (category.children?.length) {
                  const found = findParentDepartment(category.children, targetId);
                  if (found) return found;
                }
              }
              return null;
            };

            const parentDept = findParentDepartment(departmentCategories, selectedCategory);
            if (parentDept) {
              console.log('选中人员节点，自动选择所属部门:', parentDept.name);
              setParentId(parentDept.id);
            } else {
              // 如果找不到对应的部门，使用默认第一个部门
              console.warn('找不到人员所属部门，使用默认部门');
              if (departmentCategories.length > 0 && departmentCategories[0]?.children && departmentCategories[0].children.length > 0) {
                setParentId(departmentCategories[0].children[0]?.id || 'all-dept');
              }
            }
          }
          // 兼容旧版人员节点格式（以resp-开头）
          else if (selectedCategory.startsWith('resp-')) {
            // 从人员ID中提取部门ID
            // 格式：resp-部门名-人员名
            const parts = selectedCategory.split('-');
            if (parts.length >= 3) {
              const deptName = parts[1]; // 获取部门名

              const parentDept = findDepartmentByName(departmentCategories, deptName);
              if (parentDept) {
                console.log('选中旧版人员节点，自动选择所属部门:', parentDept.name);
                setParentId(parentDept.id);
              } else {
                // 如果找不到对应的部门，使用默认第一个部门
                console.warn('找不到旧版人员所属部门，使用默认部门');
                if (departmentCategories.length > 0 && departmentCategories[0]?.children && departmentCategories[0].children.length > 0) {
                  setParentId(departmentCategories[0].children[0]?.id || 'all-dept');
                }
              }
            }
          }
          else {
            // 如果选中的是根节点，默认选择第一个部门（如果存在）
            if (category.id === 'all-dept') {
              if (departmentCategories.length > 0 && departmentCategories[0]?.children && departmentCategories[0].children.length > 0) {
                setParentId(departmentCategories[0].children[0]?.id || 'all-dept');
              }
            } else {
              // 对于非根节点，直接使用该节点作为父级
              setParentId(category.id);
            }
          }
        } else {
          // 如果模式是添加部门，也直接使用选中的部门作为父级
          setParentId(category.id);
        }
      }
    } else {
      // 如果没有选中的部门，默认选择根节点
      setParentId('all-dept');
    }

    // 注意：名称和错误状态的重置已在关闭时的useEffect中处理，这里不需要重复重置
  }, [mode, departmentCategories, isOpen, selectedCategory, findDepartmentById, findDepartmentByName]);

  // 实时错误清除机制 - 当用户修正了导致错误的问题时，立即清除相关错误
  useEffect(() => {
    if (!error) return;

    // 当岗位密级从无效变为有效时，清除岗位密级相关错误
    if (mode === 'person' && typeof positionSecurityLevel === 'number' && error === '岗位密级不能为空') {
      setError(null);
      return;
    }

    // 当名称从空变为非空时，清除名称相关错误
    if (name.trim() && error.includes('名称不能为空')) {
      setError(null);
      return;
    }

    // 当父级部门选择有效时，清除部门相关错误
    if (parentId && parentId !== 'all-dept' &&
        (error.includes('请选择父级部门') || error.includes('不能直接添加到公司名称'))) {
      setError(null);
      return;
    }
  }, [positionSecurityLevel, name, parentId, error, mode]);

  // 表单验证（只保留业务逻辑验证，字段验证由UI层处理）
  const validateForm = useCallback((): string | null => {
    // 基本字段验证
    if (!name.trim()) {
      return mode === 'department' ? '部门名称不能为空' : '人员名称不能为空';
    }

    // 父级部门验证
    if (!parentId) {
      return '请选择父级部门';
    }

    // 如果是添加人员，禁止在根节点下添加
    if (mode === 'person' && parentId === 'all-dept') {
      return '人员必须归属于某个具体的部门，不能直接添加到公司名称';
    }

    // 如果是添加人员，岗位密级为必填项 - 修复：0是有效值（非涉密人员）
    if (mode === 'person' && typeof positionSecurityLevel !== 'number') {
      return '岗位密级不能为空';
    }

    // 如果是添加部门，检查同级部门是否已存在同名部门
    if (mode === 'department') {
      const parentDept = findDepartmentById(departmentCategories, parentId);
      if (parentDept) {
        // 检查同级部门是否已存在同名部门
        const existingDept = parentDept.children?.find((dept: DepartmentCategory) =>
          dept.id.startsWith('dept-') && dept.name.trim() === name.trim()
        );

        if (existingDept) {
          return `同节点下的部门不允许重名，已存在同名部门: ${name.trim()}`;
        }
      }
    }

    // 如果是添加人员，检查同部门下是否已存在相同的人员（人员名称+备注组合）
    if (mode === 'person') {
      const parentDept = findDepartmentById(departmentCategories, parentId);
      if (parentDept) {
        // 获取部门下的现有人员
        const existingPersons = (parentDept.children || [])
          .filter((person: DepartmentCategory) => person.id.startsWith('person-'))
          .map((person: DepartmentCategory) => ({ name: person.name }));

        // 使用统一的人员唯一性检查
        const uniquenessCheck = checkPersonUniqueness(
          name.trim(),
          alias?.trim() || '',
          existingPersons,
          { checkDisplayName: true }
        );

        if (!uniquenessCheck.isUnique) {
          const currentPersonDisplayName = buildPersonDisplayName(name.trim(), alias?.trim());
          return `该部门下已有该人员，不允许重复: ${currentPersonDisplayName}`;
        }
      }
    }

    return null;
  }, [name, parentId, alias, mobileNumber, positionSecurityLevel, mode, departmentCategories, findDepartmentById]);

  // 处理表单提交
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // 表单验证
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // 调用添加方法
      // 如果是人员模式，传入备注、联系方式和岗位密级参数
      if (mode === 'person') {
        await onAdd(parentId, name, mode, alias, mobileNumber, positionSecurityLevel);
      } else {
        await onAdd(parentId, name, mode);
      }

      // 清空表单
      setName('');
      setAlias('');
      setMobileNumber('');
      setPositionSecurityLevel(undefined);

      // 关闭对话框
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : '添加失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  }, [name, parentId, alias, mobileNumber, positionSecurityLevel, mode, validateForm, onAdd, onClose]);

  return {
    // 表单状态
    name,
    setName,
    parentId,
    setParentId,
    alias,
    setAlias,
    mobileNumber,
    setMobileNumber,
    positionSecurityLevel,
    setPositionSecurityLevel,

    // UI状态
    isLoading,
    error,
    disableParentSelection,

    // 业务处理方法
    handleSubmit,
    clearError,

    // 数据处理
    parentDepartments
  };
}