import React, { useRef, useEffect, useState } from 'react';
import {
  Search,
  Plus,
  Trash2,
  Columns,
  FileUp,
  FileDown,
  Laptop,
  Users,
  X,
  Settings,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import ResizeHandle from '../../../components/ui/ResizeHandle';
import CategoryTree from './CategoryTree';
import InventoryTable from './InventoryTable';
import AddInventoryDialog from './Dialogs/AddInventoryDialog';
import EditInventoryDialog from './Dialogs/EditInventoryDialog';
import DeleteConfirmDialog from './Dialogs/DeleteConfirmDialog';
import AddCategoryDialog from './Dialogs/AddCategoryDialog';
import AddDepartmentDialog from './Dialogs/AddDepartmentDialog';
import MoveConfirmDialog from './Dialogs/MoveConfirmDialog';
import ChangeDepartmentDialog from './Dialogs/ChangeDepartmentDialog';
import EditPersonDialog from './Dialogs/EditPersonDialog';
import ColumnSettingsDialog from './Dialogs/ColumnSettingsDialog';
import RfidManagementDialog from './Dialogs/RfidManagementDialog';
import DepartmentRequiredDialog from './Dialogs/DepartmentRequiredDialog';
import useInventoryManagement from '../../../hooks/Inventory/useInventoryManagement';
import { FieldDefinition } from '../../../types/inventory';
import Import from '../Import';
import Export from '../Export';
import useExport from '../../../hooks/Inventory/Export/useExport';
import InventoryService from '../../../services/Inventory/inventoryService';
import ExtFieldService from '../../../services/Inventory/extFieldService';
import DepartmentService from '../../../services/Inventory/departmentService';
import { IncrementalCacheUpdater } from '../../../services/Inventory/department/incrementalCacheUpdater';
import { TreeUtils } from '../../../services/Inventory/department/treeUtils';
import { usePageFocus, PageTitle } from '../../../hooks/base/usePageFocus';
import { useListFocus } from '../../../hooks/base/useListFocus';

const InventoryManagement: React.FC = () => {
  // 页面焦点管理
  usePageFocus({
    title: '台账管理',
    autoFocus: true
  });

  // 列表焦点管理
  const { listRef, handleItemsChange } = useListFocus({
    itemSelector: '[data-item-id]',
    autoFocusOnAdd: true,
    scrollToNewItem: true
  });

  // 对话框状态
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showColumnSettingsDialog, setShowColumnSettingsDialog] = useState(false);
  const [showChangeDepartmentDialog, setShowChangeDepartmentDialog] = useState(false);
  const [changeDepartmentPersonId, setChangeDepartmentPersonId] = useState<string>('');
  const [changeDepartmentPersonName, setChangeDepartmentPersonName] = useState<string>('');
  const [changeDepartmentCurrentDeptName, setChangeDepartmentCurrentDeptName] = useState<string>('');
  const [showEditPersonDialog, setShowEditPersonDialog] = useState(false);
  const [editPersonId, setEditPersonId] = useState<string>('');
  const [exportMode, setExportMode] = useState<'selected' | 'all' | 'filtered'>('filtered');
  const [exportCategoryId, setExportCategoryId] = useState<string | undefined>(undefined);
  const [exportCategoryMode, setExportCategoryMode] = useState<'device' | 'department'>('device');
  const [showRfidManagementDialog, setShowRfidManagementDialog] = useState(false);
  const [showSelectionTipDialog, setShowSelectionTipDialog] = useState(false);

  // 左侧树状图宽度状态
  const [leftPanelWidth, setLeftPanelWidth] = useState(256); // 默认宽度为256px (w-64 = 16rem = 256px)
  const minLeftPanelWidth = 200; // 最小宽度
  const maxLeftPanelWidth = 500; // 最大宽度
  const collapsedWidth = 0; // 收起状态的宽度
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false); // 是否收起左侧面板
  const previousWidthRef = useRef(leftPanelWidth); // 保存收起前的宽度，用于恢复

  // 使用导出Hook
  const { exportTemplateAndSendToServer } = useExport();

  // 使用自定义Hook获取状态和方法
  const {
    // 加载数据方法
    loadInventory,
    // 基础状态
    inventoryList,
    deviceCategories,
    departmentCategories,
    currentCategory,
    currentDepartmentCategory,
    filteredItems,
    columnVisibility,
    selectedItems,
    isLoading,

    // UI状态
    categoryMode,
    showColumnSettings,
    searchInputValue,
    categorySearchValue,
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showDepartmentRequiredDialog,
    editItem,
    selectedCategoryInfo,
    showAddCategoryDialog,
    categoryDialogMode,
    showAddDepartmentDialog,
    departmentDialogMode,
    filteredCategories,
    isFiltered,

    // 表格位置记忆相关
    currentPageIndex,
    lastOperatedItemId,
    handlePageChange,

    // 方法
    setCategoryMode,
    toggleSelectItem,
    toggleSelectAll,
    setSearchQuery,
    setDepartmentSearchQuery,
    setSearchInputValue,
    setCategorySearchValue,
    setShowColumnSettings,
    setShowAddDialog,
    setShowEditDialog,
    setShowDepartmentRequiredDialog,
    toggleColumn,
    resetColumnVisibility,
    handleCategorySelect,
    handleAdd,
    handleDepartmentRequiredConfirm,
    handleEdit,
    handleDelete,
    handleBatchDelete,
    executeAdd,
    executeUpdate,
    executeDelete,
    handleAddType,
    handleAddDevice,
    executeAddCategory,
    handleAddDepartment,
    handleAddPerson,
    executeAddDepartmentOrPerson,
    handleRenameCategory,
    handleDeleteCategory,
    handleUpdateParentCategory,
    handleDeleteParentCategory,
    handleUpdateSubCategory,
    handleDeleteSubCategory,
    handleRenameDepartmentOrPerson,
    handleDeleteDepartmentOrPerson,
    handleDrop,
    confirmMove,
    cancelMove,
    showMoveConfirmDialog,
    moveConfirmDraggedNode,
    moveConfirmTargetNode,
    canDragNode,
    canDropNode,
    isItemSelected,
    areAllItemsSelected,
    someItemsSelected,
    tableFields,
    fieldDictionary,
    currentExtFields,
    extFieldValues,
    handleExtFieldChange,

    // 单行删除相关
    singleDeleteId
  } = useInventoryManagement();

  // 引用列设置按钮和菜单
  const columnSettingsBtnRef = useRef<HTMLButtonElement>(null);
  const columnSettingsMenuRef = useRef<HTMLDivElement>(null);

  // 查找节点的父部门
  const findParentDepartment = (categories: DepartmentCategory[], targetId: string): DepartmentCategory | undefined => {
    for (const category of categories) {
      if (category.children?.some(child => child.id === targetId)) {
        return category;
      }
      if (category.children?.length) {
        const found = findParentDepartment(category.children, targetId);
        if (found) return found;
      }
    }
    return undefined;
  };

  // 获取人员当前所属部门名称
  const getCurrentDepartmentName = (personId: string): string => {
    // 找到人员所属的部门
    const parentDept = findParentDepartment(departmentCategories, personId);
    return parentDept ? parentDept.name : '';
  };

  // 处理更改部门
  const handleChangeDepartment = (personId: string, personName: string) => {
    const currentDepartmentName = getCurrentDepartmentName(personId);

    setChangeDepartmentPersonId(personId);
    setChangeDepartmentPersonName(personName);
    setChangeDepartmentCurrentDeptName(currentDepartmentName);
    setShowChangeDepartmentDialog(true);
  };

  // 执行更改部门
  const executeChangeDepartment = async (newDepartmentId: string) => {
    try {
      // 使用handleDrop方法触发拖拽逻辑，这与拖拽功能使用相同的API
      await handleDrop(changeDepartmentPersonId, newDepartmentId);
      // 确认移动操作
      await confirmMove();
      return true;
    } catch (error) {
      console.error('更改部门失败:', error);
      return false;
    }
  };

  // 处理编辑人员信息
  const handleEditPerson = (personId: string) => {
    setEditPersonId(personId);
    setShowEditPersonDialog(true);
  };

  // 处理RFID管理
  const handleRfidManagement = () => {
    setShowRfidManagementDialog(true);
  };

  // 处理显示选择提示
  const handleShowSelectionTip = () => {
    setShowSelectionTipDialog(true);
  };

  // 获取选中的设备项目详情
  const getSelectedItemDetails = () => {
    return inventoryList.filter(item => selectedItems.includes(item.id));
  };

  // 人员信息更新完成后的回调
  const handlePersonUpdated = async (
    updatedPersonInfo?: any,
    departmentChanged?: boolean,
    oldDepartmentId?: string,
    newDepartmentId?: string
  ) => {
    console.log('人员信息更新完成，开始刷新相关数据...', {
      departmentChanged,
      oldDepartmentId,
      newDepartmentId
    });

    try {
      // 获取服务实例
      const departmentService = DepartmentService.getInstance();

      // 1. 如果部门发生了变化，使用高效的增量更新方式
      if (departmentChanged && oldDepartmentId && newDepartmentId && updatedPersonInfo) {
        console.log('1. 处理人员部门变化：从', oldDepartmentId, '移动到', newDepartmentId);
        console.log('使用增量更新方式，避免重复的API调用');

        // 使用增量更新器直接更新前端部门树结构
        // 注意：数据库已经通过编辑对话框的API调用更新了，这里只需要同步前端缓存
        console.log('使用增量更新器移动人员节点...');
        console.log('调试信息:', {
          personId: `person-${updatedPersonInfo.id}`,
          oldDepartmentId,
          newDepartmentId,
          personInfo: updatedPersonInfo
        });

        // 检查人员节点是否还在旧位置，如果已经移动了就不需要增量更新
        const currentCategories = departmentService.getState().departmentCategories;
        const personNodeId = `person-${updatedPersonInfo.id}`;
        const existingPersonNode = TreeUtils.findNodeById(currentCategories, personNodeId);

        if (existingPersonNode) {
          // 人员节点还存在，需要进行移动
          console.log('人员节点还在旧位置，执行增量更新...');

          try {
            // 先从旧部门删除人员节点
            let updatedCategories = IncrementalCacheUpdater.removeNode(
              currentCategories,
              personNodeId
            );
            console.log('人员节点删除成功');

            // 再添加到新部门
            const personData = {
              id: updatedPersonInfo.id,
              user_name: updatedPersonInfo.user_name,
              alias: updatedPersonInfo.alias || ''
            };

            updatedCategories = IncrementalCacheUpdater.addPersonNode(
              updatedCategories,
              newDepartmentId,
              personData
            );
            console.log('人员节点添加成功');

            // 更新部门服务的状态
            departmentService.updateState({ departmentCategories: updatedCategories });
            console.log('部门树增量更新完成');
          } catch (error) {
            console.error('增量更新失败:', error);
            // 如果增量更新失败，回退到重新加载部门树
            console.log('回退到重新加载部门树...');
            await departmentService.loadDepartmentTree(true);
            console.log('部门树重新加载完成');
          }
        } else {
          // 人员节点已经不在旧位置了，可能已经通过其他机制更新了
          console.log('人员节点已经不在旧位置，可能已经自动同步，跳过增量更新');

          // 检查人员是否已经在新部门
          const newDepartmentNode = TreeUtils.findNodeById(currentCategories, newDepartmentId);
          if (newDepartmentNode && newDepartmentNode.children) {
            const personInNewDept = newDepartmentNode.children.find(child =>
              child.id === personNodeId
            );
            if (personInNewDept) {
              console.log('确认：人员已经在新部门，无需额外操作');
            } else {
              console.log('人员不在新部门，需要重新加载部门树确保同步');
              await departmentService.loadDepartmentTree(true);
              console.log('部门树重新加载完成');
            }
          } else {
            console.log('无法找到新部门，重新加载部门树');
            await departmentService.loadDepartmentTree(true);
            console.log('部门树重新加载完成');
          }
        }

      } else if (updatedPersonInfo && updatedPersonInfo.id) {
        // 2. 如果只是人员信息更新（没有部门变化），使用增量更新
        console.log('1. 增量更新部门树中的人员节点');
        departmentService.updatePersonNodeInTree(updatedPersonInfo.id, updatedPersonInfo);
      } else {
        // 3. 如果没有具体的人员信息，只刷新人员缓存
        console.log('1. 刷新人员缓存（不重新加载部门树）');
        await departmentService.refreshPersonCache();
      }

      // 2. 统一刷新设备台账数据（避免重复刷新）
      console.log('2. 强制刷新设备台账数据');
      await loadInventory(true);

      // 3. 使用最新的设备列表更新部门树计数
      console.log('3. 开始更新部门树计数...');
      const inventoryService = InventoryService.getInstance();
      const inventoryList = inventoryService.getState().inventoryList;
      departmentService.updateDepartmentTreeCounts(inventoryList);
      console.log('部门树计数更新完成');

      // 4. 等待一小段时间确保数据加载完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 5. 强制刷新当前筛选结果并触发界面更新
      console.log('4. 强制刷新界面显示');
      inventoryService.forceRefreshCurrentFilter();

      console.log('人员信息更新后的数据刷新完成');
    } catch (error) {
      console.error('人员信息更新后刷新数据失败:', error);
      // 即使出错也尝试基本的数据刷新
      await loadInventory(true);
    }
  };

  // 处理左侧面板宽度调整
  const handleResize = (delta: number) => {
    if (isLeftPanelCollapsed) return; // 收起状态下不允许调整宽度

    setLeftPanelWidth(prevWidth => {
      // 计算新宽度，并确保在最小和最大宽度范围内
      const newWidth = Math.max(minLeftPanelWidth, Math.min(maxLeftPanelWidth, prevWidth + delta));
      previousWidthRef.current = newWidth; // 更新保存的宽度
      return newWidth;
    });
  };

  // 切换左侧面板收起/展开状态
  const toggleLeftPanel = () => {
    setIsLeftPanelCollapsed(prev => {
      if (prev) {
        // 从收起状态展开 - 使用setTimeout避免抖动
        requestAnimationFrame(() => {
          setLeftPanelWidth(previousWidthRef.current);
        });
        return false;
      } else {
        // 从展开状态收起 - 保存当前宽度
        previousWidthRef.current = leftPanelWidth;
        return true;
      }
    });
  };

  // 注意：数据加载逻辑已移至 useInventoryManagement 中集中处理
  // 避免多处重复加载导致的重复请求

  // 处理菜单定位
  useEffect(() => {
    // 仅在菜单显示时调整位置
    if (showColumnSettings && columnSettingsBtnRef.current && columnSettingsMenuRef.current) {
      const btnRect = columnSettingsBtnRef.current.getBoundingClientRect();
      const menu = columnSettingsMenuRef.current;

      // 设置菜单位置在按钮下方
      menu.style.top = `${btnRect.bottom + 5}px`;
      menu.style.left = `${btnRect.right - menu.offsetWidth}px`;
    }
  }, [showColumnSettings]);

  // 添加点击文档其它地方关闭菜单的处理
  useEffect(() => {
    if (!showColumnSettings) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        columnSettingsMenuRef.current &&
        !columnSettingsMenuRef.current.contains(event.target as Node) &&
        columnSettingsBtnRef.current &&
        !columnSettingsBtnRef.current.contains(event.target as Node)
      ) {
        setShowColumnSettings(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showColumnSettings, setShowColumnSettings]);

  return (
    <div className="h-full flex flex-col relative">
      <div className="flex-1 flex h-full min-h-0 gap-0">
        {/* 移除原来的收起/展开按钮 */}

        {/* 左侧分类树 */}
        <div
          className={`border border-gray-200 rounded-md bg-white shadow-sm overflow-hidden flex flex-col transition-all duration-300 ease-in-out ${
            isLeftPanelCollapsed ? 'opacity-0 invisible' : 'opacity-100 visible'
          }`}
          style={{
            width: `${leftPanelWidth}px`,
            height: '100%', // 确保填充整个高度
            flexShrink: 0,
            position: 'relative',
            marginLeft: isLeftPanelCollapsed ? `-${leftPanelWidth}px` : '5px',
            marginTop: '0px', // 确保上边距为0，与右侧对齐
            transition: 'margin-left 0.3s ease, opacity 0.3s ease, visibility 0.3s ease'
          }}
        >
          {/* 分类搜索框和按钮 */}
          <div className="px-3 pt-3 pb-3 border-b border-gray-200">
            {/* 搜索框 */}
            <div className="relative mb-2">
              <input
                type="text"
                placeholder={categoryMode === 'device' ? '搜索设备分类' : '搜索部门分类'}
                value={categorySearchValue}
                onChange={e => setCategorySearchValue(e.target.value)}
                className="pl-8 pr-3 py-1.5 w-full text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <Search className="absolute search-icon left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 h-3.5 w-3.5" />
            </div>

            {/* 添加按钮 - 只在设备分类模式下显示 */}
            {categoryMode === 'device' ? (
              <div className="flex space-x-2">
                <button
                  onClick={handleAddType}
                  className="flex-1 flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-1.5 font-bold stroke-[3]" />
                  设备分类
                </button>
                <button
                  onClick={handleAddDevice}
                  className="flex-1 flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-1.5 font-bold stroke-[3]" />
                  设备类型
                </button>
              </div>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={handleAddDepartment}
                  className="flex-1 flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-1.5 font-bold stroke-[3]" />
                  添加部门
                </button>
                <button
                  onClick={handleAddPerson}
                  className="flex-1 flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-1.5 font-bold stroke-[3]" />
                  添加人员
                </button>
              </div>
            )}
          </div>

          <div className="flex-1 overflow-auto">
            {categoryMode === 'device' ? (
              <CategoryTree
                categories={filteredCategories}
                currentCategory={currentCategory}
                onSelectCategory={handleCategorySelect}
                isFiltered={isFiltered}
                categoryMode="device"
                onRenameCategory={handleRenameCategory}
                onDeleteCategory={handleDeleteCategory}
                onAddInventory={(categoryId) => {
                  // 所有调用都传递true表示来自右键菜单
                  // 根据节点类型调用不同的添加方法
                  if (categoryId === 'all') {
                    // 根节点 - 调用添加分类方法
                    handleAddType(true);
                  } else if (categoryId && categoryId.startsWith('parent-') && !categoryId.includes('-', 'parent-'.length)) {
                    // 一级分类 - 调用添加类型方法（添加二级分类）
                    handleAddDevice(true);
                  } else {
                    // 二级分类或其他 - 调用添加台账方法
                    handleAdd(true);
                  }
                }}
                onExportCategory={(categoryId, categoryName) => {
                  // 设置导出模式为filtered，表示导出当前筛选的数据
                  setExportMode('filtered');
                  // 设置要导出的分类ID
                  setExportCategoryId(categoryId);
                  // 设置分类模式为设备分类
                  setExportCategoryMode('device');
                  // 先选中该分类，确保数据正确
                  handleCategorySelect(categoryId);
                  // 打开导出对话框
                  setShowExportDialog(true);
                }}
                onUpdateParentCategory={handleUpdateParentCategory}
                onDeleteParentCategory={handleDeleteParentCategory}
                onUpdateSubCategory={handleUpdateSubCategory}
                onDeleteSubCategory={handleDeleteSubCategory}
              />
            ) : (
              <CategoryTree
                categories={filteredCategories}
                currentCategory={currentDepartmentCategory}
                onSelectCategory={handleCategorySelect}
                isFiltered={isFiltered}
                categoryMode="department"
                onRenameCategory={handleRenameDepartmentOrPerson}
                onDeleteCategory={handleDeleteDepartmentOrPerson}
                onAddInventory={(categoryId) => {
                  // 所有调用都传递true表示来自右键菜单
                  // 根据传入的categoryId判断要执行的操作
                  if (categoryId === 'all-dept-add-department') {
                    // 根节点 - 添加部门
                    handleAddDepartment(true);
                  } else if (categoryId && categoryId.startsWith('dept-add-department-')) {
                    // 部门节点 - 添加部门
                    // 先选中该部门
                    const deptId = categoryId.replace('dept-add-department-', '');
                    handleCategorySelect(deptId);
                    // 然后打开添加部门对话框
                    handleAddDepartment(true);
                  } else if (categoryId && categoryId.startsWith('dept-add-person-')) {
                    // 部门节点 - 添加人员
                    // 先选中该部门
                    const deptId = categoryId.replace('dept-add-person-', '');
                    handleCategorySelect(deptId);
                    // 然后打开添加人员对话框
                    handleAddPerson(true);
                  } else {
                    // 人员节点或其他 - 添加台账
                    handleAdd(true);
                  }
                }}
                onExportCategory={(categoryId, categoryName) => {
                  // 设置导出模式为filtered，表示导出当前筛选的数据
                  setExportMode('filtered');
                  // 设置要导出的分类ID
                  setExportCategoryId(categoryId);
                  // 设置分类模式为部门分类
                  setExportCategoryMode('department');
                  // 先选中该分类，确保数据正确
                  handleCategorySelect(categoryId);
                  // 打开导出对话框
                  setShowExportDialog(true);
                }}
                draggable={true}
                onDrop={handleDrop}
                canDrag={canDragNode}
                canDrop={canDropNode}
                onChangeDepartment={handleChangeDepartment}
                onEditPerson={handleEditPerson}
              />
            )}
          </div>

          {/* 底部分类切换按钮 */}
          <div className="flex border-t border-gray-200 bg-gray-50 h-10">
            <button
              onClick={() => setCategoryMode('department')}
              className={`flex-1 flex items-center justify-center py-2 ${categoryMode === 'department' ? 'bg-blue-100 text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <Users className="h-4 w-4 mr-1.5" />
              部门
            </button>
            <div className="w-px bg-gray-300"></div>
            <button
              onClick={() => setCategoryMode('device')}
              className={`flex-1 flex items-center justify-center py-2 ${categoryMode === 'device' ? 'bg-blue-100 text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <Laptop className="h-4 w-4 mr-1.5" />
              设备
            </button>
          </div>
        </div>

        {/* 拖拽手柄容器 */}
        <div
          className="relative flex items-center"
          style={{
            width: '12px', // 从20px减小到12px
            marginLeft: isLeftPanelCollapsed ? '0' : '-2px', // 调整边距
            zIndex: 30,
            transition: 'margin-left 0.3s ease-in-out',
            backgroundColor: 'transparent'
          }}
        >
          {/* 拖拽手柄 */}
          {!isLeftPanelCollapsed && (
            <ResizeHandle
              direction="horizontal"
              onResize={handleResize}
              className="hover:bg-blue-100/50 active:bg-blue-200/50 transition-all duration-200 ease-in-out"
            />
          )}
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1 border border-gray-200 rounded-md bg-white shadow-sm overflow-hidden flex flex-col transition-all duration-300 ease-in-out"
          style={{
            height: '100%', // 确保填充整个高度
            marginLeft: isLeftPanelCollapsed ? '10px' : '3px',
            marginTop: '0px' // 确保上边距为0，与左侧对齐
          }}> {/* 调整边距从5px到3px */}
          {/* 工具栏 */}
          <div className="p-3 bg-gray-50 border-b flex items-center">
            {/* 收起/展开分类面板按钮 - 放在搜索框左侧 */}
            <button
              onClick={toggleLeftPanel}
              className="flex items-center justify-center w-6 h-6 p-0 text-gray-500 hover:text-gray-700 transition-colors"
              title={isLeftPanelCollapsed ? "展开分类面板" : "收起分类面板"}
            >
              {/* 使用带方向的箭头图标 */}
              {isLeftPanelCollapsed ? (
                <ChevronRight className="w-4 h-4" />
              ) : (
                <ChevronLeft className="w-4 h-4" />
              )}
            </button>

            {/* 搜索框 - 实时搜索 */}
            <div className="relative flex-1 ml-3">
              <input
                type="text"
                placeholder="搜索当前分类设备（多条件用逗号,或，分隔）..."
                value={searchInputValue}
                onChange={(e) => {
                  setSearchInputValue(e.target.value);
                  // 根据当前分类模式调用不同的搜索方法
                  if (categoryMode === 'device') {
                    setSearchQuery(e.target.value);
                  } else {
                    // 部门分类模式下，使用部门搜索方法
                    setDepartmentSearchQuery(e.target.value);
                  }
                }}
                className="pl-8 pr-3 py-1.5 w-full border rounded focus:ring-blue-500 focus:border-blue-500"
                title="支持多条件搜索，使用中文逗号，或英文逗号,分隔不同的搜索条件"
              />
              <Search className="absolute search-icon left-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              {searchInputValue && (
                <button
                  onClick={() => {
                    setSearchInputValue('');
                    // 根据当前分类模式清除不同的搜索查询
                    if (categoryMode === 'device') {
                      setSearchQuery('');
                    } else {
                      setDepartmentSearchQuery('');
                    }
                  }}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-2 ml-4">
              <button
                onClick={() => handleAdd(false)}
                className="flex items-center px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-1" />
                添加设备台账
              </button>

              <button
                onClick={handleBatchDelete}
                disabled={selectedItems.length === 0}
                className={`flex items-center px-3 py-1.5 rounded ${
                  selectedItems.length > 0
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                }`}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                删除所选
              </button>

              <div className="relative">
                <button
                  ref={columnSettingsBtnRef}
                  onClick={() => setShowColumnSettings(!showColumnSettings)}
                  className="flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                >
                  <Columns className="h-4 w-4 mr-1" />
                  列显示
                </button>

                {/* 列设置下拉菜单 */}
                {showColumnSettings && (
                  <div
                    ref={columnSettingsMenuRef}
                    className="fixed w-56 bg-white border rounded-md shadow-xl z-50 overflow-hidden animate-fadeIn"
                    style={{
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                    }}
                  >
                    <div className="p-3 border-b flex justify-between items-center bg-gray-50">
                      <span className="font-medium text-gray-700">列显示设置</span>
                      <button
                        onClick={resetColumnVisibility}
                        className="text-xs text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50"
                      >
                        重置
                      </button>
                    </div>
                    <div className="max-h-80 overflow-y-auto">
                      {/* 标准字段 */}
                      <div className="px-3 py-1 bg-gray-100 font-medium text-sm text-gray-600 sticky top-0 z-10">标准字段</div>
                      {(() => {
                        // 从fieldDictionary中获取所有标准字段
                        // 按照fieldDictionary中键的定义顺序排列，与表格列顺序保持一致
                        // 排除不需要在列设置中显示的字段：select（选择框）、actions（操作列）、parentCategory（设备类型分类）
                        const standardFields = Object.entries(fieldDictionary)
                          .filter(([key]) => key !== 'select' && key !== 'actions' && key !== 'parentCategory');
                        // 不再按字母顺序排序，保持fieldDictionary中的原始顺序

                        return standardFields.map(([key, title]) => (
                          <div key={key} className="px-3 py-2 flex items-center justify-between hover:bg-gray-50">
                            <span className="text-gray-700">{title}</span>
                            <input
                              type="checkbox"
                              checked={columnVisibility[key] === undefined ? true : columnVisibility[key]}
                              onChange={() => toggleColumn(key)}
                              className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                            />
                          </div>
                        ));
                      })()}

                      {/* 扩展字段 */}
                      <div className="px-3 py-1 mt-2 bg-gray-100 font-medium text-sm text-gray-600 sticky top-0 z-10">扩展字段</div>

                      {/* 获取当前分类的扩展字段 */}
                      {(() => {
                        // 获取当前分类的扩展字段
                        // 使用ExtFieldService中的currentExtFields，这是当前分类应该显示的扩展字段
                        // 这样可以确保即使字段被隐藏，也能在列设置菜单中显示
                        const extFieldsForCurrentCategory = currentExtFields || [];

                        // 如果当前分类没有扩展字段，显示提示信息
                        if (extFieldsForCurrentCategory.length === 0) {
                          return (
                            <div className="px-3 py-2 text-gray-500 text-center italic">
                              当前分类没有可用的扩展字段
                            </div>
                          );
                        }

                        return (
                          <>
                            {/* 当前分类的扩展字段 */}
                            {extFieldsForCurrentCategory.length > 0 && (
                              <>
                                {extFieldsForCurrentCategory
                                  .sort((a, b) => a.title.localeCompare(b.title))
                                  .map(field => {
                                    // 使用extFieldService的方法获取列键名，确保一致性
                                    const key = `ext_${field.key}`;
                                    return (
                                      <div key={key} className="px-3 py-2 flex items-center justify-between hover:bg-gray-50">
                                        <span className="text-gray-700">{field.title}</span>
                                        <input
                                          type="checkbox"
                                          checked={columnVisibility[key] === undefined ? true : columnVisibility[key]}
                                          onChange={() => toggleColumn(key)}
                                          className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                                        />
                                      </div>
                                    );
                                  })
                                }
                              </>
                            )}
                          </>
                        );
                      })()}
                    </div>
                  </div>
                )}
              </div>

              {/* 列设置按钮 */}
              <button
                onClick={() => setShowColumnSettingsDialog(true)}
                className="flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
              >
                <Settings className="h-4 w-4 mr-1" />
                列设置
              </button>

              <Import onImportComplete={() => {
                console.log('导入完成，开始全面刷新数据...');

                // 获取服务实例
                const inventoryService = InventoryService.getInstance();
                const extFieldService = ExtFieldService.getInstance();
                const departmentService = DepartmentService.getInstance();

                // 使用异步函数包装刷新逻辑
                (async () => {
                  try {
                    console.log('1. 强制刷新总表数据');
                    // 强制刷新总表数据
                    await loadInventory(true);

                    console.log('2. 强制刷新设备分类树');
                    // 强制刷新设备分类树
                    await inventoryService.generateCategoryTree(undefined, true);

                    console.log('3. 强制刷新部门分类树');
                    // 强制刷新部门分类树
                    await departmentService.loadDepartmentTree(true);

                    console.log('4. 强制刷新扩展字段');
                    // 强制刷新扩展字段
                    await extFieldService.getAllExtFields(true);

                    // 重新生成表格字段，包含扩展字段
                    console.log('5. 重新生成表格字段');
                    inventoryService.generateTableFields();

                    // 强制触发状态更新
                    console.log('6. 强制触发状态更新');
                    inventoryService.forceUpdate();

                    console.log('导入后数据刷新完成');
                  } catch (error) {
                    console.error('导入后刷新数据失败:', error);
                  }
                })();
              }} />

              <Export
                items={inventoryList} // 传递所有数据，而不仅仅是过滤后的数据
                selectedItems={selectedItems}
                deviceCategories={deviceCategories}
                departmentCategories={departmentCategories}
                tableFields={tableFields}
                isOpen={showExportDialog}
                onOpen={() => {
                  // 根据选中项数量决定导出模式
                  if (selectedItems.length > 0) {
                    setExportMode('selected');
                  } else {
                    setExportMode('all'); // 使用'all'模式，表示导出所有数据
                  }
                  setShowExportDialog(true);
                }}
                onClose={() => {
                  setShowExportDialog(false);
                  // 关闭对话框时清除导出分类ID和分类模式
                  setExportCategoryId(undefined);
                  setExportCategoryMode('device');
                }}
                exportMode={exportMode}
                totalCount={inventoryList.length}
                initialSelectedCategory={exportCategoryId}
                initialCategoryMode={exportCategoryMode}
                onExportTemplate={async () => {
                  try {
                    console.log('开始导出导入模板...');

                    // 生成文件名
                    const now = new Date();
                    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
                    const fileName = `设备台账导入模板_${timestamp}`;

                    // 导出模板并发送到服务器
                    await exportTemplateAndSendToServer(
                      tableFields,
                      fileName,
                      'excel'
                    );

                    console.log('导入模板导出完成');
                  } catch (error) {
                    console.error('导出导入模板失败:', error);
                    // 这里可以添加错误提示
                  }
                }}
              />


            </div>
          </div>

          {/* 表格内容 */}
          <div className="flex-1 overflow-auto">
            <InventoryTable
              items={filteredItems}
              columnVisibility={columnVisibility}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onSelect={toggleSelectItem}
              onSelectAll={toggleSelectAll}
              isSelected={isItemSelected}
              isAllSelected={areAllItemsSelected}
              someSelected={someItemsSelected}
              tableFields={tableFields}
              fieldDictionary={fieldDictionary}
              isLoading={isLoading}
              onRfidManagement={handleRfidManagement}
              rememberPosition={true}
              initialPageIndex={currentPageIndex}
              onPageChange={handlePageChange}
              lastOperatedItemId={lastOperatedItemId}
            />
          </div>
        </div>
      </div>

      {/* 提取公共条件判断 */}
      {(() => {
        // 只有从右键菜单添加时才禁用相关字段
        const isFromContextMenu = selectedCategoryInfo?.fromContextMenu === true;

        return (
          <>
            {/* 添加设备对话框 */}
            <AddInventoryDialog
              isOpen={showAddDialog}
              onClose={() => setShowAddDialog(false)}
              onAdd={executeAdd}
              initialCategoryInfo={selectedCategoryInfo}
              extFields={currentExtFields}
              extFieldValues={extFieldValues}
              onExtFieldChange={handleExtFieldChange}
              disableTypeSelection={isFromContextMenu} // 只有从右键菜单添加时才禁用设备类型选择
              disableDepartmentSelection={isFromContextMenu} // 只有从右键菜单添加时才禁用部门选择
              disableResponsibleSelection={isFromContextMenu} // 只有从右键菜单添加时才禁用责任人选择
            />

            {/* 编辑设备对话框 */}
            <EditInventoryDialog
              isOpen={showEditDialog}
              onClose={() => setShowEditDialog(false)}
              onUpdate={executeUpdate}
              item={editItem}
              extFields={currentExtFields}
              extFieldValues={extFieldValues}
              onExtFieldChange={handleExtFieldChange}
            />

            {/* 删除确认对话框 */}
            <DeleteConfirmDialog
              isOpen={showDeleteDialog}
              onClose={() => handleBatchDelete()}
              onConfirm={executeDelete}
              onCancel={() => {
                // 取消删除时清空选中项
                if (selectedItems.length > 0) {
                  // 使用toggleSelectItem方法清空选中项
                  selectedItems.forEach(id => toggleSelectItem(id));
                }
              }}
              itemsCount={singleDeleteId ? 1 : selectedItems.length}
              isLoading={isLoading}
              isSingleDelete={singleDeleteId !== null}
            />

            {/* 部门必需提示对话框 */}
            <DepartmentRequiredDialog
              isOpen={showDepartmentRequiredDialog}
              onClose={() => setShowDepartmentRequiredDialog(false)}
              onConfirm={handleDepartmentRequiredConfirm}
            />

            {/* 添加分类对话框 */}
            <AddCategoryDialog
              isOpen={showAddCategoryDialog}
              onClose={() => handleAddType()}
              onAdd={executeAddCategory}
              deviceCategories={deviceCategories}
              mode={categoryDialogMode}
              selectedCategory={categoryMode === 'device' ? currentCategory : undefined}
              disableParentSelection={isFromContextMenu} // 只有从右键菜单添加时才禁用父级分类选择
            />

            {/* 添加部门对话框 */}
            <AddDepartmentDialog
              isOpen={showAddDepartmentDialog}
              onClose={() => handleAddDepartment()}
              onAdd={executeAddDepartmentOrPerson}
              departmentCategories={departmentCategories}
              mode={departmentDialogMode}
              selectedCategory={categoryMode === 'department' ? currentDepartmentCategory : undefined}
              disableParentSelection={isFromContextMenu} // 只有从右键菜单添加时才禁用父级部门选择
            />
          </>
        );
      })()}

      {/* 移动确认对话框 */}
      <MoveConfirmDialog
        isOpen={showMoveConfirmDialog}
        onClose={cancelMove}
        onConfirm={confirmMove}
        draggedNode={moveConfirmDraggedNode}
        targetNode={moveConfirmTargetNode}
        isLoading={isLoading}
      />

      {/* 更改部门对话框 */}
      <ChangeDepartmentDialog
        isOpen={showChangeDepartmentDialog}
        onClose={() => setShowChangeDepartmentDialog(false)}
        onConfirm={executeChangeDepartment}
        departmentCategories={departmentCategories}
        personName={changeDepartmentPersonName}
        currentDepartmentName={changeDepartmentCurrentDeptName}
        isLoading={isLoading}
      />

      {/* 编辑人员信息对话框 */}
      <EditPersonDialog
        isOpen={showEditPersonDialog}
        onClose={() => setShowEditPersonDialog(false)}
        personId={editPersonId}
        onUpdate={handlePersonUpdated}
      />

      {/* 列设置对话框 */}
      <ColumnSettingsDialog
        isOpen={showColumnSettingsDialog}
        onClose={() => {
          // 关闭对话框
          setShowColumnSettingsDialog(false);
          // 注意：成功和错误提示的清除已经在ColumnSettingsDialog组件内部处理
        }}
        onSuccess={() => {
          // 使用前端缓存，不需要强制刷新数据
          console.log('扩展字段列设置成功，使用前端缓存更新');

          // 获取ExtFieldService实例
          const extFieldService = ExtFieldService.getInstance();

          // 获取InventoryService实例
          const inventoryService = InventoryService.getInstance();

          // 获取当前选中的分类信息
          const currentSelectedCategory = inventoryService.getCurrentSelectedCategory();

          // 获取当前树状图选中的节点信息
          const treeSelectedNode = inventoryService.getState().treeSelectedNode;

          // 更新总表和一级分类的扩展字段缓存
          if (typeof extFieldService.updateAllTableExtFields === 'function') {
            console.log('更新总表和一级分类的扩展字段缓存');
            extFieldService.updateAllTableExtFields();
          }

          // 根据当前树状图选中的节点来决定如何刷新表格
          if (treeSelectedNode) {
            console.log('根据当前树状图选中的节点刷新表格:', treeSelectedNode);

            // 如果当前选中的是设备分类树的节点
            if (treeSelectedNode.type === 'device') {
              // 如果是二级分类节点
              if (treeSelectedNode.level === 2) {
                console.log('当前选中的是二级分类节点，使用二级分类筛选');
                inventoryService.generateTableFields({
                  parentCategory: treeSelectedNode.parentName,
                  subCategory: treeSelectedNode.name
                });
              }
              // 如果是一级分类节点
              else if (treeSelectedNode.level === 1) {
                console.log('当前选中的是一级分类节点，使用一级分类筛选');
                inventoryService.generateTableFields({
                  parentCategory: treeSelectedNode.name
                });
              }
              // 如果是根节点
              else {
                console.log('当前选中的是公司名称，显示总表');
                inventoryService.generateTableFields();
              }
            }
            // 如果当前选中的是部门树的节点，保持当前视图
            else if (treeSelectedNode.type === 'department') {
              console.log('当前选中的是部门树节点，保持当前视图');
              if (currentSelectedCategory) {
                inventoryService.generateTableFields(currentSelectedCategory);
              } else {
                inventoryService.generateTableFields();
              }
            }
          }
          // 如果没有选中的节点，回退到基于当前选中分类的逻辑
          else {
            if (currentSelectedCategory) {
              console.log('根据当前选中的分类重新生成表格字段:', currentSelectedCategory);
              inventoryService.generateTableFields(currentSelectedCategory);
            } else {
              console.log('重新生成总表的表格字段');
              inventoryService.generateTableFields();
            }
          }

          // 强制触发状态更新，确保UI更新
          inventoryService.forceUpdate();
        }}
        // 传递当前选中的分类信息
        currentSelectedCategory={
          // 只在设备分类模式下传递分类信息
          categoryMode === 'device'
            ? InventoryService.getInstance().getCurrentSelectedCategory()
            : undefined
        }
      />

      {/* RFID管理对话框 */}
      <RfidManagementDialog
        isOpen={showRfidManagementDialog}
        onClose={() => setShowRfidManagementDialog(false)}
        selectedItems={getSelectedItemDetails()}
        allItems={inventoryList}
        onSelectItems={(items) => {
          // 更新选中的项目
          const newItemIds = items.map(item => item.id);

          // 找出需要取消选中的项目（在当前选中但不在新列表中）
          const itemsToDeselect = selectedItems.filter(id => !newItemIds.includes(id));

          // 找出需要新选中的项目（在新列表中但不在当前选中）
          const itemsToSelect = newItemIds.filter(id => !selectedItems.includes(id));

          // 取消选中不需要的项目
          itemsToDeselect.forEach(id => toggleSelectItem(id));

          // 选中新的项目
          itemsToSelect.forEach(id => toggleSelectItem(id));
        }}
        onShowSelectionTip={handleShowSelectionTip}
      />

      {/* 选择设备提示对话框 */}
      {showSelectionTipDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-slate-900">继续选择设备</h3>
              </div>
            </div>
            <div className="mb-6">
              <p className="text-sm text-slate-600">
                请在下方的设备总表中勾选您需要进行RFID管理的设备，选择完成后可以重新打开RFID管理功能。
              </p>
            </div>
            <div className="flex justify-end">
              <button
                onClick={() => setShowSelectionTipDialog(false)}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                我知道了
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryManagement;