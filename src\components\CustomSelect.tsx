import React, { useState, useRef, useEffect } from 'react';
import './customSelect.css';

interface Option {
  id: string | number;
  value: string;
  label: string;
}

interface CustomSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  direction?: 'auto' | 'top' | 'bottom'; // 添加方向属性，控制下拉框展开方向
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择...',
  disabled = false,
  required = false,
  className = '',
  direction = 'auto' // 默认为自动计算方向
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState('');
  const [selectedLabel, setSelectedLabel] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 当选项或值变化时，更新选中的标签
  useEffect(() => {
    const selectedOption = options.find(option => option.value === value);
    setSelectedLabel(selectedOption ? selectedOption.label : '');
  }, [options, value]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFilter('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 过滤选项
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(filter.toLowerCase())
  );

  // 处理选项选择
  const handleSelect = (optionValue: string, optionLabel: string) => {
    onChange(optionValue);
    setSelectedLabel(optionLabel);
    setFilter('');
    setIsOpen(false);
  };

  // 处理输入框点击
  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen && inputRef.current) {
        // 聚焦但不显示蓝色边框
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 0);
      }
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(e.target.value);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  // 清除选择
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
    setSelectedLabel('');
    setFilter('');
    // 不需要聚焦，避免出现蓝色边框
  };

  // 计算下拉菜单位置和方向
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');

  // 当下拉框打开时，计算是否应该向上展开
  useEffect(() => {
    if (isOpen && selectRef.current) {
      // 如果指定了固定方向，则使用指定的方向
      if (direction === 'top') {
        setDropdownPosition('top');
        return;
      } else if (direction === 'bottom') {
        setDropdownPosition('bottom');
        return;
      }

      // 自动计算方向
      const rect = selectRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = Math.min(filteredOptions.length * 36 + 16, 240); // 估计下拉菜单高度

      // 如果下方空间不足且上方空间足够，则向上展开
      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownPosition('top');
      } else {
        setDropdownPosition('bottom');
      }
    }
  }, [isOpen, filteredOptions.length, direction]);

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      <div
        className={`flex items-center w-full h-[38px] px-3 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer'
        }`}
        onClick={disabled ? undefined : handleInputClick}
      >
        <input
          ref={inputRef}
          type="text"
          value={isOpen ? filter : selectedLabel}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={`w-full bg-transparent border-none select-input-no-focus ${
            disabled ? 'cursor-not-allowed' : 'cursor-pointer'
          }`}
          style={{ caretColor: isOpen ? 'auto' : 'transparent' }}
          readOnly={!isOpen}
          disabled={disabled}
          required={required}
        />
        {(value || filter) && !disabled && (
          <button
            type="button"
            onClick={handleClear}
            className="text-gray-400 hover:text-gray-600 mr-1"
            style={{ outline: 'none' }}
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
        <div className="text-gray-400">
          <svg
            className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {isOpen && (
        <div
          className={`fixed z-[9999] w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto ${
            dropdownPosition === 'top' ? 'dropdown-up' : 'dropdown-down'
          }`}
          style={{
            outline: 'none',
            width: selectRef.current?.offsetWidth,
            left: selectRef.current?.getBoundingClientRect().left,
            ...(dropdownPosition === 'top'
              ? { bottom: window.innerHeight - selectRef.current?.getBoundingClientRect().top }
              : { top: selectRef.current?.getBoundingClientRect().bottom })
          }}
        >
          {filteredOptions.length === 0 ? (
            <div className="px-4 py-2 text-sm text-gray-500">无匹配选项</div>
          ) : (
            filteredOptions.map(option => (
              <div
                key={option.id}
                className="px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                onClick={() => handleSelect(option.value, option.label)}
                style={{ outline: 'none' }}
              >
                {option.label}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default CustomSelect;
