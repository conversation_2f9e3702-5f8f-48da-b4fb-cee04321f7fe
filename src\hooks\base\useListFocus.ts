import { useCallback, useRef, useEffect } from 'react';
import { getFocusManager } from './useFocusManager';

interface ListFocusOptions {
  // 列表项的选择器
  itemSelector?: string;
  // 是否在新增项目后自动聚焦
  autoFocusOnAdd?: boolean;
  // 是否滚动到新项目
  scrollToNewItem?: boolean;
  // 滚动行为
  scrollBehavior?: ScrollBehavior;
  // 滚动位置
  scrollBlock?: ScrollLogicalPosition;
}

interface ListItem {
  id: string | number;
  element?: HTMLElement;
  [key: string]: any;
}

/**
 * 列表焦点管理Hook
 * 处理列表中新增、删除、更新项目时的焦点管理
 */
export function useListFocus<T extends ListItem>({
  itemSelector = '[data-item-id]',
  autoFocusOnAdd = true,
  scrollToNewItem = true,
  scrollBehavior = 'smooth',
  scrollBlock = 'center'
}: ListFocusOptions = {}) {
  const listRef = useRef<HTMLElement>(null);
  const previousItemsRef = useRef<T[]>([]);
  
  // 聚焦到指定项目
  const focusItem = useCallback((itemId: string | number, scroll = true) => {
    if (!listRef.current) return;
    
    try {
      const focusManager = getFocusManager();
      
      // 尝试多种选择器模式
      const selectors = [
        `${itemSelector}[data-item-id="${itemId}"]`,
        `[data-id="${itemId}"]`,
        `#item-${itemId}`,
        `[id*="${itemId}"]`
      ];
      
      let targetElement: HTMLElement | null = null;
      
      for (const selector of selectors) {
        targetElement = listRef.current.querySelector(selector);
        if (targetElement) break;
      }
      
      if (targetElement) {
        // 确保元素可以接收焦点
        if (!targetElement.hasAttribute('tabindex')) {
          targetElement.setAttribute('tabindex', '-1');
        }
        
        focusManager.setFocus(targetElement);
        
        if (scroll) {
          targetElement.scrollIntoView({
            behavior: scrollBehavior,
            block: scrollBlock
          });
        }
        
        return true;
      }
    } catch (error) {
      console.warn('列表项焦点设置失败:', error);
    }
    
    return false;
  }, [itemSelector, scrollBehavior, scrollBlock]);
  
  // 聚焦到第一个项目
  const focusFirstItem = useCallback(() => {
    if (!listRef.current) return;
    
    try {
      const focusManager = getFocusManager();
      const firstItem = listRef.current.querySelector(itemSelector) as HTMLElement;
      
      if (firstItem) {
        if (!firstItem.hasAttribute('tabindex')) {
          firstItem.setAttribute('tabindex', '-1');
        }
        
        focusManager.setFocus(firstItem);
        return true;
      }
    } catch (error) {
      console.warn('列表首项焦点设置失败:', error);
    }
    
    return false;
  }, [itemSelector]);
  
  // 聚焦到最后一个项目
  const focusLastItem = useCallback(() => {
    if (!listRef.current) return;
    
    try {
      const focusManager = getFocusManager();
      const items = listRef.current.querySelectorAll(itemSelector);
      const lastItem = items[items.length - 1] as HTMLElement;
      
      if (lastItem) {
        if (!lastItem.hasAttribute('tabindex')) {
          lastItem.setAttribute('tabindex', '-1');
        }
        
        focusManager.setFocus(lastItem);
        return true;
      }
    } catch (error) {
      console.warn('列表末项焦点设置失败:', error);
    }
    
    return false;
  }, [itemSelector]);
  
  // 聚焦到下一个项目
  const focusNextItem = useCallback(() => {
    if (!listRef.current) return;
    
    try {
      const focusManager = getFocusManager();
      const items = Array.from(listRef.current.querySelectorAll(itemSelector)) as HTMLElement[];
      const currentElement = document.activeElement as HTMLElement;
      const currentIndex = items.indexOf(currentElement);
      
      if (currentIndex >= 0 && currentIndex < items.length - 1) {
        const nextItem = items[currentIndex + 1];
        if (!nextItem.hasAttribute('tabindex')) {
          nextItem.setAttribute('tabindex', '-1');
        }
        focusManager.setFocus(nextItem);
        return true;
      }
    } catch (error) {
      console.warn('下一项焦点设置失败:', error);
    }
    
    return false;
  }, [itemSelector]);
  
  // 聚焦到上一个项目
  const focusPreviousItem = useCallback(() => {
    if (!listRef.current) return;
    
    try {
      const focusManager = getFocusManager();
      const items = Array.from(listRef.current.querySelectorAll(itemSelector)) as HTMLElement[];
      const currentElement = document.activeElement as HTMLElement;
      const currentIndex = items.indexOf(currentElement);
      
      if (currentIndex > 0) {
        const previousItem = items[currentIndex - 1];
        if (!previousItem.hasAttribute('tabindex')) {
          previousItem.setAttribute('tabindex', '-1');
        }
        focusManager.setFocus(previousItem);
        return true;
      }
    } catch (error) {
      console.warn('上一项焦点设置失败:', error);
    }
    
    return false;
  }, [itemSelector]);
  
  // 检测新增项目并自动聚焦
  const handleItemsChange = useCallback((newItems: T[]) => {
    if (!autoFocusOnAdd || !listRef.current) return;
    
    const previousItems = previousItemsRef.current;
    const previousIds = new Set(previousItems.map(item => item.id));
    const newlyAddedItems = newItems.filter(item => !previousIds.has(item.id));
    
    if (newlyAddedItems.length > 0) {
      // 聚焦到最新添加的项目
      const latestItem = newlyAddedItems[newlyAddedItems.length - 1];
      
      // 使用setTimeout确保DOM更新完成
      setTimeout(() => {
        focusItem(latestItem.id, scrollToNewItem);
      }, 100);
    }
    
    previousItemsRef.current = newItems;
  }, [autoFocusOnAdd, focusItem, scrollToNewItem]);
  
  // 处理键盘导航
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        focusNextItem();
        break;
      case 'ArrowUp':
        event.preventDefault();
        focusPreviousItem();
        break;
      case 'Home':
        event.preventDefault();
        focusFirstItem();
        break;
      case 'End':
        event.preventDefault();
        focusLastItem();
        break;
    }
  }, [focusNextItem, focusPreviousItem, focusFirstItem, focusLastItem]);
  
  return {
    listRef,
    focusItem,
    focusFirstItem,
    focusLastItem,
    focusNextItem,
    focusPreviousItem,
    handleItemsChange,
    handleKeyDown
  };
}

// JSX组件已移动到单独的文件中
// 这是一个纯TypeScript Hook文件，不包含JSX代码
