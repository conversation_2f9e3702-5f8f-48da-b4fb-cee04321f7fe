import React, { useEffect } from 'react';
import { Alert, Spin } from 'antd';
import useAddDepartmentDialog, { DepartmentCategory } from '../../../../hooks/Inventory/useAddDepartmentDialog';
import DepartmentTreeSelect from '../../../../components/DepartmentTreeSelect';
import SecurityLevelSelect from '../../../../components/ui/SecurityLevelSelect';
import { useDialogEscapeKey } from '../../../../hooks/base/useEscapeKey';
import { useValidatedFields } from '../../../../hooks/base/useValidatedField';
import { ValidatedInput } from '../../../../components/ui/FormComponents';

// 添加部门对话框属性
interface AddDepartmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (parentId: string, name: string, itemType: 'department' | 'person', alias?: string, mobileNumber?: string, positionSecurityLevel?: number) => Promise<void>;
  departmentCategories: DepartmentCategory[];
  mode: 'department' | 'person'; // 'department'表示添加部门，'person'表示添加人员
  selectedCategory?: string; // 当前选中的部门ID
  disableParentSelection?: boolean; // 是否禁用父级部门选择，用于从树状图联动添加时
}

/**
 * 添加部门/人员对话框组件
 * 专注于UI渲染和用户交互，业务逻辑由useAddDepartmentDialog处理
 */
const AddDepartmentDialog: React.FC<AddDepartmentDialogProps> = (props) => {
  const {
    isOpen,
    onClose,
    onAdd,
    departmentCategories,
    mode,
    selectedCategory,
    disableParentSelection = false
  } = props;

  // 使用自定义Hook获取状态和方法
  const {
    // 表单状态
    name,
    setName,
    parentId,
    setParentId,
    alias,
    setAlias,
    mobileNumber,
    setMobileNumber,
    positionSecurityLevel,
    setPositionSecurityLevel,

    // UI状态
    isLoading,
    error,

    // 业务处理方法
    handleSubmit,
    clearError,

    // 数据处理
    parentDepartments
  } = useAddDepartmentDialog({
    isOpen,
    onClose,
    onAdd,
    departmentCategories,
    mode,
    selectedCategory,
    disableParentSelection
  });

  // 使用多字段验证Hook
  const validationConfig = {
    name: mode === 'department' ? 'departmentName' : 'personName',
    alias: 'personAlias',
    mobileNumber: 'mobileNumber'
  };

  const { getFieldProps, clearErrors } = useValidatedFields(
    { name, alias, mobileNumber },
    { name: setName, alias: setAlias, mobileNumber: setMobileNumber },
    validationConfig
  );

  // 使用统一的ESC键处理
  useDialogEscapeKey(isOpen, onClose, {
    debugId: 'AddDepartmentDialog',
    priority: 100
  });

  // 监听对话框关闭，清除验证错误状态
  useEffect(() => {
    if (!isOpen) {
      // 对话框关闭时，清除验证错误状态
      clearErrors();
    }
  }, [isOpen, clearErrors]);

  // 将部门列表转换为DepartmentTreeSelect需要的格式
  const convertToDepartmentOptions = (departments: DepartmentCategory[]): any[] => {
    return departments.map(department => {
      const option = {
        id: department.id,
        value: department.id,
        label: department.name,
      };

      if (department.children && department.children.length > 0) {
        // 只包含部门节点，过滤掉人员节点
        const departmentChildren = department.children.filter(child => child.id.startsWith('dept-'));
        if (departmentChildren.length > 0) {
          option.children = convertToDepartmentOptions(departmentChildren);
        }
      }

      return option;
    });
  };

  // 获取部门选项 - 从原始部门数据构建树形结构，而不是使用扁平化的列表
  const departmentOptions = (() => {
    // 检查是否有根节点
    if (departmentCategories.length > 0 && departmentCategories[0].id === 'all-dept') {
      // 如果是添加部门模式，包含根节点
      if (mode === 'department') {
        return convertToDepartmentOptions(departmentCategories);
      } else {
        // 如果是添加人员模式，只返回部门节点，过滤掉根节点
        // 找到根节点的子部门（一级部门）
        const rootNode = departmentCategories[0];
        if (rootNode.children && rootNode.children.length > 0) {
          // 只返回部门节点，过滤掉人员节点
          const departmentChildren = rootNode.children.filter(child => child.id.startsWith('dept-'));
          return convertToDepartmentOptions(departmentChildren);
        }
        return [];
      }
    }
    // 如果没有根节点，返回原始转换结果
    return convertToDepartmentOptions(departmentCategories);
  })();

  // 获取默认展开的节点ID
  const defaultExpandedNodes = (() => {
    // 如果是添加部门模式，默认展开根节点
    if (mode === 'department' && departmentCategories.length > 0 && departmentCategories[0].id === 'all-dept') {
      return [departmentCategories[0].id];
    }
    return [];
  })();

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-auto">
        <div className="p-6">
          {/* 对话框标题 */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800">
              {mode === 'department' ? '添加部门' : '添加人员'}
            </h2>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert
              message="错误"
              description={error}
              type="error"
              showIcon
              className="mb-4"
              closable
              onClose={clearError}
            />
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="mb-4 flex items-center space-x-2">
              <Spin />
              <span className="text-blue-600">正在处理请求...</span>
            </div>
          )}

          {/* 表单 */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 父级部门选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                父级部门 <span className="text-red-500">*</span>
                {mode === 'person' && <span className="text-sm text-gray-500 ml-1">(必须选择具体部门)</span>}
              </label>
              <DepartmentTreeSelect
                options={departmentOptions}
                value={parentId}
                onChange={setParentId}
                placeholder="请选择父级部门"
                disabled={isLoading || disableParentSelection}
                className="w-full"
                defaultExpandedNodes={defaultExpandedNodes}
              />
            </div>

            {/* 名称输入 */}
            <ValidatedInput
              label={mode === 'department' ? '部门名称' : '人员名称'}
              {...getFieldProps('name')}
              placeholder={mode === 'department' ? '请输入部门名称' : '请输入人员名称'}
              disabled={isLoading}
              preset="name"
              autoFocus={true}
            />

            {/* 人员备注输入（仅在添加人员时显示） */}
            {mode === 'person' && (
              <ValidatedInput
                label="人员备注"
                {...getFieldProps('alias')}
                placeholder="人员备注会以括号形式在人员姓名后面"
                disabled={isLoading}
                preset="alias"
              />
            )}

            {/* 岗位密级选择（仅在添加人员时显示） */}
            {mode === 'person' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  岗位密级 <span className="text-red-500">*</span>
                </label>
                <SecurityLevelSelect
                  value={positionSecurityLevel}
                  onChange={setPositionSecurityLevel}
                  disabled={isLoading}
                  className="w-full"
                  required={true}
                />
              </div>
            )}

            {/* 联系方式输入（仅在添加人员时显示） - 移动到最下面 */}
            {mode === 'person' && (
              <ValidatedInput
                label="联系方式"
                {...getFieldProps('mobileNumber')}
                placeholder="请输入联系方式"
                disabled={isLoading}
                preset="mobile"
              />
            )}

            {/* 按钮 */}
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 rounded-md text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                取消
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-blue-600 rounded-md text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                保存
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddDepartmentDialog;