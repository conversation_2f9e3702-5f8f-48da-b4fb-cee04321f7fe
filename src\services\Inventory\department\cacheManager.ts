import { DEFAULT_CACHE_DURATION, CACHE_TYPES, LOG_PREFIXES, formatLogMessage } from './constants';

/**
 * 统一缓存管理器
 * 整合所有部门相关的缓存，提供统一的缓存生命周期管理
 */
export class CacheManager {
  private static instance: CacheManager;
  
  // 各种缓存存储
  private pathCache = new Map<string, string>(); // 节点路径缓存
  private departmentPathsCache = new Map<string, string[]>(); // 部门及其子部门路径缓存
  private sameNameDepartmentsCache = new Map<string, string[]>(); // 同名部门缓存
  private departmentPersonsCache = new Map<string, string[]>(); // 部门下人员缓存
  private personCache = new Map<number, any>(); // 人员详细信息缓存
  
  // 缓存时间戳
  private cacheTimestamps = new Map<string, number>();
  
  private constructor() {}
  
  /**
   * 获取单例实例
   */
  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }
  
  // ==================== 路径缓存管理 ====================
  
  /**
   * 获取路径缓存
   */
  public getPathCache(key: string): string | undefined {
    return this.pathCache.get(key);
  }
  
  /**
   * 设置路径缓存
   */
  public setPathCache(key: string, value: string): void {
    this.pathCache.set(key, value);
    this.updateTimestamp('pathCache');
  }
  
  /**
   * 检查路径缓存是否存在
   */
  public hasPathCache(key: string): boolean {
    return this.pathCache.has(key);
  }
  
  // ==================== 部门路径缓存管理 ====================
  
  /**
   * 获取部门路径缓存
   */
  public getDepartmentPathsCache(key: string): string[] | undefined {
    return this.departmentPathsCache.get(key);
  }
  
  /**
   * 设置部门路径缓存
   */
  public setDepartmentPathsCache(key: string, value: string[]): void {
    this.departmentPathsCache.set(key, value);
    this.updateTimestamp('departmentPathsCache');
  }
  
  /**
   * 检查部门路径缓存是否存在
   */
  public hasDepartmentPathsCache(key: string): boolean {
    return this.departmentPathsCache.has(key);
  }
  
  // ==================== 同名部门缓存管理 ====================
  
  /**
   * 获取同名部门缓存
   */
  public getSameNameDepartmentsCache(key: string): string[] | undefined {
    return this.sameNameDepartmentsCache.get(key);
  }
  
  /**
   * 设置同名部门缓存
   */
  public setSameNameDepartmentsCache(key: string, value: string[]): void {
    this.sameNameDepartmentsCache.set(key, value);
    this.updateTimestamp('sameNameDepartmentsCache');
  }
  
  /**
   * 检查同名部门缓存是否存在
   */
  public hasSameNameDepartmentsCache(key: string): boolean {
    return this.sameNameDepartmentsCache.has(key);
  }
  
  // ==================== 部门人员缓存管理 ====================
  
  /**
   * 获取部门人员缓存
   */
  public getDepartmentPersonsCache(key: string): string[] | undefined {
    return this.departmentPersonsCache.get(key);
  }
  
  /**
   * 设置部门人员缓存
   */
  public setDepartmentPersonsCache(key: string, value: string[]): void {
    this.departmentPersonsCache.set(key, value);
    this.updateTimestamp('departmentPersonsCache');
  }
  
  /**
   * 检查部门人员缓存是否存在
   */
  public hasDepartmentPersonsCache(key: string): boolean {
    return this.departmentPersonsCache.has(key);
  }
  
  // ==================== 人员信息缓存管理 ====================
  
  /**
   * 获取人员信息缓存
   */
  public getPersonCache(personId: number): any | undefined {
    return this.personCache.get(personId);
  }
  
  /**
   * 设置人员信息缓存
   */
  public setPersonCache(personId: number, personInfo: any): void {
    this.personCache.set(personId, personInfo);
    this.updateTimestamp('personCache');
  }
  
  /**
   * 检查人员信息缓存是否存在
   */
  public hasPersonCache(personId: number): boolean {
    return this.personCache.has(personId);
  }
  
  /**
   * 获取所有人员缓存
   */
  public getAllPersonCache(): Map<number, any> {
    return this.personCache;
  }
  
  /**
   * 批量设置人员缓存
   */
  public setPersonCacheBatch(persons: any[]): void {
    this.personCache.clear();
    persons.forEach(person => {
      this.personCache.set(person.id, person);
    });
    this.updateTimestamp('personCache');
  }
  
  // ==================== 缓存生命周期管理 ====================
  
  /**
   * 检查缓存是否有效
   */
  public isCacheValid(cacheType: string, duration: number = DEFAULT_CACHE_DURATION): boolean {
    const timestamp = this.cacheTimestamps.get(cacheType);
    if (!timestamp) return false;

    const now = Date.now();
    return (now - timestamp) < duration;
  }
  
  /**
   * 更新缓存时间戳
   */
  private updateTimestamp(cacheType: string): void {
    this.cacheTimestamps.set(cacheType, Date.now());
  }
  
  /**
   * 清除指定类型的缓存
   */
  public clearCache(cacheType: string): void {
    switch (cacheType) {
      case CACHE_TYPES.PATH_CACHE:
        this.pathCache.clear();
        break;
      case CACHE_TYPES.DEPARTMENT_PATHS_CACHE:
        this.departmentPathsCache.clear();
        break;
      case CACHE_TYPES.SAME_NAME_DEPARTMENTS_CACHE:
        this.sameNameDepartmentsCache.clear();
        break;
      case CACHE_TYPES.DEPARTMENT_PERSONS_CACHE:
        this.departmentPersonsCache.clear();
        break;
      case CACHE_TYPES.PERSON_CACHE:
        this.personCache.clear();
        break;
    }
    this.cacheTimestamps.delete(cacheType);
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `已清除 ${cacheType} 缓存`));
  }
  
  /**
   * 清除所有缓存
   */
  public clearAllCaches(): void {
    console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, '清除所有部门相关缓存'));
    this.pathCache.clear();
    this.departmentPathsCache.clear();
    this.sameNameDepartmentsCache.clear();
    this.departmentPersonsCache.clear();
    this.personCache.clear();
    this.cacheTimestamps.clear();
  }
  
  /**
   * 清除过期缓存
   */
  public clearExpiredCaches(duration: number = DEFAULT_CACHE_DURATION): void {
    const now = Date.now();
    const expiredCaches: string[] = [];

    this.cacheTimestamps.forEach((timestamp, cacheType) => {
      if ((now - timestamp) >= duration) {
        expiredCaches.push(cacheType);
      }
    });

    expiredCaches.forEach(cacheType => {
      this.clearCache(cacheType);
    });

    if (expiredCaches.length > 0) {
      console.log(formatLogMessage(LOG_PREFIXES.CACHE_MANAGER, `已清除过期缓存: ${expiredCaches.join(', ')}`));
    }
  }
  
  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): {
    pathCache: number;
    departmentPathsCache: number;
    sameNameDepartmentsCache: number;
    departmentPersonsCache: number;
    personCache: number;
    totalSize: number;
  } {
    return {
      pathCache: this.pathCache.size,
      departmentPathsCache: this.departmentPathsCache.size,
      sameNameDepartmentsCache: this.sameNameDepartmentsCache.size,
      departmentPersonsCache: this.departmentPersonsCache.size,
      personCache: this.personCache.size,
      totalSize: this.pathCache.size + this.departmentPathsCache.size + 
                this.sameNameDepartmentsCache.size + this.departmentPersonsCache.size + 
                this.personCache.size
    };
  }
}
