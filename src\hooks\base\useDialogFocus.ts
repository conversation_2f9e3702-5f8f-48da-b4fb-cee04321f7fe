import { useRef, useEffect, useCallback } from 'react';
import { useEscapeKey } from './useEscapeKey';
import { useManagersWithDebug } from './useManagers';

interface DialogFocusOptions {
  // 对话框是否打开
  isOpen: boolean;
  // 关闭对话框的回调
  onClose: () => void;
  // 是否自动聚焦到第一个元素
  autoFocus?: boolean;
  // 关闭时是否恢复焦点
  restoreFocus?: boolean;
  // ESC键处理器的优先级
  escPriority?: number;
  // Tab键处理器的优先级
  tabPriority?: number;
}

/**
 * 对话框焦点管理Hook
 * 整合键盘事件管理和焦点管理，为对话框提供统一的焦点行为
 */
export function useDialogFocus({
  isOpen,
  onClose,
  autoFocus = true,
  restoreFocus = true,
  escPriority = 100, // 默认较高优先级
  tabPriority = 100
}: DialogFocusOptions) {
  // 对话框容器引用 - 这个引用将被传递给UI组件
  const dialogRef = useRef<HTMLDivElement>(null);

  // 使用统一的管理器获取
  const { getManagers } = useManagersWithDebug('DialogFocus');

  // 使用统一的ESC键处理
  useEscapeKey(onClose, {
    enabled: isOpen,
    priority: escPriority,
    debugId: 'DialogFocus'
  });

  // 处理Tab键导航
  const handleTab = useCallback((event: KeyboardEvent) => {
    if (isOpen && dialogRef.current) {
      const managers = getManagers();
      if (managers) {
        managers.focusManager.handleTabNavigation(event, dialogRef.current);
      }
    }
  }, [isOpen, getManagers]);

  // 注册Tab键处理器（ESC键已通过useEscapeKey处理）
  useEffect(() => {
    if (isOpen) {
      const managers = getManagers();
      if (managers) {
        // 注册Tab键处理器
        const tabCleanup = managers.keyboardManager.registerTabHandler(handleTab, tabPriority);

        return () => {
          tabCleanup();
        };
      }
    }
  }, [isOpen, getManagers, handleTab, tabPriority]);

  // 管理焦点
  useEffect(() => {
    if (isOpen) {
      const managers = getManagers();
      if (managers) {
        // 保存当前焦点
        if (restoreFocus) {
          managers.focusManager.saveFocus();
        }

        // 自动聚焦到对话框的第一个可聚焦元素
        if (autoFocus && dialogRef.current) {
          setTimeout(() => {
            managers.focusManager.focusFirst(dialogRef.current);
          }, 0);
        }

        return () => {
          // 恢复之前的焦点
          if (restoreFocus) {
            managers.focusManager.restoreFocus();
          }
        };
      }
    }
  }, [isOpen, autoFocus, restoreFocus, getManagers]);

  return {
    dialogRef
  };
}
