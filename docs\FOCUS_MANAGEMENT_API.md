# 焦点管理 API 文档

## 📚 API 概览

本文档详细介绍了焦点管理系统的所有API接口、Hook函数和组件使用方法。

## 🎯 核心 Hooks

### useFocusManager()

核心焦点管理Hook，提供基础的焦点操作功能。

```typescript
interface FocusManager {
  saveFocus: () => void;
  restoreFocus: () => void;
  setFocus: (element: HTMLElement | null) => void;
  focusFirst: (container: HTMLElement | null) => void;
  focusLast: (container: HTMLElement | null) => void;
  getFocusableElements: (container: HTMLElement | null) => HTMLElement[];
  handleTabNavigation: (event: KeyboardEvent, container: HTMLElement | null) => void;
}

const focusManager = useFocusManager();
```

**方法说明：**
- `saveFocus()`: 保存当前焦点元素
- `restoreFocus()`: 恢复之前保存的焦点
- `setFocus(element)`: 设置焦点到指定元素
- `focusFirst(container)`: 聚焦容器内第一个可聚焦元素
- `focusLast(container)`: 聚焦容器内最后一个可聚焦元素
- `getFocusableElements(container)`: 获取容器内所有可聚焦元素
- `handleTabNavigation(event, container)`: 处理Tab键导航

### usePageFocus(options)

页面级焦点管理Hook，处理路由切换时的焦点设置。

```typescript
interface PageFocusOptions {
  title?: string;                    // 页面标题
  autoFocus?: boolean;              // 是否自动聚焦
  focusTarget?: string;             // 自定义焦点目标选择器
  skipFocus?: boolean;              // 是否跳过焦点设置
}

const { pageRef, titleRef, setPageFocus } = usePageFocus({
  title: '台账管理',
  autoFocus: true,
  focusTarget: '#main-content'
});
```

**返回值：**
- `pageRef`: 页面容器引用
- `titleRef`: 页面标题引用
- `setPageFocus()`: 手动设置页面焦点

### useFormFocus(options)

表单焦点管理Hook，处理表单验证和错误焦点。

```typescript
interface FormFocusOptions {
  autoFocusOnError?: boolean;       // 验证失败时自动聚焦
  errorSelector?: string;           // 错误字段选择器
  scrollToError?: boolean;          // 是否滚动到错误字段
}

interface FormFieldError {
  field: string;
  message: string;
  element?: HTMLElement;
}

const {
  formRef,
  focusFirstError,
  focusField,
  focusFirstField,
  validateAndFocus
} = useFormFocus({
  autoFocusOnError: true,
  scrollToError: true
});
```

**返回值：**
- `formRef`: 表单容器引用
- `focusFirstError(errors)`: 聚焦到第一个错误字段
- `focusField(fieldName)`: 聚焦到指定字段
- `focusFirstField()`: 聚焦到第一个字段
- `validateAndFocus(validationFn, onValid, onInvalid)`: 验证并处理焦点

### useListFocus(options)

列表焦点管理Hook，处理列表项的焦点和键盘导航。

```typescript
interface ListFocusOptions {
  itemSelector?: string;            // 列表项选择器
  autoFocusOnAdd?: boolean;         // 新增时自动聚焦
  scrollToNewItem?: boolean;        // 滚动到新项目
  scrollBehavior?: ScrollBehavior;  // 滚动行为
  scrollBlock?: ScrollLogicalPosition; // 滚动位置
}

const {
  listRef,
  focusItem,
  focusFirstItem,
  focusLastItem,
  focusNextItem,
  focusPreviousItem,
  handleItemsChange,
  handleKeyDown
} = useListFocus({
  autoFocusOnAdd: true,
  scrollToNewItem: true
});
```

**返回值：**
- `listRef`: 列表容器引用
- `focusItem(itemId, scroll)`: 聚焦到指定项目
- `focusFirstItem()`: 聚焦到第一项
- `focusLastItem()`: 聚焦到最后一项
- `focusNextItem()`: 聚焦到下一项
- `focusPreviousItem()`: 聚焦到上一项
- `handleItemsChange(newItems)`: 处理列表项变化
- `handleKeyDown(event)`: 处理键盘导航

### useDialogFocus(options)

对话框焦点管理Hook，处理模态对话框的焦点陷阱。

```typescript
interface DialogFocusOptions {
  isOpen: boolean;                  // 对话框是否打开
  onClose: () => void;              // 关闭回调
  autoFocus?: boolean;              // 是否自动聚焦
  restoreFocus?: boolean;           // 是否恢复焦点
  escPriority?: number;             // ESC键优先级
  tabPriority?: number;             // Tab键优先级
}

const { dialogRef } = useDialogFocus({
  isOpen: true,
  onClose: handleClose,
  autoFocus: true,
  restoreFocus: true
});
```

**返回值：**
- `dialogRef`: 对话框容器引用

## 🧩 UI 组件

### DialogBase

基础对话框组件，提供完整的焦点管理功能。

```typescript
interface DialogBaseProps {
  isOpen: boolean;                  // 是否打开
  onClose: () => void;              // 关闭回调
  children: React.ReactNode;        // 子内容
  width?: string;                   // 宽度
  maxHeight?: string;               // 最大高度
  className?: string;               // CSS类名
  autoFocus?: boolean;              // 自动聚焦
  restoreFocus?: boolean;           // 恢复焦点
}

<DialogBase
  isOpen={isOpen}
  onClose={onClose}
  autoFocus={true}
  restoreFocus={true}
>
  <h2>对话框标题</h2>
  <p>对话框内容</p>
  <button onClick={onClose}>关闭</button>
</DialogBase>
```

### SkipLinks

跳转链接组件，提供快速导航功能。

```typescript
interface SkipLink {
  href: string;                     // 目标锚点
  label: string;                    // 链接文本
}

interface SkipLinksProps {
  links: SkipLink[];                // 跳转链接数组
}

<SkipLinks
  links={[
    { href: '#main-content', label: '跳转到主要内容' },
    { href: '#navigation', label: '跳转到导航菜单' }
  ]}
/>
```

### FormComponents

表单相关的焦点管理组件。

```typescript
// FormField - 表单字段包装器
<FormField
  fieldName="username"
  label="用户名"
  required={true}
  error={errors.username}
>
  <input name="username" />
</FormField>

// FieldError - 字段错误显示
<FieldError
  fieldName="username"
  message="用户名不能为空"
  className="custom-error"
/>
```

### PageComponents

页面相关的焦点管理组件。

```typescript
// PageTitle - 页面标题
<PageTitle
  level={1}
  id="page-title"
  autoFocus={true}
>
  台账管理
</PageTitle>

// MainContent - 主要内容区域
<MainContent
  id="main-content"
  className="container"
>
  <p>页面内容</p>
</MainContent>
```

### ListComponents

列表相关的焦点管理组件。

```typescript
// ListContainer - 列表容器
<ListContainer
  onKeyDown={handleKeyDown}
  className="item-list"
>
  {items.map(item => (
    <ListItem key={item.id} itemId={item.id}>
      {item.name}
    </ListItem>
  ))}
</ListContainer>

// ListItem - 列表项
<ListItem
  itemId="item-1"
  onClick={handleClick}
  className="list-item"
>
  列表项内容
</ListItem>
```

## 🔧 工具函数

### getFocusManager()

获取全局焦点管理器实例。

```typescript
import { getFocusManager } from '../hooks/base/useFocusManager';

try {
  const focusManager = getFocusManager();
  focusManager.setFocus(element);
} catch (error) {
  console.warn('焦点管理器未初始化');
}
```

### getKeyboardManager()

获取全局键盘事件管理器实例。

```typescript
import { getKeyboardManager } from '../hooks/base/useKeyboardManager';

try {
  const keyboardManager = getKeyboardManager();
  const cleanup = keyboardManager.registerEscHandler(handleEsc);
  return cleanup;
} catch (error) {
  console.warn('键盘管理器未初始化');
}
```

## 🎨 样式类

### 焦点样式类

```css
/* 基础焦点环 */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

## 📝 使用示例

### 完整的页面示例

```typescript
import React from 'react';
import { usePageFocus, useFormFocus } from '../hooks/base';
import { PageTitle, MainContent, FormField } from '../components/ui';

const InventoryPage = () => {
  // 页面焦点管理
  const { pageRef, titleRef } = usePageFocus({
    title: '台账管理',
    autoFocus: true
  });

  // 表单焦点管理
  const { formRef, validateAndFocus } = useFormFocus({
    autoFocusOnError: true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    validateAndFocus(
      () => validateForm(),
      () => console.log('提交成功'),
      (errors) => console.log('验证失败', errors)
    );
  };

  return (
    <div ref={pageRef}>
      <PageTitle ref={titleRef} level={1}>
        台账管理
      </PageTitle>
      
      <MainContent>
        <form ref={formRef} onSubmit={handleSubmit}>
          <FormField fieldName="name" label="设备名称" required>
            <input name="name" />
          </FormField>
          
          <button type="submit">提交</button>
        </form>
      </MainContent>
    </div>
  );
};
```

### 对话框示例

```typescript
import React from 'react';
import DialogBase from '../components/ui/DialogBase';

const EditDialog = ({ isOpen, onClose, item }) => {
  return (
    <DialogBase
      isOpen={isOpen}
      onClose={onClose}
      autoFocus={true}
      restoreFocus={true}
    >
      <h2>编辑设备</h2>
      <form>
        <input defaultValue={item.name} autoFocus />
        <div>
          <button type="submit">保存</button>
          <button type="button" onClick={onClose}>
            取消
          </button>
        </div>
      </form>
    </DialogBase>
  );
};
```

## 🔍 调试工具

### 焦点调试

```typescript
// 在控制台中使用
console.log('当前焦点:', document.activeElement);

// 启用键盘导航模式
document.body.classList.add('keyboard-navigation');

// 查看所有可聚焦元素
const focusableElements = Array.from(
  document.querySelectorAll(
    'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
  )
);
console.log('可聚焦元素:', focusableElements);
```

### 性能监控

```typescript
// 监控焦点变化
let focusChangeCount = 0;
document.addEventListener('focusin', () => {
  focusChangeCount++;
  console.log(`焦点变化次数: ${focusChangeCount}`);
});

// 监控键盘事件
document.addEventListener('keydown', (e) => {
  if (e.key === 'Tab') {
    console.log('Tab导航:', e.shiftKey ? '向前' : '向后');
  }
});
```

---

**版本**: 1.0.0  
**最后更新**: 2025-01-09  
**兼容性**: React 18+, TypeScript 4.5+
