import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface Option {
  code: string;
  value: string;
}

interface ExtFieldDropdownProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: string;
}

/**
 * 扩展字段专用自定义下拉选择框组件 - 完全自绘，不依赖任何第三方组件
 * 兼容谷歌108m版本浏览器
 */
const ExtFieldDropdown: React.FC<ExtFieldDropdownProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择',
  className = '',
  disabled = false,
  error
}) => {
  // 下拉框是否展开
  const [isOpen, setIsOpen] = useState(false);

  // 引用下拉框容器元素
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 获取当前选中的选项 - 现在value存储的是显示文本
  const selectedOption = options.find(option => option.value === value);

  // 下拉列表引用
  const dropdownListRef = useRef<HTMLDivElement>(null);

  // 下拉列表位置
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    maxHeight: 200
  });

  // 处理选项点击事件
  const handleOptionClick = (optionCode: string) => {
    // 对于扩展字段下拉选择，保存选项的显示内容而不是ID
    if (optionCode === '') {
      onChange('');
    } else {
      const selectedOption = options.find(opt => opt.code === optionCode);
      onChange(selectedOption ? selectedOption.value : optionCode);
    }
    setIsOpen(false);
  };

  // 处理下拉框切换事件
  const toggleDropdown = () => {
    if (!disabled) {
      // 在打开下拉框之前计算位置
      if (!isOpen && dropdownRef.current) {
        calculateDropdownPosition();
      }
      setIsOpen(!isOpen);
    }
  };

  // 计算下拉框位置，避免界面抖动
  const calculateDropdownPosition = () => {
    if (!dropdownRef.current) return;

    const rect = dropdownRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;

    // 默认向下展开
    let top = rect.bottom + window.scrollY;
    let maxHeight = Math.min(200, spaceBelow - 10); // 最大高度，留10px边距

    // 如果下方空间不足且上方空间更多，则向上展开
    if (spaceBelow < 100 && spaceAbove > spaceBelow) {
      top = rect.top + window.scrollY - Math.min(200, spaceAbove - 10);
      maxHeight = Math.min(200, spaceAbove - 10);
    }

    setDropdownPosition({
      top,
      left: rect.left + window.scrollX,
      width: rect.width,
      maxHeight
    });
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查点击是否在下拉框容器或下拉列表之外
      const isOutsideDropdown = dropdownRef.current && !dropdownRef.current.contains(event.target as Node);
      const isOutsideDropdownList = !dropdownListRef.current || !dropdownListRef.current.contains(event.target as Node);

      // 如果点击在两者之外，则关闭下拉框
      if (isOutsideDropdown && isOutsideDropdownList) {
        setIsOpen(false);
      }
    };

    // 处理窗口大小变化事件
    const handleResize = () => {
      if (isOpen) {
        // 重新计算位置或关闭下拉框
        calculateDropdownPosition();
      }
    };

    // 处理页面滚动事件（不包括下拉框内部滚动）
    const handleScroll = (event: Event) => {
      // 如果滚动事件来自下拉列表内部，则不关闭下拉框
      if (dropdownListRef.current && (
        dropdownListRef.current === event.target ||
        dropdownListRef.current.contains(event.target as Node)
      )) {
        return;
      }

      // 如果是页面其他部分的滚动，则重新计算位置
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  return (
    <div className="relative">
      <div
        ref={dropdownRef}
        className={`w-full px-3 py-2 border rounded-md focus:outline-none cursor-pointer flex justify-between items-center transition-colors
          ${error ? 'border-red-300' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white hover:border-gray-400'}`}
        onClick={toggleDropdown}
      >
        <span className={`${!value ? 'text-gray-400' : 'text-gray-700'}`}>
          {selectedOption ? selectedOption.value : placeholder}
        </span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </div>

      {isOpen && !disabled && createPortal(
        <div
          ref={dropdownListRef}
          className="fixed z-[9000] bg-white border border-gray-300 rounded-md shadow-lg overflow-auto"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: `${dropdownPosition.maxHeight}px`
          }}
        >
          <ul className="py-1">
            {/* 添加空选项 */}
            <li
              className={`px-3 py-1.5 cursor-pointer text-sm hover:bg-gray-100 ${
                value === '' ? 'bg-blue-50 text-blue-700' : 'text-gray-400'
              }`}
              onClick={() => handleOptionClick('')}
            >
              {placeholder}
            </li>

            {/* 渲染所有选项 */}
            {options.map((option) => (
              <li
                key={option.code}
                className={`px-3 py-1.5 cursor-pointer text-sm hover:bg-gray-100 ${
                  option.value === value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                }`}
                onClick={() => handleOptionClick(option.code)}
              >
                {option.value}
              </li>
            ))}
          </ul>
        </div>,
        document.body
      )}


    </div>
  );
};

export default ExtFieldDropdown;
