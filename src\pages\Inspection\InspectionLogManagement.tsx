import React, { useState } from 'react';
import {
  ClipboardList,
  CheckCircle,
  Repeat,
  Users,
  Activity,
  Search,
  RefreshCw,
  X
} from 'lucide-react';
import InspectionTaskLog from './InspectionTaskLog';
import AccountLog from './AccountLog';
import DatePicker from '../../components/ui/DatePicker';
import { usePageFocus } from '../../hooks/base';

/**
 * 巡检日志管理页面
 * 包含5个日志类型的子页面导航
 */
const InspectionLogManagement: React.FC = () => {
  // 页面焦点管理
  const { pageRef, titleRef } = usePageFocus({
    title: '巡检日志',
    autoFocus: true
  });

  // 当前活动的子页面
  const [activeSubTab, setActiveSubTab] = useState<string>('taskLog');

  // 搜索和筛选状态
  const [searchInputValue, setSearchInputValue] = useState('');
  const [startDateStr, setStartDateStr] = useState('');
  const [endDateStr, setEndDateStr] = useState('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // 处理搜索输入变化
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInputValue(e.target.value);
  };

  // 处理刷新
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };



  // 子页面导航项配置
  const subNavigationItems = [
    {
      key: 'taskLog',
      label: '巡检任务日志',
      icon: ClipboardList
    },
    {
      key: 'resultLog',
      label: '巡检结果日志',
      icon: CheckCircle
    },
    {
      key: 'syncLog',
      label: '数据同步日志',
      icon: Repeat
    },
    {
      key: 'accountLog',
      label: '账户管理日志',
      icon: Users
    },
    {
      key: 'statusLog',
      label: '巡检状态日志',
      icon: Activity
    }
  ];

  // 渲染子页面内容
  const renderSubContent = () => {
    const commonProps = {
      searchText: searchInputValue,
      startDate: startDateStr,
      endDate: endDateStr,
      refreshTrigger: refreshTrigger
    };

    switch (activeSubTab) {
      case 'taskLog':
        return <InspectionTaskLog {...commonProps} />;
      case 'resultLog':
        return <div className="p-6 text-center text-gray-500">巡检结果日志页面开发中...</div>;
      case 'syncLog':
        return <div className="p-6 text-center text-gray-500">数据同步日志页面开发中...</div>;
      case 'accountLog':
        return <AccountLog {...commonProps} />;
      case 'statusLog':
        return <div className="p-6 text-center text-gray-500">巡检状态日志页面开发中...</div>;
      default:
        return <InspectionTaskLog {...commonProps} />;
    }
  };

  return (
    <div ref={pageRef as any} className="h-full flex flex-col overflow-hidden bg-slate-50">
      {/* 页面标题（屏幕阅读器用） */}
      <h1 ref={titleRef as any} className="sr-only">巡检日志</h1>

      {/* 主内容区域 - 更紧凑设计 */}
      <div className="flex-1 flex flex-col overflow-hidden p-2 space-y-2">
        {/* 功能区域 */}
        <div className="bg-white border border-slate-200 rounded-md shadow-sm overflow-hidden">
          <div className="p-3">
            <div className="grid grid-cols-12 gap-3 items-end">
              {/* 搜索框和日志类型选择 */}
              <div className="col-span-12">
                <div className="grid grid-cols-12 gap-3">
                  {/* 搜索框 */}
                  <div className="col-span-6">
                    <label className="block text-sm font-medium text-slate-700 mb-1">搜索</label>
                    <div className="relative">
                      <input
                        type="text"
                        value={searchInputValue}
                        onChange={handleSearchInputChange}
                        placeholder="输入关键词搜索日志，多个条件用逗号分隔..."
                        className="w-full bg-white border border-slate-300 rounded-md pl-9 pr-9 py-1.5 text-sm text-slate-700 placeholder-slate-400 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-all"
                      />
                      {searchInputValue && (
                        <button
                          onClick={() => setSearchInputValue('')}
                          className="absolute right-2.5 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                      <div className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-slate-400">
                        <Search className="h-4 w-4" />
                      </div>
                    </div>
                  </div>

                  {/* 日期范围选择 */}
                  <div className="col-span-6">
                    <label className="block text-sm font-medium text-slate-700 mb-1">日期范围</label>
                    <div className="flex items-center space-x-2">
                      <div className="relative flex-1">
                        <DatePicker
                          name="startDate"
                          value={startDateStr}
                          onChange={(e) => setStartDateStr(e.target.value)}
                          placeholder="开始日期"
                        />
                      </div>
                      <span className="text-slate-500 text-sm">至</span>
                      <div className="relative flex-1">
                        <DatePicker
                          name="endDate"
                          value={endDateStr}
                          onChange={(e) => setEndDateStr(e.target.value)}
                          placeholder="结束日期"
                        />
                      </div>
                      {(startDateStr || endDateStr) && (
                        <button
                          onClick={() => {
                            setStartDateStr('');
                            setEndDateStr('');
                          }}
                          className="flex items-center justify-center bg-rose-50 hover:bg-rose-100 border border-rose-200 text-rose-500 w-7 h-7 rounded-md transition-colors"
                          title="清除日期范围"
                        >
                          <X className="h-3.5 w-3.5" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 日志类型选择和刷新按钮 */}
            <div className="flex justify-between items-center mt-3 pt-2.5 border-t border-slate-100">
              {/* 日志类型选择 */}
              <div className="flex flex-wrap items-center">
                <label className="text-sm font-medium text-slate-700 mr-2.5">日志类型:</label>
                <div className="flex flex-wrap gap-1">
                  {subNavigationItems.map(item => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.key}
                        onClick={() => setActiveSubTab(item.key)}
                        className={`px-2 py-1 text-sm rounded-md border transition-all duration-200 ${
                          activeSubTab === item.key
                            ? 'bg-blue-500 text-white border-blue-500 shadow-sm'
                            : 'bg-white text-slate-700 border-slate-300 hover:bg-slate-50 hover:border-slate-400'
                        }`}
                      >
                        {item.label}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* 刷新按钮 */}
              <button
                onClick={handleRefresh}
                className="flex items-center bg-blue-500 hover:bg-blue-600 text-white px-2.5 py-1 rounded-md shadow-sm transition-colors duration-200"
                title="刷新数据"
              >
                <RefreshCw className="h-4 w-4 mr-1.5" />
                <span className="text-sm font-medium">刷新全部数据</span>
              </button>
            </div>
          </div>
        </div>

        {/* 表格区域 */}
        <div className="flex-1 bg-white border border-slate-200 rounded-md shadow-sm overflow-hidden">
          <div className="h-full">
            {renderSubContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InspectionLogManagement;
