import { DepartmentCategory } from './types';
import { CacheManager } from './cacheManager';
import {
  ROOT_NODE_ID,
  NODE_ID_PREFIXES,
  PATH_SEPARATOR,
  SORT_ORDER,
  LOG_PREFIXES,
  formatLogMessage
} from './constants';

/**
 * 统一的树操作工具库
 * 整合所有重复的节点查找、路径处理、树遍历逻辑
 */
export class TreeUtils {
  private static cacheManager = CacheManager.getInstance();

  // ==================== 节点查找相关 ====================

  /**
   * 统一的节点查找方法
   * @param categories 部门分类树
   * @param nodeId 节点ID
   * @returns 找到的节点或null
   */
  public static findNodeById(categories: DepartmentCategory[], nodeId: string): DepartmentCategory | null {
    for (const category of categories) {
      if (category.id === nodeId) {
        return category;
      }
      if (category.children?.length) {
        const found = this.findNodeById(category.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 查找节点的父节点
   * @param categories 部门分类树
   * @param targetId 目标节点ID
   * @returns 父节点或null
   */
  public static findParentNode(categories: DepartmentCategory[], targetId: string): DepartmentCategory | null {
    for (const category of categories) {
      if (category.children && category.children.some(child => child.id === targetId)) {
        return category;
      }
      if (category.children && category.children.length) {
        const found = this.findParentNode(category.children, targetId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 检查一个节点是否是另一个节点的子节点
   * @param childId 潜在的子节点ID
   * @param parentId 潜在的父节点ID
   * @param departmentCategories 完整的部门分类树
   * @returns 是否是子节点
   */
  public static isChildOf(childId: string, parentId: string, departmentCategories: DepartmentCategory[]): boolean {
    // 如果ID相同，则不是子节点关系
    if (childId === parentId) {
      return false;
    }

    const parentNode = this.findNodeById(departmentCategories, parentId);
    if (!parentNode || !parentNode.children) {
      return false;
    }

    // 检查直接子节点
    for (const child of parentNode.children) {
      if (child.id === childId) {
        return true;
      }

      // 递归检查子节点的子节点
      if (this.isChildOf(childId, child.id, departmentCategories)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 查找节点的父节点
   * @param categories 部门分类树
   * @param targetId 目标节点ID
   * @returns 父节点或null
   */
  public static findParentDepartment(
    categories: DepartmentCategory[],
    targetId: string
  ): DepartmentCategory | null {
    for (const category of categories) {
      if (category.children?.some((child: DepartmentCategory) => child.id === targetId)) {
        return category;
      }
      if (category.children?.length) {
        const found = this.findParentDepartment(category.children, targetId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 从人员节点中提取人员信息
   * @param categories 部门分类树
   * @param personId 人员节点ID
   * @returns 人员信息或null
   */
  public static extractPersonInfo(categories: DepartmentCategory[], personId: string): { name: string, alias?: string } | null {
    const category = this.findNodeById(categories, personId);
    if (!category) return null;

    return {
      name: this.extractPersonName(category.name),
      alias: this.extractPersonAlias(category.name) || undefined
    };
  }



  // ==================== 路径处理相关 ====================

  /**
   * 获取节点的完整路径（包含根节点）
   * @param category 部门节点
   * @param departmentCategories 完整的部门分类树
   * @returns 完整路径
   */
  public static getFullPath(category: DepartmentCategory, departmentCategories: DepartmentCategory[]): string {
    // 检查缓存
    if (this.cacheManager.hasPathCache(category.id)) {
      return this.cacheManager.getPathCache(category.id)!;
    }

    // 如果是根节点，直接返回名称
    if (category.id === ROOT_NODE_ID) {
      this.cacheManager.setPathCache(category.id, category.name);
      return category.name;
    }

    // 总是基于树结构动态计算路径，确保数据一致性
    // 不再优先使用存储的departmentPath字段，避免重命名后的数据不一致问题
    const pathParts: string[] = [];

    // 递归向上查找父节点，构建路径
    const buildPath = (node: DepartmentCategory): void => {
      // 查找父节点
      const parent = this.findParentNode(departmentCategories, node.id);

      // 如果有父节点，继续向上查找（包括根节点）
      if (parent) {
        buildPath(parent);
      }

      // 将当前节点名称添加到路径中
      pathParts.push(node.name);
    };

    buildPath(category);

    const path = pathParts.join(PATH_SEPARATOR);

    // 缓存结果
    this.cacheManager.setPathCache(category.id, path);

    return path;
  }

  /**
   * 获取API路径（不包含根节点）
   * @param category 部门节点
   * @param departmentCategories 完整的部门分类树
   * @returns API路径
   */
  public static getApiPath(category: DepartmentCategory, departmentCategories: DepartmentCategory[]): string {
    // 如果是根节点，返回空字符串
    if (category.id === ROOT_NODE_ID) {
      return '';
    }

    // 总是基于树结构动态计算路径，确保数据一致性
    // 不再优先使用存储的departmentPath字段，避免重命名后的数据不一致问题
    const pathParts: string[] = [];

    // 递归向上查找父节点，构建路径
    const buildPath = (node: DepartmentCategory): void => {
      // 查找父节点
      const parent = this.findParentNode(departmentCategories, node.id);

      // 如果有父节点且不是根节点，继续向上查找
      if (parent && parent.id !== ROOT_NODE_ID) {
        buildPath(parent);
      }

      // 将当前节点名称添加到路径中（根节点除外）
      if (node.id !== ROOT_NODE_ID) {
        pathParts.push(node.name);
      }
    };

    buildPath(category);

    // 返回构建的路径
    return pathParts.join(PATH_SEPARATOR);
  }

  /**
   * 递归构建路径
   * @param categories 部门分类树
   * @param targetId 目标节点ID
   * @param currentPath 当前路径
   * @returns 构建的路径
   */
  private static buildPathRecursively(categories: DepartmentCategory[], targetId: string, currentPath: string = ''): string {
    for (const category of categories) {
      const newPath = currentPath ? `${currentPath}${PATH_SEPARATOR}${category.name}` : category.name;
      
      if (category.id === targetId) {
        return newPath;
      }
      
      if (category.children?.length) {
        const found = this.buildPathRecursively(category.children, targetId, newPath);
        if (found) return found;
      }
    }
    return '';
  }

  // ==================== 树遍历相关 ====================

  /**
   * 遍历树并执行回调函数
   * @param categories 部门分类树
   * @param callback 回调函数
   * @param depth 当前深度
   */
  public static traverseTree(
    categories: DepartmentCategory[], 
    callback: (node: DepartmentCategory, depth: number) => void,
    depth: number = 0
  ): void {
    for (const category of categories) {
      callback(category, depth);
      if (category.children?.length) {
        this.traverseTree(category.children, callback, depth + 1);
      }
    }
  }

  /**
   * 查找所有匹配条件的节点
   * @param categories 部门分类树
   * @param predicate 匹配条件函数
   * @returns 匹配的节点数组
   */
  public static findAllNodes(
    categories: DepartmentCategory[], 
    predicate: (node: DepartmentCategory) => boolean
  ): DepartmentCategory[] {
    const result: DepartmentCategory[] = [];
    
    this.traverseTree(categories, (node) => {
      if (predicate(node)) {
        result.push(node);
      }
    });
    
    return result;
  }

  /**
   * 查找所有同名的部门
   * @param categories 部门分类树
   * @param departmentName 部门名称
   * @returns 所有同名部门的路径列表
   */
  public static findAllDepartmentsWithName(categories: DepartmentCategory[], departmentName: string): string[] {
    // 检查缓存
    const cacheKey = departmentName;
    if (this.cacheManager.hasSameNameDepartmentsCache(cacheKey)) {
      return this.cacheManager.getSameNameDepartmentsCache(cacheKey)!;
    }

    // 使用Set避免重复路径
    const resultSet = new Set<string>();

    this.traverseTree(categories, (node) => {
      // 只处理部门节点
      if ((node.id.startsWith(NODE_ID_PREFIXES.DEPARTMENT) || node.id === ROOT_NODE_ID) && node.name === departmentName) {
        // 总是基于树结构动态计算路径，确保数据一致性
        const nodePath = this.getFullPath(node, categories);
        resultSet.add(nodePath);
      }
    });

    // 转换为数组
    const result = Array.from(resultSet);

    // 缓存结果
    this.cacheManager.setSameNameDepartmentsCache(cacheKey, result);

    return result;
  }

  // ==================== 树结构操作相关 ====================

  /**
   * 对部门树进行排序
   * @param nodes 要排序的节点数组
   */
  public static sortDepartmentTree(nodes: DepartmentCategory[]): void {
    for (const node of nodes) {
      if (node.children && node.children.length > 0) {
        // 先对子节点进行排序
        node.children.sort((a, b) => {
          // 如果a是人员节点而b是部门节点，a排在前面
          if (a.id.startsWith(NODE_ID_PREFIXES.PERSON) && b.id.startsWith(NODE_ID_PREFIXES.DEPARTMENT)) {
            return SORT_ORDER.PERSON_FIRST;
          }
          // 如果a是部门节点而b是人员节点，b排在前面
          if (a.id.startsWith(NODE_ID_PREFIXES.DEPARTMENT) && b.id.startsWith(NODE_ID_PREFIXES.PERSON)) {
            return SORT_ORDER.DEPARTMENT_FIRST;
          }
          // 如果都是同类型节点，按名称排序
          return a.name.localeCompare(b.name, 'zh-CN');
        });

        // 递归排序子节点
        this.sortDepartmentTree(node.children);
      }
    }
  }

  /**
   * 获取部门下的所有人员
   * @param department 部门节点
   * @returns 人员名称列表
   */
  public static getAllPersonsInDepartment(department: DepartmentCategory): string[] {
    // 检查缓存
    if (this.cacheManager.hasDepartmentPersonsCache(department.id)) {
      return this.cacheManager.getDepartmentPersonsCache(department.id)!;
    }

    const result: string[] = [];

    // 如果有子节点，遍历子节点
    if (department.children?.length) {
      for (const child of department.children) {
        // 如果是人员节点，添加到结果中
        if (child.id.startsWith(NODE_ID_PREFIXES.PERSON)) {
          result.push(child.name);
        }
      }
    }

    // 缓存结果
    this.cacheManager.setDepartmentPersonsCache(department.id, result);

    return result;
  }

  // ==================== 工具方法 ====================

  /**
   * 提取人员名称（去除备注部分）
   * @param fullName 完整名称，可能包含备注，如 "张三 (小张)"
   * @returns 纯净的人员名称
   */
  public static extractPersonName(fullName: string): string {
    // 匹配模式：姓名 (备注) 或 姓名（备注）
    const match = fullName.match(/^(.+?)\s*[（(].*[）)]$/);
    return match ? match[1].trim() : fullName.trim();
  }

  /**
   * 提取人员备注
   * @param fullName 完整名称，可能包含备注，如 "张三 (小张)"
   * @returns 备注或null
   */
  public static extractPersonAlias(fullName: string): string | null {
    // 匹配模式：姓名 (备注) 或 姓名（备注）
    const match = fullName.match(/^.+?\s*[（(](.+)[）)]$/);
    return match ? match[1].trim() : null;
  }
}
