import React from 'react';
import { X } from 'lucide-react';
import DialogBase from './DialogBase';

interface DialogProps {
  // 对话框是否打开
  isOpen: boolean;
  // 关闭对话框的回调
  onClose: () => void;
  // 对话框标题
  title: string;
  // 对话框内容
  children: React.ReactNode;
  // 对话框宽度
  width?: string | number;
  // 对话框最大宽度
  maxWidth?: string | number;
  // 对话框最大高度
  maxHeight?: string | number;
  // 对话框CSS类名
  className?: string;
  // 对话框样式
  style?: React.CSSProperties;
  // 对话框动画
  animation?: 'fade' | 'slide' | 'scale' | 'none';
  // 是否显示关闭按钮
  showCloseButton?: boolean;
  // 是否显示底部按钮区域
  showFooter?: boolean;
  // 底部按钮区域内容
  footer?: React.ReactNode;
  // 是否禁用关闭按钮
  disableCloseButton?: boolean;
  // 是否自动聚焦到第一个元素
  autoFocus?: boolean;
  // 关闭时是否恢复焦点
  restoreFocus?: boolean;
  // ESC键处理器的优先级
  escPriority?: number;
  // Tab键处理器的优先级
  tabPriority?: number;
}

/**
 * 通用对话框组件
 * 基于DialogBase，提供标准的对话框布局和样式
 */
const Dialog: React.FC<DialogProps> = ({
  isOpen,
  onClose,
  title,
  children,
  width = '100%',
  maxWidth = '28rem',
  maxHeight = '90vh',
  className = '',
  style = {},
  animation = 'fade',
  showCloseButton = true,
  showFooter = false,
  footer = null,
  disableCloseButton = false,
  autoFocus = true,
  restoreFocus = true,
  escPriority = 100,
  tabPriority = 100
}) => {
  return (
    <DialogBase
      isOpen={isOpen}
      onClose={onClose}
      width={width}
      maxWidth={maxWidth}
      maxHeight={maxHeight}
      className={className}
      style={style}
      animation={animation}
      autoFocus={autoFocus}
      restoreFocus={restoreFocus}
      escPriority={escPriority}
      tabPriority={tabPriority}
    >
      {/* 对话框标题 */}
      <div className="flex items-center justify-between px-6 py-2.5 border-b">
        <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
        {showCloseButton && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus-ring rounded-full p-1"
            disabled={disableCloseButton}
            aria-label="关闭"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* 对话框内容 */}
      <div className="px-6 py-4 flex-1 overflow-auto">
        {children}
      </div>

      {/* 对话框底部 */}
      {showFooter && (
        <div className="px-6 py-3 border-t flex justify-end space-x-3">
          {footer}
        </div>
      )}
    </DialogBase>
  );
};

export default Dialog;
