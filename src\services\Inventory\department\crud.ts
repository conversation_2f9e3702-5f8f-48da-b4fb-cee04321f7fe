import { DepartmentCategory } from './types';
import { TreeUtils } from './treeUtils';
import { DepartmentUtils } from './utils';
import { BaseService } from '../../base/baseService';
import { DepartmentPathResolver } from './pathResolver';

/**
 * 部门CRUD操作类
 * 负责部门和人员的增删改查操作
 */
export class DepartmentCRUD {
  private baseService: BaseService<any, any>;

  constructor(baseService: BaseService<any, any>) {
    this.baseService = baseService;
  }

  /**
   * 添加部门
   * @param parentId 父级部门ID
   * @param departmentName 部门名称
   * @param departmentCategories 当前部门分类树
   */
  public async addDepartment(
    parentId: string,
    departmentName: string,
    departmentCategories: DepartmentCategory[]
  ): Promise<any> {
    // 创建路径解析器
    const pathResolver = new DepartmentPathResolver(departmentCategories);

    // 检查同级部门重名
    if (parentId === 'all-dept') {
      // 检查一级部门重名
      const rootDept = TreeUtils.findNodeById(departmentCategories, 'all-dept');
      if (rootDept && rootDept.children) {
        const existingDept = rootDept.children.find((dept: DepartmentCategory) =>
          dept.id.startsWith('dept-') && dept.name === departmentName
        );

        if (existingDept) {
          throw new Error(`同节点下的部门不允许重名，已存在同名部门: ${departmentName}`);
        }
      }
    } else {
      // 检查子部门重名
      const parentDept = TreeUtils.findNodeById(departmentCategories, parentId);
      if (!parentDept) {
        throw new Error(`未找到父级部门: ${parentId}`);
      }

      if (parentDept.children) {
        const existingDept = parentDept.children.find((dept: DepartmentCategory) =>
          dept.id.startsWith('dept-') && dept.name === departmentName
        );

        if (existingDept) {
          throw new Error(`同节点下的部门不允许重名，已存在同名部门: ${departmentName}`);
        }
      }
    }

    // 使用路径解析器构建部门路径
    const departmentPath = pathResolver.buildNewDepartmentPath(parentId, departmentName);

    // 验证路径有效性
    if (!pathResolver.isValidPath(departmentPath)) {
      throw new Error(`无效的部门路径: ${departmentPath}`);
    }

    // 构建请求参数，使用新的API格式
    const params = {
      action: "add_department",
      action_params: {
        department_path: departmentPath
      }
    };

    console.log('开始添加部门:', JSON.stringify(params));

    // 提交任务并等待结果
    const result = await this.baseService.submitTask('DbFun', params);

    console.log('添加部门API响应:', result);
    return result;
  }

  /**
   * 添加人员
   * @param departmentId 所属部门ID
   * @param personName 人员名称
   * @param alias 备注（可选）
   * @param departmentCategories 当前部门分类树
   * @param mobileNumber 联系方式（可选）
   * @param positionSecurityLevel 岗位密级（可选）
   */
  public async addPerson(
    departmentId: string,
    personName: string,
    alias: string | undefined,
    departmentCategories: DepartmentCategory[],
    mobileNumber?: string,
    positionSecurityLevel?: number
  ): Promise<any> {
    // 创建路径解析器并查找部门信息
    const pathResolver = new DepartmentPathResolver(departmentCategories);

    if (departmentId === 'all-dept') {
      throw new Error('不能在根节点下直接添加人员，请选择一个部门');
    }

    const department = TreeUtils.findNodeById(departmentCategories, departmentId);
    if (!department) {
      throw new Error(`未找到部门: ${departmentId}`);
    }

    // 使用路径解析器获取部门路径信息
    const departmentPath = pathResolver.getApiPath(department);

    // 构建请求参数，使用新的API格式
    const params: any = {
      action: "add_person",
      action_params: {
        user_name: personName,
        department_path: departmentPath
      }
    };

    // 如果提供了备注且不为空，添加到参数中
    if (alias && alias.trim() !== '') {
      params.action_params.alias = alias.trim();
    }

    // 如果提供了联系方式且不为空，添加到参数中
    if (mobileNumber && mobileNumber.trim() !== '') {
      params.action_params.mobile_number = mobileNumber.trim();
    }

    // 添加岗位密级参数（默认为0）
    params.action_params.position_security_level = typeof positionSecurityLevel === 'number' ? positionSecurityLevel : 0;

    console.log('开始添加人员:', JSON.stringify(params));

    // 提交任务并等待结果
    const result = await this.baseService.submitTask('DbFun', params);

    console.log('添加人员API响应:', result);
    return result;
  }

  /**
   * 重命名部门或人员
   * @param nodeId 节点ID
   * @param newName 新名称
   * @param departmentCategories 当前部门分类树
   */
  public async renameDepartmentOrPerson(
    nodeId: string,
    newName: string,
    departmentCategories: DepartmentCategory[]
  ): Promise<void> {
    // 不允许重命名根节点
    if (nodeId === 'all-dept') {
      throw new Error('不能重命名公司名称');
    }

    if (nodeId.startsWith('dept-')) {
      // 更新部门
      // 使用统一的findNodeById方法查找部门
      const department = TreeUtils.findNodeById(departmentCategories, nodeId);
      if (!department) {
        throw new Error(`未找到部门: ${nodeId}`);
      }

      // 创建路径解析器并获取当前部门路径
      const pathResolver = new DepartmentPathResolver(departmentCategories);
      const currentDepartmentPath = pathResolver.getApiPath(department);

      // 构建新的部门路径
      const pathParts = currentDepartmentPath.split('/');
      pathParts[pathParts.length - 1] = newName; // 替换最后一部分为新名称
      const newDepartmentPath = pathParts.join('/');

      // 验证新路径有效性
      if (!pathResolver.isValidPath(newDepartmentPath)) {
        throw new Error(`无效的新部门路径: ${newDepartmentPath}`);
      }

      // 使用新的API格式更新部门
      const params = {
        action: "update_department",
        action_params: {
          department_path: currentDepartmentPath,
          new_department_path: newDepartmentPath
        }
      };

      console.log('开始重命名部门:', JSON.stringify(params));

      // 提交任务并等待结果
      await this.baseService.submitTask('DbFun', params);
    } else if (nodeId.startsWith('person-')) {
      // 更新人员
      // 使用extractPersonInfo方法提取原人员信息
      const personInfo = TreeUtils.extractPersonInfo(departmentCategories, nodeId);
      if (!personInfo) {
        throw new Error(`未找到人员: ${nodeId}`);
      }

      // 从新名称中提取实际名称和备注
      let newUserName = newName;
      let newAlias = ''; // 默认新备注为空

      // 检查新名称是否包含备注格式（支持中文和英文括号）
      // 先提取括号前的部分作为名称
      const newNameMatch = newName.match(/^([^(（]+)/);

      // 分别匹配英文括号和中文括号中的备注
      const englishAliasMatch = newName.match(/\(([^)]+)\)/);
      const chineseAliasMatch = newName.match(/（([^）]+)）/);

      if (newNameMatch) {
        newUserName = newNameMatch[1].trim();
      }

      // 如果新名称中包含备注（括号部分），则提取备注
      if (englishAliasMatch) {
        newAlias = englishAliasMatch[1];
      } else if (chineseAliasMatch) {
        newAlias = chineseAliasMatch[1];
      }
      // 如果新名称中不包含备注，则备注为空字符串

      console.log(`检测备注: 英文括号=${!!englishAliasMatch}, 中文括号=${!!chineseAliasMatch}, 提取的备注="${newAlias}"`);

      // 处理特殊情况：如果新名称包含括号但提取失败
      if ((newName.includes('(') || newName.includes('（')) && !newAlias) {
        console.log('警告: 名称中包含括号但无法提取备注，尝试使用更宽松的正则表达式');

        // 尝试使用更宽松的正则表达式
        const lastOpenBracketIndex = Math.max(newName.lastIndexOf('('), newName.lastIndexOf('（'));
        if (lastOpenBracketIndex > 0) {
          // 提取括号前的部分作为名称
          newUserName = newName.substring(0, lastOpenBracketIndex).trim();

          // 提取括号中的内容作为备注
          const bracketContent = newName.substring(lastOpenBracketIndex + 1);
          const closeBracketIndex = Math.min(
            bracketContent.indexOf(')') >= 0 ? bracketContent.indexOf(')') : Number.MAX_SAFE_INTEGER,
            bracketContent.indexOf('）') >= 0 ? bracketContent.indexOf('）') : Number.MAX_SAFE_INTEGER
          );

          if (closeBracketIndex < Number.MAX_SAFE_INTEGER) {
            newAlias = bracketContent.substring(0, closeBracketIndex);
            console.log(`使用备用方法提取备注: 名称="${newUserName}", 备注="${newAlias}"`);
          }
        }
      }

      console.log(`解析新名称: 原姓名="${personInfo.name}", 原备注="${personInfo.alias || ''}", 新姓名="${newUserName}", 新备注="${newAlias}"`);

      // 创建路径解析器并获取当前部门信息
      const pathResolver = new DepartmentPathResolver(departmentCategories);
      const currentDepartmentNode = TreeUtils.findParentDepartment(departmentCategories, nodeId);
      if (!currentDepartmentNode) {
        throw new Error(`未找到人员的所属部门: ${nodeId}`);
      }

      // 使用路径解析器获取部门路径信息
      const currentDepartmentPath = pathResolver.getApiPath(currentDepartmentNode);

      // 确保名称字段只包含 UTF-8 格式的纯文本，不包含括号和备注部分
      // 使用新的API格式更新人员
      const params: any = {
        action: "update_person",
        action_params: {
          user_name: personInfo.name, // 原姓名，纯文本
          alias: personInfo.alias || '', // 原备注，如果没有则传空字符串
          current_department_path: currentDepartmentPath, // 当前部门路径
          new_user_name: newUserName, // 新姓名，纯文本（不包含括号和备注部分）
          new_alias: newAlias // 新备注，如果没有则传空字符串
        }
      };

      console.log('开始更新人员:', JSON.stringify(params));

      // 提交任务并等待结果
      await this.baseService.submitTask('DbFun', params);
    } else {
      throw new Error(`不支持的节点类型: ${nodeId}`);
    }
  }

  /**
   * 检查部门是否有子部门或人员
   * @param department 部门节点
   * @returns 如果有子部门或人员返回true，否则返回false
   */
  private hasDepartmentChildren(department: DepartmentCategory): boolean {
    // 检查是否有子节点
    if (!department.children || department.children.length === 0) {
      return false;
    }

    // 检查子节点中是否有部门或人员
    return department.children.some((child: DepartmentCategory) =>
      child.id.startsWith('dept-') || child.id.startsWith('person-')
    );
  }

  /**
   * 检查人员是否有管理的设备
   * @param personName 人员名称
   * @param personAlias 人员备注
   * @returns 如果有管理的设备返回true，否则返回false
   */
  public async hasPersonDevices(personName: string, personAlias?: string): Promise<boolean> {
    try {
      // 构建请求参数，获取所有设备
      const params = {
        action: "get_device",
        action_params: {}
      };

      console.log(`检查人员是否有管理的设备: ${personName}${personAlias ? ` (${personAlias})` : ''}`);

      // 提交任务并等待结果
      const result = await this.baseService.submitTask('DbFun', params);

      // 检查返回结果的格式
      if (!result) {
        console.warn('获取设备数据失败，结果为空');
        return false;
      }

      // 使用类型断言处理结果
      const typedResult = result as any;

      if (!typedResult.success || !typedResult.data) {
        console.warn('获取设备数据失败或数据格式不正确:', typedResult);
        return false;
      }

      // 获取设备数据 - 适配新的API数据结构
      let devices = [];
      if (Array.isArray(typedResult.data)) {
        // 新的API格式：data直接是设备数组
        devices = typedResult.data;
      } else if (typedResult.data.devices && Array.isArray(typedResult.data.devices)) {
        // 旧的API格式：data.devices是设备数组
        devices = typedResult.data.devices;
      } else {
        console.warn('设备数据格式不符合预期:', typedResult);
        return false;
      }

      // 检查是否有该人员管理的设备
      const hasDevices = devices.some((device: any) => {
        // 检查责任人姓名是否匹配
        if (!device.responsible_person_name) {
          return false;
        }

        // 构建完整的责任人名称（包含备注）
        let responsibleFullName = device.responsible_person_name;
        if (device.responsible_person_alias) {
          responsibleFullName += ` (${device.responsible_person_alias})`;
        }

        // 构建目标人员的完整名称
        let targetFullName = personName;
        if (personAlias) {
          targetFullName += ` (${personAlias})`;
        }

        // 使用统一的匹配逻辑
        return DepartmentUtils.isResponsibleMatch(responsibleFullName, targetFullName);
      });

      console.log(`人员 ${personName} ${hasDevices ? '有' : '没有'}管理的设备`);
      return hasDevices;
    } catch (error) {
      console.error('检查人员是否有管理的设备失败:', error);
      // 出错时默认返回false，避免阻止删除操作
      return false;
    }
  }

  /**
   * 删除部门或人员
   * @param nodeId 节点ID
   * @param departmentCategories 当前部门分类树
   */
  public async deleteDepartmentOrPerson(nodeId: string, departmentCategories: DepartmentCategory[]): Promise<void> {
    // 不允许删除根节点
    if (nodeId === 'all-dept') {
      throw new Error('不能删除公司名称');
    }

    if (nodeId.startsWith('dept-')) {
      // 删除部门
      // 使用统一的findNodeById方法查找部门
      const department = TreeUtils.findNodeById(departmentCategories, nodeId);
      if (!department) {
        throw new Error(`未找到部门: ${nodeId}`);
      }

      // 检查部门是否有子部门或人员
      if (this.hasDepartmentChildren(department)) {
        throw new Error(`该部门下含有子部门或人员不能直接删除`);
      }

      // 创建路径解析器并获取API路径
      const pathResolver = new DepartmentPathResolver(departmentCategories);
      const departmentPath = pathResolver.getApiPath(department);

      // 使用新的API格式删除部门
      const params = {
        action: "delete_department",
        action_params: {
          department_path: departmentPath
        }
      };

      console.log('开始删除部门:', JSON.stringify(params));

      // 提交任务并等待结果
      await this.baseService.submitTask('DbFun', params);
    } else if (nodeId.startsWith('person-')) {
      // 删除人员
      // 使用extractPersonInfo方法提取人员信息
      const personInfo = TreeUtils.extractPersonInfo(departmentCategories, nodeId);
      if (!personInfo) {
        throw new Error(`未找到人员: ${nodeId}`);
      }

      // 检查人员是否有管理的设备
      const hasDevices = await this.hasPersonDevices(personInfo.name, personInfo.alias);
      if (hasDevices) {
        throw new Error(`该人员有管理的设备不能直接删除`);
      }

      // 创建路径解析器并获取人员所在部门信息
      const pathResolver = new DepartmentPathResolver(departmentCategories);
      const departmentNode = TreeUtils.findParentDepartment(departmentCategories, nodeId);
      if (!departmentNode) {
        throw new Error(`未找到人员的所属部门: ${nodeId}`);
      }

      // 使用路径解析器获取部门路径信息
      const departmentPath = pathResolver.getApiPath(departmentNode);

      // 确保名称字段只包含 UTF-8 格式的纯文本，不包含括号和备注部分
      // 使用新的API格式删除人员
      const params: any = {
        action: "delete_person",
        action_params: {
          user_name: personInfo.name, // 纯文本名称，不包含括号和备注部分
          alias: personInfo.alias || '', // 别名，如果没有则传空字符串
          department_path: departmentPath // 人员所在部门路径
        }
      };

      console.log('开始删除人员:', JSON.stringify(params));

      // 提交任务并等待结果
      await this.baseService.submitTask('DbFun', params);
    } else {
      throw new Error(`不支持的节点类型: ${nodeId}`);
    }
  }

  /**
   * 更新部门位置
   * @param departmentId 部门ID
   * @param newParentId 新的父部门ID
   * @param departmentCategories 当前部门分类树
   */
  public async updateDepartmentPosition(
    departmentId: string,
    newParentId: string,
    departmentCategories: DepartmentCategory[]
  ): Promise<void> {
    // 不允许移动根节点
    if (departmentId === 'all-dept') {
      throw new Error('不能移动公司名称');
    }

    // 不允许将部门移动到自己下面（避免循环引用）
    if (TreeUtils.isChildOf(newParentId, departmentId, departmentCategories)) {
      throw new Error('不能将部门移动到其子部门下');
    }

    // 创建路径解析器并获取部门信息
    const pathResolver = new DepartmentPathResolver(departmentCategories);
    const departmentNode = TreeUtils.findNodeById(departmentCategories, departmentId);
    if (!departmentNode) {
      throw new Error(`未找到部门: ${departmentId}`);
    }

    // 获取当前部门路径
    const currentDepartmentPath = pathResolver.getApiPath(departmentNode);

    // 构建新的部门路径
    let newDepartmentPath: string;
    if (newParentId === 'all-dept') {
      // 移动到根节点下，直接使用部门名称
      newDepartmentPath = departmentNode.name;
    } else {
      // 移动到其他部门下
      const newParentNode = TreeUtils.findNodeById(departmentCategories, newParentId);
      if (!newParentNode) {
        throw new Error(`未找到新的父部门: ${newParentId}`);
      }
      const newParentPath = pathResolver.getApiPath(newParentNode);
      newDepartmentPath = newParentPath ? `${newParentPath}/${departmentNode.name}` : departmentNode.name;
    }

    // 验证新路径有效性
    if (!pathResolver.isValidPath(newDepartmentPath)) {
      throw new Error(`无效的新部门路径: ${newDepartmentPath}`);
    }

    // 构建请求参数，使用新的API格式
    const params = {
      action: "update_department",
      action_params: {
        current_department_path: currentDepartmentPath,
        department_path: newDepartmentPath
      }
    };

    console.log('开始更新部门位置:', JSON.stringify(params));

    // 提交任务并等待结果
    await this.baseService.submitTask('DbFun', params);
  }

  /**
   * 更新人员位置
   * @param personId 人员ID
   * @param newDepartmentId 新的部门ID
   * @param departmentCategories 当前部门分类树
   */
  public async updatePersonPosition(
    personId: string,
    newDepartmentId: string,
    departmentCategories: DepartmentCategory[]
  ): Promise<void> {
    // 不允许将人员移动到根节点
    if (newDepartmentId === 'all-dept') {
      throw new Error('不能将人员移动到根节点');
    }

    // 获取人员信息
    const personInfo = TreeUtils.extractPersonInfo(departmentCategories, personId);
    if (!personInfo) {
      throw new Error(`未找到人员: ${personId}`);
    }

    // 查找人员的旧部门
    const findParentDepartment = (categories: DepartmentCategory[], targetId: string): DepartmentCategory | null => {
      for (const category of categories) {
        if (category.children && category.children.some(function(child: DepartmentCategory) { return child.id === targetId; })) {
          return category;
        }
        if (category.children && category.children.length) {
          const found = findParentDepartment(category.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    // 创建路径解析器并获取部门信息
    const pathResolver = new DepartmentPathResolver(departmentCategories);

    // 获取当前部门信息（旧部门）
    const currentDepartmentNode = TreeUtils.findParentDepartment(departmentCategories, personId);
    if (!currentDepartmentNode) {
      throw new Error(`未找到人员的所属部门: ${personId}`);
    }

    // 获取新部门信息
    const newDepartmentNode = TreeUtils.findNodeById(departmentCategories, newDepartmentId);
    if (!newDepartmentNode) {
      throw new Error(`未找到新的部门: ${newDepartmentId}`);
    }

    // 使用路径解析器获取部门路径信息
    const currentDepartmentPath = pathResolver.getApiPath(currentDepartmentNode);
    const newDepartmentPath = pathResolver.getApiPath(newDepartmentNode);

    // 构建请求参数，使用新的API格式
    const params: any = {
      action: "update_person",
      action_params: {
        user_name: personInfo.name,
        alias: personInfo.alias || '', // 别名
        current_department_path: currentDepartmentPath, // 当前部门路径
        department_path: newDepartmentPath // 新部门路径
      }
    };

    console.log('开始更新人员位置:', JSON.stringify(params));

    // 提交任务并等待结果
    await this.baseService.submitTask('DbFun', params);
  }
}
