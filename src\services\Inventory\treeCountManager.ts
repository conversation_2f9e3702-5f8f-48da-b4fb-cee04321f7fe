import { InventoryItem, DeviceCategory } from '../../types/inventory';
import { DepartmentCategory } from './department/types';
import InventoryService from './inventoryService';
import DepartmentService from './departmentService';

/**
 * 树状图计数管理器
 * 统一管理设备分类树和部门分类树的计数更新
 * 解决多层防抖、竞态条件等问题
 */
export class TreeCountManager {
  private static instance: TreeCountManager;
  private updateQueue: Set<string> = new Set();
  private isUpdating = false;
  private updateTimer: NodeJS.Timeout | null = null;
  private readonly DEBOUNCE_DELAY = 100; // 减少防抖延迟

  private constructor() {}

  public static getInstance(): TreeCountManager {
    if (!TreeCountManager.instance) {
      TreeCountManager.instance = new TreeCountManager();
    }
    return TreeCountManager.instance;
  }

  /**
   * 请求更新树状图计数
   * @param type 更新类型：'device' | 'department' | 'both'
   * @param immediate 是否立即执行
   */
  public requestUpdate(type: 'device' | 'department' | 'both' = 'both', immediate = false): void {
    console.log(`TreeCountManager: 请求更新 ${type} 树计数, immediate: ${immediate}`);

    // 添加到更新队列
    if (type === 'both') {
      this.updateQueue.add('device');
      this.updateQueue.add('department');
    } else {
      this.updateQueue.add(type);
    }

    if (immediate) {
      this.executeUpdate();
    } else {
      this.scheduleUpdate();
    }
  }

  /**
   * 调度更新（防抖）
   */
  private scheduleUpdate(): void {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }

    this.updateTimer = setTimeout(() => {
      this.executeUpdate();
    }, this.DEBOUNCE_DELAY);
  }

  /**
   * 执行更新
   */
  private async executeUpdate(): Promise<void> {
    if (this.isUpdating || this.updateQueue.size === 0) {
      return;
    }

    this.isUpdating = true;
    const updateTypes = Array.from(this.updateQueue);
    this.updateQueue.clear();

    try {
      console.log(`TreeCountManager: 开始执行更新 ${updateTypes.join(', ')}`);

      const inventoryService = InventoryService.getInstance();
      const departmentService = DepartmentService.getInstance();
      const inventoryList = inventoryService.getState().inventoryList;

      // 并行更新两个树的计数
      const updatePromises: Promise<any>[] = [];

      if (updateTypes.includes('device')) {
        updatePromises.push(this.updateDeviceTreeCounts(inventoryService));
      }

      if (updateTypes.includes('department')) {
        updatePromises.push(this.updateDepartmentTreeCounts(departmentService, inventoryList));
      }

      // 等待所有更新完成
      await Promise.allSettled(updatePromises);

      console.log('TreeCountManager: 所有计数更新完成');
    } catch (error) {
      console.error('TreeCountManager: 更新失败:', error);
    } finally {
      this.isUpdating = false;

      // 如果在更新过程中又有新的请求，继续处理
      if (this.updateQueue.size > 0) {
        this.scheduleUpdate();
      }
    }
  }

  /**
   * 更新设备分类树计数
   */
  private async updateDeviceTreeCounts(inventoryService: InventoryService): Promise<void> {
    try {
      console.log('TreeCountManager: 更新设备分类树计数');
      // 直接调用categoryService的方法，避免使用废弃的方法
      const state = inventoryService.getState();
      const categoryService = inventoryService['categoryService']; // 访问私有属性

      if (categoryService) {
        const updatedCategories = categoryService.updateCategoryTreeCounts(
          state.deviceCategories,
          state.inventoryList,
          true, // forceUpdate
          true  // immediate
        );

        if (updatedCategories) {
          inventoryService['stateService'].updateState({ deviceCategories: updatedCategories });
        }
      }
    } catch (error) {
      console.error('TreeCountManager: 更新设备分类树计数失败:', error);
    }
  }

  /**
   * 更新部门分类树计数
   */
  private async updateDepartmentTreeCounts(
    departmentService: DepartmentService, 
    inventoryList: InventoryItem[]
  ): Promise<void> {
    try {
      console.log('TreeCountManager: 更新部门分类树计数');
      departmentService.updateDepartmentTreeCounts(inventoryList);
    } catch (error) {
      console.error('TreeCountManager: 更新部门分类树计数失败:', error);
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
    this.updateQueue.clear();
    this.isUpdating = false;
  }
}

export default TreeCountManager;
