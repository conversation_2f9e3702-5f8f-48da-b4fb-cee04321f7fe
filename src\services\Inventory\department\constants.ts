/**
 * 部门模块统一常量配置
 * 整合所有重复的常量定义
 */

// ==================== API相关常量 ====================

/**
 * API调用间延迟时间（毫秒）
 */
export const API_DELAY = 100;

/**
 * API动作常量
 */
export const API_ACTIONS = {
  GET_DEPARTMENT: 'get_department',
  GET_PERSON: 'get_person',
  ADD_DEPARTMENT: 'add_department',
  ADD_PERSON: 'add_person',
  DELETE_DEPARTMENT: 'delete_department',
  DELETE_PERSON: 'delete_person',
  UPDATE_DEPARTMENT: 'update_department',
  UPDATE_PERSON: 'update_person'
} as const;

// ==================== 缓存相关常量 ====================

/**
 * 默认缓存持续时间（毫秒）
 */
export const DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5分钟

/**
 * 缓存类型常量
 */
export const CACHE_TYPES = {
  PATH_CACHE: 'pathCache',
  DEPARTMENT_PATHS_CACHE: 'departmentPathsCache',
  SAME_NAME_DEPARTMENTS_CACHE: 'sameNameDepartmentsCache',
  DEPARTMENT_PERSONS_CACHE: 'departmentPersonsCache',
  PERSON_CACHE: 'personCache'
} as const;

// ==================== 节点ID相关常量 ====================

/**
 * 根节点ID
 */
export const ROOT_NODE_ID = 'all-dept';

/**
 * 节点ID前缀
 */
export const NODE_ID_PREFIXES = {
  DEPARTMENT: 'dept-',
  PERSON: 'person-'
} as const;

// ==================== 请求相关常量 ====================

/**
 * 最大重试次数
 */
export const MAX_RETRIES = 10;

/**
 * 重试间隔时间（毫秒）
 */
export const RETRY_INTERVAL = 300;

// ==================== 树操作相关常量 ====================

/**
 * 路径分隔符
 */
export const PATH_SEPARATOR = '/';

/**
 * 排序规则
 */
export const SORT_ORDER = {
  PERSON_FIRST: -1,
  DEPARTMENT_FIRST: 1,
  EQUAL: 0
} as const;

// ==================== 错误消息常量 ====================

/**
 * 错误消息
 */
export const ERROR_MESSAGES = {
  DEPARTMENT_NOT_FOUND: '未找到部门',
  PERSON_NOT_FOUND: '未找到人员',
  INVALID_DEPARTMENT_PATH: '无效的部门路径',
  DUPLICATE_DEPARTMENT_NAME: '同节点下的部门不允许重名',
  CANNOT_MOVE_ROOT_NODE: '不能移动根节点',
  CANNOT_MOVE_TO_CHILD: '不能将部门移动到其子部门下',
  CANNOT_ADD_PERSON_TO_ROOT: '不能在根节点下直接添加人员，请选择一个部门',
  DATA_FETCH_FAILED: '数据获取失败',
  CACHE_UPDATE_FAILED: '缓存更新失败'
} as const;

// ==================== 成功消息常量 ====================

/**
 * 成功消息
 */
export const SUCCESS_MESSAGES = {
  DEPARTMENT_ADDED: '部门添加成功',
  PERSON_ADDED: '人员添加成功',
  DEPARTMENT_DELETED: '部门删除成功',
  PERSON_DELETED: '人员删除成功',
  DEPARTMENT_UPDATED: '部门更新成功',
  PERSON_UPDATED: '人员更新成功',
  TREE_LOADED: '部门树加载完成',
  CACHE_CLEARED: '缓存清除完成'
} as const;

// ==================== 日志相关常量 ====================

/**
 * 日志前缀
 */
export const LOG_PREFIXES = {
  DATA_FETCH_SERVICE: 'DataFetchService:',
  CACHE_MANAGER: 'CacheManager:',
  TREE_UTILS: 'TreeUtils:',
  DEPARTMENT_UTILS: 'DepartmentUtils:',
  TREE_LOADER: 'DepartmentTreeLoader:',
  DEPARTMENT_SERVICE: 'DepartmentService:'
} as const;

// ==================== 类型守卫和验证 ====================

/**
 * 检查是否为部门节点ID
 */
export function isDepartmentNodeId(id: string): boolean {
  return id === ROOT_NODE_ID || id.startsWith(NODE_ID_PREFIXES.DEPARTMENT);
}

/**
 * 检查是否为人员节点ID
 */
export function isPersonNodeId(id: string): boolean {
  return id.startsWith(NODE_ID_PREFIXES.PERSON);
}

/**
 * 检查是否为根节点ID
 */
export function isRootNodeId(id: string): boolean {
  return id === ROOT_NODE_ID;
}

/**
 * 检查是否为有效的部门路径
 */
export function isValidDepartmentPath(path: string): boolean {
  if (!path || path.trim() === '') {
    return false;
  }
  
  // 检查路径格式
  const pathParts = path.split(PATH_SEPARATOR);
  return pathParts.every(part => part.trim() !== '');
}

// ==================== 工具函数 ====================

/**
 * 生成部门节点ID
 */
export function generateDepartmentNodeId(departmentId: number): string {
  return `${NODE_ID_PREFIXES.DEPARTMENT}${departmentId}`;
}

/**
 * 生成人员节点ID
 */
export function generatePersonNodeId(personId: number, departmentId?: number): string {
  if (departmentId !== undefined) {
    return `${NODE_ID_PREFIXES.PERSON}${personId}-${NODE_ID_PREFIXES.DEPARTMENT}${departmentId}`;
  }
  return `${NODE_ID_PREFIXES.PERSON}${personId}`;
}

/**
 * 从节点ID中提取原始ID
 */
export function extractOriginalId(nodeId: string): number | null {
  if (isDepartmentNodeId(nodeId) && nodeId !== ROOT_NODE_ID) {
    const match = nodeId.match(/^dept-(\d+)$/);
    return match ? parseInt(match[1], 10) : null;
  }
  
  if (isPersonNodeId(nodeId)) {
    const match = nodeId.match(/^person-(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  }
  
  return null;
}

/**
 * 格式化日志消息
 */
export function formatLogMessage(prefix: string, message: string): string {
  return `${prefix} ${message}`;
}

// ==================== 类型定义 ====================

/**
 * API动作类型
 */
export type ApiAction = typeof API_ACTIONS[keyof typeof API_ACTIONS];

/**
 * 缓存类型
 */
export type CacheType = typeof CACHE_TYPES[keyof typeof CACHE_TYPES];

/**
 * 节点ID前缀类型
 */
export type NodeIdPrefix = typeof NODE_ID_PREFIXES[keyof typeof NODE_ID_PREFIXES];

/**
 * 日志前缀类型
 */
export type LogPrefix = typeof LOG_PREFIXES[keyof typeof LOG_PREFIXES];
