import { useEffect, RefObject } from 'react';

/**
 * 统一的点击外部关闭Hook
 * 当用户点击指定元素外部时触发回调函数
 * 
 * @param ref - 要监听的元素引用
 * @param handler - 点击外部时的回调函数
 * @param enabled - 是否启用监听，默认为true
 */
export const useClickOutside = (
  ref: RefObject<HTMLElement>,
  handler: () => void,
  enabled: boolean = true
) => {
  useEffect(() => {
    if (!enabled) return;

    const handleClickOutside = (event: MouseEvent) => {
      // 如果ref不存在或者点击的是ref内部元素，则不触发
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler, enabled]);
};

/**
 * 支持多个ref的点击外部关闭Hook
 * 当用户点击所有指定元素外部时触发回调函数
 * 
 * @param refs - 要监听的元素引用数组
 * @param handler - 点击外部时的回调函数
 * @param enabled - 是否启用监听，默认为true
 */
export const useClickOutsideMultiple = (
  refs: RefObject<HTMLElement>[],
  handler: () => void,
  enabled: boolean = true
) => {
  useEffect(() => {
    if (!enabled) return;

    const handleClickOutside = (event: MouseEvent) => {
      // 检查是否点击在任何一个ref内部
      const isClickInside = refs.some(ref => 
        ref.current && ref.current.contains(event.target as Node)
      );

      // 如果点击在所有ref外部，则触发回调
      if (!isClickInside) {
        handler();
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [refs, handler, enabled]);
};

export default useClickOutside;
