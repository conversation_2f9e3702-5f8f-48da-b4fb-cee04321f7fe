import React, { useState, useEffect } from 'react';

interface CustomTooltipProps {
  content: string;
  children: React.ReactNode;
  className?: string;
  delay?: number; // 延迟显示时间（毫秒）
  placement?: 'top' | 'bottom' | 'left' | 'right';
}

/**
 * 自定义悬浮提示组件
 * 提供优雅的悬停提示效果，支持多种位置和自定义样式
 */
const CustomTooltip: React.FC<CustomTooltipProps> = ({
  content,
  children,
  className = '',
  delay = 300,
  placement = 'top'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [showTimer, setShowTimer] = useState<NodeJS.Timeout | null>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 添加全局动画样式
  useEffect(() => {
    // 检查是否已经添加了动画样式
    if (!document.getElementById('custom-tooltip-animation-style')) {
      const style = document.createElement('style');
      style.id = 'custom-tooltip-animation-style';
      style.innerHTML = `
        @keyframes tooltipFadeIn {
          from {
            opacity: 0;
            transform: translateY(4px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .tooltip-animation {
          animation: tooltipFadeIn 0.2s ease-out;
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      // 清理函数，组件卸载时移除样式
      const styleElement = document.getElementById('custom-tooltip-animation-style');
      if (styleElement) {
        styleElement.parentNode?.removeChild(styleElement);
      }
    };
  }, []);

  // 处理鼠标进入
  const handleMouseEnter = (e: React.MouseEvent) => {
    // 清除之前的定时器
    if (showTimer) {
      clearTimeout(showTimer);
    }

    // 获取容器元素的位置信息
    const container = containerRef.current;
    if (!container) return;

    const rect = container.getBoundingClientRect();
    const tooltipWidth = 120; // 预估提示框宽度
    const tooltipHeight = 36; // 预估提示框高度
    const offset = 8; // 与目标元素的距离

    let x = 0;
    let y = 0;

    // 根据placement计算位置
    switch (placement) {
      case 'top':
        x = rect.left + rect.width / 2 - tooltipWidth / 2;
        y = rect.top - tooltipHeight - offset;
        break;
      case 'bottom':
        x = rect.left + rect.width / 2 - tooltipWidth / 2;
        y = rect.bottom + offset;
        break;
      case 'left':
        x = rect.left - tooltipWidth - offset;
        y = rect.top + rect.height / 2 - tooltipHeight / 2;
        break;
      case 'right':
        x = rect.right + offset;
        y = rect.top + rect.height / 2 - tooltipHeight / 2;
        break;
    }

    // 确保不超出视口边界
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (x < 10) x = 10;
    if (x + tooltipWidth > viewportWidth - 10) x = viewportWidth - tooltipWidth - 10;
    if (y < 10) y = 10;
    if (y + tooltipHeight > viewportHeight - 10) y = viewportHeight - tooltipHeight - 10;

    // 设置延迟显示
    const timer = setTimeout(() => {
      setPosition({ x, y });
      setIsVisible(true);
    }, delay);

    setShowTimer(timer);
  };

  // 处理鼠标离开
  const handleMouseLeave = () => {
    // 清除显示定时器
    if (showTimer) {
      clearTimeout(showTimer);
      setShowTimer(null);
    }
    setIsVisible(false);
  };



  return (
    <>
      <div
        ref={containerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={`inline-block ${className}`}
      >
        {children}
      </div>

      {/* 悬停提示框 */}
      {isVisible && (
        <div
          className="fixed z-50 px-3 py-2 text-sm text-white bg-gray-700 rounded-lg shadow-lg pointer-events-none tooltip-animation"
          style={{
            left: position.x + 'px',
            top: position.y + 'px',
            maxWidth: '200px',
            whiteSpace: 'nowrap',
            fontSize: '13px',
            fontWeight: '500',
          }}
        >
          {content}
        </div>
      )}
    </>
  );
};

export default CustomTooltip;
