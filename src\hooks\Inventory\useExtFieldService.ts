import { useState, useEffect, useCallback } from 'react';
import ExtFieldService from '../../services/Inventory/extFieldService';
import { ExtFieldDefinition } from '../../types/inventory';

/**
 * 扩展字段服务Hook
 * 提供扩展字段相关的状态和方法
 */
export default function useExtFieldService() {
  const extFieldService = ExtFieldService.getInstance();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [categoryExtFields, setCategoryExtFields] = useState<Record<string, ExtFieldDefinition[]>>(
    extFieldService.getState().categoryExtFields
  );
  const [currentExtFields, setCurrentExtFields] = useState<ExtFieldDefinition[]>(
    extFieldService.getState().currentExtFields
  );
  const [formExtFields, setFormExtFields] = useState<ExtFieldDefinition[]>(
    extFieldService.getState().formExtFields
  );
  const [allExtFields, setAllExtFields] = useState<ExtFieldService['state']['allExtFields']>(
    extFieldService.getState().allExtFields
  );

  // 获取指定分类的扩展字段定义
  const getCategoryExtFields = useCallback(async (
    categoryType: string,
    categoryName: string
  ): Promise<ExtFieldDefinition[]> => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await extFieldService.getCategoryExtFields(categoryType, categoryName);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取扩展字段失败';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [extFieldService]);

  // 设置当前选中分类的扩展字段（用于表格显示）
  const setExtFields = useCallback((extFields: ExtFieldDefinition[]): void => {
    extFieldService.setCurrentExtFields(extFields);
  }, [extFieldService]);

  // 清除当前扩展字段（用于表格显示）
  const clearExtFields = useCallback((): void => {
    extFieldService.clearCurrentExtFields();
  }, [extFieldService]);

  // 设置表单扩展字段（用于添加/修改台账对话框）
  const updateFormExtFields = useCallback((extFields: ExtFieldDefinition[]): void => {
    extFieldService.setFormExtFields(extFields);
  }, [extFieldService]);

  // 清除表单扩展字段（用于添加/修改台账对话框）
  const clearFormExtFields = useCallback((): void => {
    extFieldService.clearFormExtFields();
  }, [extFieldService]);

  // 清除扩展字段缓存
  const clearExtFieldsCache = useCallback((): void => {
    extFieldService.clearExtFieldsCache();
  }, [extFieldService]);

  // 获取所有分类的扩展字段定义
  const getAllExtFields = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await extFieldService.getAllExtFields();
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取所有扩展字段失败';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [extFieldService]);

  // 验证扩展字段值
  const validateExtFields = useCallback((
    extFields: Record<string, any> = {},
    extFieldDefinitions: ExtFieldDefinition[] = []
  ): { isValid: boolean; errors: Record<string, string> } => {
    return extFieldService.validateExtFields(extFields, extFieldDefinitions);
  }, [extFieldService]);

  // 转换扩展字段为后端格式
  const convertExtFieldsToBackendFormat = useCallback((
    extFields: Record<string, any> = {}
  ): Record<string, any> => {
    return extFieldService.convertExtFieldsToBackendFormat(extFields);
  }, [extFieldService]);

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (state: any) => {
      setCategoryExtFields(state.categoryExtFields);
      setCurrentExtFields(state.currentExtFields);
      setFormExtFields(state.formExtFields);
      setAllExtFields(state.allExtFields);
      setIsLoading(state.isLoading);
    };

    const handleError = (errorMessage: string) => {
      setError(errorMessage);
    };

    const handleLoading = (loading: boolean) => {
      setIsLoading(loading);
    };

    // 监听所有扩展字段加载事件
    const handleAllExtFieldsLoaded = (allExtFields: any) => {
      setAllExtFields(allExtFields);
    };

    // 监听当前扩展字段变化事件（用于表格显示）
    const handleCurrentExtFieldsChanged = (extFields: ExtFieldDefinition[]) => {
      setCurrentExtFields(extFields);
    };

    // 监听表单扩展字段变化事件（用于添加/修改台账对话框）
    const handleFormExtFieldsChanged = (extFields: ExtFieldDefinition[]) => {
      setFormExtFields(extFields);
    };

    // 订阅事件
    extFieldService.on('state-change', handleStateChange);
    extFieldService.on('error', handleError);
    extFieldService.on('loading', handleLoading);
    extFieldService.on('all-ext-fields-loaded', handleAllExtFieldsLoaded);
    extFieldService.on('current-ext-fields-changed', handleCurrentExtFieldsChanged);
    extFieldService.on('form-ext-fields-changed', handleFormExtFieldsChanged);

    // 清理函数
    return () => {
      extFieldService.off('state-change', handleStateChange);
      extFieldService.off('error', handleError);
      extFieldService.off('loading', handleLoading);
      extFieldService.off('all-ext-fields-loaded', handleAllExtFieldsLoaded);
      extFieldService.off('current-ext-fields-changed', handleCurrentExtFieldsChanged);
      extFieldService.off('form-ext-fields-changed', handleFormExtFieldsChanged);
    };
  }, [extFieldService]);

  return {
    // 状态
    isLoading,
    error,
    categoryExtFields,
    currentExtFields,
    formExtFields,
    allExtFields,

    // 方法
    getCategoryExtFields,
    getAllExtFields,
    setExtFields,
    clearExtFields,
    setFormExtFields: updateFormExtFields,
    clearFormExtFields,
    clearExtFieldsCache,
    validateExtFields,
    convertExtFieldsToBackendFormat
  };
}
