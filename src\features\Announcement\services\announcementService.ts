import mitt, { Emitter } from 'mitt';

/**
 * 公告数据接口
 */
interface AnnouncementData {
  announcement: string;
}

/**
 * 公告响应接口
 */
interface AnnouncementResponse {
  success: boolean;
  timestamp: string;
  message: string;
  type: string;
  data: AnnouncementData;
}

/**
 * 公告服务状态接口
 */
interface AnnouncementServiceState {
  isLoading: boolean;
  announcement: string;
  error: string | null;
  lastFetchTime: number;
}

/**
 * 公告服务事件接口
 */
interface AnnouncementServiceEvents {
  'state-change': AnnouncementServiceState;
  'announcement-loaded': string;
  'error': string;
  'loading': boolean;
  [key: string]: any; // 添加索引签名以满足mitt类型约束
}

/**
 * 公告服务类
 * Service层：只负责数据获取和WebSocket通信
 */
class AnnouncementService {
  private static instance: AnnouncementService;
  private emitter: Emitter<AnnouncementServiceEvents> = mitt<AnnouncementServiceEvents>();
  private ws: WebSocket | null = null;
  private state: AnnouncementServiceState = {
    isLoading: false,
    announcement: '',
    error: null,
    lastFetchTime: 0
  };

  // WebSocket服务器配置
  private readonly WS_URL = 'ws://39.107.221.146:8765';
  private readonly CONNECTION_TIMEOUT = 10000; // 10秒连接超时
  private readonly REQUEST_TIMEOUT = 5000; // 5秒请求超时

  /**
   * 获取公告服务实例
   */
  public static getInstance(): AnnouncementService {
    if (!AnnouncementService.instance) {
      AnnouncementService.instance = new AnnouncementService();
    }
    return AnnouncementService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 获取当前状态
   */
  public getState(): AnnouncementServiceState {
    return { ...this.state };
  }

  /**
   * 更新状态
   */
  private updateState(newState: Partial<AnnouncementServiceState>): void {
    this.state = { ...this.state, ...newState };
    this.emitter.emit('state-change', this.state);
    
    if (newState.error) {
      this.emitter.emit('error', newState.error);
    }
    
    if (newState.isLoading !== undefined) {
      this.emitter.emit('loading', newState.isLoading);
    }
  }

  /**
   * 连接WebSocket服务器
   */
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log('正在连接公告服务器:', this.WS_URL);
        this.ws = new WebSocket(this.WS_URL);

        // 连接超时处理
        const connectionTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            reject(new Error('连接公告服务器超时'));
          }
        }, this.CONNECTION_TIMEOUT);

        this.ws.onopen = () => {
          clearTimeout(connectionTimeout);
          console.log('公告服务器连接成功');
          resolve();
        };

        this.ws.onerror = (error) => {
          clearTimeout(connectionTimeout);
          console.error('公告服务器连接错误:', error);
          reject(new Error('连接公告服务器失败'));
        };

        this.ws.onclose = () => {
          console.log('公告服务器连接已关闭');
          this.ws = null;
        };

      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 发送请求并等待响应
   */
  private async sendRequest(request: any): Promise<AnnouncementResponse> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket连接未建立');
    }

    return new Promise((resolve, reject) => {
      // 请求超时处理
      const requestTimeout = setTimeout(() => {
        reject(new Error('请求超时'));
      }, this.REQUEST_TIMEOUT);

      // 监听消息响应
      const messageHandler = (event: MessageEvent) => {
        try {
          const response = JSON.parse(event.data);
          console.log('收到公告服务器响应:', response);

          if (response.type === 'announcement_response') {
            clearTimeout(requestTimeout);
            this.ws?.removeEventListener('message', messageHandler);
            resolve(response);
          }
        } catch (error) {
          console.error('解析响应数据失败:', error);
        }
      };

      if (this.ws) {
        this.ws.addEventListener('message', messageHandler);
        
        // 发送请求
        console.log('发送公告请求:', request);
        this.ws.send(JSON.stringify(request));
      }
    });
  }

  /**
   * 获取公告信息
   */
  public async getAnnouncement(): Promise<string> {
    this.updateState({ isLoading: true, error: null });

    try {
      // 连接WebSocket服务器
      await this.connectWebSocket();

      // 发送获取公告请求
      const request = {
        type: "get_announcement"
      };

      const response = await this.sendRequest(request);

      // 检查响应
      if (!response.success) {
        throw new Error(response.message || '获取公告失败');
      }

      const announcement = response.data?.announcement || '';
      
      // 更新状态
      this.updateState({
        isLoading: false,
        announcement,
        lastFetchTime: Date.now()
      });

      // 触发事件
      this.emitter.emit('announcement-loaded', announcement);

      console.log('公告获取成功:', announcement);
      return announcement;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取公告失败';
      console.error('获取公告失败:', errorMessage);
      
      this.updateState({
        isLoading: false,
        error: errorMessage
      });

      throw error;
    } finally {
      // 关闭WebSocket连接
      this.closeConnection();
    }
  }

  /**
   * 关闭WebSocket连接
   */
  private closeConnection(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 事件订阅
   */
  public on<K extends keyof AnnouncementServiceEvents>(
    event: K, 
    callback: (data: AnnouncementServiceEvents[K]) => void
  ): void {
    this.emitter.on(event, callback as any);
  }

  /**
   * 取消事件订阅
   */
  public off<K extends keyof AnnouncementServiceEvents>(
    event: K, 
    callback: (data: AnnouncementServiceEvents[K]) => void
  ): void {
    this.emitter.off(event, callback as any);
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    this.closeConnection();
    this.emitter.all.clear();
  }
}

export default AnnouncementService;
