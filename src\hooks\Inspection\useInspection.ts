import { useState, useEffect, useCallback } from 'react';
import InspectionService, { InspectionServiceState } from '../../services/Inspection/inspectionService';

/**
 * 巡检管理Hook返回值接口
 */
interface UseInspectionReturn {
  // 状态
  isLoading: boolean;
  error?: string;
  activeTab: string;
  isInitialized: boolean;
  
  // 方法
  setActiveTab: (tabKey: string) => void;
  initialize: () => Promise<void>;
}

/**
 * 巡检管理Hook
 * 提供巡检管理的基础功能和状态管理
 */
export function useInspection(): UseInspectionReturn {
  // 获取服务实例
  const service = InspectionService.getInstance();
  
  // 状态
  const [state, setState] = useState<InspectionServiceState>(service.getState());
  
  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: InspectionServiceState) => {
      setState(newState);
    };
    
    // 订阅状态变化事件
    service.on('state-change', handleStateChange);
    
    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
    };
  }, [service]);
  
  // 初始化服务
  const initialize = useCallback(async () => {
    if (!state.isInitialized) {
      await service.initialize();
    }
  }, [service, state.isInitialized]);
  
  // 设置当前激活的标签页
  const setActiveTab = useCallback((tabKey: string) => {
    service.setActiveTab(tabKey);
  }, [service]);
  
  // 初始化
  useEffect(() => {
    initialize().catch(error => {
      console.error('初始化巡检服务失败:', error);
    });
  }, [initialize]);
  
  return {
    // 状态
    isLoading: state.isLoading,
    error: state.error,
    activeTab: state.activeTab,
    isInitialized: state.isInitialized,
    
    // 方法
    setActiveTab,
    initialize
  };
}

export default useInspection;
