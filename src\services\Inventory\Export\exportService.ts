import { InventoryItem } from '../../../types/inventory';
import { BaseService, BaseServiceEvents, BaseServiceState } from '../../base/baseService';
import WebSocketManager from '../../../utils/websocket';
import TaskManager from '../../../utils/taskManager';
import { Packer } from 'docx';
// 导入jspdf用于PDF生成
import 'jspdf';
// 导入XLSX库，用于生成基础Excel文件
import * as XLSX from 'xlsx';
// 导入ExcelJS库，用于生成高质量Excel文件
import ExcelJS from 'exceljs';
// 导入样式助手
import ExcelStyleHelper from './excelStyleHelper';
// 导入日期格式化工具
import { formatTimeValue, isTimeField } from '../../../utils/fieldUtils';
import PdfHelper from './pdfHelper';
import WordHelper from './wordHelper';
// 导入部门服务，用于获取部门完整路径
import DepartmentService, { DepartmentCategory } from '../departmentService';
// 导入导出设置服务，用于获取当前选中的分类
import ExportSettingsService from './exportSettingsService';

// 导入通用格式化工具
import { getStringWidth } from '../../../utils/formatUtils';

// 导出格式类型
export type ExportFormat = 'excel' | 'pdf' | 'word';

// 导出服务事件类型
export interface ExportServiceEvents extends BaseServiceEvents<ExportServiceState> {
  'export-started': { fileName: string; format: ExportFormat };
  'export-progress': { progress: number };
  'export-completed': { fileName: string; blob: Blob };
  'export-failed': { error: string };
  'send-started': { taskId: string; fileName: string };
  'send-progress': { taskId: string; progress: number };
  'send-completed': {
    taskId: string;
    fileName: string;
    outputPath?: string;
    fileType?: string
  };
  'send-failed': { taskId: string; error: string };
}

// 导出服务状态类型
export interface ExportServiceState extends BaseServiceState {
  isExporting: boolean;
  isSending: boolean;
  progress: number;
  lastExportFileName: string;
  currentTaskId: string | null;
}

/**
 * 导出服务
 * 负责将设备台账数据导出为CSV或Excel格式
 */
class ExportService extends BaseService<ExportServiceState, ExportServiceEvents> {
  private static instance: ExportService;

  // 标准字段映射常量
  private static readonly STANDARD_FIELD_MAP = new Map<string, string>([
    ['id', '序号'],
    ['parentCategory', '设备类型分类'],
    ['type', '设备类型'],
    ['securityCode', '保密编号'],
    ['securityLevel', '密级'],
    ['responsible', '责任人'],
    ['name', '名称'],
    ['model', '型号'],
    ['department', '所属部门'],
    ['location', '放置地点'],
    ['startTime', '启用时间'],
    ['status', '使用情况'],
    ['purpose', '用途'],
    ['securityRfid', '安全码']
  ]);
  private pdfOptions: {
    format: 'a3' | 'a4';
    orientation: 'landscape' | 'portrait';
  } = {
    format: 'a3',
    orientation: 'landscape'
  };

  private wordOptions: {
    format: 'a3' | 'a4';
    orientation: 'landscape' | 'portrait';
  } = {
    format: 'a3',
    orientation: 'portrait'
  };

  /**
   * 构造函数
   */
  private constructor() {
    super('ExportService', {
      isLoading: false,
      isExporting: false,
      isSending: false,
      progress: 0,
      lastExportFileName: '',
      currentTaskId: null
    });
  }

  /**
   * 判断是否为扩展字段
   * @param columnName 列名
   * @returns 是否为扩展字段
   */
  private isExtendedField(columnName: string): boolean {
    return columnName.startsWith('ext_') ||
           ['456', '2222', '333333'].includes(columnName) ||
           columnName.includes('windows操作系统') ||
           columnName.includes('飞行速度') ||
           columnName.includes('飞行高度') ||
           columnName.includes('重甲装备') ||
           columnName.includes('导弹_number') ||
           columnName.includes('可编辑字段_已修改') ||
           columnName.includes('不可编辑字段') ||
           columnName.includes('无畏契约') ||
           columnName.includes('字段名1');
  }



  /**
   * 按分类分组数据的通用方法
   * @param data 数据
   * @param categoryField 分类字段名
   * @returns 分组后的数据Map
   */
  private groupDataByCategory(data: any[], categoryField: string): Map<string, any[]> {
    const categories = new Map<string, any[]>();

    data.forEach(item => {
      const category = item[categoryField] || '未分类';

      if (!categories.has(category)) {
        categories.set(category, []);
      }

      categories.get(category)?.push(item);
    });

    return categories;
  }

  /**
   * 获取单例实例
   * @returns ExportService实例
   */
  public static getInstance(): ExportService {
    if (!ExportService.instance) {
      ExportService.instance = new ExportService();
    }
    return ExportService.instance;
  }

  /**
   * 设置PDF选项
   * @param options PDF选项
   */
  public setPdfOptions(options: { format: 'a3' | 'a4'; orientation: 'landscape' | 'portrait' }): void {
    this.pdfOptions = { ...options };
    console.log('PDF选项已更新:', this.pdfOptions);
  }

  /**
   * 获取PDF选项
   * @returns 当前PDF选项
   */
  public getPdfOptions(): { format: 'a3' | 'a4'; orientation: 'landscape' | 'portrait' } {
    return { ...this.pdfOptions };
  }

  /**
   * 设置Word选项
   * @param options Word选项
   */
  public setWordOptions(options: { format: 'a3' | 'a4'; orientation: 'landscape' | 'portrait' }): void {
    this.wordOptions = { ...options };
  }

  /**
   * 获取Word选项
   * @returns 当前Word选项
   */
  public getWordOptions(): { format: 'a3' | 'a4'; orientation: 'landscape' | 'portrait' } {
    return { ...this.wordOptions };
  }

  /**
   * 通用导出方法 - 消除重复代码
   * @param items 设备台账数据
   * @param fileName 文件名
   * @param format 导出格式
   * @param dataProcessor 数据处理函数（支持同步和异步）
   * @returns 导出的Blob对象
   */
  private async executeExport(
    items: InventoryItem[],
    fileName: string,
    format: ExportFormat,
    dataProcessor: (items: InventoryItem[]) => any[] | Promise<any[]>
  ): Promise<Blob> {
    try {
      // 更新状态
      this.updateState({
        isExporting: true,
        progress: 0,
        error: undefined
      });

      // 触发导出开始事件
      this.emitter.emit('export-started', { fileName, format });

      // 处理导出数据（支持异步）
      const exportData = await dataProcessor(items);

      // 更新进度
      this.updateState({ progress: 30 });
      this.emitter.emit('export-progress', { progress: 30 });

      // 根据格式生成不同的文件
      let blob: Blob;
      switch (format) {
        case 'excel':
          blob = await this.generateExcelFile(exportData);
          break;
        case 'pdf':
          blob = await this.generatePdfFile(exportData);
          break;
        case 'word':
          blob = await this.generateWordFile(exportData);
          break;
        default:
          throw new Error(`不支持的导出格式: ${format}`);
      }

      // 更新进度
      this.updateState({
        isExporting: false,
        progress: 100,
        lastExportFileName: fileName
      });

      // 触发导出完成事件
      this.emitter.emit('export-completed', { fileName, blob });

      return blob;
    } catch (error: any) {
      // 更新状态
      this.updateState({
        isExporting: false,
        error: error.message
      });

      // 触发错误事件
      this.emitter.emit('export-failed', { error: error.message });
      this.emitter.emit('error', error.message);

      throw error;
    }
  }

  /**
   * 导出设备台账数据
   * @param items 要导出的设备台账数据
   * @param fileName 导出文件名
   * @param format 导出文件格式
   * @returns 导出的Blob对象
   */
  public async exportInventory(
    items: InventoryItem[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> {
    return this.executeExport(items, fileName, format, (items) => this.prepareExportData(items));
  }

  /**
   * 导出导入模板
   * @param columns 列定义
   * @param fileName 文件名
   * @param format 导出格式
   * @returns 导出的Blob对象
   */
  public async exportImportTemplate(
    columns: any[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> {
    try {
      // 更新状态
      this.updateState({
        isExporting: true,
        progress: 0,
        error: undefined
      });

      // 触发导出开始事件
      this.emitter.emit('export-started', { fileName, format });

      console.log('开始生成导入模板，列数:', columns.length);

      // 准备模板数据
      const templateData = this.prepareTemplateData(columns);

      // 更新进度
      this.updateState({ progress: 50 });
      this.emitter.emit('export-progress', { progress: 50 });

      // 生成文件
      let blob: Blob;
      switch (format) {
        case 'excel':
          blob = await this.generateTemplateExcelFile(templateData, columns);
          break;
        case 'csv':
          blob = await this.generateTemplateCsvFile(templateData);
          break;
        default:
          throw new Error(`不支持的导出格式: ${format}`);
      }

      // 更新状态
      this.updateState({
        isExporting: false,
        progress: 100,
        lastExportFileName: fileName
      });

      // 触发导出完成事件
      this.emitter.emit('export-completed', { fileName, blob });

      return blob;
    } catch (error: any) {
      // 更新状态
      this.updateState({
        isExporting: false,
        error: error.message
      });

      // 触发错误事件
      this.emitter.emit('export-failed', { error: error.message });
      this.emitter.emit('error', error.message);

      throw error;
    }
  }

  /**
   * 生成Excel文件
   * @param data 导出数据
   * @param originalItems 原始设备数据（用于分类sheet）
   * @returns Blob对象
   */
  private async generateExcelFile(data: any[], originalItems?: InventoryItem[]): Promise<Blob> {
    try {
      // 确保数据有效
      if (!data || !data.length) {
        throw new Error('没有可导出的数据');
      }

      console.log('开始生成Excel文件，数据条数:', data.length);

      // 使用纯ExcelJS生成Excel文件，避免XLSX.js兼容性问题
      return await this.generateExcelWithExcelJS(data, originalItems);
    } catch (error) {
      console.error('生成Excel文件失败:', error);
      throw new Error('生成Excel文件失败: ' + (error as Error).message);
    }
  }

  /**
   * 使用纯ExcelJS生成Excel文件（避免与XLSX.js的兼容性问题）
   * @param data 导出数据
   * @param originalItems 原始设备数据
   * @returns Blob对象
   */
  private async generateExcelWithExcelJS(data: any[], originalItems?: InventoryItem[]): Promise<Blob> {
    try {
      // 创建新的工作簿
      const workbook = new ExcelJS.Workbook();

      // 获取当前选中的分类ID
      const settingsService = ExportSettingsService.getInstance();
      const selectedCategoryIds = settingsService.getState().selectedCategories;

      // 判断是否只选择了一个一级分类
      let singleParentCategory = false;
      let singleParentCategoryName = '设备台账总表'; // 默认总表名称

      if (selectedCategoryIds.length > 0) {
        // 检查是否只有一个一级分类ID (格式为 parent-X)
        const parentCategoryIds = selectedCategoryIds.filter((id: string) => id.match(/^parent-\d+$/));
        if (parentCategoryIds.length === 1) {
          singleParentCategory = true;

          // 获取一级分类名称
          if (data.length > 0) {
            const uniqueParentCategories = new Set<string>();
            data.forEach(item => {
              const parentCategory = item['设备类型分类'] || item.parentCategory;
              if (parentCategory) {
                uniqueParentCategories.add(parentCategory);
              }
            });

            if (uniqueParentCategories.size === 1) {
              singleParentCategoryName = Array.from(uniqueParentCategories)[0];
              console.log(`找到一级分类名称: ${singleParentCategoryName}`);
            } else if (uniqueParentCategories.size > 1) {
              singleParentCategoryName = data[0]['设备类型分类'] || data[0].parentCategory || '设备台账总表';
              console.log(`多个一级分类名称，使用第一个: ${singleParentCategoryName}`);
            }
          }
        }
      }

      // 获取根节点名称用于标题
      const CompanyNameHelper = (await import('../../../utils/companyNameHelper')).default;
      const rootNodeName = await CompanyNameHelper.getExportTitle('设备台账');

      // 获取列顺序
      const columnOrder = data.length > 0 ? Object.keys(data[0]) : [];
      const userColumnOrder = settingsService.getState().columnOrder;
      const sortedColumnOrder = this.getSortedColumnOrder(columnOrder, userColumnOrder);

      console.log('Excel导出使用的列顺序:', sortedColumnOrder);

      // 创建总表工作表
      await this.createExcelJSWorksheet(workbook, data, singleParentCategoryName, sortedColumnOrder, rootNodeName);

      console.log('总表工作表创建完成');

      // 2. 根据选择的分类动态创建子表
      await this.createCategoryWorksheets(workbook, data, originalItems, selectedCategoryIds, singleParentCategory, rootNodeName);

      // 生成最终的Excel文件
      const buffer = await workbook.xlsx.writeBuffer();
      return new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    } catch (error) {
      console.error('使用ExcelJS生成Excel文件失败:', error);
      throw error;
    }
  }

  /**
   * 获取排序后的列顺序
   */
  private getSortedColumnOrder(columnOrder: string[], userColumnOrder: string[]): string[] {
    // 创建映射
    const totalColumnMapping = new Map<string, string>();
    totalColumnMapping.set('id', '序号');
    totalColumnMapping.set('parentCategory', '设备类型分类');
    totalColumnMapping.set('type', '设备类型');
    totalColumnMapping.set('securityCode', '保密编号');
    totalColumnMapping.set('securityLevel', '密级');
    totalColumnMapping.set('responsible', '责任人');
    totalColumnMapping.set('name', '名称');
    totalColumnMapping.set('model', '型号');
    totalColumnMapping.set('department', '所属部门');
    totalColumnMapping.set('location', '放置地点');
    totalColumnMapping.set('startTime', '启用时间');
    totalColumnMapping.set('status', '使用情况');
    totalColumnMapping.set('purpose', '用途');
    totalColumnMapping.set('securityRfid', '安全码');

    // 扩展字段的映射
    userColumnOrder.forEach(colId => {
      if (colId.startsWith('ext_')) {
        const extFieldKey = colId.substring(4);
        totalColumnMapping.set(colId, extFieldKey);
      }
    });

    // 根据用户定义的列顺序重新排序列
    const sortedColumnOrder: string[] = [];
    userColumnOrder.forEach(colId => {
      const mappedColName = totalColumnMapping.get(colId);
      if (mappedColName && columnOrder.includes(mappedColName)) {
        sortedColumnOrder.push(mappedColName);
      }
    });

    // 添加任何未包含的列
    columnOrder.forEach(colName => {
      if (!sortedColumnOrder.includes(colName)) {
        sortedColumnOrder.push(colName);
      }
    });

    return sortedColumnOrder;
  }







  /**
   * 生成PDF文件
   * @param data 导出数据
   * @returns Blob对象
   */
  private async generatePdfFile(data: any[]): Promise<Blob> {
    try {
      // 确保数据有效
      if (!data || !data.length) {
        throw new Error('没有可导出的数据');
      }

      // 添加调试日志，检查数据中是否包含扩展字段
      console.log('PDF导出数据示例:', data[0]);
      console.log('PDF导出数据字段:', Object.keys(data[0]));
      console.log('PDF导出数据总行数:', data.length);
      console.log('PDF导出数据总字段数:', Object.keys(data[0]).length);

      // 获取根节点名称
      const CompanyNameHelper = (await import('../../../utils/companyNameHelper')).default;
      const companyName = await CompanyNameHelper.getExportTitle('设备台账');
      const rootNodeName = companyName !== '设备台账' ? `${companyName}设备台账` : '设备台账';
      console.log('PDF导出使用根节点名称作为标题:', rootNodeName);

      // 配置PDF导出选项 - 使用用户设置的选项
      const pdfOptions = {
        format: this.pdfOptions.format, // 使用用户选择的纸张大小
        orientation: this.pdfOptions.orientation, // 使用用户选择的页面方向
        scale: 2, // 高清晰度
        containerWidth: this.pdfOptions.format === 'a3' ? 2200 : 1800, // 根据纸张大小调整容器宽度
        autoSize: true, // 启用自动调整大小，确保内容完全显示
        allowMultiplePages: true // 启用多页显示，不再限制在一页内
      };

      console.log('使用的PDF选项:', pdfOptions);

      // 使用PdfHelper生成PDF，并传入配置选项
      return await PdfHelper.generatePdfFromData(data, rootNodeName, pdfOptions);
    } catch (error) {
      console.error('生成PDF文件失败:', error);
      throw new Error('生成PDF文件失败: ' + (error as Error).message);
    }
  }

  /**
   * 生成Word文件
   * @param data 导出数据
   * @returns Blob对象
   */
  private async generateWordFile(data: any[]): Promise<Blob> {
    try {
      // 确保数据有效
      if (!data || !data.length) {
        throw new Error('没有可导出的数据');
      }

      // 获取根节点名称
      const CompanyNameHelper = (await import('../../../utils/companyNameHelper')).default;
      const companyName = await CompanyNameHelper.getExportTitle('设备台账');
      const rootNodeName = companyName !== '设备台账' ? `${companyName}设备台账` : '设备台账';
      console.log('Word导出使用根节点名称作为标题:', rootNodeName);

      // 创建文档
      const doc = WordHelper.createDocument();

      // 添加标题
      WordHelper.addTitle(doc, rootNodeName);

      // 准备表格数据
      const headers = Object.keys(data[0]);

      // 创建表格并添加到文档 - 使用用户设置的页面选项（页脚已集成）
      WordHelper.createTableAndAddToDocument(doc, headers, data, this.wordOptions);

      // 导出为Word
      return await Packer.toBlob(doc);
    } catch (error) {
      console.error('生成Word文件失败:', error);
      throw new Error('生成Word文件失败: ' + (error as Error).message);
    }
  }

  /**
   * 发送文件到服务器
   * @param blob 文件Blob对象
   * @param fileName 文件名
   * @param format 文件格式
   * @returns 任务ID
   */
  public async sendToServer(
    blob: Blob,
    fileName: string,
    format: ExportFormat
  ): Promise<string> {
    try {
      // 检查WebSocket连接状态
      const ws = WebSocketManager.getInstance();
      if (!ws.isConnected()) {
        throw new Error('WebSocket未连接，无法发送文件到服务器');
      }

      // 更新状态
      this.updateState({
        isSending: true,
        progress: 0,
        error: undefined
      });

      // 将Blob转换为Base64
      const base64Data = await this.blobToBase64(blob);

      // 将格式转换为fileType参数
      let fileType: string;
      switch (format) {
        case 'excel':
          fileType = 'xlsx';
          break;
        case 'pdf':
          fileType = 'pdf';
          break;
        case 'word':
          fileType = 'docx';
          break;
        default:
          fileType = 'xlsx';
      }

      // 构建请求参数 - 使用新的API格式
      const params = {
        data: base64Data,
        fileType: fileType
      };



      // 获取任务管理器
      const task = TaskManager.getInstance();

      // 提交任务 - 使用新的DLL名称和函数
      const taskId = await task.submitTask('ExportDll', 'ProcessBase64ToXlsx', params);

      // 更新状态
      this.updateState({ currentTaskId: taskId });

      // 触发发送开始事件
      this.emitter.emit('send-started', { taskId, fileName });

      // 监听任务进度
      task.onTaskUpdate(taskId, async (taskInfo) => {
        // 更新进度
        if (taskInfo.progress !== undefined) {
          this.updateState({ progress: taskInfo.progress });
          this.emitter.emit('send-progress', {
            taskId,
            progress: taskInfo.progress
          });
        }

        // 处理任务完成
        if (taskInfo.status === 'completed') {


          // 解析返回结果 - 支持JSON字符串格式
          let result: any = {};

          try {
            // 如果result是字符串，尝试解析为JSON
            if (typeof taskInfo.result === 'string') {
              result = JSON.parse(taskInfo.result);
              console.log('解析JSON格式的任务结果:', result);
            } else {
              result = taskInfo.result || {};
            }
          } catch (error) {
            console.warn('解析任务结果JSON失败，使用原始值:', error);
            result = taskInfo.result || {};
          }

          // 检查结果是否包含success字段，如果没有，默认为成功
          // 因为任务状态为completed，通常意味着任务已成功完成
          const isSuccess = result.success !== false; // 只有明确为false时才认为失败

          if (isSuccess) {
            // 成功处理
            this.updateState({
              isSending: false,
              progress: 100,
              currentTaskId: null
            });

            // 获取输出路径和文件类型
            let outputPath = result.outputPath || '文件已保存到服务器';
            let fileType = result.fileType || '未知';

            console.log('提取的导出信息:', { outputPath, fileType });

            // 向后端发送导入导出日志记录
            try {
              await this.sendExportLogToBackend(outputPath, fileType);
            } catch (logError) {
              console.warn('发送导出日志失败:', logError);
              // 日志发送失败不影响导出成功的状态
            }

            // 触发完成事件，包含输出路径和文件类型
            this.emitter.emit('send-completed', {
              taskId,
              fileName,
              outputPath,
              fileType
            });


          } else {
            // API返回失败
            const errorMessage = result.message || '服务器处理文件失败';
            this.updateState({
              isSending: false,
              error: errorMessage,
              currentTaskId: null
            });

            this.emitter.emit('send-failed', {
              taskId,
              error: errorMessage
            });


          }

          task.offTaskUpdate(taskId, () => {});
        }

        // 处理任务失败（WebSocket或任务执行失败）
        if (taskInfo.status === 'failed') {
          const errorMessage = taskInfo.error || '发送文件到服务器失败';
          this.updateState({
            isSending: false,
            error: errorMessage,
            currentTaskId: null
          });

          this.emitter.emit('send-failed', {
            taskId,
            error: errorMessage
          });


          task.offTaskUpdate(taskId, () => {});
        }
      });

      return taskId;
    } catch (error: any) {
      // 更新状态
      this.updateState({
        isSending: false,
        error: error.message
      });

      // 触发错误事件
      this.emitter.emit('error', error.message);

      throw error;
    }
  }

  /**
   * 按分类筛选设备台账数据
   * @param items 设备台账数据
   * @param categories 分类树
   * @param selectedCategoryIds 选中的分类ID
   * @returns 筛选后的设备台账数据
   */
  public async filterItemsByCategories(
    items: InventoryItem[],
    categories: any[],
    selectedCategoryIds: string[]
  ): Promise<InventoryItem[]> {
    try {
      // 添加调试日志
      console.log('筛选前数据条数:', items.length);
      console.log('选中的分类ID:', selectedCategoryIds);

      // 如果没有选中任何分类，返回所有数据
      if (!selectedCategoryIds || selectedCategoryIds.length === 0) {
        console.log('没有选中任何分类，返回所有数据');
        return [...items];
      }

      // 如果选中了"全部"分类，返回所有数据
      if (selectedCategoryIds.includes('all') || selectedCategoryIds.includes('all-dept')) {
        console.log('选中了根节点，返回所有数据');
        return [...items];
      }

      // 递归查找所有子分类ID
      const findAllChildCategories = (categoryId: string, allCategories: any[]): string[] => {
        const result: string[] = [];

        // 查找指定ID的分类
        const findCategory = (cats: any[]): any => {
          for (const cat of cats) {
            if (cat.id === categoryId) return cat;
            if (cat.children?.length) {
              const found = findCategory(cat.children);
              if (found) return found;
            }
          }
          return null;
        };

        const category = findCategory(allCategories);
        if (!category) return result;

        // 添加当前分类ID
        result.push(category.id);

        // 递归添加所有子分类ID
        if (category.children?.length) {
          category.children.forEach((child: any) => {
            result.push(...findAllChildCategories(child.id, allCategories));
          });
        }

        return result;
      };

      // 获取所有选中分类及其子分类的ID
      const allSelectedCategoryIds = new Set<string>();
      selectedCategoryIds.forEach(id => {
        allSelectedCategoryIds.add(id);
        findAllChildCategories(id, categories).forEach(childId => {
          allSelectedCategoryIds.add(childId);
        });
      });

      console.log('所有选中的分类及子分类ID:', Array.from(allSelectedCategoryIds));

      // 筛选符合条件的设备
      const filteredItems: InventoryItem[] = [];

      // 设备分类模式
      if (categories[0]?.id === 'all') {
        // 添加调试信息，查看数据中的分类ID格式
        if (items.length > 0) {
          const sampleItem = items[0];
          console.log('样本数据:', {
            id: sampleItem.id,
            parentCategoryId: sampleItem.parentCategoryId,
            subCategoryId: sampleItem.subCategoryId,
            categoryId: sampleItem.categoryId,
            parentCategory: sampleItem.parentCategory,
            type: sampleItem.type
          });
        }

        // 创建映射，分别存储选中的一级分类ID和二级分类ID
        const selectedParentIds = new Set<number>();
        const selectedSubIds = new Set<number>();

        // 记录是否选择了一级分类
        let hasSelectedParent = false;

        selectedCategoryIds.forEach(id => {
          // 尝试提取数字部分
          const match = id.match(/parent-(\d+)(?:-(\d+))?/);
          if (match) {
            if (match[2]) {
              // 这是二级分类ID，如 parent-1-2 中的 2
              selectedSubIds.add(Number(match[2]));
            } else {
              // 这是一级分类ID，如 parent-1 中的 1
              selectedParentIds.add(Number(match[1]));
              hasSelectedParent = true;
            }
          } else {
            // 尝试直接转换为数字
            const numId = Number(id);
            if (!isNaN(numId)) {
              selectedSubIds.add(numId);
            }
          }
        });

        console.log('选中的一级分类ID:', Array.from(selectedParentIds));
        console.log('选中的二级分类ID:', Array.from(selectedSubIds));

        // 筛选符合条件的设备
        items.forEach(item => {
          let shouldInclude = false;

          // 如果选择了一级分类，则检查设备的一级分类ID
          if (hasSelectedParent && item.parentCategoryId !== undefined) {
            const parentId = Number(item.parentCategoryId);
            if (selectedParentIds.has(parentId)) {
              // 如果设备的一级分类ID在选中的一级分类ID中，则包含该设备
              shouldInclude = true;
              console.log(`匹配到设备 ${item.id} 的一级分类ID: ${item.parentCategoryId}`);
            }
          }

          // 如果选择了二级分类，则检查设备的二级分类ID
          if (selectedSubIds.size > 0 && item.subCategoryId !== undefined) {
            const subId = Number(item.subCategoryId);
            if (selectedSubIds.has(subId)) {
              // 如果设备的二级分类ID在选中的二级分类ID中，则包含该设备
              shouldInclude = true;
              console.log(`匹配到设备 ${item.id} 的二级分类ID: ${item.subCategoryId}`);
            }
          }

          // 如果设备应该被包含，则添加到结果中
          if (shouldInclude) {
            filteredItems.push(item);
          }
        });

        console.log(`筛选后找到 ${filteredItems.length} 条符合条件的数据`);

        // 如果没有找到任何匹配的数据，返回所有数据
        if (filteredItems.length === 0) {
          console.log('没有找到匹配的数据，返回所有数据');
          return [...items];
        }
      }
      // 部门分类模式
      else if (categories[0]?.id === 'all-dept') {
        // 添加调试信息，查看数据中的部门ID格式
        if (items.length > 0) {
          const sampleItem = items[0];
          console.log('样本数据(部门):', {
            id: sampleItem.id,
            departmentId: sampleItem.departmentId,
            responsibleId: sampleItem.responsibleId,
            department: sampleItem.department,
            responsible: sampleItem.responsible
          });
        }

        // 创建一个映射，将选中的部门ID转换为数字
        const selectedDeptIds = new Set<number>();
        const selectedPersonIds = new Set<number>();

        selectedCategoryIds.forEach(id => {
          // 尝试提取部门ID
          const deptMatch = id.match(/dept-(\d+)/);
          if (deptMatch) {
            selectedDeptIds.add(Number(deptMatch[1]));
          }

          // 尝试提取人员ID
          const personMatch = id.match(/person-(\d+)/);
          if (personMatch) {
            selectedPersonIds.add(Number(personMatch[1]));
          }
        });

        console.log('转换后的部门ID:', Array.from(selectedDeptIds));
        console.log('转换后的人员ID:', Array.from(selectedPersonIds));

        // 筛选符合条件的设备
        items.forEach(item => {
          let shouldInclude = false;

          // 检查设备的部门ID
          if (item.departmentId !== undefined && selectedDeptIds.has(Number(item.departmentId))) {
            shouldInclude = true;
            console.log(`匹配到设备 ${item.id} 的部门ID: ${item.departmentId}`);
          }

          // 检查设备的责任人ID
          if (item.responsibleId !== undefined && selectedPersonIds.has(Number(item.responsibleId))) {
            shouldInclude = true;
            console.log(`匹配到设备 ${item.id} 的责任人ID: ${item.responsibleId}`);
          }

          // 如果设备应该被包含，则添加到结果中
          if (shouldInclude) {
            filteredItems.push(item);
          }
        });

        console.log(`筛选后找到 ${filteredItems.length} 条符合条件的数据`);

        // 如果没有找到任何匹配的数据，返回所有数据
        if (filteredItems.length === 0) {
          console.log('没有找到匹配的数据，返回所有数据');
          return [...items];
        }
      }

      // 如果筛选后没有数据，返回空数组，而不是所有数据
      if (filteredItems.length === 0) {
        console.log('筛选后没有数据，返回空数组');
        return [];
      }

      // 添加调试日志
      console.log('筛选后数据条数:', filteredItems.length);
      return filteredItems;

    } catch (error) {
      console.error('按分类筛选设备台账数据失败:', error);
      // 出错时返回原始数据
      return [...items];
    }
  }

  /**
   * 按分类导出设备台账数据
   * @param items 设备台账数据
   * @param categories 分类树
   * @param selectedCategoryIds 选中的分类ID
   * @param fileName 文件名
   * @param format 导出格式
   * @returns 导出的Blob对象
   */
  public async exportByCategories(
    items: InventoryItem[],
    categories: any[],
    selectedCategoryIds: string[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> {
    return this.executeExport(items, fileName, format, async (items) => {
      // 按分类筛选数据
      const filteredItems = await this.filterItemsByCategories(items, categories, selectedCategoryIds);

      // 检查筛选后的数据是否为空
      if (filteredItems.length === 0) {
        throw new Error('没有找到符合所选分类的数据');
      }

      return this.prepareExportData(filteredItems);
    });
  }

  /**
   * 使用自定义列导出设备台账数据
   * @param items 设备台账数据
   * @param columns 列定义
   * @param selectedColumnIds 选中的列ID
   * @param columnOrder 列顺序
   * @param fileName 文件名
   * @param format 导出格式
   * @returns 导出的Blob对象
   */
  public async exportWithCustomColumns(
    items: InventoryItem[],
    columns: any[],
    selectedColumnIds: string[],
    columnOrder: string[],
    fileName: string,
    format: ExportFormat
  ): Promise<Blob> {
    try {
      // 更新状态
      this.updateState({
        isExporting: true,
        progress: 0,
        error: undefined
      });

      // 触发导出开始事件
      this.emitter.emit('export-started', { fileName, format });

      // 如果没有选中任何列，使用所有列
      if (!selectedColumnIds || selectedColumnIds.length === 0) {
        selectedColumnIds = columns.map(col => col.key);
      }

      // 如果没有指定列顺序，使用选中列的顺序
      if (!columnOrder || columnOrder.length === 0) {
        columnOrder = [...selectedColumnIds];
      }

      // 确保列顺序中只包含选中的列
      columnOrder = columnOrder.filter(colId => selectedColumnIds.includes(colId));

      // 确保所有选中的列都在列顺序中
      selectedColumnIds.forEach(colId => {
        if (!columnOrder.includes(colId)) {
          columnOrder.push(colId);
        }
      });

      // 更新进度
      this.updateState({ progress: 20 });
      this.emitter.emit('export-progress', { progress: 20 });

      // 检查数据是否为空
      if (items.length === 0) {
        throw new Error('没有找到符合所选分类的数据');
      }

      // 获取部门服务实例，用于处理部门路径
      const departmentService = DepartmentService.getInstance();

      // 使用通用方法构建部门映射
      const { departmentNameMap, departmentIdMap, departmentNameNormalizedMap } = this.buildDepartmentMaps();

      console.log('自定义列导出 - 部门名称映射数量:', departmentNameMap.size);

      // 准备导出数据 - 只包含选中的列，并按指定顺序排列
      const exportData = items.map((item, index) => {
        // 使用Map而不是普通对象，以保证属性顺序
        const exportItemMap = new Map<string, any>();

        // 按列顺序添加字段
        columnOrder.forEach(colId => {
          // 跳过选择列和操作列
          if (colId === 'select' || colId === 'actions') {
            return;
          }

          // 获取列定义
          const column = columns.find(col => col.key === colId);
          if (!column) return;

          // 获取列标题作为导出字段名 - 对于扩展字段，去掉ext_前缀
          let fieldName = column.title;

          // 如果是扩展字段，使用不带前缀的标题
          if (colId.startsWith('ext_')) {
            // 使用不带ext_前缀的字段名作为标题
            const extFieldKey = colId.substring(4); // 移除"ext_"前缀
            fieldName = extFieldKey;

            // 调试日志
            if (index < 2) {
              console.log(`处理扩展字段标题 - 原始ID: ${colId}, 处理后标题: ${fieldName}`);
            }
          }

          // 获取字段值
          let fieldValue;

          // 特殊处理所属部门字段，使用完整路径
          if (colId === 'department' && column.title === '所属部门') {
            // 使用通用方法获取部门路径
            fieldValue = this.getDepartmentPath(item, departmentNameMap, departmentIdMap, departmentNameNormalizedMap, departmentService);

            // 如果是前5个项目，打印调试信息
            if (index < 5) {
              console.log(`自定义列导出 - 设备${index}部门信息:`, {
                id: item.id,
                department: item.department,
                departmentId: item.departmentId,
                完整路径: fieldValue
              });
            }
          }
          // 处理扩展字段
          else if (colId.startsWith('ext_')) {
            const extFieldKey = colId.substring(4); // 移除"ext_"前缀
            fieldValue = item.extendedFields?.[extFieldKey] ?? '';

            // 调试日志
            if (index < 2) {
              console.log(`导出扩展字段 - ID: ${colId}, 键: ${extFieldKey}, 标题: ${fieldName}, 值: ${fieldValue}`);
            }

            // 对扩展字段中的日期字段进行格式化 - 显示时分秒
            if (isTimeField(extFieldKey) || isTimeField(fieldName)) {
              fieldValue = formatTimeValue(fieldValue, true, true); // showTime=true, showSeconds=true
            }
          } else {
            // 处理其他标准字段
            fieldValue = item[colId as keyof InventoryItem] ?? '';

            // 对日期字段进行格式化 - 基础字段只显示日期
            if (colId === 'startTime' || isTimeField(fieldName)) {
              fieldValue = formatTimeValue(fieldValue, false); // showTime=false，只显示日期
            }
          }

          // 添加到导出项Map
          exportItemMap.set(fieldName, fieldValue);
        });

        // 将Map转换为普通对象，保持顺序
        const exportItem: Record<string, any> = {};
        exportItemMap.forEach((value, key) => {
          exportItem[key] = value;
        });

        return exportItem;
      });

      // 更新进度
      this.updateState({ progress: 40 });
      this.emitter.emit('export-progress', { progress: 40 });

      // 根据格式生成不同的文件
      let blob: Blob;
      switch (format) {
        case 'excel':
          blob = await this.generateExcelFile(exportData, items); // 传递原始数据
          break;
        case 'pdf':
          blob = await this.generatePdfFile(exportData);
          break;
        case 'word':
          blob = await this.generateWordFile(exportData);
          break;
        default:
          throw new Error(`不支持的导出格式: ${format}`);
      }

      // 更新进度
      this.updateState({
        isExporting: false,
        progress: 100,
        lastExportFileName: fileName
      });

      // 触发导出完成事件
      this.emitter.emit('export-completed', { fileName, blob });

      return blob;
    } catch (error: any) {
      // 更新状态
      this.updateState({
        isExporting: false,
        error: error.message
      });

      // 触发错误事件
      this.emitter.emit('export-failed', { error: error.message });
      this.emitter.emit('error', error.message);

      throw error;
    }
  }

  /**
   * 将Blob转换为Base64
   * @param blob Blob对象
   * @returns Base64字符串
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const dataUrl = reader.result as string;
        const base64 = dataUrl.split(',')[1];
        resolve(base64);
      };
      reader.onerror = () => {
        reject(new Error('转换文件为Base64失败'));
      };
      reader.readAsDataURL(blob);
    });
  }

  /**
   * 取消当前任务
   */
  public cancelTask(): void {
    const taskId = this.getState().currentTaskId;
    if (taskId) {
      const task = TaskManager.getInstance();
      task.cancelTask(taskId);
      this.updateState({
        isExporting: false,
        isSending: false,
        progress: 0,
        currentTaskId: null
      });
    }
  }

  /**
   * 重置导出状态
   */
  public resetState(): void {
    this.updateState({
      isExporting: false,
      isSending: false,
      progress: 0,
      currentTaskId: null,
      error: undefined
    });
  }

  /**
   * 创建字段映射 - 消除重复代码
   * @param extFieldKeys 扩展字段键集合
   * @returns 字段ID到标题的映射
   */
  private createFieldMapping(extFieldKeys: Set<string>): Map<string, string> {
    // 复制标准字段映射
    const fieldIdToTitle = new Map(ExportService.STANDARD_FIELD_MAP);

    // 添加扩展字段到映射
    extFieldKeys.forEach(key => {
      fieldIdToTitle.set(`ext_${key}`, key);
    });

    return fieldIdToTitle;
  }

  /**
   * 构建部门映射关系 - 消除重复代码
   * @returns 部门映射对象
   */
  private buildDepartmentMaps(): {
    departmentNameMap: Map<string, DepartmentCategory>;
    departmentIdMap: Map<string, DepartmentCategory>;
    departmentNameNormalizedMap: Map<string, DepartmentCategory>;
  } {
    const departmentService = DepartmentService.getInstance();
    const departmentCategories = departmentService.getState().departmentCategories;

    const departmentNameMap = new Map<string, DepartmentCategory>();
    const departmentIdMap = new Map<string, DepartmentCategory>();
    const departmentNameNormalizedMap = new Map<string, DepartmentCategory>();

    // 递归构建部门映射
    const buildMaps = (categories: DepartmentCategory[]) => {
      for (const category of categories) {
        // 只处理部门节点
        if (category.id === 'all-dept' || category.id.startsWith('dept-')) {
          // 保存部门名称到节点的映射
          departmentNameMap.set(category.name, category);

          // 保存规范化的部门名称到节点的映射（小写无空格）
          const normalizedName = category.name.toLowerCase().trim();
          departmentNameNormalizedMap.set(normalizedName, category);

          // 保存部门ID到节点的映射
          if (category.id.startsWith('dept-')) {
            const deptId = category.id.substring(5); // 移除"dept-"前缀
            departmentIdMap.set(deptId, category);
            departmentIdMap.set(`dept-${deptId}`, category); // 同时保存带前缀的格式
          }

          // 递归处理子部门
          if (category.children?.length) {
            buildMaps(category.children);
          }
        }
      }
    };

    buildMaps(departmentCategories);

    return {
      departmentNameMap,
      departmentIdMap,
      departmentNameNormalizedMap
    };
  }

  /**
   * 为特定分类的设备准备导出数据 - 只包含该分类的扩展字段
   * @param items 设备数据
   * @param categoryName 分类名称（用于调试）
   * @returns 导出格式的数据
   */
  private prepareExportDataForCategory(items: InventoryItem[], categoryName?: string): any[] {
    // 收集该分类设备的扩展字段名称（只收集当前分类的）
    const categoryExtFieldKeys = new Set<string>();
    items.forEach(item => {
      if (item.extendedFields) {
        Object.keys(item.extendedFields).forEach(key => {
          categoryExtFieldKeys.add(key);
        });
      }
    });

    console.log(`${categoryName || ''}分类扩展字段数量:`, categoryExtFieldKeys.size);
    if (categoryExtFieldKeys.size > 0) {
      console.log(`${categoryName || ''}分类扩展字段:`, Array.from(categoryExtFieldKeys));
    }

    // 使用通用方法准备导出数据，只传递该分类的扩展字段
    return this.prepareExportDataCommon(items, categoryExtFieldKeys, `${categoryName || ''}分类导出数据`);
  }

  /**
   * 移除部门路径中的根节点部分
   * @param departmentPath 完整的部门路径
   * @returns 移除根节点后的路径
   */
  private removeRootNodeFromPath(departmentPath: string): string {
    if (!departmentPath) return '';

    // 获取部门服务实例来获取根节点名称
    const departmentService = DepartmentService.getInstance();
    const departmentCategories = departmentService.getState().departmentCategories;

    // 如果有部门分类树，获取根节点名称
    if (departmentCategories && departmentCategories.length > 0) {
      const rootNode = departmentCategories[0];
      const rootNodeName = rootNode.name;

      // 移除根节点名称和后面的斜杠
      if (departmentPath.startsWith(`${rootNodeName}/`)) {
        return departmentPath.substring(`${rootNodeName}/`.length);
      }

      // 如果路径就是根节点名称本身，返回空字符串
      if (departmentPath === rootNodeName) {
        return '';
      }
    }

    // 兼容旧的硬编码根节点名称
    if (departmentPath.startsWith('全部部门/')) {
      return departmentPath.substring('全部部门/'.length);
    }

    // 如果路径就是"全部部门"，返回空字符串
    if (departmentPath === '全部部门') {
      return '';
    }

    return departmentPath;
  }

  /**
   * 获取部门完整路径 - 提取通用方法
   */
  private getDepartmentPath(
    item: InventoryItem,
    departmentNameMap: Map<string, DepartmentCategory>,
    departmentIdMap: Map<string, DepartmentCategory>,
    departmentNameNormalizedMap: Map<string, DepartmentCategory>,
    departmentService: any
  ): string {
    // 安全检查：确保部门名称存在
    const departmentName = item.department || '';

    // 获取部门节点
    let departmentNode: DepartmentCategory | undefined;

    // 尝试多种方式查找部门节点
    if (departmentName && departmentNameMap.has(departmentName)) {
      departmentNode = departmentNameMap.get(departmentName);
    }
    else if (item.departmentId && departmentIdMap.has(String(item.departmentId))) {
      departmentNode = departmentIdMap.get(String(item.departmentId));
    }
    else if (departmentName && departmentNameNormalizedMap.has(departmentName.toLowerCase().trim())) {
      departmentNode = departmentNameNormalizedMap.get(departmentName.toLowerCase().trim());
    }

    // 获取部门的完整路径
    let departmentPath = departmentName; // 默认使用原始部门名称

    if (departmentNode) {
      try {
        // 使用departmentService的getDepartmentPath方法获取完整路径
        departmentPath = departmentService.getDepartmentPath(departmentNode);

        // 使用通用方法移除根节点部分
        departmentPath = this.removeRootNodeFromPath(departmentPath);
      } catch (error) {
        console.warn('获取部门路径失败:', error);
        departmentPath = departmentName; // 回退到原始部门名称
      }
    }

    return departmentPath || ''; // 确保返回字符串
  }

  /**
   * 准备导出数据
   * 将前端格式的设备台账数据转换为导出格式
   * @param items 前端格式的设备台账数据
   * @returns 导出格式的数据
   */
  private prepareExportData(items: InventoryItem[]): any[] {
    // 收集所有扩展字段名称
    const allExtFieldKeys = new Set<string>();
    items.forEach(item => {
      if (item.extendedFields) {
        Object.keys(item.extendedFields).forEach(key => {
          allExtFieldKeys.add(key);
        });
      }
    });

    // 调试日志
    if (allExtFieldKeys.size < 10) {
      allExtFieldKeys.forEach(key => {
        console.log(`扩展字段映射 - 键: ext_${key}, 值: ${key}`);
      });
    }

    // 使用通用方法准备导出数据
    return this.prepareExportDataCommon(items, allExtFieldKeys, '导出数据');
  }

  /**
   * 通用的导出数据准备方法
   * @param items 设备数据
   * @param extFieldKeys 扩展字段键集合
   * @param logPrefix 日志前缀
   * @returns 导出格式的数据
   */
  private prepareExportDataCommon(items: InventoryItem[], extFieldKeys: Set<string>, logPrefix: string): any[] {
    // 获取部门服务实例
    const departmentService = DepartmentService.getInstance();

    // 获取导出设置服务实例，用于获取列顺序
    const settingsService = ExportSettingsService.getInstance();
    const columnOrder = settingsService.getState().columnOrder;

    console.log(`开始准备${logPrefix}，设备数量:`, items.length);
    console.log('使用列顺序:', columnOrder);

    // 使用通用方法构建部门映射
    const { departmentNameMap, departmentIdMap, departmentNameNormalizedMap } = this.buildDepartmentMaps();

    console.log('部门名称映射数量:', departmentNameMap.size);
    console.log('部门ID映射数量:', departmentIdMap.size);
    console.log('规范化部门名称映射数量:', departmentNameNormalizedMap.size);

    // 创建字段ID到标题的映射 - 使用通用方法
    const fieldIdToTitle = this.createFieldMapping(extFieldKeys);

    // 处理每个设备项
    const result = items.map((item, index) => {
      // 获取部门的完整路径 - 复用通用方法
      let departmentPath = this.getDepartmentPath(item, departmentNameMap, departmentIdMap, departmentNameNormalizedMap, departmentService);

      // 如果是前5个项目，打印调试信息
      if (index < 5) {
        console.log(`设备${index}部门信息:`, {
          id: item.id,
          department: item.department || '',
          departmentId: item.departmentId,
          完整路径: departmentPath
        });
      }

      // 使用Map保证字段顺序
      const exportItemMap = new Map<string, any>();

      // 如果有列顺序，按列顺序添加字段
      if (columnOrder && columnOrder.length > 0) {
        // 按列顺序添加字段
        columnOrder.forEach(colId => {
          // 跳过选择列和操作列
          if (colId === 'select' || colId === 'actions') {
            return;
          }

          // 获取字段标题
          const fieldTitle = fieldIdToTitle.get(colId);
          if (!fieldTitle) return;

          // 获取字段值
          let fieldValue;

          // 特殊处理所属部门字段
          if (colId === 'department') {
            fieldValue = departmentPath;
          }
          // 处理扩展字段
          else if (colId.startsWith('ext_')) {
            const extFieldKey = colId.substring(4); // 移除"ext_"前缀
            fieldValue = item.extendedFields?.[extFieldKey] ?? '';

            // 调试日志 - 记录扩展字段的处理
            if (index < 2) {
              console.log(`处理扩展字段值 - ID: ${colId}, 键: ${extFieldKey}, 值: ${fieldValue}`);
            }

            // 对扩展字段中的日期字段进行格式化 - 显示时分秒
            if (isTimeField(extFieldKey) || isTimeField(fieldTitle)) {
              fieldValue = formatTimeValue(fieldValue, true, true); // showTime=true, showSeconds=true
            }
          } else {
            // 处理其他标准字段
            fieldValue = item[colId as keyof InventoryItem] ?? '';

            // 对日期字段进行格式化 - 基础字段只显示日期
            if (colId === 'startTime' || isTimeField(fieldTitle)) {
              fieldValue = formatTimeValue(fieldValue, false); // showTime=false，只显示日期
            }
          }

          // 添加到导出项Map
          exportItemMap.set(fieldTitle, fieldValue);
        });
      } else {
        // 如果没有列顺序，使用默认顺序
        // 基础字段
        exportItemMap.set('序号', item.id);
        exportItemMap.set('设备类型分类', item.parentCategory);
        exportItemMap.set('设备类型', item.type);
        exportItemMap.set('保密编号', item.securityCode);
        exportItemMap.set('密级', item.securityLevel);
        exportItemMap.set('责任人', item.responsible);
        exportItemMap.set('名称', item.name);
        exportItemMap.set('型号', item.model);
        exportItemMap.set('所属部门', departmentPath);
        exportItemMap.set('放置地点', item.location);
        exportItemMap.set('启用时间', formatTimeValue(item.startTime));
        exportItemMap.set('使用情况', item.status);
        exportItemMap.set('用途', item.purpose);
        exportItemMap.set('安全码', item.securityRfid);

        // 添加指定的扩展字段 - 不带ext_前缀
        extFieldKeys.forEach(key => {
          const value = item.extendedFields && item.extendedFields[key] !== undefined
            ? item.extendedFields[key]
            : '';
          // 使用原始字段名作为键，不带ext_前缀
          exportItemMap.set(key, value);

          // 调试日志
          if (index < 1 && extFieldKeys.size < 5) {
            console.log(`添加扩展字段 - 键: ${key}, 值: ${value}`);
          }
        });
      }

      // 将Map转换为普通对象，保持顺序
      const exportItem: Record<string, any> = {};
      exportItemMap.forEach((value, key) => {
        exportItem[key] = value;
      });

      return exportItem;
    });

    return result;
  }

  /**
   * 使用ExcelJS创建工作表
   */
  private async createExcelJSWorksheet(
    workbook: ExcelJS.Workbook,
    data: any[],
    sheetName: string,
    columnOrder: string[],
    rootNodeName: string
  ): Promise<void> {
    // 创建工作表
    const worksheet = workbook.addWorksheet(sheetName);

    if (!data || data.length === 0) {
      return;
    }

    // 1. 添加标题行
    const titleText = `${rootNodeName}${sheetName}`;
    worksheet.addRow([titleText]);

    // 设置标题行样式
    const titleRow = worksheet.getRow(1);
    titleRow.height = 35;
    const titleCell = titleRow.getCell(1);
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '2E5BBA' }
    };
    titleCell.font = {
      name: '微软雅黑',
      size: 14,
      bold: true,
      color: { argb: 'FFFFFF' }
    };
    titleCell.alignment = {
      vertical: 'middle',
      horizontal: 'center'
    };

    // 合并标题行单元格
    if (columnOrder.length > 1) {
      worksheet.mergeCells(1, 1, 1, columnOrder.length);
    }

    // 2. 添加表头行
    worksheet.addRow(columnOrder);

    // 设置表头样式
    const headerRow = worksheet.getRow(2);
    headerRow.height = 25;
    columnOrder.forEach((_, colIndex) => {
      const cell = headerRow.getCell(colIndex + 1);
      ExcelStyleHelper.setCellStyle(cell, true, 2, colIndex + 1);
    });

    // 3. 添加数据行
    data.forEach((item, rowIndex) => {
      const rowData = columnOrder.map(col => item[col] || '');
      const dataRow = worksheet.addRow(rowData);

      // 设置数据行样式
      dataRow.height = 20;
      rowData.forEach((_, colIndex) => {
        const cell = dataRow.getCell(colIndex + 1);
        ExcelStyleHelper.setCellStyle(cell, false, rowIndex + 3, colIndex + 1);
      });
    });

    // 4. 设置列宽
    ExcelStyleHelper.adjustColumnWidths(worksheet, data);

    // 5. 设置工作表属性
    ExcelStyleHelper.setWorksheetProperties(worksheet);

    // 6. 设置边框
    const totalRows = data.length + 2; // +1 for title, +1 for header
    ExcelStyleHelper.setTableBorders(worksheet, totalRows, columnOrder.length);
  }

  /**
   * 创建分类工作表
   */
  private async createCategoryWorksheets(
    workbook: ExcelJS.Workbook,
    data: any[],
    originalItems: InventoryItem[] | undefined,
    selectedCategoryIds: string[],
    singleParentCategory: boolean,
    rootNodeName: string
  ): Promise<void> {
    // 如果只选择了一个一级分类，并且选择了多个二级分类，则按二级分类生成子表
    if (singleParentCategory) {
      const subCategoryIds = selectedCategoryIds.filter((id: string) => id.match(/^parent-\d+-\d+$/));

      if (subCategoryIds.length > 1) {
        console.log('选择了多个二级分类，按二级分类生成子表');

        const subCategories = this.groupDataByCategory(data, '设备类型');

        for (const [categoryName, items] of subCategories.entries()) {
          if (!items.length) continue;

          console.log(`创建二级分类工作表: ${categoryName}, 数据条数: ${items.length}`);

          let categoryData;
          if (originalItems) {
            const categoryOriginalItems = originalItems.filter(item => {
              const type = item.type || '未分类';
              return type === categoryName;
            });
            categoryData = this.prepareExportDataForCategory(categoryOriginalItems, categoryName);
          } else {
            categoryData = items;
          }

          const sheetName = categoryName.length > 31 ? categoryName.substring(0, 28) + '...' : categoryName;
          const subColumnOrder = categoryData.length > 0 ? Object.keys(categoryData[0]) : [];

          await this.createExcelJSWorksheet(workbook, categoryData, sheetName, subColumnOrder, rootNodeName);
          console.log(`工作表 ${sheetName} 创建完成`);
        }
        return;
      }
    }

    // 其他情况：按一级分类创建子表
    const parentCategories = this.groupDataByCategory(data, '设备类型分类');

    for (const [categoryName, items] of parentCategories.entries()) {
      if (!items.length) continue;

      console.log(`创建一级分类工作表: ${categoryName}, 数据条数: ${items.length}`);

      let categoryData;
      if (originalItems) {
        const categoryOriginalItems = originalItems.filter(item => {
          const parentCategory = item.parentCategory || '未分类';
          return parentCategory === categoryName;
        });
        categoryData = this.prepareExportDataForCategory(categoryOriginalItems, categoryName);
      } else {
        categoryData = items;
      }

      const sheetName = categoryName.length > 31 ? categoryName.substring(0, 28) + '...' : categoryName;
      const parentColumnOrder = categoryData.length > 0 ? Object.keys(categoryData[0]) : [];

      await this.createExcelJSWorksheet(workbook, categoryData, sheetName, parentColumnOrder, rootNodeName);
      console.log(`工作表 ${sheetName} 创建完成`);
    }
  }

  /**
   * 准备模板数据
   * @param columns 列定义
   * @returns 模板数据
   */
  private prepareTemplateData(columns: any[]): any[] {
    // 过滤掉不需要的列（巡检状态、RFID码、选择列、操作列）
    const filteredColumns = columns.filter(col =>
      col.key !== 'inspectionStatus' &&
      col.key !== 'securityRfid' &&
      col.key !== 'select' &&
      col.key !== 'actions'
    );

    // 创建示例数据行
    const exampleData: Record<string, any> = {};

    filteredColumns.forEach(col => {
      const fieldTitle = col.title;

      // 根据字段类型提供示例数据
      switch (col.key) {
        case 'id':
          exampleData[fieldTitle] = '1';
          break;
        case 'parentCategory':
          exampleData[fieldTitle] = '计算机设备';
          break;
        case 'type':
          exampleData[fieldTitle] = '台式计算机';
          break;
        case 'name':
          exampleData[fieldTitle] = '联想台式机';
          break;
        case 'securityCode':
          exampleData[fieldTitle] = 'SC001';
          break;
        case 'securityLevel':
          exampleData[fieldTitle] = '内部';
          break;
        case 'responsible':
          exampleData[fieldTitle] = '张三';
          break;
        case 'model':
          exampleData[fieldTitle] = 'ThinkCentre M720q';
          break;
        case 'department':
          exampleData[fieldTitle] = '技术部/开发组';
          break;
        case 'location':
          exampleData[fieldTitle] = '办公楼3层301室';
          break;
        case 'startTime':
          exampleData[fieldTitle] = '2024-01-15';
          break;
        case 'status':
          exampleData[fieldTitle] = '正常使用';
          break;
        case 'purpose':
          exampleData[fieldTitle] = '日常办公';
          break;
        case 'manufacturer':
          exampleData[fieldTitle] = '联想';
          break;
        default:
          // 扩展字段或其他字段
          if (col.key.startsWith('ext_')) {
            // 根据字段名称提供合适的示例
            const fieldName = col.key.substring(4);
            const fieldTitle_lower = fieldTitle.toLowerCase();

            // 特殊字段处理
            if (fieldTitle === '岗位密级') {
              exampleData[fieldTitle] = '一般';
            } else if (fieldTitle === '联系方式') {
              exampleData[fieldTitle] = '13800138000';
            } else if (fieldTitle_lower.includes('mac')) {
              exampleData[fieldTitle] = 'AA:BB:CC:DD:EE:FF';
            } else if (fieldTitle_lower.includes('ip') || (fieldTitle_lower.includes('地址') && !fieldTitle_lower.includes('mac'))) {
              exampleData[fieldTitle] = '*************';
            } else if (fieldTitle === '是否联网') {
              exampleData[fieldTitle] = '是/否';
            } else if (fieldTitle === '操作系统') {
              exampleData[fieldTitle] = 'Windows XP/Windows Vista/Windows 7/Windows 8/Windows 10/Windows 11';
            } else if (fieldTitle.includes('操作系统安装日期') || fieldTitle.includes('安装日期')) {
              exampleData[fieldTitle] = '2024-01-15 10:30:00';
            } else if (fieldTitle.includes('启用时间') || fieldTitle.includes('启用日期')) {
              exampleData[fieldTitle] = '2024-01-15';
            } else if (fieldName.includes('时间') || fieldName.includes('日期')) {
              // 默认时间字段使用日期格式
              exampleData[fieldTitle] = '2024-01-15';
            } else if (fieldName.includes('数量') || fieldName.includes('价格')) {
              exampleData[fieldTitle] = '1';
            } else {
              exampleData[fieldTitle] = '示例数据';
            }
          } else {
            exampleData[fieldTitle] = '示例数据';
          }
          break;
      }
    });

    return [exampleData];
  }

  /**
   * 生成模板Excel文件
   * @param templateData 模板数据
   * @param columns 列定义
   * @returns Blob对象
   */
  private async generateTemplateExcelFile(templateData: any[], columns: any[]): Promise<Blob> {
    try {
      console.log('开始生成模板Excel文件');

      // 创建工作簿
      const workbook = new ExcelJS.Workbook();

      // 获取根节点名称用于标题
      const CompanyNameHelper = (await import('../../../utils/companyNameHelper')).default;
      const rootNodeName = await CompanyNameHelper.getExportTitle('设备台账导入模板');

      // 获取列顺序
      const columnOrder = templateData.length > 0 ? Object.keys(templateData[0]) : [];

      console.log('模板Excel使用的列顺序:', columnOrder);

      // 创建模板工作表
      await this.createTemplateExcelJSWorksheet(workbook, templateData, '设备台账导入模板', columnOrder, rootNodeName);

      console.log('模板工作表创建完成');

      // 生成Excel文件
      const buffer = await workbook.xlsx.writeBuffer();
      return new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
    } catch (error) {
      console.error('生成模板Excel文件失败:', error);
      throw new Error('生成模板Excel文件失败: ' + (error as Error).message);
    }
  }

  /**
   * 生成模板CSV文件
   * @param templateData 模板数据
   * @returns Blob对象
   */
  private async generateTemplateCsvFile(templateData: any[]): Promise<Blob> {
    try {
      console.log('开始生成模板CSV文件');

      if (!templateData || templateData.length === 0) {
        throw new Error('没有可导出的模板数据');
      }

      // 获取列标题
      const headers = Object.keys(templateData[0]);

      // 构建CSV内容
      const csvRows: string[] = [];

      // 添加标题行
      csvRows.push(headers.join(','));

      // 添加示例数据行
      templateData.forEach(item => {
        const row = headers.map(header => {
          const value = item[header] || '';
          // 如果值包含逗号、引号或换行符，需要用引号包围并转义
          if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        });
        csvRows.push(row.join(','));
      });

      const csvContent = csvRows.join('\n');

      // 添加BOM以支持中文
      const bom = '\uFEFF';
      return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8;' });
    } catch (error) {
      console.error('生成模板CSV文件失败:', error);
      throw new Error('生成模板CSV文件失败: ' + (error as Error).message);
    }
  }

  /**
   * 创建模板Excel工作表
   */
  private async createTemplateExcelJSWorksheet(
    workbook: ExcelJS.Workbook,
    data: any[],
    sheetName: string,
    columnOrder: string[],
    rootNodeName: string
  ): Promise<void> {
    // 创建工作表
    const worksheet = workbook.addWorksheet(sheetName);

    if (!data || data.length === 0) {
      return;
    }

    // 1. 添加标题行
    const titleText = rootNodeName;
    worksheet.addRow([titleText]);

    // 设置标题行样式
    const titleRow = worksheet.getRow(1);
    titleRow.height = 35;
    const titleCell = titleRow.getCell(1);
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '2E5BBA' }
    };
    titleCell.font = {
      name: '微软雅黑',
      size: 14,
      bold: true,
      color: { argb: 'FFFFFF' }
    };
    titleCell.alignment = {
      vertical: 'middle',
      horizontal: 'center'
    };

    // 合并标题行单元格
    if (columnOrder.length > 1) {
      worksheet.mergeCells(1, 1, 1, columnOrder.length);
    }

    // 2. 添加表头行
    worksheet.addRow(columnOrder);

    // 设置表头样式
    const headerRow = worksheet.getRow(2);
    headerRow.height = 25;
    columnOrder.forEach((_, colIndex) => {
      const cell = headerRow.getCell(colIndex + 1);
      ExcelStyleHelper.setCellStyle(cell, true, 2, colIndex + 1);
    });

    // 3. 添加示例数据行
    data.forEach((item, rowIndex) => {
      const rowData = columnOrder.map(col => item[col] || '');
      const dataRow = worksheet.addRow(rowData);

      // 设置数据行样式
      dataRow.height = 20;
      rowData.forEach((value, colIndex) => {
        const cell = dataRow.getCell(colIndex + 1);
        const columnName = columnOrder[colIndex];

        // 对于日期字段，设置为文本格式防止Excel自动转换
        if (columnName === '启用时间' ||
            columnName.includes('时间') ||
            columnName.includes('日期') ||
            (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value))) {
          cell.numFmt = '@'; // 设置为文本格式
        }

        ExcelStyleHelper.setCellStyle(cell, false, rowIndex + 3, colIndex + 1);
      });
    });

    // 3.5. 添加提示信息行
    const hintRowData = columnOrder.map(col => {
      if (col === '所属部门') {
        return '用/来分割部门，跳过公司名称开始填写部门';
      }
      return '';
    });
    const hintRow = worksheet.addRow(hintRowData);
    hintRow.height = 35; // 增加行高以容纳提示文本

    hintRowData.forEach((value, colIndex) => {
      const cell = hintRow.getCell(colIndex + 1);
      const columnName = columnOrder[colIndex];

      // 设置提示信息的样式
      if (columnName === '所属部门' && value) {
        cell.font = {
          name: '微软雅黑',
          size: 9,
          italic: true,
          color: { argb: '666666' } // 灰色文字
        };
        cell.alignment = {
          vertical: 'top',
          horizontal: 'left',
          wrapText: true
        };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F0F8FF' } // 淡蓝色背景
        };
        // 设置单元格为文本格式
        cell.numFmt = '@';
      }

      // 对于日期字段，设置为文本格式
      if (columnName === '启用时间' ||
          columnName.includes('时间') ||
          columnName.includes('日期')) {
        cell.numFmt = '@';
      }

      // 设置边框
      cell.border = {
        top: { style: 'thin', color: { argb: 'D0D0D0' } },
        left: { style: 'thin', color: { argb: 'D0D0D0' } },
        bottom: { style: 'thin', color: { argb: 'D0D0D0' } },
        right: { style: 'thin', color: { argb: 'D0D0D0' } }
      };
    });

    // 4. 添加一些空白行供用户填写，并设置相同的格式
    for (let i = 0; i < 5; i++) {
      const emptyRowData = columnOrder.map(() => '');
      const emptyRow = worksheet.addRow(emptyRowData);
      emptyRow.height = 20;

      emptyRowData.forEach((_, colIndex) => {
        const cell = emptyRow.getCell(colIndex + 1);
        const columnName = columnOrder[colIndex];

        // 对于日期字段，设置为文本格式
        if (columnName === '启用时间' ||
            columnName.includes('时间') ||
            columnName.includes('日期')) {
          cell.numFmt = '@'; // 设置为文本格式
        }

        ExcelStyleHelper.setCellStyle(cell, false, data.length + i + 4, colIndex + 1); // +4 因为多了一行提示信息
      });
    }

    // 4. 设置列宽 - 使用与导出相同的宽度计算
    ExcelStyleHelper.adjustColumnWidths(worksheet, data);

    // 4.5. 为所属部门列设置最小宽度以确保提示信息完全显示
    const departmentColIndex = columnOrder.findIndex(col => col === '所属部门');
    if (departmentColIndex !== -1) {
      const departmentColumn = worksheet.getColumn(departmentColIndex + 1);
      const hintText = '用/来分割部门，跳过公司名称开始填写部门';
      const minWidth = Math.max(departmentColumn.width || 15, hintText.length * 1.2); // 确保有足够宽度显示提示
      departmentColumn.width = minWidth;
    }

    // 5. 设置工作表属性
    ExcelStyleHelper.setWorksheetProperties(worksheet);

    // 6. 设置边框
    const totalRows = data.length + 2; // +1 for title, +1 for header
    ExcelStyleHelper.setTableBorders(worksheet, totalRows, columnOrder.length);
  }

  /**
   * 向后端发送导出日志记录
   * @param outputPath 导出文件路径
   * @param fileType 文件类型
   */
  private async sendExportLogToBackend(outputPath: string, fileType: string): Promise<void> {
    try {
      console.log('发送导出日志到后端:', { outputPath, fileType });

      // 构建请求参数
      const requestParams = {
        action: "export_device_data",
        action_params: {
          export_file_path: outputPath,
          export_file_type: fileType
        }
      };

      console.log('导出日志请求参数:', requestParams);

      // 获取任务管理器
      const task = TaskManager.getInstance();

      // 提交任务到后端
      const taskId = await task.submitTask('AccountTableDll', 'DbFun', requestParams);
      console.log('导出日志任务提交成功，任务ID:', taskId);

      // 等待任务完成
      const result = await task.waitForTaskResult(taskId);
      console.log('导出日志记录结果:', result);

      // 检查结果
      if (result && typeof result === 'object' && 'success' in result) {
        if (result.success) {
          console.log('导出日志记录成功');
        } else {
          console.warn('导出日志记录失败:', result);
        }
      } else {
        console.log('导出日志记录完成，结果:', result);
      }

    } catch (error) {
      console.error('发送导出日志到后端失败:', error);
      throw error;
    }
  }
}

export default ExportService;
