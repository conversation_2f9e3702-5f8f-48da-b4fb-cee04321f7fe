# 键盘操作与焦点管理指南

## 📋 目录
- [系统概述](#系统概述)
- [键盘操作指南](#键盘操作指南)
- [焦点管理功能](#焦点管理功能)
- [开发者指南](#开发者指南)
- [无障碍支持](#无障碍支持)
- [故障排除](#故障排除)

## 🎯 系统概述

本系统实现了完整的键盘导航和焦点管理功能，确保用户可以完全通过键盘操作应用程序，同时提供优秀的无障碍体验。

### 核心特性
- ✅ **全键盘导航**：所有功能都可通过键盘访问
- ✅ **智能焦点管理**：自动设置和恢复焦点
- ✅ **对话框焦点陷阱**：防止焦点逃出模态对话框
- ✅ **屏幕阅读器支持**：完整的ARIA标签和语义化
- ✅ **视觉焦点指示**：清晰的焦点样式
- ✅ **跳转链接**：快速导航到主要内容

## ⌨️ 键盘操作指南

### 全局导航
| 按键 | 功能 | 说明 |
|------|------|------|
| `Tab` | 下一个焦点 | 移动到下一个可聚焦元素 |
| `Shift + Tab` | 上一个焦点 | 移动到上一个可聚焦元素 |
| `Enter` | 激活元素 | 点击按钮、链接或激活控件 |
| `Space` | 激活按钮 | 激活按钮或切换复选框 |
| `Escape` | 关闭/取消 | 关闭对话框或取消操作 |

### 页面导航
| 按键 | 功能 | 说明 |
|------|------|------|
| `Alt + 1` | 跳转到主要内容 | 绕过导航直接到内容区 |
| `Alt + 2` | 跳转到导航菜单 | 快速访问主导航 |
| `Home` | 页面顶部 | 滚动到页面顶部 |
| `End` | 页面底部 | 滚动到页面底部 |

### 对话框操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `Escape` | 关闭对话框 | 关闭当前打开的对话框 |
| `Tab` | 对话框内导航 | 在对话框内的元素间切换 |
| `Enter` | 确认操作 | 提交表单或确认操作 |

### 表单操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `Tab` | 下一个字段 | 移动到下一个表单字段 |
| `Shift + Tab` | 上一个字段 | 移动到上一个表单字段 |
| `Enter` | 提交表单 | 提交当前表单 |
| `Escape` | 取消编辑 | 取消表单编辑并关闭 |

### 列表操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `↑` | 上一项 | 选择列表中的上一项 |
| `↓` | 下一项 | 选择列表中的下一项 |
| `Home` | 第一项 | 跳转到列表第一项 |
| `End` | 最后一项 | 跳转到列表最后一项 |
| `Enter` | 激活项目 | 激活或编辑选中的项目 |

### 表格操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `Tab` | 下一个单元格 | 移动到下一个可聚焦单元格 |
| `Shift + Tab` | 上一个单元格 | 移动到上一个可聚焦单元格 |
| `↑↓←→` | 方向导航 | 在表格单元格间导航 |
| `Enter` | 编辑单元格 | 进入单元格编辑模式 |

## 🎯 焦点管理功能

### 自动焦点设置
系统会在以下情况自动设置焦点：

#### 页面切换
- 路由变化时自动聚焦到页面标题或主要内容
- 支持自定义焦点目标
- 平滑滚动到目标元素

#### 对话框打开
- 对话框打开时自动聚焦到第一个可聚焦元素
- 关闭时恢复到触发元素
- 焦点陷阱防止逃出对话框

#### 表单验证
- 验证失败时自动聚焦到第一个错误字段
- 支持多种错误字段查找策略
- 自动滚动到错误位置

#### 列表操作
- 新增项目后自动聚焦到新项目
- 删除项目后聚焦到合适的位置
- 支持键盘导航

### 焦点恢复
- 对话框关闭后恢复到触发元素
- 页面返回时恢复之前的焦点位置
- 动态内容更新后保持焦点状态

### 焦点陷阱
- 模态对话框内的焦点循环
- Tab键在对话框内循环导航
- Shift+Tab反向循环导航

## 👨‍💻 开发者指南

### 使用焦点管理Hook

#### 页面焦点管理
```typescript
import { usePageFocus } from '../hooks/base';

const MyPage = () => {
  const { pageRef, titleRef, setPageFocus } = usePageFocus({
    title: '页面标题',
    autoFocus: true,
    focusTarget: '#main-content'
  });

  return (
    <div ref={pageRef}>
      <h1 ref={titleRef}>页面标题</h1>
      <main id="main-content">内容</main>
    </div>
  );
};
```

#### 表单焦点管理
```typescript
import { useFormFocus } from '../hooks/base';

const MyForm = () => {
  const { formRef, focusFirstError, validateAndFocus } = useFormFocus({
    autoFocusOnError: true,
    scrollToError: true
  });

  const handleSubmit = () => {
    validateAndFocus(
      () => validateForm(), // 验证函数
      () => console.log('验证通过'),
      (errors) => console.log('验证失败', errors)
    );
  };

  return (
    <form ref={formRef} onSubmit={handleSubmit}>
      {/* 表单字段 */}
    </form>
  );
};
```

#### 列表焦点管理
```typescript
import { useListFocus } from '../hooks/base';

const MyList = () => {
  const { listRef, focusItem, handleKeyDown } = useListFocus({
    autoFocusOnAdd: true,
    scrollToNewItem: true
  });

  return (
    <div ref={listRef} onKeyDown={handleKeyDown}>
      {items.map(item => (
        <div key={item.id} data-item-id={item.id}>
          {item.name}
        </div>
      ))}
    </div>
  );
};
```

#### 对话框焦点管理
```typescript
import DialogBase from '../components/ui/DialogBase';

const MyDialog = ({ isOpen, onClose }) => {
  return (
    <DialogBase
      isOpen={isOpen}
      onClose={onClose}
      autoFocus={true}
      restoreFocus={true}
    >
      <h2>对话框标题</h2>
      <button onClick={onClose}>关闭</button>
    </DialogBase>
  );
};
```

### 自定义焦点样式
```css
/* 焦点样式已在 src/styles/focusStyles.css 中定义 */
.focus-ring {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* 键盘导航模式下显示焦点 */
.keyboard-navigation *:focus {
  @apply focus-ring;
}
```

### 无障碍属性
```typescript
// 跳转链接组件
import SkipLinks from '../components/ui/SkipLinks';

<SkipLinks
  links={[
    { href: '#main-content', label: '跳转到主要内容' },
    { href: '#navigation', label: '跳转到导航菜单' }
  ]}
/>

// ARIA标签示例
<button
  aria-label="关闭对话框"
  aria-describedby="dialog-description"
>
  ×
</button>
```

## ♿ 无障碍支持

### WCAG 2.1 合规性
- **AA级别**：满足WCAG 2.1 AA级别要求
- **键盘访问**：所有功能都可通过键盘访问
- **焦点管理**：清晰的焦点指示和合理的焦点顺序
- **语义化**：正确的HTML语义和ARIA标签

### 屏幕阅读器支持
- **NVDA**：完全兼容
- **JAWS**：完全兼容  
- **VoiceOver**：完全兼容
- **TalkBack**：移动端支持

### 视觉辅助
- **高对比度**：支持高对比度模式
- **焦点指示**：清晰的焦点边框
- **颜色独立**：不依赖颜色传达信息

## 🔧 故障排除

### 常见问题

#### 焦点管理器未初始化
**错误**：`FocusManager未初始化`
**解决**：确保在应用根组件中使用了 `KeyboardManager`

```typescript
// main.tsx
import KeyboardManager from './components/KeyboardManager';

<KeyboardManager>
  <App />
</KeyboardManager>
```

#### 焦点无法设置
**问题**：调用焦点管理函数但焦点没有改变
**解决**：
1. 检查目标元素是否可聚焦
2. 确保元素在DOM中存在
3. 添加 `tabindex="-1"` 使元素可聚焦

#### 对话框焦点陷阱失效
**问题**：Tab键可以跳出对话框
**解决**：
1. 使用 `DialogBase` 组件
2. 确保对话框是模态的
3. 检查 `useDialogFocus` 是否正确初始化

### 调试技巧
```typescript
// 启用焦点调试
console.log('当前焦点元素:', document.activeElement);

// 检查焦点管理器状态
import { getFocusManager } from '../hooks/base/useFocusManager';
const focusManager = getFocusManager();
```

## 🎨 焦点样式指南

### 默认焦点样式
系统提供了统一的焦点样式，确保在所有浏览器中的一致性：

```css
/* 基础焦点环 */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
  border-radius: 4px;
}

/* 键盘导航模式 */
body.keyboard-navigation *:focus {
  @apply focus-ring;
}

/* 鼠标模式隐藏焦点环 */
body:not(.keyboard-navigation) *:focus {
  outline: none;
  box-shadow: none;
}
```

### 组件特定样式
不同组件有专门的焦点样式：

```css
/* 按钮焦点 */
button:focus {
  @apply focus-ring;
  transform: translateY(-1px);
}

/* 输入框焦点 */
input:focus, textarea:focus, select:focus {
  @apply focus-ring;
  border-color: #3b82f6;
}

/* 链接焦点 */
a:focus {
  @apply focus-ring;
  text-decoration: underline;
}

/* 表格行焦点 */
tr:focus {
  background-color: rgba(59, 130, 246, 0.1);
}
```

## 🔄 焦点管理生命周期

### 初始化流程
1. **应用启动**：`KeyboardManager` 组件初始化
2. **管理器创建**：创建全局焦点和键盘管理器实例
3. **事件监听**：注册全局键盘事件监听器
4. **样式应用**：根据输入模式应用焦点样式

### 页面切换流程
1. **路由变化**：检测到路由变化
2. **保存焦点**：保存当前页面的焦点状态
3. **设置新焦点**：在新页面设置合适的初始焦点
4. **通知屏幕阅读器**：发送页面变化通知

### 对话框生命周期
1. **打开前**：保存当前焦点元素
2. **打开时**：设置焦点陷阱和初始焦点
3. **使用中**：管理对话框内的焦点导航
4. **关闭时**：恢复到之前保存的焦点元素

## 🧪 测试指南

### 键盘导航测试
使用以下步骤测试键盘导航：

1. **基础导航测试**
   - 使用Tab键遍历所有可聚焦元素
   - 确保焦点顺序符合逻辑
   - 验证所有交互元素都可访问

2. **对话框测试**
   - 打开对话框，验证焦点设置
   - 使用Tab键确认焦点陷阱
   - 按Escape键确认正确关闭

3. **表单测试**
   - 提交无效表单，验证错误焦点
   - 使用键盘填写和提交表单
   - 测试字段间的导航

4. **列表测试**
   - 使用方向键导航列表项
   - 测试Home/End键功能
   - 验证新增项目的焦点设置

### 屏幕阅读器测试
1. **NVDA测试**（Windows）
   - 启动NVDA
   - 使用虚拟光标模式浏览
   - 测试表单和焦点模式

2. **VoiceOver测试**（macOS）
   - 启动VoiceOver（Cmd+F5）
   - 使用VO+方向键导航
   - 测试转子功能

3. **移动端测试**
   - iOS：启用VoiceOver
   - Android：启用TalkBack
   - 测试触摸导航

## 📊 性能优化

### 焦点管理性能
- **延迟设置**：使用setTimeout避免阻塞渲染
- **事件节流**：限制键盘事件处理频率
- **内存管理**：及时清理事件监听器

### 最佳实践
```typescript
// 好的做法：使用useCallback缓存函数
const handleFocus = useCallback((element: HTMLElement) => {
  // 焦点处理逻辑
}, []);

// 好的做法：清理副作用
useEffect(() => {
  const cleanup = registerFocusHandler();
  return cleanup;
}, []);

// 避免：频繁的DOM查询
// 坏的做法
const element = document.querySelector('.target');

// 好的做法：使用ref
const elementRef = useRef<HTMLElement>(null);
```

## 📚 相关文档
- [焦点管理系统架构](./FOCUS_MANAGEMENT_CLEANUP.md)
- [组件使用指南](./COMPONENT_GUIDE.md)
- [无障碍开发规范](./ACCESSIBILITY_STANDARDS.md)
- [键盘快捷键配置](./KEYBOARD_SHORTCUTS.md)

## 🆘 技术支持

### 常见问题解答
**Q: 如何自定义焦点样式？**
A: 修改 `src/styles/focusStyles.css` 文件中的样式定义。

**Q: 如何禁用某个元素的焦点管理？**
A: 添加 `data-focus-ignore="true"` 属性。

**Q: 如何调试焦点问题？**
A: 在浏览器开发者工具中使用 `document.activeElement` 查看当前焦点。

### 联系方式
- **技术支持**：<EMAIL>
- **文档反馈**：<EMAIL>
- **Bug报告**：使用项目Issue系统

---

**版本**：1.0.0
**更新时间**：2025-01-09
**维护者**：开发团队
**下次审查**：2025-04-09
