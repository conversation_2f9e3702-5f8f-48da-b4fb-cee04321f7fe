import { useState, useEffect, useCallback } from 'react';
import AccountLogService, {
  AccountLogServiceState,
  AccountLogItem,
  AccountLogFilter
} from '../../services/Inspection/accountLogService';

// 重新导出类型，这样UI层可以从Hook导入，而不是直接从Service层导入
export type { AccountLogItem, AccountLogFilter };

/**
 * 账户管理日志Hook返回值接口
 */
interface UseAccountLogReturn {
  // 状态
  isLoading: boolean;
  error?: string;
  logs: AccountLogItem[];
  filter: AccountLogFilter;
  filteredLogs: AccountLogItem[];

  // 分页相关
  currentPage: number;
  pageSize: number;
  totalPages: number;
  paginatedLogs: AccountLogItem[];

  // 方法
  getAccountLogs: (logType?: number) => Promise<AccountLogItem[]>;
  forceRefreshAccountLogs: (logType?: number) => Promise<AccountLogItem[]>;
  setFilter: (filter: Partial<AccountLogFilter>) => void;
  clearFilter: () => void;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;

  // 辅助方法
  getLogTypes: () => string[];
  getUserNames: () => string[];
  getPersonNames: () => string[];
}

/**
 * 账户管理日志Hook
 * 提供账户管理日志功能和状态管理
 */
export function useAccountLog(): UseAccountLogReturn {
  // 获取服务实例
  const service = AccountLogService.getInstance();

  // 状态
  const [state, setState] = useState<AccountLogServiceState>(service.getState());

  // 分页状态
  const [currentPage, setCurrentPage] = useState<number>(0); // 0-based index
  const [pageSize, setPageSize] = useState<number>(10); // 默认每页10条

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: AccountLogServiceState) => {
      setState(newState);
    };

    // 订阅状态变化事件
    service.on('state-change', handleStateChange);

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
    };
  }, [service]);

  // 获取账户管理日志
  const getAccountLogs = useCallback(async (logType?: number) => {
    return await service.getAccountLogs(logType);
  }, [service]);

  // 强制刷新账户管理日志（忽略缓存）
  const forceRefreshAccountLogs = useCallback(async (logType?: number) => {
    return await service.forceRefreshAccountLogs(logType);
  }, [service]);

  // 设置过滤条件
  const setFilter = useCallback((filter: Partial<AccountLogFilter>) => {
    service.setFilter(filter);
  }, [service]);

  // 清除所有过滤条件
  const clearFilter = useCallback(() => {
    service.clearFilter();
  }, [service]);

  // 获取所有日志类型
  const getLogTypes = useCallback(() => {
    return service.getLogTypes();
  }, [service]);

  // 获取所有用户名
  const getUserNames = useCallback(() => {
    return service.getUserNames();
  }, [service]);

  // 获取所有人员姓名
  const getPersonNames = useCallback(() => {
    return service.getPersonNames();
  }, [service]);

  // 设置当前页
  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // 设置每页大小
  const setPageSizeCallback = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(0); // 重置到第一页
  }, []);

  // 计算分页数据
  const totalPages = pageSize === -1 ? 1 : Math.ceil(state.filteredLogs.length / pageSize);
  const paginatedLogs = pageSize === -1 
    ? state.filteredLogs 
    : state.filteredLogs.slice(currentPage * pageSize, (currentPage + 1) * pageSize);

  // 当过滤条件变化时，重置到第一页
  useEffect(() => {
    setCurrentPage(0);
  }, [state.filter]);

  return {
    // 状态
    isLoading: state.isLoading,
    error: state.error,
    logs: state.logs,
    filter: state.filter,
    filteredLogs: state.filteredLogs,

    // 分页相关
    currentPage,
    pageSize,
    totalPages,
    paginatedLogs,

    // 方法
    getAccountLogs,
    forceRefreshAccountLogs,
    setFilter,
    clearFilter,
    setPage,
    setPageSize: setPageSizeCallback,

    // 辅助方法
    getLogTypes,
    getUserNames,
    getPersonNames
  };
}
