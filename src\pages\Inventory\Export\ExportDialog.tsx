import React, { useState, useEffect, useCallback } from 'react';
import { X, FileDown, Check, AlertCircle, FileText, FileImage } from 'lucide-react';
import useExport from '../../../hooks/Inventory/Export/useExport';
import { InventoryItem, FieldDefinition, ExtFieldDefinition } from '../../../types/inventory';
import { ExportFormat } from '../../../services/Inventory/Export/exportService';
import CategorySelector from './CategorySelector';
import ColumnSelector from './ColumnSelector';
import { DeviceCategory } from '../../../types/inventory';
import DepartmentService, { DepartmentCategory } from '../../../services/Inventory/departmentService';
import ExtFieldService from '../../../services/Inventory/extFieldService';
import DialogBase from '../../../components/ui/DialogBase';

// 对话框属性类型
interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  items: InventoryItem[];
  deviceCategories: DeviceCategory[];
  departmentCategories: DepartmentCategory[];
  tableFields: FieldDefinition[];
  selectedCount: number;
  totalCount: number;
  exportMode: 'selected' | 'all' | 'filtered';
  // 新增属性，用于自动选中分类
  initialSelectedCategory?: string;
  // 新增属性，用于自动选择分类模式
  initialCategoryMode?: 'device' | 'department';
  // 选中的项目ID列表
  selectedItems?: string[];
}

/**
 * 导出对话框组件
 * 用于导出设备台账数据，支持分类选择和列设置
 */
const ExportDialog: React.FC<ExportDialogProps> = ({
  isOpen,
  onClose,
  items,
  deviceCategories,
  departmentCategories,
  tableFields,
  selectedCount,
  totalCount,
  exportMode,
  initialSelectedCategory,
  initialCategoryMode,
  selectedItems = []
}) => {
  // 使用导出Hook
  const {
    isExporting,
    isSending,
    progress,
    error,
    selectedCategories,
    selectedColumns,
    columnOrder,
    exportInventory,
    exportWithSettingsAndSend,
    setSelectedCategories,
    setSelectedColumns,
    setColumnOrder,
    initializeColumnSelection,
    resetSettings,
    cancelTask,
    getService
  } = useExport();

  // 获取服务实例
  const exportService = getService();

  // 本地状态
  const [format, setFormat] = useState<ExportFormat>('excel');
  const [pdfFormat, setPdfFormat] = useState<'a3' | 'a4'>('a3');
  const [pdfOrientation, setPdfOrientation] = useState<'landscape' | 'portrait'>('landscape');
  const [wordFormat, setWordFormat] = useState<'a3' | 'a4'>('a3');
  const [wordOrientation, setWordOrientation] = useState<'landscape' | 'portrait'>('portrait');
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [outputPath, setOutputPath] = useState<string>('');
  const [localError, setLocalError] = useState<string | undefined>(error);
  const [categoryMode, setCategoryMode] = useState<'device' | 'department'>('device');
  const [dynamicTableFields, setDynamicTableFields] = useState<FieldDefinition[]>(tableFields || []);



  // 处理发送完成事件
  const handleSendCompleted = useCallback((data: any) => {
    console.log('文件发送完成:', data);

    // 设置输出路径
    if (data.outputPath) {
      setOutputPath(data.outputPath);
    }

    // 显示成功消息
    setShowSuccess(true);

    // 5秒后关闭对话框并重置状态
    setTimeout(() => {
      onClose();
      setShowSuccess(false);
      setOutputPath('');
      setLocalError(undefined);
    }, 5000);

    // 取消事件监听
    exportService.off('send-completed', handleSendCompleted);
  }, [exportService, onClose]);

  // 同步错误状态
  useEffect(() => {
    setLocalError(error);
  }, [error]);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有事件监听器
      exportService.off('send-completed', handleSendCompleted);
    };
  }, [exportService, handleSendCompleted]);

  // 处理取消
  const handleCancel = () => {
    if (isExporting || isSending) {
      cancelTask();
    }

    // 重置状态
    exportService.resetState();
    setShowSuccess(false);
    setOutputPath('');
    setLocalError(undefined);

    // 清理事件监听器
    exportService.off('send-completed', handleSendCompleted);

    onClose();
  };

  // ESC键处理已由DialogBase统一管理，无需重复实现

  // 确保列定义中包含parentCategory字段（设备类型分类）并放在序号后面
  const ensureParentCategoryField = (fields: FieldDefinition[]): FieldDefinition[] => {
    // 检查是否已经包含parentCategory字段
    const hasParentCategory = fields.some(field => field.key === 'parentCategory');

    // 如果已经包含，需要确保它的位置正确
    if (hasParentCategory) {
      // 先移除现有的parentCategory字段
      const filteredFields = fields.filter(field => field.key !== 'parentCategory');

      // 创建新的parentCategory字段定义
      const parentCategoryField = {
        key: 'parentCategory',
        title: '设备类型分类',
        sortable: true,
        width: 120,
        titleStyle: { whiteSpace: 'nowrap' }
      };

      // 找到序号字段的位置
      const idFieldIndex = filteredFields.findIndex(field => field.key === 'id');

      // 如果找到序号字段，在其后插入parentCategory字段
      if (idFieldIndex !== -1) {
        filteredFields.splice(idFieldIndex + 1, 0, parentCategoryField);
        return filteredFields;
      }

      // 如果没有找到序号字段，将parentCategory字段放在最前面（除了select字段之后）
      const selectFieldIndex = filteredFields.findIndex(field => field.key === 'select');
      if (selectFieldIndex !== -1) {
        filteredFields.splice(selectFieldIndex + 1, 0, parentCategoryField);
      } else {
        // 如果连select字段都没有，就放在最前面
        filteredFields.unshift(parentCategoryField);
      }

      return filteredFields;
    }

    // 如果不包含，添加parentCategory字段到序号字段后面
    const newFields = [...fields];
    const parentCategoryField = {
      key: 'parentCategory',
      title: '设备类型分类',
      sortable: true,
      width: 120,
      titleStyle: { whiteSpace: 'nowrap' }
    };

    // 找到序号字段的位置
    const idFieldIndex = newFields.findIndex(field => field.key === 'id');

    // 如果找到序号字段，在其后插入parentCategory字段
    if (idFieldIndex !== -1) {
      newFields.splice(idFieldIndex + 1, 0, parentCategoryField);
      return newFields;
    }

    // 如果没有找到序号字段，将parentCategory字段放在最前面（除了select字段之后）
    const selectFieldIndex = newFields.findIndex(field => field.key === 'select');
    if (selectFieldIndex !== -1) {
      newFields.splice(selectFieldIndex + 1, 0, parentCategoryField);
    } else {
      // 如果连select字段都没有，就放在最前面
      newFields.unshift(parentCategoryField);
    }

    return newFields;
  };

  // 根据选中的设备分类获取扩展字段
  const getExtFieldsByCategory = (categoryIds: string[]) => {
    if (categoryMode !== 'device') {
      return;
    }

    // 即使没有选择分类，也尝试获取所有可用的扩展字段
    if (categoryIds.length === 0) {
      try {
        // 获取扩展字段服务实例
        const extFieldService = ExtFieldService.getInstance();

        // 创建标准字段的副本
        const standardFields = tableFields.filter(field => !field.key.startsWith('ext_'));

        // 获取所有可用的扩展字段
        const allExtFields = extFieldService.getExtFieldsToShow();

        // 合并标准字段和扩展字段
        const newFields = [...standardFields, ...allExtFields];

        // 更新动态表格字段
        setDynamicTableFields(newFields);

        console.log(`导出对话框 - 加载所有可用扩展字段，共 ${allExtFields.length} 个`);
      } catch (error) {
        console.error('获取所有扩展字段失败:', error);
      }
      return;
    }

    try {
      // 获取扩展字段服务实例
      const extFieldService = ExtFieldService.getInstance();

      // 创建标准字段的副本
      const standardFields = tableFields.filter(field => !field.key.startsWith('ext_'));

      // 存储所有找到的扩展字段
      const allExtFields: FieldDefinition[] = [];

      // 处理每个选中的分类
      for (const categoryId of categoryIds) {
        // 跳过非设备分类的ID
        if (!categoryId.startsWith('parent-')) {
          continue;
        }

        // 查找分类信息
        const findCategoryById = (categories: DeviceCategory[]): { category: DeviceCategory; parent?: DeviceCategory } | null => {
          for (const category of categories) {
            if (category.id === categoryId) {
              return { category };
            }

            if (category.children?.length) {
              const result = findCategoryById(category.children);
              if (result) {
                return { ...result, parent: category };
              }
            }
          }
          return null;
        };

        const result = findCategoryById(deviceCategories);
        if (!result) continue;

        const { category, parent } = result;

        // 判断节点类型并处理
        if (categoryId.startsWith('parent-')) {
          if (!categoryId.includes('-', 'parent-'.length)) {
            // 一级分类 - 获取该一级分类下所有扩展字段
            console.log(`导出对话框 - 选择一级分类: ${category.name}`);

            // 获取该一级分类下的所有扩展字段
            const extFields = extFieldService.getExtFieldsToShow({
              parentCategory: category.name
            });

            // 转换为表格字段格式
            extFields.forEach(field => {
              const extField: FieldDefinition = {
                key: `ext_${field.key}`,
                title: field.title,
                sortable: true,
                width: 120
              };

              // 检查是否已存在相同的字段
              if (!allExtFields.some(f => f.key === extField.key)) {
                allExtFields.push(extField);
              }
            });
          }
          else {
            // 二级分类 - 获取该二级分类的扩展字段
            console.log(`导出对话框 - 选择二级分类: ${category.name}`);

            // 获取父分类名称
            const parentName = parent ? parent.name : '';

            if (parentName) {
              // 获取该二级分类的扩展字段
              const extFields = extFieldService.getExtFieldsToShow({
                parentCategory: parentName,
                subCategory: category.name
              });

              // 转换为表格字段格式
              extFields.forEach(field => {
                const extField: FieldDefinition = {
                  key: `ext_${field.key}`,
                  title: field.title,
                  sortable: true,
                  width: 120
                };

                // 检查是否已存在相同的字段
                if (!allExtFields.some(f => f.key === extField.key)) {
                  allExtFields.push(extField);
                }
              });
            }
          }
        }
      }

      // 合并标准字段和扩展字段
      const newFields = [...standardFields, ...allExtFields];

      // 更新动态表格字段
      setDynamicTableFields(newFields);

      // 更新列选择器
      const fieldsWithParentCategory = ensureParentCategoryField(newFields);

      // 如果没有选中的列，或者选中的列与当前字段不匹配，则重新初始化列选择
      const currentFieldKeys = fieldsWithParentCategory.map(field => field.key);
      const hasInvalidSelection = selectedColumns.some(col => !currentFieldKeys.includes(col));

      if (selectedColumns.length === 0 || hasInvalidSelection) {
        initializeColumnSelection(fieldsWithParentCategory);
      }

      console.log(`导出对话框 - 更新表格字段完成，共 ${newFields.length} 个字段，其中扩展字段 ${allExtFields.length} 个`);
    } catch (error) {
      console.error('获取扩展字段失败:', error);
    }
  };

  // 监听分类选择变化
  useEffect(() => {
    if (categoryMode === 'device' && selectedCategories.length > 0) {
      getExtFieldsByCategory(selectedCategories);
    }
  }, [categoryMode, selectedCategories]);

  // 监听分类模式变化，重置选中的分类
  useEffect(() => {
    // 当分类模式改变时，重置选中的分类
    console.log(`分类模式变化: ${categoryMode}, 重置选中的分类`);
    setSelectedCategories([]);

    // 如果切换到设备分类模式，加载所有可用的扩展字段
    if (categoryMode === 'device') {
      console.log('切换到设备分类模式，加载所有可用的扩展字段');
      getExtFieldsByCategory([]);
    } else if (categoryMode === 'department') {
      console.log('切换到部门分类模式');
      // 确保部门树已加载
      const departmentService = DepartmentService.getInstance();
      departmentService.loadDepartmentTree(false).then(() => {
        console.log('部门树加载完成');
      });
    }
  }, [categoryMode]);

  // 初始化设置
  useEffect(() => {
    if (isOpen) {
      // 如果有指定的初始分类模式，则设置分类模式
      if (initialCategoryMode) {
        setCategoryMode(initialCategoryMode);
      }

      // 如果有指定的初始分类，则选中该分类
      if (initialSelectedCategory) {
        setSelectedCategories([initialSelectedCategory]);

        // 确保包含parentCategory字段
        const fieldsWithParentCategory = ensureParentCategoryField(tableFields);

        // 查找该分类对应的所有标准字段和扩展字段
        const allFields = fieldsWithParentCategory.map(field => field.key);

        // 设置选中的列，包括所有标准字段和扩展字段
        setSelectedColumns(allFields);
        setColumnOrder(allFields);
      } else {
        // 初始化分类选择 - 默认不选中任何分类
        setSelectedCategories([]);

        // 初始化列选择 - 只选中标准字段
        if (tableFields && tableFields.length > 0) {
          // 确保包含parentCategory字段
          const fieldsWithParentCategory = ensureParentCategoryField(tableFields);
          initializeColumnSelection(fieldsWithParentCategory);
        }
      }

      // 无论是否有初始分类，都尝试加载所有可用的扩展字段
      if (categoryMode === 'device') {
        getExtFieldsByCategory(initialSelectedCategory ? [initialSelectedCategory] : []);
      }
    }
  }, [isOpen, tableFields, initialSelectedCategory, initialCategoryMode, initializeColumnSelection, setSelectedCategories, setSelectedColumns, setColumnOrder]);

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  // 获取导出项数量
  const getExportCount = () => {
    switch (exportMode) {
      case 'selected':
        return selectedCount;
      case 'filtered':
        return items.length;
      case 'all':
        return totalCount;
      default:
        return 0;
    }
  };

  // 获取导出项
  const getExportItems = () => {
    // 注意：这里我们只返回传入的数据，实际的筛选将在exportService中根据导出对话框中的分类选择进行
    // 这样可以确保导出的数据是基于导出对话框中的分类选择，而不是基于左侧树状图的选择
    switch (exportMode) {
      case 'selected':
        // 获取选中的项
        const selectedIds = new Set(selectedItems);
        return items.filter(item => selectedIds.has(item.id));
      case 'filtered':
        return items;
      case 'all':
        // 这里应该是所有项，但我们只能导出当前已加载的项
        return items;
      default:
        return [];
    }
  };



  // 生成文件名
  const generateFileName = () => {
    const date = new Date();
    const dateStr = `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`;
    const timeStr = `${date.getHours().toString().padStart(2, '0')}${date.getMinutes().toString().padStart(2, '0')}`;

    // 在选中导出模式下添加"选中"标识
    const prefix = exportMode === 'selected' ? '设备台账_选中' : '设备台账';

    return `${prefix}_${dateStr}_${timeStr}.${format === 'excel' ? 'xlsx' : format}`;
  };

  // 验证导出设置
  const validateExportSettings = (): string | null => {
    // 在选中导出模式下，不需要验证分类选择
    if (exportMode === 'selected') {
      // 只需要验证列选择
      if (selectedColumns.length === 0) {
        return '请至少选择一个导出列';
      }
      return null;
    }

    // 在其他导出模式下，需要验证分类和列选择
    if (selectedCategories.length === 0) {
      const categoryTypeName = categoryMode === 'device' ? '设备分类' : '部门分类';
      return `请至少选择一个${categoryTypeName}`;
    }

    if (selectedColumns.length === 0) {
      return '请至少选择一个导出列';
    }

    return null;
  };

  // 检查是否可以导出
  const canExport = (): boolean => {
    // 如果正在导出或发送，不能导出
    if (isExporting || isSending) {
      return false;
    }

    // 检查验证条件
    return validateExportSettings() === null;
  };



  // 处理导出
  const handleExport = async () => {
    try {
      // 重置状态
      setShowSuccess(false);
      setLocalError(undefined);

      // 验证必要的选择
      const validationError = validateExportSettings();
      if (validationError) {
        setLocalError(validationError);
        return;
      }

      // 确保部门树已加载
      if (categoryMode === 'department') {
        console.log('确保部门树已加载...');
        const departmentService = DepartmentService.getInstance();
        await departmentService.loadDepartmentTree(true); // 强制刷新部门树
      }

      // 获取导出项
      const exportItems = getExportItems();
      if (exportItems.length === 0) {
        setLocalError('没有可导出的数据');
        return;
      }

      // 生成文件名
      const fullFileName = generateFileName();

      console.log(`开始导出${exportItems.length}条数据，格式: ${format}, 文件名: ${fullFileName}`);
      console.log('选中的分类:', selectedCategories);
      console.log('选中的列:', selectedColumns);
      console.log('列顺序:', columnOrder);

      try {
        // 如果是PDF格式，设置PDF选项
        if (format === 'pdf') {
          exportService.setPdfOptions({
            format: pdfFormat,
            orientation: pdfOrientation
          });
        }

        // 如果是Word格式，设置Word选项
        if (format === 'word') {
          exportService.setWordOptions({
            format: wordFormat,
            orientation: wordOrientation
          });
        }

        // 注册事件监听
        exportService.on('send-completed', handleSendCompleted);

        // 执行导出并发送到服务器
        await exportWithSettingsAndSend(
          exportItems,
          categoryMode === 'device' ? (deviceCategories || []) : (departmentCategories || []),
          ensureParentCategoryField(dynamicTableFields),
          fullFileName,
          format
        );

        // 注意：成功消息和对话框关闭逻辑已移到 handleSendCompleted 中处理
        // 这样可以确保在服务器处理完成后才显示成功消息
      } catch (exportError: any) {
        console.error('导出过程中发生错误:', exportError);

        // 提供更详细的错误信息
        let errorMessage = '导出失败';
        if (exportError.message) {
          if (exportError.message.includes('autoTable')) {
            errorMessage = 'PDF生成失败: 表格生成组件错误';
          } else if (exportError.message.includes('Word')) {
            errorMessage = 'Word文档生成失败: ' + exportError.message;
          } else {
            errorMessage = exportError.message;
          }
        }

        setLocalError(errorMessage);
      }
    } catch (err: any) {
      console.error('导出处理失败:', err);
      setLocalError(err.message || '导出处理失败');
    }
  };



  // 重置分类选择
  const handleResetCategories = () => {
    setSelectedCategories([]);
  };

  // 重置列选择
  const handleResetColumns = () => {
    setSelectedColumns([]);
    setColumnOrder([]);
  };

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleCancel}
      width="100%"
      maxWidth="56rem"
      maxHeight="95vh"
      animation="fade"
      escPriority={200} // 较高优先级，确保可以取消导出任务
      closeOnOverlayClick={false} // 防止意外关闭，保护用户的导出配置
    >
      <div className="flex flex-col h-full max-h-[95vh]">
        {/* 对话框标题 - 固定高度 */}
        <div className="flex-shrink-0 flex items-center justify-between px-4 sm:px-6 py-2.5 border-b">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-800">导出设备台账</h2>
            {exportMode === 'selected' && (
              <p className="text-xs sm:text-sm text-blue-600 mt-1">
                已选择 {selectedCount} 条记录进行导出
              </p>
            )}
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-500 hover:text-gray-700 flex-shrink-0"
            disabled={isExporting || isSending}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 对话框内容 - 可滚动区域 */}
        <div className="flex-1 overflow-y-auto min-h-0 p-3 sm:p-4">{/* 响应式内边距 */}
          {/* 成功消息 */}
          {showSuccess && (
            <div className="mb-3 p-2 bg-green-50 border border-green-200 rounded text-sm flex items-start">
              <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <div className="text-green-700">
                <p>导出成功！</p>
                {outputPath && <p className="mt-1">文件已保存至: {outputPath}</p>}
              </div>
            </div>
          )}

          {/* 分类选择和列选择区域 */}
          <div className="mb-3 space-y-3">
              {/* 分类选择模式 */}
              <div>
                <div className="flex space-x-4 text-sm">
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio"
                      name="categoryMode"
                      value="device"
                      checked={categoryMode === 'device'}
                      onChange={() => {
                        setCategoryMode('device');
                        console.log('切换到设备分类模式');
                      }}
                      disabled={isExporting || isSending || exportMode === 'selected'}
                    />
                    <span className={`ml-2 ${exportMode === 'selected' ? 'text-gray-400' : ''}`}>设备分类</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio"
                      name="categoryMode"
                      value="department"
                      checked={categoryMode === 'department'}
                      onChange={() => {
                        setCategoryMode('department');
                        console.log('切换到部门分类模式');
                      }}
                      disabled={isExporting || isSending || exportMode === 'selected'}
                    />
                    <span className={`ml-2 ${exportMode === 'selected' ? 'text-gray-400' : ''}`}>部门分类</span>
                  </label>
                </div>
                {exportMode === 'selected' && (
                  <div className="mt-1 text-xs text-blue-600 italic">
                    <span>选中导出模式下，将直接导出选中的台账，无需选择分类</span>
                  </div>
                )}
              </div>

              {/* 分类选择器和列选择器容器 - 响应式布局 */}
              <div className="flex flex-col lg:flex-row gap-3">
                {/* 分类选择器 - 响应式高度和宽度 */}
                <div className="h-48 sm:h-56 lg:h-80 lg:w-[35%]">
                  <CategorySelector
                    categories={categoryMode === 'device'
                      ? (deviceCategories || [])
                      : (departmentCategories || [])}
                    selectedCategories={selectedCategories}
                    onSelectCategory={setSelectedCategories}
                    categoryMode={categoryMode}
                    title={exportMode === 'selected'
                      ? '选中导出模式'
                      : (categoryMode === 'device' ? '选择设备分类 *' : '选择部门分类 *')}
                    onReset={handleResetCategories}
                    disabled={exportMode === 'selected'}
                  />
                </div>

                {/* 列选择器 - 响应式高度和宽度 */}
                <div className="h-48 sm:h-56 lg:h-80 lg:w-[65%]">
                  <ColumnSelector
                    columns={ensureParentCategoryField(dynamicTableFields)}
                    selectedColumns={selectedColumns}
                    onSelectColumns={setSelectedColumns}
                    onReorderColumns={setColumnOrder}
                    title="选择导出列 *"
                    onReset={handleResetColumns}
                  />
                </div>
              </div>

              {/* 必选项提示 */}
              {exportMode !== 'selected' && (
                <div className="mt-2 text-xs text-gray-500 flex items-center">
                  <span className="text-red-500 mr-1">*</span>
                  <span>标记为必选项，请确保已选择分类和导出列</span>
                </div>
              )}
              {exportMode === 'selected' && (
                <div className="mt-2 text-xs text-gray-500 flex items-center">
                  <span className="text-red-500 mr-1">*</span>
                  <span>请确保已选择导出列</span>
                </div>
              )}
          </div>

          {/* 导出格式选择 - 响应式紧凑样式 */}
          <div className="mb-3">
            <label className="block text-gray-700 font-medium mb-2 text-sm">导出格式</label>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              <label className="inline-flex items-center p-2 border rounded hover:bg-gray-50 cursor-pointer transition-colors">
                <input
                  type="radio"
                  className="form-radio hidden"
                  name="format"
                  value="excel"
                  checked={format === 'excel'}
                  onChange={() => setFormat('excel')}
                  disabled={isExporting || isSending}
                />
                <div className={`flex flex-col sm:flex-col items-center w-full ${format === 'excel' ? 'text-blue-600' : 'text-gray-700'}`}>
                  <FileDown className="h-5 w-5 sm:h-6 sm:w-6 mb-1" />
                  <span className="text-xs">Excel (.xlsx)</span>
                </div>
              </label>

              <label className="inline-flex items-center p-2 border rounded hover:bg-gray-50 cursor-pointer transition-colors">
                <input
                  type="radio"
                  className="form-radio hidden"
                  name="format"
                  value="pdf"
                  checked={format === 'pdf'}
                  onChange={() => setFormat('pdf')}
                  disabled={isExporting || isSending}
                />
                <div className={`flex flex-col sm:flex-col items-center w-full ${format === 'pdf' ? 'text-blue-600' : 'text-gray-700'}`}>
                  <FileText className="h-5 w-5 sm:h-6 sm:w-6 mb-1" />
                  <span className="text-xs">PDF (.pdf)</span>
                </div>
              </label>

              <label className="inline-flex items-center p-2 border rounded hover:bg-gray-50 cursor-pointer transition-colors">
                <input
                  type="radio"
                  className="form-radio hidden"
                  name="format"
                  value="word"
                  checked={format === 'word'}
                  onChange={() => setFormat('word')}
                  disabled={isExporting || isSending}
                />
                <div className={`flex flex-col sm:flex-col items-center w-full ${format === 'word' ? 'text-blue-600' : 'text-gray-700'}`}>
                  <FileImage className="h-5 w-5 sm:h-6 sm:w-6 mb-1" />
                  <span className="text-xs">Word (.docx)</span>
                </div>
              </label>
            </div>

            {/* PDF格式额外设置 - 响应式紧凑样式 */}
            {format === 'pdf' && (
              <div className="mt-2 p-2 sm:p-3 bg-gray-50 rounded">
                <h4 className="text-xs font-medium text-gray-700 mb-2">PDF设置</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {/* 页面大小设置 */}
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">页面大小</label>
                    <div className="flex space-x-2">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="pdfFormat"
                          value="a3"
                          checked={pdfFormat === 'a3'}
                          onChange={() => setPdfFormat('a3')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">A3</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="pdfFormat"
                          value="a4"
                          checked={pdfFormat === 'a4'}
                          onChange={() => setPdfFormat('a4')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">A4</span>
                      </label>
                    </div>
                  </div>

                  {/* 页面方向设置 */}
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">页面方向</label>
                    <div className="flex space-x-2">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="pdfOrientation"
                          value="landscape"
                          checked={pdfOrientation === 'landscape'}
                          onChange={() => setPdfOrientation('landscape')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">横向</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="pdfOrientation"
                          value="portrait"
                          checked={pdfOrientation === 'portrait'}
                          onChange={() => setPdfOrientation('portrait')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">纵向</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Word格式额外设置 - 响应式紧凑样式 */}
            {format === 'word' && (
              <div className="mt-2 p-2 sm:p-3 bg-gray-50 rounded">
                <h4 className="text-xs font-medium text-gray-700 mb-2">Word设置</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {/* 页面大小设置 */}
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">页面大小</label>
                    <div className="flex space-x-2">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="wordFormat"
                          value="a3"
                          checked={wordFormat === 'a3'}
                          onChange={() => setWordFormat('a3')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">A3</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="wordFormat"
                          value="a4"
                          checked={wordFormat === 'a4'}
                          onChange={() => setWordFormat('a4')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">A4</span>
                      </label>
                    </div>
                  </div>

                  {/* 页面方向设置 */}
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">页面方向</label>
                    <div className="flex space-x-2">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="wordOrientation"
                          value="landscape"
                          checked={wordOrientation === 'landscape'}
                          onChange={() => setWordOrientation('landscape')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">横向</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          className="form-radio"
                          name="wordOrientation"
                          value="portrait"
                          checked={wordOrientation === 'portrait'}
                          onChange={() => setWordOrientation('portrait')}
                          disabled={isExporting || isSending}
                        />
                        <span className="ml-1 text-xs">纵向</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 错误消息 - 紧凑样式 */}
          {localError && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm flex items-start">
              <AlertCircle className="h-4 w-4 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
              <div className="text-red-700">{localError}</div>
            </div>
          )}

          {/* 进度条 - 紧凑样式 */}
          {(isExporting || isSending) && (
            <div className="mb-3">
              <div className="flex justify-between text-xs text-gray-600 mb-1">
                <span>{isExporting ? '正在生成文件...' : '正在发送到服务器...'}</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* 对话框底部按钮 - 固定在底部，响应式样式 */}
        <div className="flex-shrink-0 px-3 sm:px-4 py-2.5 border-t bg-white flex justify-end space-x-3">
          <button
            onClick={handleCancel}
            className="px-6 py-3 border border-gray-300 rounded text-base font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            disabled={isExporting || isSending}
          >
            取消
          </button>
          <button
            onClick={handleExport}
            className="px-6 py-3 bg-blue-600 text-white rounded text-base font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-colors"
            disabled={!canExport()}
            title={!canExport() && !isExporting && !isSending ? validateExportSettings() || '' : ''}
          >
            <FileDown className="h-4 w-4 mr-2" />
            导出
          </button>
        </div>
      </div>
    </DialogBase>
  );
};

export default ExportDialog;
