import React, { useEffect, useState } from 'react';
import { Card, Table, Tabs, Progress, Alert, Typography, Spin, Empty, Tag } from 'antd';
import { useConnection } from '../contexts/ConnectionContext';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, Tooltip, Legend, Cell, ResponsiveContainer } from 'recharts';

const { Title, Text } = Typography;

// 颜色配置
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28DFF', '#FF6B6B', '#4ECDC4', '#C7F464', '#FF9F1C'];

// 将毫秒转换为可读格式
const formatDuration = (ms: number) => {
  if (ms < 1000) {
    return `${ms}毫秒`;
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}秒`;
  } else {
    return `${(ms / 60000).toFixed(1)}分钟`;
  }
};

// 定义图表数据类型
interface PieDataItem {
  name: string;
  value: number;
}

interface DurationDataItem {
  range: string;
  count: number;
}

interface TypeDataItem {
  type: string;
  count: number;
  avgTime: number;
  successRate: number;
}

interface DurationDistribution {
  [range: string]: number;
}

// 格式化任务类型显示
const formatTaskType = (type: string): string => {
  if (!type || type === 'unknown') {
    return '未知任务';
  }
  
  // 分割任务类型（通常是 dllName_funcName 格式）
  const parts = type.split('_');
  if (parts.length >= 2) {
    // 尝试转换为更友好的显示格式
    const dllName = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    const funcName = parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
    
    // 特殊处理某些已知任务类型
    if (dllName === 'Ntfstool') {
      if (funcName === 'ScanDeletedFilesStr') return '文件恢复扫描';
      if (funcName === 'RecoverFileStr') return '文件恢复';
      if (funcName === 'GetDiskListStr') return '获取磁盘列表';
      if (funcName === 'GetVolumeListStr') return '获取分区列表';
      if (funcName === 'ExportScanResultStr') return '导出扫描结果';
    }
    
    // 常规格式化
    return `${dllName}/${funcName}`;
  }
  
  return type;
};

const TaskAnalytics: React.FC = () => {
  const { task } = useConnection();
  const [loading, setLoading] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [typeStats, setTypeStats] = useState<any>(null);
  const [activeTasks, setActiveTasks] = useState<any[]>([]);
  const [completedTasks, setCompletedTasks] = useState<any[]>([]);
  const [failedTasks, setFailedTasks] = useState<any[]>([]);
  const [durationDistribution, setDurationDistribution] = useState<DurationDistribution>({});

  // 准备饼图数据
  const preparePieData = (): PieDataItem[] => {
    if (!stats) return [];
    
    return [
      { name: '完成', value: stats.completedTasks },
      { name: '失败', value: stats.failedTasks },
      { name: '取消', value: stats.cancelledTasks },
      { name: '进行中', value: activeTasks.length }
    ].filter(item => item.value > 0);
  };

  // 准备时长分布数据
  const prepareDurationData = (): DurationDataItem[] => {
    if (!durationDistribution) return [];
    
    return Object.entries(durationDistribution).map(([range, count]) => ({
      range,
      count
    })).filter(item => item.count > 0);
  };

  // 准备任务类型数据
  const prepareTypeData = (): TypeDataItem[] => {
    if (!typeStats) return [];
    
    return Object.entries(typeStats).map(([type, data]: [string, any]) => ({
      type: formatTaskType(type),
      rawType: type,
      count: data.count,
      avgTime: data.avgTime,
      successRate: data.successRate * 100
    })).sort((a, b) => b.count - a.count);
  };

  // 加载数据
  const loadData = () => {
    setLoading(true);
    
    try {
      // 获取整体统计
      const overallStats = task.getTaskStats();
      setStats(overallStats);
      
      // 获取类型统计
      const taskTypeStats = task.getTaskTypeStats();
      setTypeStats(taskTypeStats);
      
      // 获取活跃任务
      const currentActiveTasks = task.getActiveTasks();
      setActiveTasks(currentActiveTasks);
      
      // 获取最近完成的任务
      const recentCompleted = task.getRecentCompletedTasks(10);
      setCompletedTasks(recentCompleted);
      
      // 获取最近失败的任务
      const recentFailed = task.getRecentFailedTasks(10);
      setFailedTasks(recentFailed);
      
      // 获取持续时间分布
      const durDist = task.getTaskDurationDistribution();
      setDurationDistribution(durDist);
    } catch (error) {
      console.error('加载任务统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // 初始加载
  useEffect(() => {
    loadData();
    
    // 设置刷新间隔
    const interval = window.setInterval(loadData, 5000);
    setRefreshInterval(interval);
    
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  // 活跃任务表格列
  const activeColumns = [
    {
      title: '任务ID',
      dataIndex: 'taskId',
      key: 'taskId',
      ellipsis: true,
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'taskType',
      key: 'taskType',
      render: (text: string) => formatTaskType(text),
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'running' ? 'processing' : 'default'}>
          {status === 'running' ? '运行中' : '等待中'}
        </Tag>
      ),
      width: 100,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (time: number) => new Date(time).toLocaleString(),
      width: 180,
    },
    {
      title: '已运行',
      dataIndex: 'startTime',
      key: 'duration',
      render: (startTime: number) => formatDuration(Date.now() - startTime),
      width: 120,
    },
    {
      title: '尝试次数',
      dataIndex: 'attempts',
      key: 'attempts',
      width: 100,
    }
  ];

  // 已完成任务表格列
  const completedColumns = [
    {
      title: '任务ID',
      dataIndex: 'taskId',
      key: 'taskId',
      ellipsis: true,
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'taskType',
      key: 'taskType',
      render: (text: string) => formatTaskType(text),
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={
          status === 'completed' ? 'success' : 
          status === 'failed' ? 'error' : 
          'default'
        }>
          {status === 'completed' ? '已完成' : 
           status === 'failed' ? '失败' : 
           status === 'cancelled' ? '已取消' : status}
        </Tag>
      ),
      width: 100,
    },
    {
      title: '完成时间',
      dataIndex: 'endTime',
      key: 'endTime',
      render: (time: number) => time ? new Date(time).toLocaleString() : '-',
      width: 180,
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => formatDuration(duration),
      width: 120,
    },
    {
      title: '尝试次数',
      dataIndex: 'attempts',
      key: 'attempts',
      width: 100,
    }
  ];

  // 失败任务表格列
  const failedColumns = [
    ...completedColumns,
    {
      title: '错误信息',
      dataIndex: 'error',
      key: 'error',
      ellipsis: true,
    }
  ];

  const pieData = preparePieData();
  const durationData = prepareDurationData();
  const typeData = prepareTypeData();

  // 定义Tab项配置
  const tabItems = [
    {
      key: 'active',
      label: '活跃任务',
      children: (
        <>
          <Alert
            message={`当前有 ${activeTasks.length} 个任务正在执行`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Table
            dataSource={activeTasks}
            columns={activeColumns}
            rowKey="taskId"
            pagination={{ pageSize: 5 }}
            size="small"
            locale={{ emptyText: '当前没有活跃任务' }}
          />
        </>
      )
    },
    {
      key: 'completed',
      label: '最近完成',
      children: (
        <Table
          dataSource={completedTasks}
          columns={completedColumns}
          rowKey="taskId"
          pagination={{ pageSize: 5 }}
          size="small"
          locale={{ emptyText: '暂无已完成任务记录' }}
        />
      )
    },
    {
      key: 'failed',
      label: '失败任务',
      children: (
        <Table
          dataSource={failedTasks}
          columns={failedColumns}
          rowKey="taskId"
          pagination={{ pageSize: 5 }}
          size="small"
          locale={{ emptyText: '暂无失败任务记录' }}
        />
      )
    },
    {
      key: 'types',
      label: '任务类型分析',
      children: (
        <Table
          dataSource={typeData}
          columns={[
            {
              title: '任务类型',
              dataIndex: 'type',
              key: 'type',
            },
            {
              title: '任务数量',
              dataIndex: 'count',
              key: 'count',
              sorter: (a: TypeDataItem, b: TypeDataItem) => a.count - b.count,
            },
            {
              title: '平均完成时间',
              dataIndex: 'avgTime',
              key: 'avgTime',
              render: (time: number) => formatDuration(time),
              sorter: (a: TypeDataItem, b: TypeDataItem) => a.avgTime - b.avgTime,
            },
            {
              title: '成功率',
              dataIndex: 'successRate',
              key: 'successRate',
              render: (rate: number) => (
                <div>
                  <Progress
                    percent={Number(rate.toFixed(1))}
                    size="small"
                    status={rate > 90 ? 'success' : rate > 60 ? 'normal' : 'exception'}
                  />
                </div>
              ),
              sorter: (a: TypeDataItem, b: TypeDataItem) => a.successRate - b.successRate,
            }
          ]}
          rowKey="type"
          pagination={{ pageSize: 5 }}
          size="small"
          locale={{ emptyText: '暂无任务类型统计数据' }}
        />
      )
    }
  ];

  return (
    <div className="task-analytics">
      <Title level={4}>任务执行分析</Title>
      <Text type="secondary">展示系统任务执行情况和性能指标，帮助识别性能瓶颈和优化机会</Text>
      
      <div className="my-4 border-t border-gray-200" />
      
      <Spin spinning={loading}>
        {stats && (
          <div className="stats-overview">
            <Card title="任务执行概览" variant="outlined">
              <div className="stats-cards">
                <div className="stat-card">
                  <div className="stat-title">总任务数</div>
                  <div className="stat-value">{stats.totalTasks}</div>
                </div>
                <div className="stat-card">
                  <div className="stat-title">完成任务</div>
                  <div className="stat-value">{stats.completedTasks}</div>
                </div>
                <div className="stat-card">
                  <div className="stat-title">失败任务</div>
                  <div className="stat-value">{stats.failedTasks}</div>
                </div>
                <div className="stat-card">
                  <div className="stat-title">平均完成时间</div>
                  <div className="stat-value">{formatDuration(stats.averageCompletionTime)}</div>
                </div>
                <div className="stat-card">
                  <div className="stat-title">平均尝试次数</div>
                  <div className="stat-value">{stats.averageAttempts.toFixed(1)}</div>
                </div>
              </div>
              
              <div className="charts-row">
                <div className="chart-container">
                  <h3>任务状态分布</h3>
                  {pieData.length > 0 ? (
                    <ResponsiveContainer width="100%" height={250}>
                      <PieChart>
                        <Pie
                          data={pieData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }: { name: string; percent: number }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {pieData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: number) => [`${value}个任务`, '数量']} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <Empty description="暂无数据" />
                  )}
                </div>
                
                <div className="chart-container">
                  <h3>任务执行时间分布</h3>
                  {durationData.length > 0 ? (
                    <ResponsiveContainer width="100%" height={250}>
                      <BarChart
                        data={durationData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <XAxis dataKey="range" />
                        <YAxis />
                        <Tooltip formatter={(value: number) => [`${value}个任务`, '数量']} />
                        <Legend />
                        <Bar dataKey="count" fill="#8884d8" name="任务数量">
                          {durationData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <Empty description="暂无数据" />
                  )}
                </div>
              </div>
            </Card>
          </div>
        )}
        
        <Tabs defaultActiveKey="active" className="task-tabs" items={tabItems} />
      </Spin>
      
      <div className="task-analytics-styles">
        <style>
          {`
          .task-analytics {
            padding: 16px;
          }
          
          .stats-cards {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
            flex-wrap: wrap;
          }
          
          .stat-card {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 16px;
            flex: 1;
            min-width: 150px;
            margin-right: 12px;
            margin-bottom: 12px;
          }
          
          .stat-card:last-child {
            margin-right: 0;
          }
          
          .stat-title {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
          }
          
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
          }
          
          .charts-row {
            display: flex;
            margin: 0 -12px;
            flex-wrap: wrap;
          }
          
          .chart-container {
            flex: 1;
            min-width: 300px;
            padding: 0 12px;
            margin-bottom: 24px;
          }
          
          .task-tabs {
            margin-top: 24px;
          }
          
          @media (max-width: 768px) {
            .stat-card {
              min-width: calc(50% - 12px);
            }
            
            .chart-container {
              min-width: 100%;
            }
          }
          `}
        </style>
      </div>
    </div>
  );
};

export default TaskAnalytics; 