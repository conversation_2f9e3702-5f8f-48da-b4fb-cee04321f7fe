import React, { useEffect, useState } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConnectionProvider, useConnection } from './contexts/ConnectionContext';
import { CacheProvider } from './contexts/CacheContext';
import Layout from './components/Layout';
import { GlobalRefreshProgress } from './components/GlobalRefreshProgress';

// 导入自定义拖拽样式
import './styles/customDrag.css';

// 导入服务初始化器
import InventoryService from './services/Inventory/inventoryService';
import LogsService from './services/logs/logsService';

import SystemInitService from './services/systemInitService';
import GlobalConfigService from './services/globalConfigService';

// 导入弹窗组件
import AnnouncementDialog from './pages/Announcement/AnnouncementDialog';
import ActivationSuccessDialog from './pages/Announcement/ActivationSuccessDialog';
import ActivationErrorDialog from './pages/Announcement/ActivationErrorDialog';
import ErrorDialog from './components/ErrorDialog';

// 导入Hook
import { useAnnouncementDialog } from './hooks/Announcement/useAnnouncementDialog';
import { useActivation } from './hooks/Announcement/useActivation';

// 导入页面组件
import InventoryManagement from './pages/Inventory/InventoryManagement';
import LogsManagement from './pages/logs/LogsManagement';
import InspectionManagement from './pages/Inspection';
import { DatabaseBackupPage } from './pages/Exceptions/DatabaseBackup/DatabaseBackupPage';

// 连接状态组件
const ConnectionStatus = () => {
  const { state, reconnect } = useConnection();

  if (state.wsStatus === 'connected') {
    return null;
  }

  return (
    <div className={`fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
      state.wsStatus === 'error' ? 'bg-red-50' : 'bg-blue-50'
    }`}>
      <div className="flex items-center space-x-2">
        {state.wsStatus === 'connecting' && (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent" />
            <span className="text-blue-700">正在连接服务器...</span>
          </>
        )}
        {state.wsStatus === 'error' && (
          <>
            <span className="text-red-700">●</span>
            <span className="text-red-700">连接服务器失败: {state.error}</span>
            <button
              onClick={() => reconnect()}
              className="ml-2 px-2 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors duration-200"
            >
              重试
            </button>
          </>
        )}
        {state.wsStatus === 'disconnected' && (
          <>
            <span className="text-yellow-700">●</span>
            <span className="text-yellow-700">已断开连接</span>
            <button
              onClick={() => reconnect()}
              className="ml-2 px-2 py-1 text-sm bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 transition-colors duration-200"
            >
              重新连接
            </button>
          </>
        )}
      </div>
    </div>
  );
};

// 应用初始化组件
const AppInitializer = () => {
  const { state, ws } = useConnection();

  // 记录应用是否已经初始化
  const [isInitialized, setIsInitialized] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // 使用公告弹窗Hook（包含公告数据）
  const {
    isAnnouncementDialogOpen,
    openAnnouncementDialog,
    closeAnnouncementDialog,
    closeActivationDialog,
    handleActivationSuccess,
    handleActivationErrorRetry,
    announcement,
    isAnnouncementLoading,
    announcementError,
    retryLoadAnnouncement
  } = useAnnouncementDialog();

  // 使用激活Hook
  const {
    companyName,
    isLoading: isActivationLoading,
    isQueryingCompany,
    isActivating,
    error: activationError,
    showSuccessDialog,
    showErrorDialog: showActivationErrorDialog,
    setCompanyName,
    handleQueryCompany,
    clearError,
    hideActivationSuccess,
    hideActivationError,
    resetActivation
  } = useActivation();

  useEffect(() => {
    const initialize = async () => {
      // 如果已经初始化，则不重复初始化
      if (isInitialized) {
        console.log('应用已经初始化，跳过重复初始化');
        return;
      }

      if (state.wsStatus === 'connected') {
        try {
          console.log('应用初始化...');
          console.log('WebSocket已连接，状态:', state.wsStatus);

          // 步骤1: 系统初始化（包括根节点验证）
          console.log('开始系统初始化...');
          const systemInitService = SystemInitService.getInstance();
          const systemInitSuccess = await systemInitService.initialize();

          if (!systemInitSuccess) {
            console.error('系统初始化失败：公司名称验证失败');
            setIsInitialized(true);

            // 根据错误类型显示不同的弹窗
            const errorType = GlobalConfigService.getRootNodeValidationError();
            console.log('公司名称验证错误类型:', errorType);

            if (errorType === 'T00表为空') {
              console.log('显示公告窗口');
              openAnnouncementDialog();
            } else {
              console.log('显示错误提示窗口');
              setErrorMessage(errorType || '未知错误');
              setShowErrorDialog(true);
            }

            // 重要：直接返回，不再执行任何后续的服务初始化
            console.log('公司名称验证失败，停止所有服务初始化');
            return;
          }

          console.log('系统初始化成功，开始初始化业务服务...');

          // 步骤2: 初始化业务服务（只有在根节点验证成功后才执行）
          console.log('创建InventoryService实例...');
          const inventoryService = InventoryService.getInstance();

          // 等待InventoryService初始化完成
          console.log('等待InventoryService初始化完成...');
          await inventoryService.initializeWhenReady();

          // 不再在App初始化时加载部门树，交由InventoryService统一管理
          console.log('部门树加载将由InventoryService统一管理，跳过App层的部门树加载');

          // 初始化LogsService
          console.log('初始化LogsService...');
          LogsService.getInstance();

          console.log('应用初始化完成');

          // 标记应用已初始化
          setIsInitialized(true);
        } catch (error) {
          console.error('应用初始化失败:', error);
          setIsInitialized(true);

          // 显示错误提示窗口
          setErrorMessage(error instanceof Error ? error.message : '未知错误');
          setShowErrorDialog(true);
        }
      } else {
        console.log('等待WebSocket连接后再初始化应用...');
        console.log('WebSocket当前状态:', state.wsStatus);
        // 尝试连接WebSocket
        try {
          console.log('尝试连接WebSocket...');
          await ws.connect();
        } catch (error) {
          console.error('WebSocket连接失败:', error);
        }
      }
    };

    initialize();
  }, [state.wsStatus, ws, isInitialized, openAnnouncementDialog]);

  return (
    <>
      {/* 公告窗口 */}
      <AnnouncementDialog
        isOpen={isAnnouncementDialogOpen}
        onClose={closeAnnouncementDialog}
        announcement={announcement}
        isLoading={isAnnouncementLoading}
        error={announcementError}
        onRetry={retryLoadAnnouncement}
        // 激活相关props
        companyName={companyName}
        isActivationLoading={isActivationLoading}
        isQueryingCompany={isQueryingCompany}
        isActivating={isActivating}
        activationError={activationError}
        onCompanyNameChange={setCompanyName}
        onActivate={handleQueryCompany}
        onClearActivationError={clearError}
      />



      {/* 激活成功弹窗 */}
      <ActivationSuccessDialog
        isOpen={showSuccessDialog}
        onConfirm={() => {
          hideActivationSuccess();
          handleActivationSuccess();
        }}
      />

      {/* 激活错误弹窗 */}
      <ActivationErrorDialog
        isOpen={showActivationErrorDialog}
        onClose={() => {
          hideActivationError();
          closeActivationDialog();
        }}
        errorMessage={activationError || '激活失败'}
        onRetry={() => {
          hideActivationError();
          resetActivation(); // 重置激活状态，清除之前的错误
          handleActivationErrorRetry();
        }}
      />

      {/* 错误提示窗口 */}
      <ErrorDialog
        isOpen={showErrorDialog}
        onClose={() => setShowErrorDialog(false)}
        errorMessage={errorMessage}
      />
    </>
  );
};

// 应用路由组件
const AppRoutes = () => {
  return (
    <Router
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <AppInitializer />
      <Routes>
        <Route path="/" element={<Layout />}>
          {/* 台账管理 */}
          <Route path="inventory" element={<InventoryManagement />} />

          {/* 巡检管理 */}
          <Route path="data-sync" element={<InspectionManagement />} />

          {/* 异常处理 */}
          <Route path="exceptions" element={<DatabaseBackupPage />} />

          {/* 日志管理 */}
          <Route path="logs" element={<LogsManagement />} />

          {/* 默认重定向到台账管理 */}
          <Route index element={<Navigate to="/inventory" replace />} />
        </Route>
      </Routes>
      <ConnectionStatus />
      <GlobalRefreshProgress />
    </Router>
  );
};

function App() {
  return (
    <ConnectionProvider>
      <CacheProvider>
        <AppRoutes />
      </CacheProvider>
    </ConnectionProvider>
  );
}

export default App;