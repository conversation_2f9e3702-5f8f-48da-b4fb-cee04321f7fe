import mitt from 'mitt';
import { InventoryItem } from '../../../types/inventory';
import { BaseService, BaseServiceEvents, BaseServiceState } from '../../base/baseService';
import * as XLSX from 'xlsx';

// 导入服务事件类型
export interface ImportServiceEvents extends BaseServiceEvents<ImportServiceState> {
  'import-started': { fileName: string };
  'import-progress': { progress: number };
  'import-completed': { importedData: InventoryItem[]; count: number };
  'import-failed': { error: string };
  'validation-error': { errors: string[] };
}

// 导入服务状态类型
export interface ImportServiceState extends BaseServiceState {
  isImporting: boolean;
  progress: number;
  lastImportCount: number;
  validationErrors: string[];
  failedRows: any[]; // 存储验证失败的行数据
  originalData: any[]; // 存储原始导入数据
}

// 导入结果类型
export interface ImportResult {
  success: boolean;
  importedData: InventoryItem[];
  count: number;
  errors?: string[];
}

// 导入字段映射类型
interface FieldMapping {
  [key: string]: string;
}

/**
 * 导入服务
 * 负责从CSV或Excel文件导入设备台账数据
 */
class ImportService extends BaseService<ImportServiceState, ImportServiceEvents> {
  private static instance: ImportService;

  // 字段映射表 - 将Excel表头映射到InventoryItem属性
  private fieldMapping: FieldMapping = {
    '设备ID': 'id',
    '设备名称': 'name',
    '设备类型': 'type',
    '一级分类': 'parentCategory',
    '使用状态': 'status',
    '厂商': 'manufacturer',
    '型号': 'model',
    '所属部门': 'department',
    '责任人': 'responsible',
    '岗位密级': 'ext_position_security_level',
    '联系方式': 'ext_contact_info',
    '位置': 'location',
    '启用时间': 'startTime',
    '保密编号': 'securityCode',
    '密级': 'securityLevel',
    '用途': 'purpose',
    '安全RFID': 'securityRfid'
  };

  // 必填字段列表
  private requiredFields: string[] = [
    'name',
    'type',
    'responsible'
  ];

  /**
   * 构造函数
   */
  private constructor() {
    super('ImportService', {
      isLoading: false,
      isImporting: false,
      progress: 0,
      lastImportCount: 0,
      validationErrors: [],
      failedRows: [],
      originalData: []
    });
  }

  /**
   * 获取单例实例
   * @returns ImportService实例
   */
  public static getInstance(): ImportService {
    if (!ImportService.instance) {
      ImportService.instance = new ImportService();
    }
    return ImportService.instance;
  }

  /**
   * 导入设备台账数据
   * @param file 导入文件对象
   * @returns 导入结果
   */
  public async importInventory(file: File): Promise<ImportResult> {
    try {
      // 更新状态
      this.updateState({
        isImporting: true,
        progress: 0,
        validationErrors: [],
        failedRows: [],
        originalData: [],
        error: undefined
      });

      // 触发导入开始事件
      this.emitter.emit('import-started', { fileName: file.name });
      console.log(`开始导入设备台账数据，文件: ${file.name}`);

      // 读取文件
      const fileData = await this.readFile(file);

      // 更新进度
      this.updateState({ progress: 30 });
      this.emitter.emit('import-progress', { progress: 30 });

      // 解析文件数据
      const parsedData = this.parseFileData(fileData, file.name);

      // 保存原始数据
      this.updateState({ originalData: parsedData });

      // 更新进度
      this.updateState({ progress: 60 });
      this.emitter.emit('import-progress', { progress: 60 });

      // 验证数据
      const { isValid, errors, validData } = this.validateData(parsedData);

      if (!isValid) {
        // 更新验证错误和失败行
        this.updateState({
          isImporting: false,
          validationErrors: errors,
          failedRows: this.getFailedRows(parsedData, errors)
        });

        // 触发验证错误事件
        this.emitter.emit('validation-error', { errors });

        return {
          success: false,
          importedData: [],
          count: 0,
          errors
        };
      }

      // 转换为InventoryItem格式
      const importedData = this.convertToInventoryItems(validData);

      // 更新进度
      this.updateState({
        isImporting: false,
        progress: 100,
        lastImportCount: importedData.length
      });

      // 触发导入完成事件
      this.emitter.emit('import-completed', {
        importedData,
        count: importedData.length
      });

      return {
        success: true,
        importedData,
        count: importedData.length
      };
    } catch (error: any) {
      // 更新状态
      this.updateState({
        isImporting: false,
        error: error.message
      });

      // 触发错误事件
      this.emitter.emit('import-failed', { error: error.message });
      this.emitter.emit('error', error.message);

      return {
        success: false,
        importedData: [],
        count: 0,
        errors: [error.message]
      };
    }
  }

  /**
   * 读取文件内容
   * @param file 文件对象
   * @returns 文件数据
   */
  private readFile(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as ArrayBuffer);
        } else {
          reject(new Error('读取文件失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('读取文件失败'));
      };

      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * 解析文件数据
   * @param fileData 文件数据
   * @param fileName 文件名
   * @returns 解析后的数据
   */
  private parseFileData(fileData: ArrayBuffer, fileName: string): any[] {
    try {
      // 创建工作簿
      const wb = XLSX.read(fileData, { type: 'array' });

      // 获取第一个工作表
      const wsName = wb.SheetNames[0];
      const ws = wb.Sheets[wsName];

      // 转换为JSON
      const data = XLSX.utils.sheet_to_json(ws, { header: 'A' });

      // 如果是标题行数据，需要处理
      if (data.length > 0 && typeof data[0] === 'object') {
        // 检查是否有标题行
        const firstRow = data[0] as Record<string, any>;
        const hasHeaderRow = Object.values(firstRow).some(value =>
          typeof value === 'string' &&
          Object.keys(this.fieldMapping).includes(value)
        );

        if (hasHeaderRow) {
          // 使用标题行作为字段名
          return XLSX.utils.sheet_to_json(ws);
        }
      }

      return data;
    } catch (error) {
      console.error('解析文件数据失败:', error);
      throw new Error('解析文件数据失败，请确保文件格式正确');
    }
  }

  /**
   * 验证导入数据
   * @param data 导入数据
   * @returns 验证结果
   */
  private validateData(data: any[]): { isValid: boolean; errors: string[]; validData: any[] } {
    const errors: string[] = [];
    const validData: any[] = [];

    // 检查是否有数据
    if (!data || data.length === 0) {
      errors.push('导入文件中没有数据');
      return { isValid: false, errors, validData };
    }

    // 检查每一行数据
    data.forEach((row, index) => {
      const rowErrors: string[] = [];

      // 检查必填字段
      for (const [excelField, itemField] of Object.entries(this.fieldMapping)) {
        if (this.requiredFields.includes(itemField)) {
          // 查找对应的Excel字段值
          const value = row[excelField];
          if (value === undefined || value === null || value === '') {
            rowErrors.push(`第${index + 1}行缺少必填字段: ${excelField}`);
          }
        }
      }

      if (rowErrors.length === 0) {
        validData.push(row);
      } else {
        errors.push(...rowErrors);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      validData
    };
  }

  /**
   * 将导入数据转换为InventoryItem格式
   * @param data 导入数据
   * @returns InventoryItem数组
   */
  private convertToInventoryItems(data: any[]): InventoryItem[] {
    return data.map(row => {
      // 创建基础InventoryItem对象
      const item: Partial<InventoryItem> = {};

      // 映射标准字段
      for (const [excelField, itemField] of Object.entries(this.fieldMapping)) {
        if (row[excelField] !== undefined) {
          (item as any)[itemField] = row[excelField];
        }
      }

      // 处理扩展字段
      const extendedFields: Record<string, any> = {};
      for (const [field, value] of Object.entries(row)) {
        if (field.startsWith('扩展_') && value !== undefined && value !== null && value !== '') {
          const extFieldName = field.substring(3); // 去掉"扩展_"前缀
          extendedFields[extFieldName] = value;
        }
      }

      if (Object.keys(extendedFields).length > 0) {
        item.extendedFields = extendedFields;
      }

      // 确保ID字段格式正确（现在使用数字格式）
      if (!item.id || (typeof item.id !== 'string' && typeof item.id !== 'number')) {
        // 生成临时数字ID，实际ID会在添加到数据库时生成
        item.id = Math.floor(Math.random() * 10000).toString();
      } else {
        // 确保ID是字符串格式（便于前端处理）
        item.id = item.id.toString();
      }

      return item as InventoryItem;
    });
  }

  /**
   * 生成导入模板
   * @param format 导出格式
   * @returns Blob对象
   */
  public generateImportTemplate(format: 'csv' | 'excel'): Blob {
    try {
      // 创建模板数据
      const templateData = [
        // 创建一个包含所有字段的空行
        Object.keys(this.fieldMapping).reduce((obj, key) => {
          obj[key] = '';
          return obj;
        }, {} as Record<string, string>)
      ];

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(templateData);

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '设备台账导入模板');

      // 根据格式导出
      if (format === 'csv') {
        // 导出为CSV
        const csvContent = XLSX.utils.sheet_to_csv(ws);
        return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      } else {
        // 导出为Excel
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        return new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      }
    } catch (error: any) {
      console.error('生成导入模板失败:', error);
      throw new Error('生成导入模板失败: ' + error.message);
    }
  }

  /**
   * 下载Blob对象
   * @param blob Blob对象
   * @param fileName 文件名
   */
  public downloadBlob(blob: Blob, fileName: string): void {
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;

    // 触发点击事件下载文件
    document.body.appendChild(a);
    a.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 0);
  }

  /**
   * 清除验证错误
   */
  public clearValidationErrors(): void {
    this.updateState({ validationErrors: [] });
  }

  /**
   * 获取失败行数据
   * @param data 原始数据
   * @param errors 错误信息
   * @returns 失败行数据
   */
  private getFailedRows(data: any[], errors: string[]): any[] {
    const failedRows: any[] = [];

    // 从错误信息中提取行号
    errors.forEach(error => {
      const match = error.match(/第(\d+)行/);
      if (match && match[1]) {
        const lineNum = parseInt(match[1]);
        // 行号是从1开始的，数组索引从0开始
        const rowIndex = lineNum - 1;

        // 确保索引有效
        if (rowIndex >= 0 && rowIndex < data.length) {
          // 添加行数据和错误信息
          failedRows.push({
            ...data[rowIndex],
            _errorMessage: error
          });
        }
      }
    });

    return failedRows;
  }

  /**
   * 获取失败行数据
   * @returns 失败行数据
   */
  public getFailedRowsData(): any[] {
    return this.getState().failedRows;
  }
}

export default ImportService;
