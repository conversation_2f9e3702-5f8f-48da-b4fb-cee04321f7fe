import React, { useState, useEffect } from 'react';
import { useInspectionSync } from '../../hooks/Inspection/useInspectionSync';
import ResultDialog from '../../components/Common/ResultDialog';
import GlobalDataRefreshService from '../../services/globalDataRefreshService';
import InspectionLogService from '../../services/Inspection/inspectionLogService';
import CustomScrollbar from '../../components/ui/CustomScrollbar';
import { usePageFocus, useListFocus } from '../../hooks/base';
import {
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Download,
  Smartphone
} from 'lucide-react';

// 巡检详情数据类型定义
interface InspectionDetail {
  device_id: number;
  confidentiality_code: string;
  device_name: string;
  location: string;
  responsible_person: string;
  responsible_person_alias: string;
  inspection_status: number;
  inspection_result: string;
  rfid: string;
  department_info: {
    department_path: string;
    department_id: number;
  };
  usage_status: string;
  confidentiality_level: string;
}

// 移除未使用的InspectionTask接口，改为使用服务中的类型

/**
 * 巡检同步页面
 * 提供从手持端读取巡检数据和保存到PC端的功能
 */
const InspectionSync: React.FC = () => {
  // 页面焦点管理
  const { pageRef, titleRef } = usePageFocus({
    title: '数据同步',
    autoFocus: true
  });

  // 表格焦点管理
  const { listRef, handleKeyDown } = useListFocus({
    itemSelector: 'tr[data-row-id]',
    autoFocusOnAdd: true
  });

  // 使用巡检同步Hook
  const {
    importFromAndroidDevice,
    exportToAndroidDevice
  } = useInspectionSync();

  const [isImporting, setIsImporting] = useState(false);
  const [syncData, setSyncData] = useState<InspectionDetail[]>([]);
  const [hasImported, setHasImported] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 键盘快捷键处理
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Ctrl+I - 获取移动端巡检数据
      if (e.ctrlKey && e.key === 'i') {
        e.preventDefault();
        handleImportFromAndroid();
      }
      // Ctrl+E - 导出数据到移动端
      else if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        handleExportToAndroid();
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, []);
  const [isRefreshingInventory, setIsRefreshingInventory] = useState(false);

  // 结果弹窗状态
  const [showResultDialog, setShowResultDialog] = useState(false);
  const [resultDialogProps, setResultDialogProps] = useState<{
    type: 'success' | 'error' | 'warning';
    title: string;
    message?: string;
    details?: Array<{ label: string; value: string | number }>;
    icon?: 'import' | 'export' | 'default';
  }>({
    type: 'success',
    title: '',
    message: '',
    details: [],
    icon: 'default'
  });

  // 刷新台账管理系统
  const refreshInventorySystem = async () => {
    try {
      console.log('开始自动刷新台账管理系统...');
      setIsRefreshingInventory(true);

      // 获取全局数据刷新服务实例
      const globalRefreshService = GlobalDataRefreshService.getInstance();

      // 执行全局数据刷新
      await globalRefreshService.refreshAllData();

      console.log('台账管理系统刷新完成');
    } catch (error) {
      console.error('刷新台账管理系统失败:', error);
      // 不影响主流程，只记录错误
    } finally {
      setIsRefreshingInventory(false);
    }
  };

  // 显示结果弹窗
  const showResult = (
    type: 'success' | 'error' | 'warning',
    title: string,
    message?: string,
    details?: Array<{ label: string; value: string | number }>,
    icon?: 'import' | 'export' | 'default'
  ) => {
    setResultDialogProps({
      type,
      title,
      message,
      details,
      icon
    });
    setShowResultDialog(true);
  };

  // 使用智能缓存获取巡检同步数据
  const fetchInspectionSyncData = async () => {
    try {
      // 使用巡检日志服务的智能缓存机制
      const inspectionLogService = InspectionLogService.getInstance();
      const taskLogs = await inspectionLogService.getInspectionResults();

      if (taskLogs.length > 0) {
        // 找到最新的任务ID（假设任务ID越大越新）
        const latestTask = taskLogs.reduce((latest, current) => {
          return current.task_id > latest.task_id ? current : latest;
        });

        // 设置同步数据为最新任务的巡检详情
        setSyncData(latestTask.inspection_details || []);
        setHasImported(true);

        console.log('获取巡检同步数据成功（使用智能缓存）:', latestTask.inspection_details);
      } else {
        console.log('暂无巡检任务数据');
        setSyncData([]);
      }
    } catch (error) {
      console.error('获取巡检同步数据异常:', error);
      setSyncData([]);
    }
  };

  // 处理从Android设备导入数据
  const handleImportFromAndroid = async () => {
    setIsImporting(true);

    try {
      // 调用真实的导入服务
      const result = await importFromAndroidDevice();

      if (result.success && result.data) {
        // 显示成功提示，包含详细统计信息
        const data = result.data;
        const importTime = new Date(data.import_time).toLocaleString('zh-CN');

        // 先显示导入成功的弹窗
        showResult(
          'success',
          '导入成功',
          '已成功从Android设备导入巡检数据',
          [
            { label: '导入源', value: data.import_source },
            { label: '本地路径', value: data.local_path },
            { label: '导入时间', value: importTime },
            { label: '处理结果', value: `${data.processed_results} 条` },
            { label: '更新设备', value: `${data.updated_devices} 个` },
            { label: '待处理设备', value: `${data.pending_devices} 个` },
            { label: '新增设备', value: `${data.new_devices_added} 个` },
            { label: '重复导入', value: `${data.duplicate_imports} 个` }
          ],
          'import'
        );

        // 自动刷新台账管理系统（异步执行，不阻塞UI）
        refreshInventorySystem();

        // 获取巡检同步数据
        await fetchInspectionSyncData();
      } else if (result.success) {
        // 成功但没有详细数据
        showResult(
          'success',
          '导入成功',
          result.message,
          undefined,
          'import'
        );

        // 自动刷新台账管理系统（异步执行，不阻塞UI）
        refreshInventorySystem();

        // 获取巡检同步数据
        await fetchInspectionSyncData();
      } else {
        // 显示失败提示
        showResult(
          'error',
          '导入失败',
          result.message,
          undefined,
          'import'
        );
      }

    } catch (error) {
      console.error('导入异常:', error);
      showResult(
        'error',
        '导入异常',
        error instanceof Error ? error.message : '未知错误',
        undefined,
        'import'
      );
    } finally {
      setIsImporting(false);
    }
  };

  // 处理导出到Android设备
  const handleExportToAndroid = async () => {
    setIsExporting(true);

    try {
      // 调用真实的导出服务
      const result = await exportToAndroidDevice();

      if (result.success) {
        // 显示成功提示
        if (result.data?.export_path) {
          const exportTime = result.data.export_time ?
            new Date(result.data.export_time).toLocaleString('zh-CN') :
            new Date().toLocaleString('zh-CN');

          const fileSize = result.data.file_size ?
            `${(result.data.file_size / 1024 / 1024).toFixed(2)} MB` :
            '未知';

          showResult(
            'success',
            '导出成功',
            result.message,
            [
              { label: '导出位置', value: result.data.export_path },
              { label: '导出时间', value: exportTime },
              { label: '文件大小', value: fileSize }
            ],
            'export'
          );
        } else {
          showResult(
            'success',
            '导出成功',
            result.message,
            undefined,
            'export'
          );
        }
      } else {
        // 显示失败提示
        showResult(
          'error',
          '导出失败',
          result.message,
          undefined,
          'export'
        );
      }

    } catch (error) {
      console.error('导出异常:', error);
      showResult(
        'error',
        '导出异常',
        error instanceof Error ? error.message : '未知错误',
        undefined,
        'export'
      );
    } finally {
      setIsExporting(false);
    }
  };



  // 获取统计数据
  const getNewItemsCount = () => syncData.filter(item => item.inspection_status === 0).length;
  const getExceptionItemsCount = () => syncData.filter(item => item.inspection_status === 1).length;

  // 简化的状态配置
  const getStatusConfig = (status: number) => {
    switch (status) {
      case 0:
        return { text: '正常', className: 'status-success', icon: CheckCircle };
      case 1:
        return { text: '异常', className: 'status-error', icon: AlertTriangle };
      default:
        return { text: '未知', className: 'status-neutral', icon: AlertTriangle };
    }
  };

  return (
    <div ref={pageRef} className="h-full flex flex-col">
      {/* 页面标题（屏幕阅读器用） */}
      <h1 ref={titleRef} className="sr-only">数据同步</h1>

      {/* 简化的工具栏 - 降低高度 */}
      <div className="flex-none bg-gray-50 border-b border-gray-200 sticky top-0 z-10">
        <div className="px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={handleImportFromAndroid}
                disabled={isImporting}
                title="获取移动端巡检数据 (Ctrl+I)"
                className="btn btn-primary"
              >
                <Download className={`h-3.5 w-3.5 mr-1.5 ${isImporting ? 'animate-pulse' : ''}`} />
                {isImporting ? '导入中...' : '获取移动端巡检数据'}
              </button>
              <button
                onClick={handleExportToAndroid}
                disabled={isExporting}
                title="导出数据到移动端 (Ctrl+E)"
                className="btn btn-secondary"
              >
                <Smartphone className={`h-3.5 w-3.5 mr-1.5 ${isExporting ? 'animate-pulse' : ''}`} />
                {isExporting ? '导出中...' : '导出数据到移动端'}
              </button>
            </div>

            {/* 简化的统计信息 */}
            <div className="flex items-center space-x-3">
              <div className="status-label status-success">
                <CheckCircle className="h-3.5 w-3.5 mr-1" />
                新增 {getNewItemsCount()}
              </div>
              <div className="status-label status-error">
                <AlertTriangle className="h-3.5 w-3.5 mr-1" />
                异常 {getExceptionItemsCount()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 同步数据表格 */}
      <div className="flex-1 flex flex-col relative">
        {/* 简化的表格中间刷新指示器 */}
        {(isImporting || isRefreshingInventory) && (
          <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-10">
            <div className="flex flex-col items-center">
              <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mb-2" />
              <p className="text-blue-600 text-sm font-medium">
                {isImporting ? '从Android设备导入数据中...' : '正在刷新台账管理系统...'}
              </p>
            </div>
          </div>
        )}

        {/* 自适应数据表格 - 参考巡检结果页面 */}
        <div className="flex-1 min-h-0 bg-white shadow-sm adaptive-table-container">
          <div className="adaptive-table-content">
            <CustomScrollbar
              className="h-full"
              horizontal={true}
              vertical={true}
            >
              <div ref={listRef} onKeyDown={handleKeyDown}>
                <table className="w-full adaptive-table" style={{ minWidth: '1200px' }}>
                <thead>
                  <tr>
                    <th className="table-header-cell table-cell-center w-20">巡检状态</th>
                    <th className="table-header-cell table-cell-left w-24">保密编号</th>
                    <th className="table-header-cell table-cell-left w-32">设备名称</th>
                    <th className="table-header-cell table-cell-left w-24">位置</th>
                    <th className="table-header-cell table-cell-left w-20">责任人</th>
                    <th className="table-header-cell table-cell-left w-32">巡检结果</th>
                    <th className="table-header-cell table-cell-left w-24">RFID码</th>
                    <th className="table-header-cell table-cell-left w-32">部门信息</th>
                    <th className="table-header-cell table-cell-left w-20">使用状态</th>
                    <th className="table-header-cell table-cell-center w-16">密级</th>
                  </tr>
                </thead>
                <tbody className="bg-white">
                {syncData.length === 0 ? (
                  <tr className="adaptive-table-empty">
                    <td colSpan={10} className="px-4 py-12 text-center">
                      <div className="flex flex-col items-center justify-center h-full min-h-[200px]">
                        {!hasImported ? (
                          <>
                            <Download className="h-8 w-8 text-gray-400 mb-3" />
                            <p className="text-gray-500 text-sm font-medium">暂未进行数据同步</p>
                            <p className="text-gray-400 text-xs mt-1">请点击"获取移动端巡检数据"按钮获取数据</p>
                          </>
                        ) : (
                          <>
                            <CheckCircle className="h-8 w-8 text-green-400 mb-3" />
                            <p className="text-gray-500 text-sm font-medium">本次巡检无异常设备</p>
                            <p className="text-gray-400 text-xs mt-1">所有设备巡检状态正常</p>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ) : (
                  syncData.map((item) => {
                    const statusConfig = getStatusConfig(item.inspection_status);
                    const StatusIcon = statusConfig.icon;

                    return (
                      <tr
                        key={item.device_id}
                        data-row-id={item.device_id}
                        className="relative focus:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                        tabIndex={0}
                      >
                        <td className="table-cell table-cell-center">
                          <span className={`status-label ${statusConfig.className}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {statusConfig.text}
                          </span>
                        </td>
                        <td className="table-cell font-medium">{item.confidentiality_code}</td>
                        <td className="table-cell">{item.device_name}</td>
                        <td className="table-cell">{item.location}</td>
                        <td className="table-cell">{item.responsible_person}</td>
                        <td className="table-cell">{item.inspection_result}</td>
                        <td className="table-cell font-mono text-xs">{item.rfid}</td>
                        <td className="table-cell">{item.department_info.department_path}</td>
                        <td className="table-cell">{item.usage_status}</td>
                        <td className="table-cell table-cell-center">
                          <span className="status-label status-info">
                            {item.confidentiality_level}
                          </span>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
                </table>
              </div>
            </CustomScrollbar>
          </div>
        </div>
      </div>

      {/* 结果弹窗 */}
      <ResultDialog
        isOpen={showResultDialog}
        onClose={() => setShowResultDialog(false)}
        type={resultDialogProps.type}
        title={resultDialogProps.title}
        message={resultDialogProps.message}
        details={resultDialogProps.details}
        icon={resultDialogProps.icon}
      />
    </div>
  );
};

export default InspectionSync;
