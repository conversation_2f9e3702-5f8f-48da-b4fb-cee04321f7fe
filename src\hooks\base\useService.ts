import { useState, useEffect, useRef } from 'react';
import { BaseService, BaseServiceState, BaseServiceEvents } from '../../services/base/baseService';

/**
 * 使用服务的钩子
 * 连接服务层和React组件
 * @param getService 获取服务实例的函数
 * @param deps 依赖数组
 * @returns [服务状态, 服务实例]
 */
export function useService<
  T extends BaseServiceState,
  E extends BaseServiceEvents<T>,
  S extends BaseService<T, E>
>(
  getService: () => S,
  deps: any[] = []
): [T, S] {
  // 获取服务实例
  const service = getService();
  
  // 服务状态
  const [state, setState] = useState<T>(service.getState());
  
  // 使用ref存储最新的依赖数组，避免闭包问题
  const depsRef = useRef(deps);
  depsRef.current = deps;

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: T) => {
      setState(newState);
    };

    // 订阅状态变化事件
    service.on('state-change', handleStateChange);

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
    };
  }, [service, ...deps]);

  return [state, service];
}

/**
 * 使用服务状态的钩子
 * 只返回服务状态，不返回服务实例
 * @param getService 获取服务实例的函数
 * @param deps 依赖数组
 * @returns 服务状态
 */
export function useServiceState<
  T extends BaseServiceState,
  E extends BaseServiceEvents<T>,
  S extends BaseService<T, E>
>(
  getService: () => S,
  deps: any[] = []
): T {
  const [state] = useService<T, E, S>(getService, deps);
  return state;
}

/**
 * 使用服务加载状态的钩子
 * 只返回服务的加载状态
 * @param getService 获取服务实例的函数
 * @param deps 依赖数组
 * @returns [isLoading, error]
 */
export function useServiceLoading<
  T extends BaseServiceState,
  E extends BaseServiceEvents<T>,
  S extends BaseService<T, E>
>(
  getService: () => S,
  deps: any[] = []
): [boolean, string | undefined] {
  const [state] = useService<T, E, S>(getService, deps);
  return [state.isLoading, state.error];
}

/**
 * 使用服务错误状态的钩子
 * 只返回服务的错误状态
 * @param getService 获取服务实例的函数
 * @param deps 依赖数组
 * @returns 错误信息
 */
export function useServiceError<
  T extends BaseServiceState,
  E extends BaseServiceEvents<T>,
  S extends BaseService<T, E>
>(
  getService: () => S,
  deps: any[] = []
): string | undefined {
  const [state] = useService<T, E, S>(getService, deps);
  return state.error;
}
