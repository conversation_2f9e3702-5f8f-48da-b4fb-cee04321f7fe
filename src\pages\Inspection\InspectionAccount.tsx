import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Key,
  AlertCircle,
  UserPlus,
  User,
  Users,
  Shield,
  Lock,
  Unlock,
  Settings,
  Clock,
  Save,
  ChevronDown,
  CheckCircle,
  X
} from 'lucide-react';
import useInspectionAccount, { AccountInfo } from '../../hooks/Inspection/useInspectionAccount';
import DepartmentTreeSelect from '../../components/DepartmentTreeSelect';
import ResponsiblePersonSelect from '../../components/ResponsiblePersonSelect';
import { useDepartment } from '../../hooks/Inventory/useDepartment';
import DepartmentService from '../../services/Inventory/departmentService';
import { DictionaryItem } from '../../types/inventory';
import InspectionTable, { TableColumn } from '../../components/Inspection/InspectionTable';
import ActionButtons, { ActionButton } from '../../components/Inspection/ActionButtons';
import LoadingOverlay from '../../components/Inspection/LoadingOverlay';
import StatusBadge, { StatusConfig } from '../../components/Inspection/StatusBadge';
import ErrorMessage from '../../components/Inspection/ErrorMessage';
import { usePageFocus, useFormFocus, useDialogEscapeKey } from '../../hooks/base';
import { extractPersonNameAndAlias, buildPersonDisplayName, checkPersonUniqueness } from '../../utils/personUtils';

/**
 * 账户表单接口
 */
interface AccountForm extends Omit<AccountInfo, 'id'> {
  password?: string;
  confirmPassword?: string;
  username?: string; // 用户名字段
}

/**
 * 账户状态配置
 */
const accountStatusConfig: Record<string, StatusConfig> = {
  locked: {
    color: 'red',
    icon: Lock,
    text: '已锁定'
  },
  normal: {
    color: 'green',
    icon: Unlock,
    text: '正常'
  }
};

/**
 * 巡检账户管理页面
 * 提供账户管理功能
 */
const InspectionAccount: React.FC = () => {
  // 页面焦点管理
  const { pageRef, titleRef } = usePageFocus({
    title: '账户管理',
    autoFocus: true
  });

  // 表单焦点管理
  const { formRef, focusFirstError, validateAndFocus } = useFormFocus({
    autoFocusOnError: true,
    scrollToError: true
  });

  // 使用巡检账户Hook
  const {
    isLoading,
    error,
    accounts,
    getAccounts,
    loadAccounts,
    addAccount,
    updateAccount,
    deleteAccount
  } = useInspectionAccount();

  // 使用稳定的空数组引用避免循环依赖
  const emptyInventoryList = useMemo(() => [], []);

  // 使用部门Hook获取部门数据
  const {
    departmentCategories
  } = useDepartment(emptyInventoryList);

  // 获取部门服务实例
  const departmentService = DepartmentService.getInstance();

  // 本地状态
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSettingsMenu, setShowSettingsMenu] = useState(false);
  const [formData, setFormData] = useState<AccountForm>({
    department: '',
    inspector: '',
    contact: '', // 现在作为用户名使用
    username: '', // 新增用户名字段
    password: '',
    confirmPassword: ''
  });

  // 编辑表单的额外状态
  const [editFormData, setEditFormData] = useState({
    currentPassword: '', // 当前密码（用于验证）
    newPassword: '', // 新密码（可选）
    confirmNewPassword: '' // 确认新密码
  });

  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  // 人员唯一性验证状态
  const [personValidation, setPersonValidation] = useState<{
    isChecking: boolean;
    isDuplicate: boolean;
    duplicateAccount?: AccountInfo;
  }>({
    isChecking: false,
    isDuplicate: false
  });

  // Toast通知状态
  const [toastMessage, setToastMessage] = useState<{
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    id: number;
  } | null>(null);

  // 错误对话框状态
  const [errorDialog, setErrorDialog] = useState<{
    show: boolean;
    title: string;
    message: string;
  }>({
    show: false,
    title: '',
    message: ''
  });

  // ESC键处理
  useDialogEscapeKey(showAddForm, () => setShowAddForm(false), {
    debugId: 'AddAccountDialog'
  });

  useDialogEscapeKey(showEditForm, () => setShowEditForm(false), {
    debugId: 'EditAccountDialog'
  });

  useDialogEscapeKey(showDeleteConfirm, () => setShowDeleteConfirm(false), {
    debugId: 'DeleteAccountDialog'
  });

  // 人员选项状态
  const [personOptions, setPersonOptions] = useState<DictionaryItem[]>([]);

  // 部门树选项状态
  interface DepartmentOption {
    id: string;
    value: string;
    label: string;
    children?: DepartmentOption[];
  }
  const [departmentTreeOptions, setDepartmentTreeOptions] = useState<DepartmentOption[]>([]);

  // 所有人员选项状态
  const [allPersonOptions, setAllPersonOptions] = useState<{
    personName: string;
    departmentId: string;
    departmentName: string;
    personId: string;
    fullName: string;
  }[]>([]);

  // 设置相关状态
  const [passwordSettings, setPasswordSettings] = useState({
    expireDays: 30,
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true
  });

  const [permissionSettings, setPermissionSettings] = useState({
    allowUserSync: true,
    allowUserViewLogs: true,
    allowUserChangePassword: true
  });

  const [lockoutSettings, setLockoutSettings] = useState({
    failureThreshold: 5,
    lockoutDuration: 30
  });

  // 显示Toast通知
  const showToast = useCallback((type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    const id = Date.now();
    setToastMessage({ type, message, id });

    // 3秒后自动隐藏
    setTimeout(() => {
      setToastMessage(null);
    }, 3000);
  }, []);

  // 显示错误对话框
  const showErrorDialog = useCallback((title: string, message: string) => {
    setErrorDialog({
      show: true,
      title,
      message
    });
  }, []);

  // 关闭错误对话框
  const closeErrorDialog = useCallback(() => {
    setErrorDialog({
      show: false,
      title: '',
      message: ''
    });
  }, []);

  // 根据部门名称获取该部门的直属人员 - 移动到Hook层
  const getPersonsByDepartment = useCallback((departmentName: string): DictionaryItem[] => {
    return departmentService.getPersonsByDepartment(departmentName, allPersonOptions);
  }, [departmentService, allPersonOptions]);

  // 提取人员姓名和备注
  const extractPersonNameAndAlias = useCallback((fullName: string): { personName: string; personAlias?: string } => {
    let personName = fullName;
    let personAlias: string | undefined;

    // 如果名称中包含备注（格式为"姓名 (备注)"或"姓名 （备注）"）
    const nameMatch = fullName.match(/^([^(（]+)/);
    if (nameMatch) {
      personName = nameMatch[1].trim();
    }

    // 分别匹配英文括号和中文括号中的备注
    const englishAliasMatch = fullName.match(/\(([^)]+)\)/);
    const chineseAliasMatch = fullName.match(/（([^）]+)）/);

    if (englishAliasMatch) {
      personAlias = englishAliasMatch[1];
    } else if (chineseAliasMatch) {
      personAlias = chineseAliasMatch[1];
    }

    return { personName, personAlias };
  }, []);

  // 验证人员唯一性
  const validatePersonUniqueness = useCallback(async (inspector: string) => {
    if (!inspector.trim()) {
      setPersonValidation({
        isChecking: false,
        isDuplicate: false
      });
      return;
    }

    setPersonValidation(prev => ({ ...prev, isChecking: true }));

    try {
      // 提取人员姓名和备注
      const { personName, personAlias } = extractPersonNameAndAlias(inspector);

      // 获取现有账户列表
      const existingAccounts = accounts.map(account => ({
        name: buildPersonDisplayName(account.inspector_name, account.inspector_alias),
        inspector_name: account.inspector_name,
        inspector_alias: account.inspector_alias
      }));

      // 使用统一的人员唯一性检查
      const uniquenessCheck = checkPersonUniqueness(
        personName,
        personAlias || '',
        existingAccounts,
        { checkDisplayName: true }
      );

      setPersonValidation({
        isChecking: false,
        isDuplicate: !uniquenessCheck.isUnique,
        duplicateAccount: uniquenessCheck.duplicatePersons[0] || undefined
      });
    } catch (error) {
      console.error('验证人员唯一性失败:', error);
      setPersonValidation({
        isChecking: false,
        isDuplicate: false
      });
    }
  }, [accounts]);

  // 当部门变化时更新人员选项（仅对添加表单生效）
  useEffect(() => {
    // 确保部门树已加载且人员选项已构建
    if (departmentCategories.length === 0 || allPersonOptions.length === 0) {
      return;
    }

    // 只有在添加表单显示时才执行这个逻辑
    if (!showAddForm) {
      return;
    }

    if (formData.department) {
      // 获取部门下的人员
      console.log('选择的部门:', formData.department);
      console.log('所有人员选项数量:', allPersonOptions.length);

      const departmentPersons = getPersonsByDepartment(formData.department);
      console.log('筛选出的部门人员:', departmentPersons);

      setPersonOptions(departmentPersons);

      // 如果有预设的巡检人，检查是否在选项列表中
      if (formData.inspector) {
        const inspectorExists = departmentPersons.some(p => p.code === formData.inspector);

        // 如果当前选中的巡检人不在新的选项列表中，清空巡检人选择
        if (!inspectorExists) {
          setFormData(prev => ({
            ...prev,
            inspector: ''
          }));
        }
      }
    } else {
      // 如果没有选择部门，清空人员选项
      setPersonOptions([]);

      // 同时清空巡检人选择
      if (formData.inspector) {
        setFormData(prev => ({
          ...prev,
          inspector: ''
        }));
      }
    }
  }, [formData.department, getPersonsByDepartment, showAddForm]);

  // 当人员选项变化时，重新计算当前部门的人员列表
  useEffect(() => {
    if (formData.department && allPersonOptions.length > 0) {
      const departmentPersons = getPersonsByDepartment(formData.department);
      setPersonOptions(departmentPersons);
    }
  }, [allPersonOptions, formData.department, getPersonsByDepartment]);

  // 从部门树构建选项的逻辑 - 简化为调用Service层方法
  useEffect(() => {
    if (departmentCategories.length === 0) {
      return;
    }

    // 使用Service层方法处理数据转换
    const { treeOptions, allPersons } = departmentService.processDepartmentData(departmentCategories);
    setDepartmentTreeOptions(treeOptions);
    setAllPersonOptions(allPersons);

    console.log('部门树选项:', treeOptions);
    console.log('所有人员选项:', allPersons);
  }, [departmentCategories, departmentService]);

  // 页面初始化时加载数据（使用智能缓存）
  useEffect(() => {
    getAccounts(); // 首次加载使用缓存，如果没有缓存则自动获取
  }, [getAccounts]);

  // 监听巡检人字段变化，进行唯一性验证
  useEffect(() => {
    // 只在添加表单显示时进行验证
    if (showAddForm && formData.inspector) {
      // 延迟验证，避免频繁调用
      const timeoutId = setTimeout(() => {
        validatePersonUniqueness(formData.inspector);
      }, 500);

      return () => clearTimeout(timeoutId);
    } else {
      // 清除验证状态
      setPersonValidation({
        isChecking: false,
        isDuplicate: false
      });
    }
  }, [formData.inspector, showAddForm, validatePersonUniqueness]);

  // 处理刷新
  const handleRefresh = () => {
    loadAccounts().catch(err => {
      console.error('刷新账户列表失败:', err);
    });
  };

  // 打开添加表单
  const openAddForm = () => {
    setFormData({
      department: '',
      inspector: '',
      contact: '', // 现在作为用户名使用
      username: '', // 新增用户名字段
      password: '',
      confirmPassword: ''
    });
    setShowAddForm(true);
  };



  // 表格列配置
  const tableColumns: TableColumn[] = [
    {
      key: 'id',
      title: '序号',
      width: 'w-16',
      render: (value, record) => (
        <span className="text-sm font-medium text-gray-900">{record.user_id || value}</span>
      )
    },
    {
      key: 'department',
      title: '所属部门',
      render: (value) => value || <span className="text-gray-400 italic">未设置</span>
    },
    {
      key: 'inspector',
      title: '巡检人',
      render: (value) => (
        <div className="flex items-center">
          <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded flex items-center justify-center mr-2">
            <User className="h-3 w-3" />
          </div>
          <span className="text-sm font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      key: 'contact',
      title: '用户名',
      render: (value) => value || <span className="text-gray-400 italic">未设置</span>
    },
    {
      key: 'registerTime',
      title: '注册时间',
      render: (value) => value || <span className="text-gray-400 italic">暂无数据</span>
    },
    {
      key: 'actions',
      title: '操作',
      width: 'w-32',
      align: 'right',
      render: (_, record) => {
        const actionButtons: ActionButton[] = [
          {
            key: 'edit',
            icon: Edit,
            title: '编辑账户',
            color: 'blue',
            onClick: () => openEditForm(record)
          },
          {
            key: 'delete',
            icon: Trash2,
            title: '删除账户',
            color: 'red',
            onClick: () => openDeleteConfirm(record.id)
          }
        ];
        return <ActionButtons buttons={actionButtons} />;
      }
    }
  ];

  // 点击外部关闭设置菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showSettingsMenu) {
        const target = event.target as Element;
        if (!target.closest('.settings-menu-container')) {
          setShowSettingsMenu(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSettingsMenu]);

  // 处理添加账户
  const handleAddAccount = async () => {
    try {
      // 基本表单验证
      if (!formData.department) {
        showToast('error', '所属部门不能为空');
        return;
      }

      if (!formData.inspector) {
        showToast('error', '巡检人不能为空');
        return;
      }

      if (!formData.contact) {
        showToast('error', '用户名不能为空');
        return;
      }

      if (!formData.password) {
        showToast('error', '密码不能为空');
        return;
      }

      if (formData.password !== formData.confirmPassword) {
        showErrorDialog('密码验证失败', '两次输入的密码不一致，请重新输入。');
        return;
      }

      // 人员唯一性验证
      if (personValidation.isDuplicate) {
        const duplicateAccount = personValidation.duplicateAccount;
        showErrorDialog(
          '人员已存在账户',
          `人员"${formData.inspector}"已存在账户，用户名为"${duplicateAccount?.contact}"。\n\n一个人员只能拥有一个账户，请选择其他人员或编辑现有账户。`
        );
        return;
      }

      // 如果正在验证，等待验证完成
      if (personValidation.isChecking) {
        showToast('warning', '正在验证人员信息，请稍候...');
        return;
      }

      // 添加账户
      await addAccount({
        department: formData.department,
        inspector: formData.inspector,
        contact: formData.contact,
        password: formData.password!
      });

      // 重置表单
      setFormData({
        department: '',
        inspector: '',
        contact: '',
        username: '',
        password: '',
        confirmPassword: ''
      });

      setShowAddForm(false);
      showToast('success', '账户添加成功');
    } catch (err) {
      showToast('error', err instanceof Error ? err.message : '添加账户失败');
    }
  };

  // 处理编辑账户
  const handleEditAccount = async () => {
    try {
      if (!selectedAccountId) {
        showToast('error', '未选择账户');
        return;
      }

      // 表单验证 - 巡检人信息直接从表格获取，无需验证

      // 验证当前密码
      if (!editFormData.currentPassword) {
        showToast('error', '请输入当前密码进行验证');
        return;
      }

      // 如果要修改密码，验证新密码
      if (editFormData.newPassword) {
        if (editFormData.newPassword !== editFormData.confirmNewPassword) {
          showErrorDialog('密码验证失败', '两次输入的新密码不一致，请重新输入。');
          return;
        }
      }

      // 更新账户 - 只更新用户名，巡检人信息直接从表格获取
      await updateAccount(
        selectedAccountId,
        {
          contact: formData.contact // 只更新用户名
        },
        editFormData.currentPassword,
        editFormData.newPassword || undefined
      );

      // 重置表单
      setFormData({
        department: '',
        inspector: '',
        contact: '',
        username: '',
        password: '',
        confirmPassword: ''
      });

      setEditFormData({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: ''
      });

      setShowEditForm(false);
      showToast('success', '账户更新成功');
    } catch (err) {
      showToast('error', err instanceof Error ? err.message : '更新账户失败');
    }
  };



  // 处理删除账户
  const handleDeleteAccount = async () => {
    try {
      if (!selectedAccountId) {
        showToast('error', '未选择账户');
        return;
      }

      // 删除账户
      await deleteAccount(selectedAccountId);

      setShowDeleteConfirm(false);
      showToast('success', '账户删除成功');
    } catch (err) {
      showToast('error', err instanceof Error ? err.message : '删除账户失败');
    }
  };

  // 打开编辑表单
  const openEditForm = (account: AccountInfo) => {
    setSelectedAccountId(account.id);
    setFormData({
      department: account.department,
      inspector: account.inspector, // 直接从表格记录获取巡检人信息
      contact: account.contact || ''
    });

    // 重置编辑表单数据
    setEditFormData({
      currentPassword: '',
      newPassword: '',
      confirmNewPassword: ''
    });

    setShowEditForm(true);
  };



  // 打开删除确认
  const openDeleteConfirm = (accountId: string) => {
    setSelectedAccountId(accountId);
    setShowDeleteConfirm(true);
  };

  // 获取要删除的账户信息
  const getSelectedAccount = () => {
    return accounts.find(account => account.id === selectedAccountId);
  };

  // 处理保存设置
  const handleSaveSettings = () => {
    try {
      // 模拟保存设置
      // 实际项目中，这里应该调用后端API

      showToast('success', '设置保存成功');
    } catch (err) {
      showToast('error', '保存设置失败');
    }
  };



  return (
    <div ref={pageRef as any} className="h-full flex flex-col">
      {/* 页面标题（屏幕阅读器用） */}
      <h1 ref={titleRef as any} className="sr-only">账户管理</h1>

      {/* 错误信息 */}
      <ErrorMessage error={error} />

      {/* Toast通知 */}
      {toastMessage && (
        <div className="fixed top-4 right-4 z-[9999] animate-in slide-in-from-right-full duration-300">
          <div className={`
            flex items-center p-4 rounded-lg shadow-lg border max-w-sm
            ${toastMessage.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : ''}
            ${toastMessage.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' : ''}
            ${toastMessage.type === 'warning' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' : ''}
            ${toastMessage.type === 'info' ? 'bg-blue-50 border-blue-200 text-blue-800' : ''}
          `}>
            <div className="flex items-center">
              {toastMessage.type === 'success' && <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0" />}
              {toastMessage.type === 'error' && <AlertCircle className="h-5 w-5 text-red-600 mr-3 flex-shrink-0" />}
              {toastMessage.type === 'warning' && <AlertCircle className="h-5 w-5 text-yellow-600 mr-3 flex-shrink-0" />}
              {toastMessage.type === 'info' && <AlertCircle className="h-5 w-5 text-blue-600 mr-3 flex-shrink-0" />}
              <span className="text-sm font-medium">{toastMessage.message}</span>
            </div>
            <button
              onClick={() => setToastMessage(null)}
              className="ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* 工具栏 */}
      <div className="flex-none bg-gray-50 border-b border-gray-200 sticky top-0 z-10">
        <div className="px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="btn btn-primary"
              >
                <RefreshCw className={`h-3.5 w-3.5 mr-1.5 ${isLoading ? 'animate-spin' : ''}`} />
                刷新数据
              </button>
              <button
                onClick={openAddForm}
                className="btn btn-secondary"
              >
                <Plus className="h-3.5 w-3.5 mr-1.5" />
                添加账户
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <div className="relative">
                <button
                  onClick={() => setShowSettingsMenu(!showSettingsMenu)}
                  className="btn btn-secondary"
                >
                  <Settings className="h-3.5 w-3.5 mr-1.5" />
                  设置
                </button>
                {showSettingsMenu && (
                  <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg border border-gray-200 z-50 settings-menu-container">
                    <div className="p-4">
                      <h3 className="text-lg font-medium text-gray-800 mb-4">系统设置</h3>

                      {/* 设置内容 */}
                      <div className="space-y-4 max-h-96 overflow-y-auto">
                        {/* 口令时长设置 */}
                        <div className="border border-gray-200 rounded p-3">
                          <div className="flex items-center mb-2">
                            <Clock className="h-4 w-4 text-blue-600 mr-2" />
                            <h4 className="text-sm font-medium text-gray-800">口令时长设置</h4>
                          </div>
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">
                              口令有效期（天）
                            </label>
                            <input
                              type="number"
                              min="1"
                              max="365"
                              value={passwordSettings.expireDays}
                              onChange={(e) => setPasswordSettings({
                                ...passwordSettings,
                                expireDays: parseInt(e.target.value) || 30
                              })}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                            />
                          </div>
                        </div>

                        {/* 口令复杂度要求 */}
                        <div className="border border-gray-200 rounded p-3">
                          <div className="flex items-center mb-2">
                            <Key className="h-4 w-4 text-blue-600 mr-2" />
                            <h4 className="text-sm font-medium text-gray-800">口令复杂度</h4>
                          </div>
                          <div className="space-y-2">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">
                                最小长度
                              </label>
                              <input
                                type="number"
                                min="6"
                                max="32"
                                value={passwordSettings.minLength}
                                onChange={(e) => setPasswordSettings({
                                  ...passwordSettings,
                                  minLength: parseInt(e.target.value) || 8
                                })}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            <div className="space-y-1">
                              <label className="flex items-center text-xs">
                                <input
                                  type="checkbox"
                                  checked={passwordSettings.requireUppercase}
                                  onChange={(e) => setPasswordSettings({
                                    ...passwordSettings,
                                    requireUppercase: e.target.checked
                                  })}
                                  className="h-3 w-3 text-blue-600 mr-2"
                                />
                                包含大写字母
                              </label>
                              <label className="flex items-center text-xs">
                                <input
                                  type="checkbox"
                                  checked={passwordSettings.requireLowercase}
                                  onChange={(e) => setPasswordSettings({
                                    ...passwordSettings,
                                    requireLowercase: e.target.checked
                                  })}
                                  className="h-3 w-3 text-blue-600 mr-2"
                                />
                                包含小写字母
                              </label>
                              <label className="flex items-center text-xs">
                                <input
                                  type="checkbox"
                                  checked={passwordSettings.requireNumbers}
                                  onChange={(e) => setPasswordSettings({
                                    ...passwordSettings,
                                    requireNumbers: e.target.checked
                                  })}
                                  className="h-3 w-3 text-blue-600 mr-2"
                                />
                                包含数字
                              </label>
                              <label className="flex items-center text-xs">
                                <input
                                  type="checkbox"
                                  checked={passwordSettings.requireSpecialChars}
                                  onChange={(e) => setPasswordSettings({
                                    ...passwordSettings,
                                    requireSpecialChars: e.target.checked
                                  })}
                                  className="h-3 w-3 text-blue-600 mr-2"
                                />
                                包含特殊字符
                              </label>
                            </div>
                          </div>
                        </div>

                        {/* 权限设置 */}
                        <div className="border border-gray-200 rounded p-3">
                          <div className="flex items-center mb-2">
                            <Shield className="h-4 w-4 text-blue-600 mr-2" />
                            <h4 className="text-sm font-medium text-gray-800">权限设置</h4>
                          </div>
                          <div className="space-y-1">
                            <label className="flex items-center text-xs">
                              <input
                                type="checkbox"
                                checked={permissionSettings.allowUserSync}
                                onChange={(e) => setPermissionSettings({
                                  ...permissionSettings,
                                  allowUserSync: e.target.checked
                                })}
                                className="h-3 w-3 text-blue-600 mr-2"
                              />
                              允许普通用户数据同步
                            </label>
                            <label className="flex items-center text-xs">
                              <input
                                type="checkbox"
                                checked={permissionSettings.allowUserViewLogs}
                                onChange={(e) => setPermissionSettings({
                                  ...permissionSettings,
                                  allowUserViewLogs: e.target.checked
                                })}
                                className="h-3 w-3 text-blue-600 mr-2"
                              />
                              允许普通用户查看日志
                            </label>
                            <label className="flex items-center text-xs">
                              <input
                                type="checkbox"
                                checked={permissionSettings.allowUserChangePassword}
                                onChange={(e) => setPermissionSettings({
                                  ...permissionSettings,
                                  allowUserChangePassword: e.target.checked
                                })}
                                className="h-3 w-3 text-blue-600 mr-2"
                              />
                              允许普通用户修改密码
                            </label>
                          </div>
                        </div>

                        {/* 账户锁定策略 */}
                        <div className="border border-gray-200 rounded p-3">
                          <div className="flex items-center mb-2">
                            <Lock className="h-4 w-4 text-blue-600 mr-2" />
                            <h4 className="text-sm font-medium text-gray-800">锁定策略</h4>
                          </div>
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">
                                失败阈值
                              </label>
                              <input
                                type="number"
                                min="0"
                                max="10"
                                value={lockoutSettings.failureThreshold}
                                onChange={(e) => setLockoutSettings({
                                  ...lockoutSettings,
                                  failureThreshold: parseInt(e.target.value) || 0
                                })}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">
                                锁定时间（分钟）
                              </label>
                              <input
                                type="number"
                                min="1"
                                max="1440"
                                value={lockoutSettings.lockoutDuration}
                                onChange={(e) => setLockoutSettings({
                                  ...lockoutSettings,
                                  lockoutDuration: parseInt(e.target.value) || 30
                                })}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-gray-200 flex justify-end space-x-2">
                        <button
                          onClick={() => setShowSettingsMenu(false)}
                          className="px-3 py-1.5 text-sm border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
                        >
                          取消
                        </button>
                        <button
                          onClick={() => {
                            handleSaveSettings();
                            setShowSettingsMenu(false);
                          }}
                          className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                          保存
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* 账户列表 - 使用共享表格组件 */}
      <div className="flex-1 flex flex-col overflow-hidden relative">
        <LoadingOverlay visible={isLoading} text="正在加载账户..." />
        <InspectionTable
          columns={tableColumns}
          data={accounts}
          loading={isLoading}
          loadingText="正在加载账户..."
          emptyText="暂无账户数据"
          emptyIcon={<Users className="h-8 w-8 text-gray-400 mb-2" />}
        />
      </div>

      {/* 添加账户表单 */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">添加账户</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  所属部门 <span className="text-red-500">*</span>
                </label>
                <DepartmentTreeSelect
                  options={departmentTreeOptions}
                  value={formData.department}
                  onChange={(value) => setFormData({ ...formData, department: value })}
                  placeholder="请选择所属部门"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  巡检人 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <ResponsiblePersonSelect
                    name="inspector"
                    value={formData.inspector}
                    onChange={(e) => setFormData({ ...formData, inspector: e.target.value })}
                    options={personOptions}
                    placeholder="请选择巡检人"
                    required={true}
                    disabled={!formData.department || personOptions.length === 0}
                    className={personValidation.isDuplicate ? 'border-red-300 bg-red-50' : ''}
                  />

                  {/* 验证状态指示 */}
                  {formData.inspector && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      {personValidation.isChecking ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                      ) : personValidation.isDuplicate ? (
                        <div className="text-red-500" title="该人员已存在账户">
                          <AlertCircle className="h-4 w-4" />
                        </div>
                      ) : (
                        <div className="text-green-500" title="该人员可以创建账户">
                          <CheckCircle className="h-4 w-4" />
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* 验证结果提示 */}
                {formData.inspector && personValidation.isDuplicate && personValidation.duplicateAccount && (
                  <div className="mt-1 text-xs text-red-600 bg-red-50 border border-red-200 rounded p-2">
                    <div className="flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span>该人员已存在账户</span>
                    </div>
                    <div className="mt-1 text-gray-600">
                      用户名：{personValidation.duplicateAccount.contact}
                    </div>
                    <div className="text-gray-600">
                      一个人员只能拥有一个账户
                    </div>
                  </div>
                )}
              </div>



              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  用户名 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.contact}
                  onChange={(e) => setFormData({ ...formData, contact: e.target.value })}
                  placeholder="请输入用户名（必须唯一）"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  密码 <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={formData.password || ''}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  确认密码 <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={formData.confirmPassword || ''}
                  onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-2">
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
              >
                取消
              </button>
              <button
                onClick={handleAddAccount}
                className="px-4 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-800"
              >
                添加
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑账户表单 */}
      {showEditForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-lg">
            <h2 className="text-xl font-bold mb-4">编辑账户</h2>

            <div className="space-y-4">
              {/* 巡检人（只读显示） */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  巡检人
                </label>
                <input
                  type="text"
                  value={formData.inspector || ''}
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
                />
              </div>

              {/* 原用户名（只读显示） */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  原用户名
                </label>
                <input
                  type="text"
                  value={(() => {
                    const selectedAccount = accounts.find(acc => acc.id === selectedAccountId);
                    return selectedAccount?.contact || '';
                  })()}
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
                />
              </div>

              {/* 新用户名 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  新用户名
                </label>
                <input
                  type="text"
                  value={formData.contact}
                  onChange={(e) => setFormData({ ...formData, contact: e.target.value })}
                  placeholder="请输入新的用户名"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* 原密码 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  原密码 <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={editFormData.currentPassword}
                  onChange={(e) => setEditFormData({ ...editFormData, currentPassword: e.target.value })}
                  placeholder="请输入当前密码进行验证"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* 新密码 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  新密码
                </label>
                <input
                  type="password"
                  value={editFormData.newPassword}
                  onChange={(e) => setEditFormData({ ...editFormData, newPassword: e.target.value })}
                  placeholder="如需修改密码请输入新密码"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* 确认新密码（条件显示） */}
              {editFormData.newPassword && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    确认新密码 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="password"
                    value={editFormData.confirmNewPassword}
                    onChange={(e) => setEditFormData({ ...editFormData, confirmNewPassword: e.target.value })}
                    placeholder="请再次输入新密码"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}
            </div>

            <div className="mt-6 flex justify-end space-x-2">
              <button
                onClick={() => setShowEditForm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
              >
                取消
              </button>
              <button
                onClick={handleEditAccount}
                className="px-4 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-800"
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}



      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            {/* 对话框标题 */}
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">确认删除</h2>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isLoading}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* 确认信息 */}
            <div className="mb-6">
              {(() => {
                const selectedAccount = getSelectedAccount();
                return (
                  <div>
                    <p className="text-gray-700 mb-3">
                      您确定要删除以下移动端用户账户吗？
                    </p>
                    {selectedAccount && (
                      <div className="bg-gray-50 rounded-md p-3 mb-3">
                        <div className="space-y-1 text-sm">
                          <div><span className="font-medium text-gray-600">巡检人：</span>{selectedAccount.inspector}</div>
                          <div><span className="font-medium text-gray-600">所属部门：</span>{selectedAccount.department}</div>
                          <div><span className="font-medium text-gray-600">用户名：</span>{selectedAccount.contact}</div>
                        </div>
                      </div>
                    )}
                    <p className="text-red-600 text-sm">
                      此操作不可逆，删除后该用户将无法使用移动端登录，数据将无法恢复。
                    </p>
                  </div>
                );
              })()}
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isLoading}
                className={`px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                取消
              </button>
              <button
                type="button"
                onClick={handleDeleteAccount}
                disabled={isLoading}
                className={`px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isLoading ? '删除中...' : '确认删除'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示对话框 */}
      {errorDialog.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4 animate-in zoom-in-95 duration-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900">{errorDialog.title}</h2>
            </div>

            <p className="text-gray-700 mb-6 leading-relaxed">
              {errorDialog.message}
            </p>

            <div className="flex justify-end">
              <button
                onClick={closeErrorDialog}
                className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InspectionAccount;
