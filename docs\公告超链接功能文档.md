# 公告超链接功能实现文档

## 概述

本文档详细说明了在产品激活对话框的公告栏中实现的超链接功能，包括前端实现、API接口设计和minibilink集成方案。

## 功能特性

### 1. 自动URL检测
- 自动识别公告文本中的HTTP/HTTPS链接
- 将URL转换为可点击的蓝色链接样式
- 支持多个链接在同一公告中显示

### 2. 自定义确认对话框
- 美观的确认对话框替代浏览器原生confirm
- 显示完整的链接地址供用户确认
- 支持取消和确认操作

### 3. 多层级安全打开策略
- 优先使用minibilink API安全打开
- 自动降级到备选方案
- 完整的错误处理机制

## 技术实现

### 前端组件结构

```
AnnouncementDialog.tsx
├── ConfirmDialog 组件 (自定义确认对话框)
├── parseTextWithLinks 函数 (URL解析和转换)
├── performLinkOpen 函数 (链接打开逻辑)
└── 状态管理 (确认对话框状态)
```

### 核心函数说明

#### 1. parseTextWithLinks
```typescript
const parseTextWithLinks = (text: string, onLinkClick: (url: string) => void): React.ReactNode[]
```
- **功能**: 解析文本中的URL并转换为可点击链接
- **参数**: 
  - `text`: 要解析的文本内容
  - `onLinkClick`: 链接点击回调函数
- **返回**: React节点数组，包含文本和链接元素

#### 2. performLinkOpen
```typescript
const performLinkOpen = (url: string) => void
```
- **功能**: 执行实际的链接打开操作
- **策略**: 
  1. 优先使用minibilink API
  2. 降级到a标签模拟点击
  3. 降级到window.open
  4. 最后降级到剪贴板方案

#### 3. ConfirmDialog组件
```typescript
interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  url?: string;
  onConfirm: () => void;
  onCancel: () => void;
}
```
- **功能**: 显示自定义确认对话框
- **特性**: 美观设计、URL预览、双按钮操作

## minibilink API接口

### 需要实现的API

```typescript
interface ElectronAPI {
  closeApp: () => void;
  openExternal: (url: string) => Promise<boolean>;
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}
```

### openExternal API详细规范

#### 函数签名
```typescript
openExternal(url: string): Promise<boolean>
```

#### 参数说明
- **url**: 要打开的外部链接
  - 类型: `string`
  - 格式: 必须以 `http://` 或 `https://` 开头
  - 长度: 建议限制在2048字符以内
  - 示例: `"https://web.jma.top/"`

#### 返回值
- **类型**: `Promise<boolean>`
- **true**: 链接打开成功
- **false**: 链接打开失败

#### 实现建议
```javascript
window.electronAPI = {
  closeApp: () => {
    // 现有实现
  },
  
  openExternal: async (url) => {
    try {
      // 1. 参数验证
      if (!url || typeof url !== 'string') {
        return false;
      }
      
      // 2. 协议检查
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        return false;
      }
      
      // 3. 长度限制
      if (url.length > 2048) {
        return false;
      }
      
      // 4. 调用系统默认浏览器
      const success = await minibilink.system.openBrowser(url);
      return success;
      
    } catch (error) {
      console.error('打开外部链接失败:', error);
      return false;
    }
  }
}
```

## 安全性考虑

### 1. URL验证
- 只允许HTTP/HTTPS协议
- 限制URL长度防止恶意攻击
- 可选的黑名单检查

### 2. 用户确认
- 显示完整URL供用户确认
- 防止恶意链接的意外点击
- 清晰的操作提示

### 3. 错误处理
- 完整的异常捕获
- 优雅的降级方案
- 详细的错误日志

## 使用示例

### 公告内容示例
```
欢迎第一次使用军密安信息设备台账管理系统
首先在网站https://web.jma.top/ 中注册账号
如有问题请访问帮助页面：http://help.example.com
```

### 用户操作流程
1. 用户看到公告中的蓝色链接
2. 点击链接触发确认对话框
3. 对话框显示要打开的URL
4. 用户确认后系统尝试打开链接
5. 成功打开或显示错误提示

## 测试用例

### 正常情况测试
```javascript
// 应该成功打开
await window.electronAPI.openExternal('https://web.jma.top/');
await window.electronAPI.openExternal('http://example.com');
```

### 异常情况测试
```javascript
// 应该返回false
await window.electronAPI.openExternal('');                    // 空字符串
await window.electronAPI.openExternal('ftp://example.com');   // 不支持协议
await window.electronAPI.openExternal('javascript:alert(1)'); // 危险协议
```

### 前端降级测试
```javascript
// 模拟API不可用
delete window.electronAPI;
// 点击链接应该降级到其他方案
```

## 部署说明

### 1. 前端部署
- 确保 `AnnouncementDialog.tsx` 文件已更新
- 验证TypeScript编译无错误
- 测试各种公告内容格式

### 2. minibilink集成
- 实现 `window.electronAPI.openExternal` 方法
- 添加必要的安全检查
- 配置系统浏览器调用

### 3. 测试验证
- 测试正常链接打开
- 测试异常情况处理
- 验证降级方案工作正常

## 故障排除

### 常见问题

#### 1. 链接无法点击
- 检查URL格式是否正确
- 确认正则表达式匹配
- 验证事件处理函数

#### 2. 确认对话框不显示
- 检查状态管理逻辑
- 确认组件渲染条件
- 验证CSS样式加载

#### 3. 链接打开失败
- 检查minibilink API实现
- 确认降级方案工作
- 查看控制台错误日志

### 调试方法
```javascript
// 启用详细日志
console.log('尝试打开链接:', url);
console.log('API可用性:', !!window.electronAPI?.openExternal);
console.log('降级方案执行');
```

## 版本历史

- **v1.0.0**: 初始实现，支持基本的链接检测和打开
- **v1.1.0**: 添加自定义确认对话框
- **v1.2.0**: 完善安全性和错误处理
- **v1.3.0**: 优化minibilink集成方案

## 联系方式

如有问题或建议，请联系开发团队。
