import { useRef, useCallback, useEffect } from 'react';

// 浏览器兼容性检测结果
interface BrowserCapabilities {
  supportsFocus: boolean;
  supportsActiveElement: boolean;
  supportsScrollIntoView: boolean;
  supportsFocusVisible: boolean;
  supportsTabIndex: boolean;
  supportsKeyboardEvents: boolean;
}

// 焦点设置选项
interface FocusOptions {
  preventScroll?: boolean;
  force?: boolean;
  fallbackToParent?: boolean;
}

// 增强的焦点管理器接口
interface FocusManager {
  // 保存当前焦点
  saveFocus: () => void;
  // 恢复之前保存的焦点
  restoreFocus: () => void;
  // 设置焦点到指定元素（增强版）
  setFocus: (element: HTMLElement | null, options?: FocusOptions) => boolean;
  // 设置焦点到第一个可聚焦元素
  focusFirst: (container: HTMLElement | null) => boolean;
  // 设置焦点到最后一个可聚焦元素
  focusLast: (container: HTMLElement | null) => boolean;
  // 获取所有可聚焦元素
  getFocusableElements: (container: HTMLElement | null) => HTMLElement[];
  // 处理Tab键导航
  handleTabNavigation: (event: KeyboardEvent, container: HTMLElement | null) => void;
  // 获取当前焦点元素（兼容性处理）
  getCurrentFocus: () => HTMLElement | null;
  // 检查元素是否可聚焦
  isFocusable: (element: HTMLElement) => boolean;
  // 浏览器兼容性信息
  capabilities: BrowserCapabilities;
}

/**
 * 检测浏览器焦点管理能力
 */
function detectBrowserCapabilities(): BrowserCapabilities {
  const testElement = document.createElement('div');
  testElement.style.position = 'absolute';
  testElement.style.left = '-9999px';
  testElement.tabIndex = -1;
  document.body.appendChild(testElement);

  const capabilities: BrowserCapabilities = {
    supportsFocus: typeof testElement.focus === 'function',
    supportsActiveElement: 'activeElement' in document,
    supportsScrollIntoView: typeof testElement.scrollIntoView === 'function',
    supportsFocusVisible: CSS.supports && CSS.supports('selector(:focus-visible)'),
    supportsTabIndex: 'tabIndex' in testElement,
    supportsKeyboardEvents: typeof KeyboardEvent !== 'undefined'
  };

  document.body.removeChild(testElement);
  return capabilities;
}

/**
 * 获取元素中所有可聚焦的元素（统一标准版本）
 * @param container 容器元素
 * @returns 可聚焦元素数组
 */
function getFocusableElements(container: HTMLElement | null): HTMLElement[] {
  if (!container) return [];

  // 统一的可聚焦元素选择器（与tabKeyFix保持一致）
  const selector = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(',');

  try {
    const elements = Array.from(container.querySelectorAll(selector)) as HTMLElement[];

    // 统一的可见性检查逻辑
    return elements.filter(element => {
      const rect = element.getBoundingClientRect();
      const style = window.getComputedStyle(element);

      return (
        rect.width > 0 &&
        rect.height > 0 &&
        style.visibility !== 'hidden' &&
        style.display !== 'none' &&
        !element.hasAttribute('hidden') &&
        (element.getAttribute('aria-hidden') !== 'true')
      );
    });
  } catch (error) {
    console.warn('获取可聚焦元素失败:', error);
    return [];
  }
}

/**
 * 焦点管理Hook
 * 提供统一的焦点管理功能
 */
export function useFocusManager(): FocusManager {
  // 保存之前聚焦的元素
  const previousFocusRef = useRef<HTMLElement | null>(null);
  const capabilitiesRef = useRef<BrowserCapabilities | null>(null);
  const focusHistoryRef = useRef<HTMLElement[]>([]);

  // 初始化浏览器能力检测
  useEffect(() => {
    capabilitiesRef.current = detectBrowserCapabilities();
    console.log('浏览器焦点管理能力:', capabilitiesRef.current);
  }, []);

  // 获取当前焦点元素（兼容性处理）
  const getCurrentFocus = useCallback((): HTMLElement | null => {
    const capabilities = capabilitiesRef.current;
    if (!capabilities) return null;

    if (capabilities.supportsActiveElement) {
      return document.activeElement as HTMLElement;
    }

    // 降级处理：从焦点历史中获取
    return focusHistoryRef.current[focusHistoryRef.current.length - 1] || null;
  }, []);

  // 检查元素是否可聚焦
  const isFocusable = useCallback((element: HTMLElement): boolean => {
    if (!element || element.offsetParent === null) return false;

    // 检查是否被禁用
    if ('disabled' in element && (element as any).disabled) return false;

    // 检查tabindex
    const tabIndex = element.tabIndex;
    if (tabIndex < 0) return false;

    // 检查元素类型
    const tagName = element.tagName.toLowerCase();
    const focusableTags = ['input', 'button', 'select', 'textarea', 'a'];

    if (focusableTags.includes(tagName)) {
      return true;
    }

    // 检查是否有tabindex属性
    return tabIndex >= 0 || element.hasAttribute('contenteditable');
  }, []);

  // 保存当前焦点
  const saveFocus = useCallback(() => {
    previousFocusRef.current = getCurrentFocus();
  }, [getCurrentFocus]);

  // 恢复之前保存的焦点
  const restoreFocus = useCallback(() => {
    if (previousFocusRef.current) {
      setFocus(previousFocusRef.current);
    }
  }, []);

  // 跨浏览器兼容的焦点设置
  const setFocus = useCallback((element: HTMLElement | null, options: FocusOptions = {}): boolean => {
    if (!element) return false;

    const capabilities = capabilitiesRef.current;
    if (!capabilities) return false;

    try {
      // 记录到焦点历史
      focusHistoryRef.current.push(element);
      if (focusHistoryRef.current.length > 10) {
        focusHistoryRef.current.shift();
      }

      // 确保元素可聚焦
      if (!isFocusable(element)) {
        if (!element.hasAttribute('tabindex')) {
          element.setAttribute('tabindex', '-1');
        }
      }

      // 尝试设置焦点
      if (capabilities.supportsFocus) {
        setTimeout(() => {
          try {
            if (options.preventScroll) {
              element.focus({ preventScroll: true });
            } else {
              element.focus();
            }

            // 验证焦点是否设置成功
            const currentFocus = getCurrentFocus();
            if (currentFocus !== element && options.fallbackToParent) {
              // 尝试聚焦父元素
              const parent = element.parentElement;
              if (parent && isFocusable(parent)) {
                parent.focus();
              }
            }
          } catch (error) {
            console.warn('焦点设置失败:', error);
          }
        }, 0);

        return true;
      } else {
        // 降级处理：手动管理焦点状态
        console.warn('浏览器不支持focus()方法，使用降级处理');
        element.setAttribute('data-focused', 'true');

        // 移除其他元素的焦点标记
        const previousFocused = document.querySelector('[data-focused="true"]');
        if (previousFocused && previousFocused !== element) {
          previousFocused.removeAttribute('data-focused');
        }

        return true;
      }
    } catch (error) {
      console.warn('焦点设置异常:', error);
      return false;
    }
  }, [getCurrentFocus, isFocusable]);
  
  // 设置焦点到第一个可聚焦元素
  const focusFirst = useCallback((container: HTMLElement | null): boolean => {
    const focusableElements = getFocusableElements(container);
    if (focusableElements.length > 0) {
      return setFocus(focusableElements[0]);
    }
    return false;
  }, [setFocus]);

  // 设置焦点到最后一个可聚焦元素
  const focusLast = useCallback((container: HTMLElement | null): boolean => {
    const focusableElements = getFocusableElements(container);
    if (focusableElements.length > 0) {
      return setFocus(focusableElements[focusableElements.length - 1]);
    }
    return false;
  }, [setFocus]);
  
  // 处理Tab键导航（增强版）
  const handleTabNavigation = useCallback((event: KeyboardEvent, container: HTMLElement | null) => {
    // 如果不是Tab键，不处理
    if (event.key !== 'Tab' || !container) return;

    const focusableElements = getFocusableElements(container);
    if (focusableElements.length === 0) return;

    // 获取当前焦点元素的索引
    const currentFocus = getCurrentFocus();
    const currentIndex = currentFocus ? focusableElements.indexOf(currentFocus) : -1;

    // 如果当前焦点不在容器内，则聚焦到第一个或最后一个元素
    if (currentIndex === -1) {
      event.preventDefault();
      if (event.shiftKey) {
        setFocus(focusableElements[focusableElements.length - 1]);
      } else {
        setFocus(focusableElements[0]);
      }
      return;
    }

    // 处理向前Tab（Shift+Tab）
    if (event.shiftKey) {
      // 如果已经是第一个元素，则循环到最后一个
      if (currentIndex === 0) {
        event.preventDefault();
        setFocus(focusableElements[focusableElements.length - 1]);
      }
    }
    // 处理向后Tab
    else {
      // 如果已经是最后一个元素，则循环到第一个
      if (currentIndex === focusableElements.length - 1) {
        event.preventDefault();
        setFocus(focusableElements[0]);
      }
    }
  }, [getCurrentFocus, setFocus]);
  
  return {
    saveFocus,
    restoreFocus,
    setFocus,
    focusFirst,
    focusLast,
    getFocusableElements,
    handleTabNavigation,
    getCurrentFocus,
    isFocusable,
    capabilities: capabilitiesRef.current || {
      supportsFocus: false,
      supportsActiveElement: false,
      supportsScrollIntoView: false,
      supportsFocusVisible: false,
      supportsTabIndex: false,
      supportsKeyboardEvents: false
    }
  };
}

// 创建全局单例实例
let focusManagerInstance: FocusManager | null = null;

/**
 * 获取全局焦点管理器实例
 */
export function getFocusManager(): FocusManager {
  if (!focusManagerInstance) {
    throw new Error('FocusManager未初始化，请在应用根组件中使用useFocusManager');
  }
  return focusManagerInstance;
}

/**
 * 初始化全局焦点管理器
 * 应在应用根组件中调用
 */
export function useInitFocusManager(): FocusManager {
  const manager = useFocusManager();
  
  useEffect(() => {
    focusManagerInstance = manager;
    return () => {
      focusManagerInstance = null;
    };
  }, [manager]);
  
  return manager;
}
