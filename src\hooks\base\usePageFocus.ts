import { useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useManagersWithDebug } from './useManagers';

interface PageFocusOptions {
  // 页面标题，用于屏幕阅读器
  title?: string;
  // 是否在页面切换时自动聚焦
  autoFocus?: boolean;
  // 自定义焦点目标选择器
  focusTarget?: string;
  // 是否跳过焦点设置（用于某些特殊页面）
  skipFocus?: boolean;
}

/**
 * 页面焦点管理Hook
 * 处理页面路由切换时的焦点管理
 */
export function usePageFocus({
  title,
  autoFocus = true,
  focusTarget,
  skipFocus = false
}: PageFocusOptions = {}) {
  const location = useLocation();
  const pageRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);

  // 使用统一的管理器获取
  const { getManagers } = useManagersWithDebug('PageFocus');

  // 设置页面焦点
  const setPageFocus = useCallback(() => {
    if (skipFocus) return;

    try {
      const managers = getManagers();
      if (!managers) return;

      const { focusManager } = managers;
      
      // 优先级：自定义目标 > 页面标题 > 主要内容区域
      let targetElement: HTMLElement | null = null;
      
      if (focusTarget) {
        targetElement = document.querySelector(focusTarget);
      } else if (titleRef.current) {
        targetElement = titleRef.current;
      } else if (pageRef.current) {
        targetElement = pageRef.current;
      }
      
      if (targetElement) {
        // 确保元素可以接收焦点
        if (!targetElement.hasAttribute('tabindex')) {
          targetElement.setAttribute('tabindex', '-1');
        }
        
        focusManager.setFocus(targetElement);
        
        // 滚动到元素位置
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    } catch (error) {
      console.warn('页面焦点设置失败:', error);
    }
  }, [focusTarget, skipFocus]);
  
  // 监听路由变化
  useEffect(() => {
    if (autoFocus) {
      // 使用setTimeout确保DOM更新完成
      const timer = setTimeout(setPageFocus, 100);
      return () => clearTimeout(timer);
    }
  }, [location.pathname, autoFocus, setPageFocus]);
  
  // 设置页面标题
  useEffect(() => {
    if (title) {
      document.title = title;
      
      // 通知屏幕阅读器页面已更改
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', 'polite');
      announcement.setAttribute('aria-atomic', 'true');
      announcement.className = 'sr-only';
      announcement.textContent = `页面已切换到: ${title}`;
      
      document.body.appendChild(announcement);
      
      // 清理
      setTimeout(() => {
        document.body.removeChild(announcement);
      }, 1000);
    }
  }, [title]);
  
  return {
    pageRef,
    titleRef,
    setPageFocus
  };
}

// JSX组件已移动到单独的文件中
// 这是一个纯TypeScript Hook文件，不包含JSX代码
