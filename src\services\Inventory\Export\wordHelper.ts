import { Document, Paragraph, Table, TableCell, TableRow, TextRun, HeadingLevel, AlignmentType, BorderStyle, PageOrientation } from 'docx';

/**
 * Word助手类
 * 提供Word文件生成相关的辅助方法
 */
export class WordHelper {
  /**
   * 创建Word文档
   * @param options 选项
   * @returns Word文档
   */
  public static createDocument(options: any = {}): Document {
    // 创建文档 - 使用极限设置以确保表格横向完整显示
    const doc = new Document({
      // 不预先创建节，避免空白页
      sections: [],
      ...options
    });

    return doc;
  }

  /**
   * 添加文档标题
   * @param doc Word文档
   * @param title 标题
   */
  public static addTitle(doc: Document, title: string): void {
    // 创建标题段落 - 简化样式
    const titleParagraph = new Paragraph({
      children: [
        new TextRun({
          text: title,
          bold: true,
          size: 28, // 减小字体大小
          font: 'SimSun', // 使用宋体，确保中文正常显示
          color: '000000' // 黑色
        })
      ],
      heading: HeadingLevel.HEADING_1,
      alignment: AlignmentType.CENTER,
      spacing: { after: 100, before: 100 } // 进一步减小间距
    });

    // 创建日期段落 - 简化样式
    const dateParagraph = new Paragraph({
      alignment: AlignmentType.RIGHT,
      spacing: { after: 100 }, // 进一步减小间距
      children: [
        new TextRun({
          text: `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}-${new Date().getDate().toString().padStart(2, '0')}`,
          font: 'SimSun', // 使用宋体，确保中文正常显示
          size: 16, // 减小字体大小
          color: '000000' // 黑色
        })
      ]
    });

    // 保存标题和日期段落，稍后与表格一起添加到节中
    this.titleParagraphs = [titleParagraph, dateParagraph];
  }

  // 保存标题段落，以便与表格一起添加
  private static titleParagraphs: Paragraph[] = [];

  /**
   * 处理长标题，在适当位置添加换行
   * @param header 原始标题
   * @param maxLength 每行最大长度
   * @returns 处理后的标题
   */
  private static formatHeaderText(header: string, maxLength: number = 6): string {
    if (header.length <= maxLength) {
      return header;
    }

    // 尝试在合适的位置断行
    const breakPoints = ['/', '-', '_', '（', '('];
    for (const breakPoint of breakPoints) {
      const index = header.indexOf(breakPoint);
      if (index > 0 && index < header.length - 1 && index <= maxLength) {
        return header.substring(0, index) + '\n' + header.substring(index);
      }
    }

    // 如果没有找到合适的断点，在中间位置断行
    if (header.length > maxLength * 2) {
      const midPoint = Math.floor(header.length / 2);
      return header.substring(0, midPoint) + '\n' + header.substring(midPoint);
    }

    return header;
  }

  /**
   * 创建表格
   * @param headers 表头
   * @param data 数据
   * @param pageWidth 页面宽度（可选，用于列宽计算）
   * @returns 表格
   */
  public static createTable(headers: string[], data: any[], pageWidth?: number): Table {
    // 计算每列的最佳宽度 - 确保足够宽以容纳内容
    const columnWidths = this.calculateOptimizedColumnWidths(headers, data, pageWidth);

    // 创建表头行 - 确保文字完全显示
    const headerRow = new TableRow({
      tableHeader: true, // 标记为表头，在多页表格中会重复显示
      height: {
        value: 600, // 增加表头高度，确保有足够空间显示文字
        rule: 'atLeast' // 使用最小高度，允许自动扩展
      },
      children: headers.map((header, index) =>
        new TableCell({
          verticalAlign: 'center',
          width: {
            size: columnWidths[index],
            type: 'dxa' // 使用DXA单位（二十分之一点）
          },
          children: [new Paragraph({
            alignment: AlignmentType.CENTER,
            spacing: {
              before: 50, // 段落前间距
              after: 50,  // 段落后间距
              line: 240   // 行间距，确保多行文字有足够间距
            },
            children: [
              new TextRun({
                text: this.formatHeaderText(header),
                bold: true,
                size: 16, // 适当减小字体大小以适应更多文字
                font: 'SimSun', // 使用宋体，确保中文正常显示
                color: 'FFFFFF' // 白色文字
              })
            ]
          })],
          shading: { fill: "4472C4" }, // 蓝色背景
          // 允许文字换行
          margins: {
            top: 50,
            bottom: 50,
            left: 50,
            right: 50
          }
        })
      )
    });

    // 创建数据行 - 简化样式
    const rows = data.map((item) =>
      new TableRow({
        height: {
          value: 300, // 增加行高，确保内容完整显示
          rule: 'atLeast'
        },
        children: Object.values(item).map((value, colIndex) =>
          new TableCell({
            verticalAlign: 'center',
            width: {
              size: columnWidths[colIndex],
              type: 'dxa'
            },
            // 移除交替行背景色，简化样式
            children: [new Paragraph({
              alignment: AlignmentType.CENTER, // 所有单元格居中对齐，提高可读性
              children: [
                new TextRun({
                  text: value !== null && value !== undefined ? String(value) : '',
                  size: 16, // 减小字体大小
                  font: 'SimSun' // 使用宋体，确保中文正常显示
                })
              ]
            })]
          })
        )
      })
    );

    // 创建表格 - 优化显示效果
    const table = new Table({
      rows: [headerRow, ...rows],
      width: {
        size: 100,
        type: 'pct' // 使用百分比宽度，确保表格占满页面宽度
      },
      borders: this.getSimpleBorders(), // 使用简化的边框样式
      alignment: AlignmentType.CENTER // 表格居中
    });

    return table;
  }

  /**
   * 计算优化后的列宽
   * @param headers 表头
   * @param data 数据
   * @param pageWidth 页面宽度（可选，默认为A3横向宽度）
   * @returns 优化后的列宽数组（单位：DXA）
   */
  private static calculateOptimizedColumnWidths(headers: string[], data: any[], pageWidth: number = 23811): number[] {
    // 计算每列的内容最大长度
    const maxLengths: number[] = headers.map((header, index) => {
      // 处理标题换行后的长度
      const formattedHeader = this.formatHeaderText(header);
      const headerLines = formattedHeader.split('\n');
      const maxHeaderLineLength = Math.max(...headerLines.map(line => line.length));

      // 初始值为处理后的表头最长行长度，给表头更多权重
      let maxLength = Math.max(maxHeaderLineLength * 1.3, 4); // 表头长度乘以1.3倍，最小为4

      // 检查每行数据
      data.forEach(row => {
        const value = Object.values(row)[index];
        const strValue = value !== null && value !== undefined ? String(value) : '';

        // 更新最大长度
        if (strValue.length > maxLength) {
          maxLength = strValue.length;
        }
      });

      return maxLength;
    });

    // 计算总长度
    const totalLength = maxLengths.reduce((sum, length) => sum + length, 0);

    // 预留页边距和表格边框的空间
    const availableWidth = pageWidth * 0.92; // 使用92%的页面宽度，留更多空间

    // 计算每列宽度 - 使用更智能的分配方式
    const calculatedWidths = maxLengths.map((length, index) => {
      // 根据内容长度比例分配宽度
      const ratio = length / totalLength;
      let width = Math.round(ratio * availableWidth);

      // 设置最小宽度，确保标题能够显示
      const formattedHeader = this.formatHeaderText(headers[index]);
      const headerLines = formattedHeader.split('\n');
      const maxHeaderLineLength = Math.max(...headerLines.map(line => line.length));
      const minWidthForHeader = Math.max(maxHeaderLineLength * 140, 1200); // 每个字符约140 DXA，最小1200 DXA

      // 使用计算宽度和最小宽度的较大值
      width = Math.max(width, minWidthForHeader);

      return width;
    });

    // 调试信息：输出列宽计算结果
    console.log('WordHelper - 列宽计算结果:', {
      headers: headers,
      columnWidths: calculatedWidths,
      pageWidth: pageWidth,
      availableWidth: availableWidth
    });

    return calculatedWidths;
  }

  /**
   * 获取简化的表格边框样式
   * @returns 简化的表格边框样式
   */
  private static getSimpleBorders() {
    return {
      top: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: "000000"
      },
      bottom: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: "000000"
      },
      left: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: "000000"
      },
      right: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: "000000"
      },
      insideHorizontal: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: "000000"
      },
      insideVertical: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: "000000"
      }
    };
  }

  /**
   * 获取基础纸张尺寸定义
   * @param format 纸张格式
   * @returns 基础纸张尺寸（纵向，单位：DXA）
   */
  private static getBasePaperSizes(format: 'a3' | 'a4'): { width: number; height: number } {
    // 纸张尺寸定义（单位：DXA，1英寸 = 1440 DXA）
    const sizes = {
      a3: {
        width: 16838,  // A3宽度：11.69英寸
        height: 23811  // A3高度：16.54英寸
      },
      a4: {
        width: 11906,  // A4宽度：8.27英寸
        height: 16838  // A4高度：11.69英寸
      }
    };

    return sizes[format];
  }

  /**
   * 获取页面尺寸（用于列宽计算）
   * @param format 纸张格式
   * @param orientation 页面方向
   * @returns 页面尺寸（单位：DXA）
   */
  private static getPageSizes(format: 'a3' | 'a4', orientation: 'landscape' | 'portrait'): { width: number; height: number } {
    const baseSize = this.getBasePaperSizes(format);

    // 对于列宽计算，我们需要根据方向调整
    if (orientation === 'landscape') {
      // 横向：宽高互换（用于列宽计算）
      return {
        width: baseSize.height,
        height: baseSize.width
      };
    } else {
      // 纵向：保持原始尺寸
      return {
        width: baseSize.width,
        height: baseSize.height
      };
    }
  }

  /**
   * 获取docx库使用的标准页面尺寸
   * @param format 纸张格式
   * @returns 标准页面尺寸（单位：DXA）
   */
  private static getStandardPageSizes(format: 'a3' | 'a4'): { width: number; height: number } {
    // 返回标准的纵向尺寸，让docx库根据orientation自动处理
    return this.getBasePaperSizes(format);
  }

  /**
   * 获取页面方向常量
   * @param orientation 方向字符串
   * @returns docx库的PageOrientation常量
   */
  private static getPageOrientation(orientation: 'landscape' | 'portrait') {
    return orientation === 'landscape'
      ? PageOrientation.LANDSCAPE
      : PageOrientation.PORTRAIT;
  }

  /**
   * 创建文档节
   * @param pageOptions 页面选项
   * @param children 节的子元素
   * @returns 节配置对象
   */
  private static createDocumentSection(
    pageOptions: { format: 'a3' | 'a4'; orientation: 'landscape' | 'portrait' },
    children: any[]
  ) {
    const standardSizes = this.getStandardPageSizes(pageOptions.format);
    const orientation = this.getPageOrientation(pageOptions.orientation);

    return {
      properties: {
        page: {
          size: {
            orientation: orientation,
            width: standardSizes.width,
            height: standardSizes.height
          },
          margin: {
            top: 200, // 极小页边距
            right: 200,
            bottom: 200,
            left: 200
          }
        }
      },
      children: children
    };
  }

  /**
   * 创建表格并添加到文档
   * @param doc Word文档
   * @param headers 表头
   * @param data 数据
   * @param pageOptions 页面选项
   */
  public static createTableAndAddToDocument(
    doc: Document,
    headers: string[],
    data: any[],
    pageOptions: { format: 'a3' | 'a4'; orientation: 'landscape' | 'portrait' } = { format: 'a3', orientation: 'portrait' }
  ): void {
    // 根据纸张大小和方向计算页面尺寸（用于列宽计算）
    const pageSizes = this.getPageSizes(pageOptions.format, pageOptions.orientation);

    // 创建表格，传递页面宽度用于列宽计算
    const table = this.createTable(headers, data, pageSizes.width);

    // 由于标题中已经包含了日期，这里不再添加重复的页脚
    // 只包含标题和表格
    const children = [...this.titleParagraphs, table];

    // 创建并添加文档节
    const section = this.createDocumentSection(pageOptions, children);
    (doc as any).addSection(section);
  }

  /**
   * 添加表格到文档（保持向后兼容）
   * @param doc Word文档
   * @param table 表格
   * @param pageOptions 页面选项
   */
  public static addTableToDocument(
    doc: Document,
    table: Table,
    pageOptions: { format: 'a3' | 'a4'; orientation: 'landscape' | 'portrait' } = { format: 'a3', orientation: 'portrait' }
  ): void {
    // 由于标题中已经包含了日期，这里不再添加重复的页脚
    // 只包含标题和表格
    const children = [...this.titleParagraphs, table];

    // 创建并添加文档节
    const section = this.createDocumentSection(pageOptions, children);
    (doc as any).addSection(section);
  }
}

export default WordHelper;
