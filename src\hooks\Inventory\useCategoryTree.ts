import { useState, useEffect, useRef } from 'react';
import { DeviceCategory } from '../../types/inventory';
import { DepartmentCategory } from '../../services/Inventory/departmentService';
import { useTreeState } from './useTreeState';
import { calculateContextMenuPositionWithPreset } from '../../utils/contextMenuPositionCalculator';
import {
  getValidationTypeByCategory,
  validateField
} from '../../utils/fieldValidation';
import CustomAlert from '../../components/CustomAlert';
import { useTreeNodeTracker } from './useTreeNodeTracker';

// 重新导出类型，这样UI层可以从Hook导入，而不是直接从服务层导入
export type { DepartmentCategory };

// 注意：此接口已不再使用，但为了保持类型兼容性暂时保留
// TODO: 在确认没有外部依赖后可以移除
// export interface ContextMenuItem {
//   icon: JSX.Element;
//   label: string;
//   action: () => void;
// }

export interface CategoryTreeState {
  // 展开的节点集合（使用统一的树状态管理）
  expandedNodes: Set<string>;
  selectedNode: string | null;
  // 右键菜单状态
  contextMenu: {
    visible: boolean;
    x: number;
    y: number;
    categoryId: string;
    categoryName: string;
  };
  // 重命名对话框状态
  renameDialog: {
    visible: boolean;
    categoryId: string;
    categoryName: string;
  };
  // 重命名输入值
  renameValue: string;
  // 重命名加载状态
  isRenaming: boolean;
  // 删除确认对话框状态
  deleteDialog: {
    visible: boolean;
    categoryId: string;
    categoryName: string;
  };
  // 删除加载状态
  isDeleting: boolean;
  // 内联编辑状态
  inlineEdit: {
    isEditing: boolean;
    nodeId: string;
    nodeName: string;
    editValue: string;
  };
  // 内联编辑输入框引用
  inlineEditInputRef: React.RefObject<HTMLInputElement>;
  // 重命名确认对话框状态
  renameConfirmDialog: {
    visible: boolean;
    categoryId: string;
    categoryName: string;
    newName: string;
  };
  // 自定义提示框状态
  alertState: {
    isOpen: boolean;
    message: string;
    type: 'error' | 'warning' | 'info' | 'success';
  };
  // 关闭提示框方法
  closeAlert: () => void;
}

export interface UseCategoryTreeProps {
  categories: DeviceCategory[] | DepartmentCategory[];
  currentCategory: string;
  isFiltered?: boolean;
  categoryMode?: 'device' | 'department';
  onRenameCategory?: (categoryId: string, categoryName: string) => Promise<void>;
  onDeleteCategory?: (categoryId: string) => Promise<void>;
  // 新增的设备父类和子类操作回调
  onUpdateParentCategory?: (curParentCategoryName: string, newParentCategoryName: string) => Promise<void>; // 更新设备父类
  onDeleteParentCategory?: (parentCategoryName: string) => Promise<void>; // 删除设备父类
  onUpdateSubCategory?: (curSubCategoryName: string, newSubCategoryName: string) => Promise<void>; // 更新设备子类
  onDeleteSubCategory?: (subCategoryName: string) => Promise<void>; // 删除设备子类
  // 添加节点选择回调
  onSelectCategory?: (categoryId: string) => void;
}

export function useCategoryTree({
  categories,
  currentCategory,
  isFiltered = false,
  categoryMode = 'device',
  onRenameCategory,
  onDeleteCategory,
  onUpdateParentCategory,
  onDeleteParentCategory,
  onUpdateSubCategory,
  onDeleteSubCategory,
  onSelectCategory
}: UseCategoryTreeProps) {
  // 使用统一的树状态管理Hook
  const treeState = useTreeState({
    defaultExpandedNodes: [],
    onNodeSelect: onSelectCategory
  });

  // 记录用户手动展开的节点集合，用于数据刷新后恢复
  const userExpandedNodesRef = useRef<Set<string>>(new Set());

  // 基于React渲染周期的节点追踪
  useTreeNodeTracker(
    categories,
    new Set(treeState.expandedNodes),
    (nodes: string[]) => {
      console.log(`useCategoryTree: 收到展开节点更新请求`, nodes);
      console.log(`useCategoryTree: 当前展开节点`, treeState.expandedNodes);

      // 更新树状态
      treeState.setExpandedNodes(nodes);

      // 完全重置用户展开节点记录，只保留新的节点
      userExpandedNodesRef.current.clear();
      nodes.forEach(nodeId => userExpandedNodesRef.current.add(nodeId));

      console.log(`useCategoryTree: 已更新展开状态`, nodes);
    },
    (nodeId: string) => {
      // 自动选中追踪到的节点
      console.log(`useCategoryTree: 自动选中节点 ${nodeId}`);
      treeState.selectNode(nodeId);
      if (onSelectCategory) {
        onSelectCategory(nodeId);
      }
    }
  );
  // 右键菜单状态
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    categoryId: string;
    categoryName: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    categoryId: '',
    categoryName: ''
  });
  // 重命名对话框状态
  const [renameDialog, setRenameDialog] = useState<{
    visible: boolean;
    categoryId: string;
    categoryName: string;
  }>({
    visible: false,
    categoryId: '',
    categoryName: ''
  });
  // 重命名输入值
  const [renameValue, setRenameValue] = useState('');
  // 重命名加载状态
  const [isRenaming, setIsRenaming] = useState(false);
  // 删除确认对话框状态
  const [deleteDialog, setDeleteDialog] = useState<{
    visible: boolean;
    categoryId: string;
    categoryName: string;
  }>({
    visible: false,
    categoryId: '',
    categoryName: ''
  });
  // 删除加载状态
  const [isDeleting, setIsDeleting] = useState(false);

  // 自定义提示框状态
  const [alertState, setAlertState] = useState<{
    isOpen: boolean;
    message: string;
    type: 'error' | 'warning' | 'info' | 'success';
  }>({
    isOpen: false,
    message: '',
    type: 'error'
  });

  // 内联编辑状态
  const [inlineEdit, setInlineEdit] = useState<{
    isEditing: boolean;
    nodeId: string;
    nodeName: string;
    editValue: string;
  }>({
    isEditing: false,
    nodeId: '',
    nodeName: '',
    editValue: ''
  });

  // 重命名确认对话框状态
  const [renameConfirmDialog, setRenameConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    nodeId: '',
    nodeName: '',
    newName: ''
  });

  // 内联编辑输入框引用
  const inlineEditInputRef = useRef<HTMLInputElement | null>(null);

  // 显示错误提示的辅助函数
  const showErrorAlert = (message: string) => {
    setAlertState({
      isOpen: true,
      message,
      type: 'error'
    });
  };

  // 关闭提示框
  const closeAlert = () => {
    setAlertState(prev => ({ ...prev, isOpen: false }));
  };

  // 点击文档时关闭右键菜单和内联编辑
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      const target = e.target as Element;

      // 关闭右键菜单（检查点击是否在菜单外部）
      if (contextMenu.visible) {
        // 使用更可靠的方法检查是否点击在菜单内部
        const isInsideContextMenu = target.closest('[data-context-menu="true"]') !== null;

        if (!isInsideContextMenu) {
          console.log('点击菜单外部，关闭右键菜单');
          setContextMenu(prev => ({ ...prev, visible: false }));
        }
      }

      // 关闭内联编辑（如果点击的不是编辑框本身）
      if (inlineEdit.isEditing && inlineEditInputRef.current) {
        // 检查点击的元素是否是编辑框或其子元素
        if (!inlineEditInputRef.current.contains(target)) {
          // 如果点击的不是编辑框，则保存并关闭编辑状态
          handleInlineEditComplete();
        }
      }
    };

    // 监听鼠标按下事件（包括左键、右键、中键）
    document.addEventListener('mousedown', handleOutsideClick);

    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [contextMenu.visible, inlineEdit.isEditing]);

  // 递归收集所有分类ID
  const collectAllIds = (items: DeviceCategory[] | DepartmentCategory[]): string[] => {
    return items.flatMap(category => {
      const ids = [category.id];
      if (category.children?.length) {
        ids.push(...collectAllIds(category.children));
      }
      return ids;
    });
  };

  // 获取默认展开的根节点
  const getDefaultExpandedRootNode = (): string => {
    return categoryMode === 'device' ? 'all' : 'all-dept';
  };

  // 初始化和更新展开节点
  useEffect(() => {
    if (categories.length === 0) return;

    if (isFiltered) {
      // 过滤状态：展开所有节点以便显示匹配结果
      const allIds = collectAllIds(categories);
      treeState.setExpandedNodes(allIds);
    } else {
      // 非过滤状态：合并默认展开的节点和用户手动展开的节点
      const newExpandedNodes = new Set<string>();

      // 添加默认展开的根节点
      newExpandedNodes.add(getDefaultExpandedRootNode());

      // 将用户手动展开的节点添加到展开集合中
      userExpandedNodesRef.current.forEach(id => {
        // 验证节点是否仍然存在于当前categories中
        const nodeExists = findNodeById(categories, id);
        if (nodeExists) {
          newExpandedNodes.add(id);
        }
      });

      treeState.setExpandedNodes(Array.from(newExpandedNodes));
    }
  }, [categories, isFiltered, categoryMode]);

  // 查找节点是否存在于当前分类树中
  const findNodeById = (items: DeviceCategory[] | DepartmentCategory[], id: string): boolean => {
    for (const item of items) {
      if (item.id === id) {
        return true;
      }
      if (item.children && item.children.length > 0) {
        if (findNodeById(item.children, id)) {
          return true;
        }
      }
    }
    return false;
  };

  const toggleNode = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();

    // 使用统一的树状态管理
    treeState.toggleNode(id, e);

    // 更新用户手动展开的节点记录
    if (treeState.isNodeExpanded(id)) {
      userExpandedNodesRef.current.add(id);
    } else {
      userExpandedNodesRef.current.delete(id);
    }
  };

  // 计算右键菜单的智能位置
  const calculateContextMenuPosition = (clientX: number, clientY: number, categoryId: string) => {
    // 根据当前分类模式和节点类型估算菜单项数量
    let menuItemCount = 4; // 默认4个菜单项

    if (categoryMode === 'device') {
      if (categoryId === 'all') {
        menuItemCount = 2; // 添加分类、导出分类
      } else {
        menuItemCount = 4; // 添加台账、导出分类、修改名称、删除
      }
    } else if (categoryMode === 'department') {
      if (categoryId === 'all-dept') {
        menuItemCount = 2; // 添加部门、导出分类
      } else if (categoryId.startsWith('person-')) {
        menuItemCount = 4; // 添加台账、编辑信息、导出分类、删除
      } else {
        menuItemCount = 4; // 添加台账、导出分类、修改名称、删除
      }
    }

    // 使用预设配置计算位置
    const preset = categoryMode === 'device' ? 'deviceCategory' : 'departmentCategory';
    return calculateContextMenuPositionWithPreset(clientX, clientY, preset, menuItemCount);
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, category: DeviceCategory | DepartmentCategory) => {
    // 阻止默认右键菜单
    e.preventDefault();
    e.stopPropagation();

    // 先选中该节点（如果提供了选择回调）
    if (onSelectCategory && category.id !== currentCategory) {
      onSelectCategory(category.id);
    }

    // 计算智能位置，避免菜单被遮挡
    const { x, y } = calculateContextMenuPosition(e.clientX, e.clientY, category.id);

    // 显示自定义右键菜单
    setContextMenu({
      visible: true,
      x: x,
      y: y,
      categoryId: category.id,
      categoryName: category.name
    });
  };

  // 打开重命名对话框
  const handleRenameClick = () => {
    setRenameDialog({
      visible: true,
      categoryId: contextMenu.categoryId,
      categoryName: contextMenu.categoryName
    });
    setRenameValue(contextMenu.categoryName);
    // 关闭右键菜单
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  // 执行重命名
  const executeRename = async () => {
    if (!renameValue.trim()) return;

    // 验证输入
    const categoryId = renameDialog.categoryId;
    const newName = renameValue.trim();

    const validationType = getValidationTypeByCategory(categoryId, categoryMode);
    const validation = validateField(validationType, newName);

    if (!validation.isValid) {
      showErrorAlert(validation.error || '输入验证失败');
      return;
    }

    try {
      setIsRenaming(true);

      // 获取当前节点的类型
      const oldName = renameDialog.categoryName;

      // 判断节点类型
      if (categoryMode === 'device') {
        // 设备分类模式
        if (categoryId.startsWith('parent-') && !categoryId.includes('-', 'parent-'.length)) {
          // 一级分类（父类）
          if (onUpdateParentCategory) {
            await onUpdateParentCategory(oldName, newName);
          }
        } else if (categoryId.startsWith('parent-') && categoryId.includes('-', 'parent-'.length)) {
          // 二级分类（子类）
          if (onUpdateSubCategory) {
            await onUpdateSubCategory(oldName, newName);
          }
        } else {
          // 其他设备分类节点，使用默认的重命名方法
          if (onRenameCategory) {
            await onRenameCategory(categoryId, newName);
          }
        }
      } else {
        // 部门分类模式，使用默认的重命名方法
        if (onRenameCategory) {
          await onRenameCategory(categoryId, newName);
        }
      }

      // 关闭对话框
      setRenameDialog(prev => ({ ...prev, visible: false }));
    } catch (error) {
      console.error('重命名失败:', error);
      showErrorAlert('重命名失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsRenaming(false);
    }
  };

  // 显示删除确认对话框
  const handleDeleteClick = () => {
    // 获取当前节点的类型
    const categoryId = contextMenu.categoryId;
    const categoryName = contextMenu.categoryName;

    // 关闭右键菜单
    setContextMenu(prev => ({ ...prev, visible: false }));

    // 显示删除确认对话框
    setDeleteDialog({
      visible: true,
      categoryId: categoryId,
      categoryName: categoryName
    });
  };

  // 执行删除操作
  const confirmDelete = async () => {
    try {
      setIsDeleting(true);

      // 获取当前节点的类型
      const categoryId = deleteDialog.categoryId;
      const categoryName = deleteDialog.categoryName;



      // 判断节点类型
      if (categoryMode === 'device') {
        // 设备分类模式
        if (categoryId.startsWith('parent-') && !categoryId.includes('-', 'parent-'.length)) {
          // 一级分类（父类）
          if (onDeleteParentCategory) {
            await onDeleteParentCategory(categoryName);
          }
        } else if (categoryId.startsWith('parent-') && categoryId.includes('-', 'parent-'.length)) {
          // 二级分类（子类）
          if (onDeleteSubCategory) {
            await onDeleteSubCategory(categoryName);
          }
        } else {
          // 其他设备分类节点，使用默认的删除方法
          if (onDeleteCategory) {
            await onDeleteCategory(categoryId);
          }
        }
      } else {
        // 部门分类模式，使用默认的删除方法
        if (onDeleteCategory) {
          await onDeleteCategory(categoryId);
        }
      }

      // 关闭删除确认对话框
      setDeleteDialog(prev => ({ ...prev, visible: false }));
    } catch (error) {
      console.error('删除失败:', error);
      // 使用更友好的错误提示
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      // 使用更醒目的提示样式
      const alertDiv = document.createElement('div');
      alertDiv.style.position = 'fixed';
      alertDiv.style.top = '20px';
      alertDiv.style.left = '50%';
      alertDiv.style.transform = 'translateX(-50%)';
      alertDiv.style.backgroundColor = '#f8d7da';
      alertDiv.style.color = '#721c24';
      alertDiv.style.padding = '12px 20px';
      alertDiv.style.borderRadius = '4px';
      alertDiv.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
      alertDiv.style.zIndex = '9999';
      alertDiv.style.maxWidth = '80%';
      alertDiv.style.textAlign = 'center';
      alertDiv.style.fontSize = '14px';
      alertDiv.style.fontWeight = 'bold';
      alertDiv.innerText = errorMessage;

      document.body.appendChild(alertDiv);

      // 3秒后自动移除提示
      setTimeout(() => {
        if (document.body.contains(alertDiv)) {
          document.body.removeChild(alertDiv);
        }
      }, 3000);

    } finally {
      setIsDeleting(false);
    }
  };

  // 注意：此方法已弃用，但为了保持接口兼容性暂时保留
  // TODO: 在确认没有外部依赖后可以移除
  const executeDelete = async () => {
    console.warn('executeDelete方法已弃用，请使用confirmDelete');
    // 为了保持兼容性，调用新的删除方法
    await confirmDelete();
  };

  // 关闭重命名对话框
  const closeRenameDialog = () => {
    setRenameDialog(prev => ({ ...prev, visible: false }));
  };

  // 关闭删除对话框
  const closeDeleteDialog = () => {
    setDeleteDialog(prev => ({ ...prev, visible: false }));
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  // 开始内联编辑
  const startInlineEdit = (category: DeviceCategory | DepartmentCategory, e: React.MouseEvent) => {
    // 阻止事件冒泡，避免触发选中节点
    e.stopPropagation();

    // 根节点不允许编辑
    if ((categoryMode === 'device' && category.id === 'all') ||
        (categoryMode === 'department' && category.id === 'all-dept')) {
      return;
    }

    // 设置内联编辑状态
    setInlineEdit({
      isEditing: true,
      nodeId: category.id,
      nodeName: category.name,
      editValue: category.name
    });

    // 下一帧聚焦到输入框
    setTimeout(() => {
      if (inlineEditInputRef.current) {
        // 聚焦并选中文本
        inlineEditInputRef.current.focus();
        inlineEditInputRef.current.select();

        // 确保输入框不会超出可视区域
        const inputRect = inlineEditInputRef.current.getBoundingClientRect();
        const viewportWidth = window.innerWidth;

        // 如果输入框右边缘超出可视区域，调整其宽度
        if (inputRect.right > viewportWidth - 20) {
          const newWidth = viewportWidth - inputRect.left - 20;
          inlineEditInputRef.current.style.maxWidth = `${newWidth}px`;
        }
      }
    }, 0);
  };

  // 更新内联编辑值
  const updateInlineEditValue = (value: string) => {
    setInlineEdit(prev => ({ ...prev, editValue: value }));
  };

  // 重置内联编辑状态
  const resetInlineEdit = () => {
    // 重置输入框样式，避免影响下次编辑
    if (inlineEditInputRef.current) {
      inlineEditInputRef.current.style.maxWidth = '';
    }

    // 重置编辑状态
    setInlineEdit({
      isEditing: false,
      nodeId: '',
      nodeName: '',
      editValue: ''
    });
  };

  // 完成内联编辑
  const handleInlineEditComplete = async () => {
    // 如果不在编辑状态，直接返回
    if (!inlineEdit.isEditing) return;

    const { nodeId, nodeName, editValue } = inlineEdit;
    const trimmedValue = editValue.trim();

    // 如果值为空或者没有变化，只关闭编辑状态
    if (!trimmedValue || trimmedValue === nodeName) {
      resetInlineEdit();
      return;
    }

    // 先关闭内联编辑状态，避免确认对话框显示时输入框还在
    resetInlineEdit();

    // 显示自绘确认对话框
    const nodeType = categoryMode === 'device' ?
      (nodeId.startsWith('parent-') ? '分类' : '节点') :
      (nodeId.startsWith('dept-') ? '部门' : '人员');

    setRenameConfirmDialog({
      isOpen: true,
      title: '确认',
      message: `是否将${nodeType}"${nodeName}"修改为"${trimmedValue}"？`,
      nodeId,
      nodeName,
      newName: trimmedValue
    });
  };

  // 确认重命名操作
  const confirmRename = async () => {
    const { nodeId, nodeName, newName } = renameConfirmDialog;

    try {
      // 调用重命名方法
      if (categoryMode === 'device') {
        // 设备分类模式
        if (nodeId.startsWith('parent-') && !nodeId.includes('-', 'parent-'.length)) {
          // 一级分类（父类）
          if (onUpdateParentCategory) {
            await onUpdateParentCategory(nodeName, newName);
          }
        } else if (nodeId.startsWith('parent-') && nodeId.includes('-', 'parent-'.length)) {
          // 二级分类（子类）
          if (onUpdateSubCategory) {
            await onUpdateSubCategory(nodeName, newName);
          }
        } else {
          // 其他设备分类节点，使用默认的重命名方法
          if (onRenameCategory) {
            await onRenameCategory(nodeId, newName);
          }
        }
      } else {
        // 部门分类模式，使用默认的重命名方法
        if (onRenameCategory) {
          await onRenameCategory(nodeId, newName);
        }
      }
    } catch (error) {
      console.error('内联编辑重命名失败:', error);
      // 如果重命名失败，显示错误提示
      showErrorAlert('重命名失败：' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  // 关闭重命名确认对话框
  const closeRenameConfirmDialog = () => {
    setRenameConfirmDialog({
      isOpen: false,
      title: '',
      message: '',
      nodeId: '',
      nodeName: '',
      newName: ''
    });
  };

  // 处理内联编辑按键事件
  const handleInlineEditKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      // 回车键保存并关闭
      e.preventDefault();
      handleInlineEditComplete();
    } else if (e.key === 'Escape') {
      // ESC键取消编辑
      e.preventDefault();
      resetInlineEdit();
    }
  };

  // 通过获取状态和方法提供给UI层使用
  return {
    // 状态（使用统一的树状态管理）
    expandedNodes: treeState.expandedNodes,
    selectedNode: treeState.selectedNode,
    contextMenu,
    renameDialog,
    renameValue,
    isRenaming,
    deleteDialog,
    isDeleting,
    inlineEdit,
    inlineEditInputRef,
    renameConfirmDialog,
    alertState,

    // 方法
    setRenameValue,
    closeAlert,
    toggleNode,
    handleContextMenu,
    handleRenameClick,
    executeRename,
    handleDeleteClick,
    confirmDelete,
    executeDelete, // 已弃用，但为了保持兼容性暂时保留
    closeRenameDialog,
    closeDeleteDialog,
    closeContextMenu,
    startInlineEdit,
    updateInlineEditValue,
    handleInlineEditComplete,
    handleInlineEditKeyDown,
    resetInlineEdit,
    confirmRename,
    closeRenameConfirmDialog,

    // 新增的统一树状态管理方法
    ...treeState
  };
}

export default useCategoryTree;