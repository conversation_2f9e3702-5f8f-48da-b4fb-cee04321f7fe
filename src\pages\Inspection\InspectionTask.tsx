import React, { useState, useEffect } from 'react';
import { ClipboardList, Plus, Search, Filter, RefreshCw, Calendar, User, MapPin, Edit, Trash2, X, CheckCircle, Clock, AlertCircle, XCircle } from 'lucide-react';
import DatePicker from '../../components/ui/DatePicker';
import CustomSelect from '../../components/ui/CustomSelect';
import CustomScrollbar from '../../components/ui/CustomScrollbar';
import DepartmentTooltip from '../../components/ui/DepartmentTooltip';
import useInspectionTask, { InspectionTaskItem, InspectionTaskFilter } from '../../hooks/Inspection/useInspectionTask';
import CreateTaskDialog from './CreateTaskDialog';
import TaskEditDialog from '../../components/Inspection/TaskEditDialog';

// 导入统一的时间戳转换函数
import { convertToUnixTimestamp } from '../../utils/fieldUtils';

/**
 * 巡检任务页面
 * 提供巡检任务管理功能
 */
const InspectionTask: React.FC = () => {
  // 使用巡检任务Hook
  const {
    isRefreshing,
    error,
    filteredTasks,
    filter,
    getTasks,
    refreshTasks,
    setFilter,
    clearFilter,
    createTask,
    updateTask,
    deleteTask,
    getInspectors,
    getDepartments,
    getExecutionStatusStyle,
    getExecutionStatusIconType,
    getStatusStyle,
    getStatusText,
    getStatusIconType
  } = useInspectionTask();

  // UI状态
  const [showFilters, setShowFilters] = useState(false);
  const [localFilter, setLocalFilter] = useState<InspectionTaskFilter>({});
  const [searchText, setSearchText] = useState('');

  // 创建任务对话框状态
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // 编辑任务对话框状态
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingTask, setEditingTask] = useState<InspectionTaskItem | null>(null);

  // 删除确认对话框状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingTask, setDeletingTask] = useState<InspectionTaskItem | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // 筛选处理方法
  const handleApplyFilter = () => {
    setFilter(localFilter);
    setShowFilters(false);
  };

  const handleClearFilter = () => {
    setLocalFilter({});
    clearFilter();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
    setFilter({ searchText: e.target.value });
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // 搜索逻辑已在handleSearchChange中处理
    }
  };

  const handleClearSearch = () => {
    setSearchText('');
    setFilter({ searchText: '' });
  };

  // 处理创建任务
  const handleCreateTask = async (formData: any) => {
    try {
      setIsCreating(true);

      // 转换日期为Unix时间戳 - 确保正确解析本地时间并匹配后端格式
      const startDate = convertToUnixTimestamp(formData.startDate);
      const endDate = convertToUnixTimestamp(formData.endDate);

      // 构建API请求数据
      const taskData = {
        task_name: formData.taskName,
        task_description: formData.taskDescription,
        person_name: formData.personName,
        person_alias: formData.personAlias,
        start_date: startDate,
        end_date: endDate,
        department_paths: formData.departmentPaths
      };

      // 调用创建任务API
      await createTask(taskData);

      // 创建成功后不需要刷新，因为createTask已经更新了缓存和本地状态
      console.log('任务创建成功');
    } catch (error) {
      console.error('创建任务失败:', error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  };

  // 处理编辑任务
  const handleEditTask = (task: InspectionTaskItem) => {
    setEditingTask(task);
    setShowEditDialog(true);
  };

  // 处理更新任务
  const handleUpdateTask = async (updateData: {
    task_id: number;
    task_name?: string;
    task_description?: string;
    start_date?: number;
    end_date?: number;
  }) => {
    try {
      await updateTask(updateData);
      setShowEditDialog(false);
      setEditingTask(null);
      console.log('任务更新成功');
    } catch (error) {
      console.error('更新任务失败:', error);
      throw error;
    }
  };

  // 处理删除任务
  const handleDeleteTask = (task: InspectionTaskItem) => {
    setDeletingTask(task);
    setShowDeleteDialog(true);
  };

  // 确认删除任务
  const handleConfirmDelete = async () => {
    if (!deletingTask) return;

    try {
      setIsDeleting(true);
      await deleteTask(deletingTask.task_id.toString());

      setShowDeleteDialog(false);
      setDeletingTask(null);
      console.log('任务删除成功');
    } catch (error) {
      console.error('删除任务失败:', error);
      // 错误处理可以在这里添加toast提示
    } finally {
      setIsDeleting(false);
    }
  };

  // 取消删除
  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
    setDeletingTask(null);
  };

  // 页面初始化时加载数据（使用智能缓存）
  useEffect(() => {
    getTasks(); // 首次加载使用缓存，如果没有缓存则自动获取
  }, [getTasks]);

  // 根据执行状态渲染图标 - 使用后端返回的task_execution_status
  const renderExecutionStatusIcon = (executionStatus: string) => {
    const iconType = getExecutionStatusIconType(executionStatus);
    switch (iconType) {
      case 'CheckCircle':
        return <CheckCircle className="h-3 w-3 mr-1" />;
      case 'Clock':
        return <Clock className="h-3 w-3 mr-1" />;
      case 'AlertCircle':
        return <AlertCircle className="h-3 w-3 mr-1" />;
      case 'XCircle':
        return <XCircle className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  // 保留原有的状态图标渲染函数用于兼容性
  const renderStatusIcon = (status: string) => {
    const iconType = getStatusIconType(status);
    switch (iconType) {
      case 'CheckCircle':
        return <CheckCircle className="h-3 w-3 mr-1" />;
      case 'Clock':
        return <Clock className="h-3 w-3 mr-1" />;
      case 'AlertCircle':
        return <AlertCircle className="h-3 w-3 mr-1" />;
      case 'XCircle':
        return <XCircle className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* 操作工具栏 - 固定在顶部 */}
      <div className="flex-none bg-gray-50 border-b border-gray-200 sticky top-0 z-10">
        <div className="px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={refreshTasks}
                disabled={isRefreshing}
                className="btn btn-primary"
              >
                <RefreshCw className={`h-3.5 w-3.5 mr-1.5 ${isRefreshing ? 'animate-spin' : ''}`} />
                刷新数据
              </button>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`btn ${
                  Object.keys(filter).length > 0
                    ? 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                } border`}
              >
                <Filter className="h-3.5 w-3.5 mr-1.5" />
                筛选
                {Object.keys(filter).length > 0 && (
                  <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-blue-600 rounded-full">
                    {Object.keys(filter).length}
                  </span>
                )}
              </button>
              <button
                onClick={() => setShowCreateDialog(true)}
                className="btn bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <Plus className="h-3.5 w-3.5 mr-1.5" />
                创建任务
              </button>
            </div>

            <div className="relative flex-1 max-w-md ml-6">
              <input
                type="text"
                placeholder="搜索任务..."
                value={searchText}
                onChange={handleSearchChange}
                onKeyPress={handleKeyPress}
                className="w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-all duration-200"
              />
              <Search className="absolute search-icon left-3 top-3 h-4 w-4 text-gray-400" />
              {searchText && (
                <button
                  onClick={handleClearSearch}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 筛选面板卡片 - 紧凑优化版 */}
      {showFilters && (
        <div className="mx-6 mt-4 mb-4 bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
          {/* 筛选面板头部 - 减小高度 */}
          <div className="px-4 py-2.5 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <h4 className="text-base font-bold text-gray-800">筛选条件</h4>
            </div>
            <button
              onClick={() => setShowFilters(false)}
              className="p-1 text-gray-400 hover:text-gray-600 hover:bg-white rounded transition-all duration-200"
              title="关闭筛选"
            >
              <X className="h-3.5 w-3.5" />
            </button>
          </div>

          {/* 筛选表单 - 紧凑布局 */}
          <div className="px-4 py-3">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* 开始日期 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  <span className="flex items-center space-x-1">
                    <span>开始日期</span>
                  </span>
                </label>
                <DatePicker
                  name="startDate"
                  value={localFilter.startDate || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, startDate: e.target.value })}
                  placeholder="请选择开始日期"
                />
              </div>

              {/* 结束日期 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  <span className="flex items-center space-x-1">
                    <span>结束日期</span>
                  </span>
                </label>
                <DatePicker
                  name="endDate"
                  value={localFilter.endDate || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, endDate: e.target.value })}
                  placeholder="请选择结束日期"
                />
              </div>

              {/* 巡检人 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  巡检人
                </label>
                <CustomSelect
                  name="inspector"
                  value={localFilter.inspector || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, inspector: e.target.value || undefined })}
                  options={[
                    { value: '', label: '全部人员' },
                    ...getInspectors().map(inspector => ({ value: inspector, label: inspector }))
                  ]}
                  placeholder="请选择巡检人"
                />
              </div>

              {/* 巡检部门 */}
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-700">
                  巡检部门
                </label>
                <CustomSelect
                  name="department"
                  value={localFilter.department || ''}
                  onChange={(e) => setLocalFilter({ ...localFilter, department: e.target.value || undefined })}
                  options={[
                    { value: '', label: '全部部门' },
                    ...getDepartments().map(department => ({ value: department, label: department }))
                  ]}
                  placeholder="请选择巡检部门"
                />
              </div>
            </div>

            {/* 操作按钮 - 紧凑布局 */}
            <div className="mt-4 flex items-center justify-between">
              <div className="text-xs text-gray-500">
                {Object.keys(filter).length > 0 && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    已应用 {Object.keys(filter).length} 个条件
                  </span>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleClearFilter}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-all duration-200"
                >
                  清除筛选
                </button>
                <button
                  onClick={handleApplyFilter}
                  className="inline-flex items-center px-4 py-1.5 border border-transparent rounded text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm hover:shadow transition-all duration-200"
                >
                  应用筛选
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 任务列表表格 - 参考巡检结果页面设计 */}
      <div className="flex-1 flex flex-col overflow-hidden relative">
        {/* 简化的表格中间刷新指示器 */}
        {isRefreshing && (
          <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-20">
            <div className="flex flex-col items-center">
              <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mb-2" />
              <p className="text-blue-600 text-sm font-medium">刷新数据中...</p>
            </div>
          </div>
        )}

        {/* 自适应数据表格 - 参考巡检结果页面 */}
        <div className="flex-1 min-h-0 bg-white shadow-sm adaptive-table-container">
          <div className="adaptive-table-content">
            <CustomScrollbar
              className="h-full"
              horizontal={true}
              vertical={true}
            >
              <table className="w-full adaptive-table" style={{ minWidth: '1000px' }}>
                <thead>
                  <tr>
                    <th className="table-header-cell table-cell-left w-20">任务ID</th>
                    <th className="table-header-cell table-cell-left w-32">任务名称</th>
                    <th className="table-header-cell table-cell-left w-40">任务描述</th>
                    <th className="table-header-cell table-cell-left w-24">责任人</th>
                    <th className="table-header-cell table-cell-left w-32">巡检部门</th>
                    <th className="table-header-cell table-cell-left w-24">开始日期</th>
                    <th className="table-header-cell table-cell-left w-24">结束日期</th>
                    <th className="table-header-cell table-cell-center w-20">状态</th>
                    <th className="table-header-cell table-cell-center w-32">操作</th>
                  </tr>
                </thead>
                <tbody className="bg-white">
                {filteredTasks.length === 0 ? (
                  <tr className="adaptive-table-empty">
                    <td colSpan={9} className="px-4 py-12 text-center">
                      <div className="flex flex-col items-center justify-center h-full min-h-[200px]">
                        <ClipboardList className="h-8 w-8 text-gray-400 mb-3" />
                        <p className="text-gray-500 text-sm font-medium">暂无任务数据</p>
                        <p className="text-gray-400 text-xs mt-1">请创建新的巡检任务</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredTasks.map((task: InspectionTaskItem, index: number) => (
                    <tr
                      key={`task-${task.task_id}-${task.task_name}-${task.start_date}-${index}`}
                      className="relative"
                    >
                      {/* 任务ID */}
                      <td className="table-cell whitespace-nowrap">
                        <span className="text-sm font-medium text-blue-600">
                          #{task.task_id}
                        </span>
                      </td>

                      {/* 任务名称 */}
                      <td className="table-cell">
                        <div className="flex items-center">
                          <div className="w-5 h-5 bg-blue-100 text-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                            <ClipboardList className="h-3 w-3" />
                          </div>
                          <span className="text-sm font-medium text-gray-900 truncate" title={task.task_name}>
                            {task.task_name}
                          </span>
                        </div>
                      </td>

                      {/* 任务描述 */}
                      <td className="table-cell">
                        <span className="text-sm text-gray-700 truncate" title={task.task_description}>
                          {task.task_description || '-'}
                        </span>
                      </td>

                      {/* 责任人 */}
                      <td className="table-cell whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-5 h-5 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                            <User className="h-3 w-3" />
                          </div>
                          <span className="text-sm text-gray-900 truncate" title={task.assignee}>
                            {task.assignee}
                          </span>
                        </div>
                      </td>

                      {/* 巡检部门 */}
                      <td className="table-cell">
                        <span className="text-sm text-gray-700 truncate" title={task.departmentList}>
                          {task.departmentList || '-'}
                        </span>
                      </td>

                      {/* 开始日期 */}
                      <td className="table-cell whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                          <span className="text-xs text-gray-700 truncate" title={task.startTime}>
                            {task.startTime}
                          </span>
                        </div>
                      </td>

                      {/* 结束日期 */}
                      <td className="table-cell whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                          <span className="text-xs text-gray-700 truncate" title={task.endTime}>
                            {task.endTime}
                          </span>
                        </div>
                      </td>

                      {/* 状态 - 使用后端返回的task_execution_status */}
                      <td className="table-cell table-cell-center whitespace-nowrap">
                        <span className={`status-label ${getExecutionStatusStyle(task.task_execution_status)}`}>
                          {renderExecutionStatusIcon(task.task_execution_status)}
                          <span className="ml-1">{task.task_execution_status}</span>
                        </span>
                      </td>

                      {/* 操作 */}
                      <td className="table-cell table-cell-center whitespace-nowrap">
                        <div className="flex items-center justify-center space-x-1">
                          <button
                            onClick={() => handleEditTask(task)}
                            className="inline-flex items-center justify-center w-7 h-7 text-green-600 bg-green-50 border border-green-200 hover:bg-green-100 transition-colors"
                            title="编辑任务"
                          >
                            <Edit className="h-3 w-3" />
                          </button>
                          <button
                            onClick={() => handleDeleteTask(task)}
                            className="inline-flex items-center justify-center w-7 h-7 text-red-600 bg-red-50 border border-red-200 hover:bg-red-100 transition-colors"
                            title="删除任务"
                          >
                            <Trash2 className="h-3 w-3" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
            </CustomScrollbar>
          </div>
        </div>
      </div>

      {/* 创建任务对话框 */}
      <CreateTaskDialog
        isOpen={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onSubmit={handleCreateTask}
        isLoading={isCreating}
      />

      {/* 编辑任务对话框 */}
      <TaskEditDialog
        isOpen={showEditDialog}
        onClose={() => {
          setShowEditDialog(false);
          setEditingTask(null);
        }}
        task={editingTask}
        onSave={handleUpdateTask}
      />

      {/* 删除确认对话框 */}
      {showDeleteDialog && deletingTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            {/* 对话框头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    确认删除任务
                  </h3>
                </div>
              </div>
              <button
                onClick={handleCancelDelete}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                disabled={isDeleting}
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* 对话框内容 */}
            <div className="p-6">
              <div className="text-sm text-gray-600 mb-4">
                您确定要删除以下巡检任务吗？此操作不可撤销。
              </div>

              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-700 w-20">任务名称：</span>
                    <span className="text-sm text-red-600 font-medium">{deletingTask.task_name}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-700 w-20">任务ID：</span>
                    <span className="text-sm text-red-600 font-medium">{deletingTask.task_id}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-700 w-20">责任人：</span>
                    <span className="text-sm text-gray-900">{deletingTask.assignee}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-700 w-20">状态：</span>
                    <span className={`status-label ${getExecutionStatusStyle(deletingTask.task_execution_status)}`}>
                      {renderExecutionStatusIcon(deletingTask.task_execution_status)}
                      <span className="ml-1">{deletingTask.task_execution_status}</span>
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start">
                  <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <div className="ml-2 text-sm text-red-700">
                    <strong>警告：</strong>删除任务将同时删除相关的巡检数据和记录，此操作无法恢复。
                  </div>
                </div>
              </div>
            </div>

            {/* 对话框底部 */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              <button
                onClick={handleCancelDelete}
                disabled={isDeleting}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={isDeleting}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                {isDeleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    删除中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    确认删除
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InspectionTask;
