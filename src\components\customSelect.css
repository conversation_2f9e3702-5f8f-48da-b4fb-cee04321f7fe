/* 焦点样式 */
*:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* 输入框和交互元素的焦点样式 */
input:focus-visible,
button:focus-visible,
select:focus-visible,
textarea:focus-visible,
[role="button"]:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
}

/* 禁用下拉框内输入框的焦点样式 */
.select-input-no-focus,
.select-input-no-focus:focus,
.select-input-no-focus:focus-visible,
.select-input-no-focus:active,
.select-input-no-focus:hover {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
  border-color: transparent !important;
  border-width: 0 !important;
  border-style: none !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
  border-bottom: none !important;
  background-image: none !important;
  background-color: transparent !important;
}

/* 强制重置部门选择框容器的边框样式 */
.department-select-container {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  outline: none !important;
  box-shadow: none !important;
  background-color: white !important;
}

.department-select-container:focus-within {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.department-select-container.disabled {
  background-color: #f3f4f6 !important;
  border-color: #d1d5db !important;
}

/* 隐藏鼠标点击时的焦点样式，但保留键盘导航时的焦点样式 */
*:focus:not(:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
}

/* 确保下拉菜单不被截断 - 增强层级管理 */
.absolute:not(.dropdown-container):not(.department-dropdown):not(.submenu-container):not(.search-icon),
.fixed:not(.dropdown-container):not(.department-dropdown):not(.submenu-container):not(.search-icon) {
  z-index: 9999 !important;
}

/* 下拉框容器特定样式 - 确保在模态对话框之上 */
.dropdown-container {
  z-index: 1000001 !important;
}

/* 子菜单容器特定样式 - 确保在下拉框之上 */
.submenu-container {
  z-index: 1000002 !important;
}

/* 自定义滚动条样式 */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 下拉菜单动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 应用动画 */
.dropdown-down {
  animation: fadeIn 0.2s ease-out;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
}

.dropdown-up {
  animation: fadeInUp 0.2s ease-out;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
}

/* 模态对话框内的下拉框特殊处理 */
[role="dialog"] .dropdown-container,
.modal .dropdown-container {
  position: fixed !important;
  z-index: 1000001 !important;
}

[role="dialog"] .submenu-container,
.modal .submenu-container {
  position: fixed !important;
  z-index: 1000002 !important;
}
