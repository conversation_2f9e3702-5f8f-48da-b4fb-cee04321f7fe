import React, { useState, useRef, useEffect } from 'react';
import Portal from './Portal';

interface DepartmentTooltipProps {
  departments: string[];
  children: React.ReactNode;
}

const DepartmentTooltip: React.FC<DepartmentTooltipProps> = ({ departments, children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);

  const updatePosition = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY + 4, // 4px gap
        left: rect.left + window.scrollX
      });
    }
  };

  const handleMouseEnter = () => {
    updatePosition();
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  useEffect(() => {
    if (isVisible) {
      const handleScroll = () => updatePosition();
      const handleResize = () => updatePosition();
      
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);
      
      return () => {
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [isVisible]);

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="relative"
      >
        {children}
      </div>
      
      {isVisible && departments.length > 1 && (
        <Portal>
          <div
            className="fixed bg-white border border-gray-200 rounded-md shadow-xl p-3 min-w-48 max-w-80 transition-all duration-200"
            style={{
              top: position.top,
              left: position.left,
              zIndex: 9999
            }}
            onMouseEnter={() => setIsVisible(true)}
            onMouseLeave={() => setIsVisible(false)}
          >
            <div className="text-xs font-medium text-gray-700 mb-2">所有巡检部门:</div>
            <div className="flex flex-wrap gap-1.5">
              {departments.map((dept, index) => (
                <span key={index} className="inline-block text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded whitespace-nowrap">
                  {dept}
                </span>
              ))}
            </div>
          </div>
        </Portal>
      )}
    </>
  );
};

export default DepartmentTooltip;
