import React, { useState, useRef, useEffect } from 'react';
import { FileUp, AlertCircle, Check, Info } from 'lucide-react';
import useImportDevice from '../../../hooks/Inventory/Import/useImportDevice';
import ImportDetailDialog from './ImportDetailDialog';

// 导入按钮属性类型
interface ImportButtonProps {
  onImportComplete: () => void;
}

/**
 * 导入按钮组件
 * 用于导入设备台账数据
 */
const ImportButton: React.FC<ImportButtonProps> = ({ onImportComplete }) => {
  // 使用导入设备钩子
  const { isImporting, importDevicesFromExcel } = useImportDevice();

  // 本地状态
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [showError, setShowError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // 详细信息对话框状态
  const [showDetailDialog, setShowDetailDialog] = useState<boolean>(false);
  const [detailDialogType, setDetailDialogType] = useState<'success' | 'error'>('success');
  const [detailDialogTitle, setDetailDialogTitle] = useState<string>('');
  const [detailDialogDetails, setDetailDialogDetails] = useState<string[]>([]);
  const [hasFailedRecords, setHasFailedRecords] = useState<boolean>(false);
  const [failedRecordsCount, setFailedRecordsCount] = useState<number>(0);

  // 定时器引用
  const successTimerRef = useRef<number | null>(null);
  const errorTimerRef = useRef<number | null>(null);

  // 处理导入
  const handleImport = async () => {
    try {
      // 重置状态
      setShowSuccess(false);
      setShowError(false);

      console.log('开始导入设备台账数据...');

      // 执行导入
      const result = await importDevicesFromExcel();

      console.log('导入结果:', result);

      // 检查结果格式
      if (result && result.success) {
        // 显示成功消息
        let successCount = result.success_count || 0;

        // 如果成功数量为0，但有原始数据，尝试从原始数据中提取
        if (successCount === 0 && result.rawData) {
          // 尝试从原始数据中提取导入数量
          const rawData = result.rawData;
          if (rawData && typeof rawData === 'object') {
            if (rawData.data) {
              // 如果data是一个数组，可能表示导入的记录
              if (Array.isArray(rawData.data)) {
                successCount = rawData.data.length;
              }
              // 如果data中有imported_count字段
              else if (rawData.data.imported_count !== undefined) {
                successCount = rawData.data.imported_count;
              }
            }
          }
        }

        // 如果成功数量仍然为0，但我们知道导入成功了，显示一个通用消息
        const successMsg = successCount > 0
          ? `导入成功！成功导入 ${successCount} 条记录${result.fail_count ? `，失败 ${result.fail_count} 条` : ''}`
          : `导入成功！数据已成功导入系统`;
        console.log(successMsg);
        setSuccessMessage(successMsg);
        setShowSuccess(true);

        // 如果有失败记录且有失败消息，同时显示错误信息
        if (result.fail_count && result.fail_count > 0 && result.failure_messages && result.failure_messages.length > 0) {
          const errorMsg = `导入失败原因: ${result.failure_messages.join('; ')}`;
          console.warn('部分导入失败:', errorMsg);
          setErrorMessage(errorMsg);
          setShowError(true);

          // 设置失败记录信息，用于详细对话框
          setHasFailedRecords(true);
          setFailedRecordsCount(result.fail_count);

          // 5秒后隐藏错误消息（比成功消息多显示2秒）
          if (errorTimerRef.current) {
            clearTimeout(errorTimerRef.current);
          }
          errorTimerRef.current = window.setTimeout(() => {
            setShowError(false);
            errorTimerRef.current = null;
          }, 5000);
        }

        // 通知父组件导入完成，刷新数据
        console.log('通知父组件刷新数据...');

        // 延迟一小段时间再刷新，确保后端处理完成
        setTimeout(() => {
          console.log('开始强制刷新数据...');
          onImportComplete();
        }, 500);

        // 5秒后隐藏成功消息，给用户更多时间感知
        // 清除之前的定时器
        if (successTimerRef.current) {
          clearTimeout(successTimerRef.current);
        }
        successTimerRef.current = window.setTimeout(() => {
          setShowSuccess(false);
          successTimerRef.current = null;
        }, 5000);
      } else {
        // 显示错误消息
        let errorMsg = '导入失败';

        // 尝试从结果中提取错误信息
        if (result && result.failure_messages && result.failure_messages.length > 0) {
          // 检查是否包含USER_CANCELLED错误
          if (result.failure_messages.some(msg => msg === 'USER_CANCELLED')) {
            errorMsg = '用户已取消导入';
          } else {
            // 检查是否包含INVALID_FILE_FORMAT错误
            if (result.failure_messages.some(msg => msg === 'INVALID_FILE_FORMAT' || msg.includes('INVALID_FILE_FORMAT'))) {
              // 如果有error_message字段，使用它
              if (result.rawData && result.rawData.error_message) {
                errorMsg = `导入失败: INVALID_FILE_FORMAT - ${result.rawData.error_message}`;
              } else {
                // 尝试从结果中提取更详细的错误信息
                const resultStr = JSON.stringify(result);
                errorMsg = `导入失败: ${resultStr}`;
              }
            } else {
              errorMsg = result.failure_messages.join(', ');
            }
          }
        } else if (result && typeof result === 'object') {
          // 尝试从结果对象中提取任何可能的错误信息
          const resultStr = JSON.stringify(result);
          if (resultStr && resultStr !== '{}') {
            // 检查是否包含USER_CANCELLED
            if (resultStr.includes('USER_CANCELLED')) {
              errorMsg = '用户已取消导入';
            } else if (resultStr.includes('INVALID_FILE_FORMAT')) {
              // 尝试提取更详细的错误信息
              try {
                if (result.rawData && result.rawData.error_message) {
                  errorMsg = `导入失败: INVALID_FILE_FORMAT - ${result.rawData.error_message}`;
                } else {
                  errorMsg = `导入失败: ${resultStr}`;
                }
              } catch (e) {
                errorMsg = `导入失败: ${resultStr}`;
              }
            } else {
              errorMsg = `导入失败: ${resultStr}`;
            }
          }
        }

        console.error('导入失败:', errorMsg);
        setErrorMessage(errorMsg);
        setShowError(true);

        // 5秒后隐藏错误消息，给用户更多时间感知
        // 清除之前的定时器
        if (errorTimerRef.current) {
          clearTimeout(errorTimerRef.current);
        }
        errorTimerRef.current = window.setTimeout(() => {
          setShowError(false);
          errorTimerRef.current = null;
        }, 5000);
      }
    } catch (err: any) {
      console.error('导入过程中发生错误:', err);

      // 显示错误消息
      let errorMsg = '导入失败';

      if (err && err.message) {
        // 将英文错误消息转换为中文
        if (err.message === 'USER_CANCELLED') {
          errorMsg = '用户已取消导入';
        } else {
          errorMsg = err.message;
        }
      }

      // 添加更多调试信息
      if (err && err.stack) {
        console.error('错误堆栈:', err.stack);
      }

      console.error('最终错误消息:', errorMsg);
      setErrorMessage(errorMsg);
      setShowError(true);

      // 5秒后隐藏错误消息，给用户更多时间感知
      // 清除之前的定时器
      if (errorTimerRef.current) {
        clearTimeout(errorTimerRef.current);
      }
      errorTimerRef.current = window.setTimeout(() => {
        setShowError(false);
        errorTimerRef.current = null;
      }, 5000);
    }
  };

  // 处理成功消息点击
  const handleSuccessClick = () => {
    // 清除定时器
    if (successTimerRef.current) {
      clearTimeout(successTimerRef.current);
      successTimerRef.current = null;
    }

    // 设置详细信息对话框内容
    setDetailDialogType('success');
    setDetailDialogTitle('导入成功');

    // 提取成功消息中的数量信息
    const successCount = successMessage.match(/成功导入\s*(\d+)\s*条记录/);
    const details = [
      '导入操作已成功完成',
      successCount ? `成功导入了 ${successCount[1]} 条记录` : '数据已成功导入系统',
      '系统已更新相关数据',
      '您可以在设备台账列表中查看导入的数据'
    ];

    // 如果有失败记录，添加相关信息
    if (hasFailedRecords && failedRecordsCount > 0) {
      details.push('');
      details.push(`注意: 有 ${failedRecordsCount} 条记录导入失败`);
      details.push('您可以点击"导出失败记录"按钮导出失败的记录，修正后重新导入');
    }

    setDetailDialogDetails(details);

    // 显示详细信息对话框
    setShowDetailDialog(true);
  };

  // 处理错误消息点击
  const handleErrorClick = () => {
    // 清除定时器
    if (errorTimerRef.current) {
      clearTimeout(errorTimerRef.current);
      errorTimerRef.current = null;
    }

    // 设置详细信息对话框内容
    setDetailDialogType('error');
    setDetailDialogTitle('导入失败');

    // 解析错误消息，提取详细信息
    const details: string[] = [];

    // 添加标题行
    details.push('导入失败原因:');

    // 直接显示错误信息，不显示红色提示区域
    if (errorMessage.includes('第') && errorMessage.includes('行') && errorMessage.includes('序号')) {
      // 这是格式化的错误信息，可能包含行号和具体错误
      // 将错误信息按分号分割，并格式化为列表项
      const errorLines = errorMessage.split(';').map(line => line.trim());

      // 过滤掉可能包含的"导入失败原因:"前缀
      const filteredLines = errorLines.map(line => line.replace(/^导入失败原因:\s*/, ''));

      // 添加到详情列表
      details.push(...filteredLines);
    } else if (errorMessage.includes('INVALID_FILE_FORMAT')) {
      // 处理文件格式无效的错误
      try {
        // 尝试从错误消息中提取JSON部分
        const jsonMatch = errorMessage.match(/\{.*\}/);
        if (jsonMatch) {
          const errorObj = JSON.parse(jsonMatch[0]);
          if (errorObj.error_message) {
            details.push(errorObj.error_message);
          } else {
            details.push('文件格式无效，请使用正确的导入模板');
          }
        } else {
          details.push('文件格式无效，请使用正确的导入模板');
        }
      } catch (e) {
        // 如果解析失败，显示通用错误
        details.push('文件格式无效，请使用正确的导入模板');
      }
    } else if (errorMessage.includes(';')) {
      // 如果错误消息包含分号，可能是多个错误
      const errorParts = errorMessage.split(';').map(msg => msg.trim());

      // 过滤掉"导入失败原因:"前缀
      const filteredParts = errorParts.map(part => part.replace(/^导入失败原因:\s*/, ''));

      // 添加到详情列表
      details.push(...filteredParts);
    } else if (errorMessage.includes('导入失败原因:')) {
      // 提取导入失败原因后的内容
      details.push(errorMessage.replace(/^导入失败原因:\s*/, ''));
    } else {
      details.push(errorMessage);
    }

    // 添加常见错误解决方案
    details.push('');
    details.push('常见解决方案:');
    details.push('1. 确保使用正确的导入模板');
    details.push('2. 检查必填字段是否已填写，特别是"使用情况"字段');
    details.push('3. 验证数据格式是否符合要求');
    details.push('4. 确保责任人所在部门与当前指定部门一致');

    setDetailDialogDetails(details);

    // 显示详细信息对话框
    setShowDetailDialog(true);
  };

  // 处理关闭详细信息对话框
  const handleCloseDetailDialog = () => {
    setShowDetailDialog(false);
  };

  // 处理鼠标进入成功消息
  const handleSuccessMouseEnter = () => {
    // 清除定时器，防止自动隐藏
    if (successTimerRef.current) {
      clearTimeout(successTimerRef.current);
      successTimerRef.current = null;
    }
  };

  // 处理鼠标离开成功消息
  const handleSuccessMouseLeave = () => {
    // 重新设置定时器
    if (!successTimerRef.current) {
      successTimerRef.current = window.setTimeout(() => {
        setShowSuccess(false);
        successTimerRef.current = null;
      }, 3000);
    }
  };

  // 处理鼠标进入错误消息
  const handleErrorMouseEnter = () => {
    // 清除定时器，防止自动隐藏
    if (errorTimerRef.current) {
      clearTimeout(errorTimerRef.current);
      errorTimerRef.current = null;
    }
  };

  // 处理鼠标离开错误消息
  const handleErrorMouseLeave = () => {
    // 重新设置定时器
    if (!errorTimerRef.current) {
      errorTimerRef.current = window.setTimeout(() => {
        setShowError(false);
        errorTimerRef.current = null;
      }, 3000);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={handleImport}
        className="flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
        title="导入设备台账数据"
        disabled={isImporting}
      >
        <FileUp className="h-4 w-4 mr-1" />
        {isImporting ? '导入中...' : '导入'}
      </button>

      {/* 成功消息 - 优化后的动画效果和样式，添加鼠标悬停和点击功能 */}
      {showSuccess && (
        <div
          className="absolute top-full mt-2 right-0 z-50 w-72 p-3 bg-green-50 border border-green-200 rounded-md shadow-md animate-slideInRight cursor-pointer hover:bg-green-100 transition-colors"
          onClick={handleSuccessClick}
          onMouseEnter={handleSuccessMouseEnter}
          onMouseLeave={handleSuccessMouseLeave}
        >
          <div className="flex items-center">
            <div className="mr-3 flex-shrink-0 h-7 w-7 rounded-full bg-green-100 flex items-center justify-center">
              <Check className="h-4 w-4 text-green-600 animate-checkmark" />
            </div>
            <div className="flex-1">
              <p className="font-medium text-green-800 text-sm">导入成功</p>
              <p className="text-green-700 text-xs mt-0.5">{successMessage}</p>
            </div>
            <Info className="h-4 w-4 text-green-600 ml-2" title="点击查看详情" />
          </div>
        </div>
      )}

      {/* 错误消息 - 优化后的动画效果和样式，添加鼠标悬停和点击功能 */}
      {showError && (
        <div
          className={`absolute ${showSuccess ? 'top-full mt-28' : 'top-full mt-2'} right-0 z-50 w-80 p-3 bg-red-50 border border-red-200 rounded-md shadow-md animate-slideInRight cursor-pointer hover:bg-red-100 transition-colors`}
          onClick={handleErrorClick}
          onMouseEnter={handleErrorMouseEnter}
          onMouseLeave={handleErrorMouseLeave}
        >
          <div className="flex items-start">
            <div className="mr-3 flex-shrink-0 h-7 w-7 rounded-full bg-red-100 flex items-center justify-center mt-0.5">
              <AlertCircle className="h-4 w-4 text-red-600 animate-pulse" />
            </div>
            <div className="flex-1">
              <p className="font-medium text-red-800 text-sm">导入失败</p>
              <p className="text-red-700 text-xs mt-1 break-words">{errorMessage}</p>
            </div>
            <Info className="h-4 w-4 text-red-600 ml-2" title="点击查看详情" />
          </div>
        </div>
      )}

      {/* 详细信息对话框 */}
      <ImportDetailDialog
        isOpen={showDetailDialog}
        onClose={handleCloseDetailDialog}
        type={detailDialogType}
        title={detailDialogTitle}
        details={detailDialogDetails}
        hasFailedRecords={hasFailedRecords}
        failedRecordsCount={failedRecordsCount}
      />
    </div>
  );
};

export default ImportButton;
