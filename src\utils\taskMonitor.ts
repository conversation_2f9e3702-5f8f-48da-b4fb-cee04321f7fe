import { TaskInfo } from './taskManager';

// 任务执行统计信息
interface TaskStats {
  totalTasks: number;        // 总任务数
  completedTasks: number;    // 完成的任务数
  failedTasks: number;       // 失败的任务数
  cancelledTasks: number;    // 取消的任务数
  averageCompletionTime: number;  // 平均完成时间(ms)
  averageAttempts: number;   // 平均尝试次数
}

// 按任务类型的统计信息
interface TaskTypeStats {
  [taskType: string]: {
    count: number;           // 该类型的任务数
    avgTime: number;         // 该类型任务的平均完成时间
    successRate: number;     // 成功率
    failureRate: number;     // 失败率
  };
}

interface TaskExecutionRecord {
  taskId: string;
  taskType: string;          // 任务类型(dllName_funcName)
  startTime: number;         // 开始时间
  endTime?: number;          // 结束时间
  duration?: number;         // 持续时间
  status: string;            // 最终状态
  attempts: number;          // 尝试次数
  maxAttempts: number;       // 最大尝试次数
  error?: string;            // 错误信息
}

export class TaskMonitor {
  private static instance: TaskMonitor;
  private taskRecords: Map<string, TaskExecutionRecord> = new Map();
  
  // 最近任务记录限制
  private maxRecordsToKeep: number = 1000;
  
  // 用于计算统计信息的记录窗口(毫秒)
  private statsWindowDuration: number = 24 * 60 * 60 * 1000; // 默认1天
  
  private constructor() {}

  public static getInstance(): TaskMonitor {
    if (!TaskMonitor.instance) {
      TaskMonitor.instance = new TaskMonitor();
    }
    return TaskMonitor.instance;
  }
  
  // 记录任务启动
  public trackTaskStart(task: TaskInfo, taskType: string): void {
    const record: TaskExecutionRecord = {
      taskId: task.id,
      taskType,
      startTime: Date.now(),
      status: 'running',
      attempts: task.attempts || 0,
      maxAttempts: task.maxAttempts || 1
    };
    
    this.taskRecords.set(task.id, record);
    this.pruneOldRecords();
  }
  
  // 更新任务状态
  public updateTaskStatus(task: TaskInfo, taskType?: string): void {
    const record = this.taskRecords.get(task.id);
    if (record) {
      record.status = task.status;
      record.attempts = task.attempts || 0;
      
      if (taskType) {
        record.taskType = taskType;
      }
      
      if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
        record.endTime = Date.now();
        record.duration = record.endTime - record.startTime;
        
        if (task.status === 'failed') {
          record.error = task.error;
        }
      }
      
      this.taskRecords.set(task.id, record);
    }
  }
  
  // 清理旧记录
  private pruneOldRecords(): void {
    if (this.taskRecords.size <= this.maxRecordsToKeep) {
      return;
    }
    
    // 将记录转换为数组并按开始时间排序
    const records = Array.from(this.taskRecords.entries())
      .sort((a, b) => a[1].startTime - b[1].startTime);
    
    // 删除最旧的记录直到达到限制
    const recordsToRemove = records.slice(0, records.length - this.maxRecordsToKeep);
    for (const [taskId] of recordsToRemove) {
      this.taskRecords.delete(taskId);
    }
  }
  
  // 获取总体任务统计信息
  public getTaskStats(): TaskStats {
    const now = Date.now();
    const cutoffTime = now - this.statsWindowDuration;
    
    // 只考虑统计窗口内的任务
    const recentTasks = Array.from(this.taskRecords.values())
      .filter(record => record.startTime >= cutoffTime);
    
    const completedTasks = recentTasks.filter(r => r.status === 'completed');
    const failedTasks = recentTasks.filter(r => r.status === 'failed');
    const cancelledTasks = recentTasks.filter(r => r.status === 'cancelled');
    
    // 计算平均完成时间
    const completionTimes = completedTasks
      .filter(r => r.duration !== undefined)
      .map(r => r.duration as number);
    
    const avgCompletionTime = completionTimes.length > 0 
      ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length 
      : 0;
    
    // 计算平均尝试次数
    const avgAttempts = recentTasks.length > 0
      ? recentTasks.reduce((sum, r) => sum + r.attempts, 0) / recentTasks.length
      : 0;
    
    return {
      totalTasks: recentTasks.length,
      completedTasks: completedTasks.length,
      failedTasks: failedTasks.length,
      cancelledTasks: cancelledTasks.length,
      averageCompletionTime: avgCompletionTime,
      averageAttempts: avgAttempts
    };
  }
  
  // 按任务类型获取统计信息
  public getTaskTypeStats(): TaskTypeStats {
    const now = Date.now();
    const cutoffTime = now - this.statsWindowDuration;
    
    // 只考虑统计窗口内的任务
    const recentTasks = Array.from(this.taskRecords.values())
      .filter(record => record.startTime >= cutoffTime);
    
    const taskTypeStats: TaskTypeStats = {};
    
    // 按任务类型分组
    recentTasks.forEach(task => {
      if (!taskTypeStats[task.taskType]) {
        taskTypeStats[task.taskType] = {
          count: 0,
          avgTime: 0,
          successRate: 0,
          failureRate: 0
        };
      }
      
      taskTypeStats[task.taskType].count++;
    });
    
    // 计算每种任务类型的详细统计信息
    Object.keys(taskTypeStats).forEach(taskType => {
      const tasksOfType = recentTasks.filter(t => t.taskType === taskType);
      const completedTasks = tasksOfType.filter(t => t.status === 'completed');
      const failedTasks = tasksOfType.filter(t => t.status === 'failed');
      
      // 计算成功率和失败率
      taskTypeStats[taskType].successRate = tasksOfType.length > 0 
        ? completedTasks.length / tasksOfType.length 
        : 0;
      
      taskTypeStats[taskType].failureRate = tasksOfType.length > 0 
        ? failedTasks.length / tasksOfType.length 
        : 0;
      
      // 计算平均完成时间
      const completionTimes = completedTasks
        .filter(t => t.duration !== undefined)
        .map(t => t.duration as number);
      
      taskTypeStats[taskType].avgTime = completionTimes.length > 0 
        ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length 
        : 0;
    });
    
    return taskTypeStats;
  }
  
  // 获取正在进行的任务
  public getActiveTasks(): TaskExecutionRecord[] {
    return Array.from(this.taskRecords.values())
      .filter(record => record.status === 'waiting' || record.status === 'running')
      .sort((a, b) => b.startTime - a.startTime); // 最新的排在前面
  }
  
  // 获取最近完成的任务
  public getRecentCompletedTasks(limit: number = 20): TaskExecutionRecord[] {
    return Array.from(this.taskRecords.values())
      .filter(record => record.status === 'completed')
      .sort((a, b) => (b.endTime || 0) - (a.endTime || 0)) // 最新完成的排在前面
      .slice(0, limit);
  }
  
  // 获取最近失败的任务
  public getRecentFailedTasks(limit: number = 20): TaskExecutionRecord[] {
    return Array.from(this.taskRecords.values())
      .filter(record => record.status === 'failed')
      .sort((a, b) => (b.endTime || 0) - (a.endTime || 0)) // 最新失败的排在前面
      .slice(0, limit);
  }
  
  // 设置保留的最大记录数
  public setMaxRecordsToKeep(max: number): void {
    if (max > 0) {
      this.maxRecordsToKeep = max;
      this.pruneOldRecords();
    }
  }
  
  // 设置统计窗口时长(毫秒)
  public setStatsWindowDuration(duration: number): void {
    if (duration > 0) {
      this.statsWindowDuration = duration;
    }
  }
  
  // 清除所有记录
  public clearAllRecords(): void {
    this.taskRecords.clear();
  }
  
  // 获取特定任务的执行记录
  public getTaskRecord(taskId: string): TaskExecutionRecord | undefined {
    return this.taskRecords.get(taskId);
  }
  
  // 获取任务执行时间分布
  public getTaskDurationDistribution(): {[range: string]: number} {
    const distribution: {[range: string]: number} = {
      "0-100ms": 0,
      "100-500ms": 0,
      "500ms-1s": 0,
      "1s-5s": 0,
      "5s-10s": 0,
      "10s-30s": 0,
      "30s-1m": 0,
      "1m-5m": 0,
      ">5m": 0
    };
    
    const completedTasks = Array.from(this.taskRecords.values())
      .filter(r => r.status === 'completed' && r.duration !== undefined);
    
    completedTasks.forEach(task => {
      const duration = task.duration as number;
      
      if (duration < 100) distribution["0-100ms"]++;
      else if (duration < 500) distribution["100-500ms"]++;
      else if (duration < 1000) distribution["500ms-1s"]++;
      else if (duration < 5000) distribution["1s-5s"]++;
      else if (duration < 10000) distribution["5s-10s"]++;
      else if (duration < 30000) distribution["10s-30s"]++;
      else if (duration < 60000) distribution["30s-1m"]++;
      else if (duration < 300000) distribution["1m-5m"]++;
      else distribution[">5m"]++;
    });
    
    return distribution;
  }
}

export default TaskMonitor; 