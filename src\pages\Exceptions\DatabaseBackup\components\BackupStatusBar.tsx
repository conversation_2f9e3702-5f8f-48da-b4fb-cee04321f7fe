import React from 'react';
import { CheckCircle, AlertTriangle, GitBranch } from 'lucide-react';
import { BackupData } from '../../../../services/Backup/backupService';

interface BackupStatusBarProps {
  activeBackup: BackupData | null;
  formatFileSize: (bytes: number) => string;
  formatRelativeTime: (timestamp: number) => string;
}

export const BackupStatusBar: React.FC<BackupStatusBarProps> = ({
  activeBackup,
  formatFileSize,
  formatRelativeTime,
}) => {
  if (!activeBackup) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
        <div className="flex items-start space-x-2">
          <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-yellow-900 text-sm">未设置活动备份</h4>
            <p className="text-xs text-yellow-800 mt-1 leading-relaxed">
              请创建或切换到一个备份版本
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-green-50 border border-green-200 rounded p-3">
      <div className="flex items-start space-x-2">
        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1 mb-2">
            <GitBranch className="w-3 h-3 text-green-600" />
            <h4 className="font-medium text-green-900 text-sm">活动版本</h4>
          </div>
          <p className="text-xs text-green-800 font-medium truncate mb-2">
            {activeBackup.backup_name || activeBackup.backup_filename}
          </p>

          <div className="space-y-1 text-xs text-green-700">
            <div className="flex justify-between">
              <span>创建时间:</span>
              <span className="font-medium">{formatRelativeTime(activeBackup.backup_timestamp)}</span>
            </div>
            <div className="flex justify-between">
              <span>文件大小:</span>
              <span className="font-medium">{formatFileSize(activeBackup.backup_size)}</span>
            </div>
            {activeBackup.md5_hash && (
              <div className="flex justify-between">
                <span>MD5:</span>
                <span className="font-mono text-xs">{activeBackup.md5_hash}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};