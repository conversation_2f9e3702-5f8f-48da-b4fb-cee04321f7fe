// 焦点管理系统统一导出

// 核心焦点管理
export { getFocusManager } from './useFocusManager';
export { getKeyboardManager } from './useKeyboardManager';
export { useDialogFocus } from './useDialogFocus';

// 键盘事件处理
export { useEscapeKey, useDialogEscapeKey } from './useEscapeKey';

// 管理器获取
export { useManagers, useManagersWithDebug } from './useManagers';

// 页面焦点管理
export { usePageFocus } from './usePageFocus';

// 表单焦点管理
export { useFormFocus } from './useFormFocus';

// 列表焦点管理
export { useListFocus } from './useListFocus';

// 其他基础hooks
export { useDialogBase } from './useDialogBase';
export { useLoading } from './useLoading';
export { useNotification } from './useNotification';
