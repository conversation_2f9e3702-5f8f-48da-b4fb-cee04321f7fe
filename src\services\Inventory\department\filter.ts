import { InventoryItem } from '../../../types/inventory';
import { DepartmentCategory } from './types';
import { DepartmentUtils } from './utils';

/**
 * 部门筛选器
 * 负责根据部门ID筛选设备列表
 */
export class DepartmentFilter {
  /**
   * 根据部门ID筛选设备列表
   * @param inventoryList 完整的设备列表
   * @param departmentCategories 部门分类树
   * @param categoryId 选中的部门ID
   * @returns 筛选后的设备列表
   */
  public static filterByDepartment(
    inventoryList: InventoryItem[],
    departmentCategories: DepartmentCategory[],
    categoryId: string
  ): InventoryItem[] {
    try {
      // 记录开始时间，用于性能分析
      const startTime = performance.now();

      // 全部部门不筛选
      if (categoryId === 'all-dept') {
        return [...inventoryList];
      }

      // 在部门树中查找指定的分类
      const selectedCategory = DepartmentUtils.findNodeById(departmentCategories, categoryId);

      if (!selectedCategory) {
        console.warn('未找到指定的部门分类:', categoryId);
        return [];
      }

      // 如果是部门节点（以dept-开头）
      if (categoryId.startsWith('dept-')) {
        // 获取当前部门的完整路径（包括所有父部门）
        const departmentPath = DepartmentUtils.getDepartmentPath(selectedCategory, departmentCategories);

        // 获取当前部门及其所有子部门的完整路径列表
        const departmentPaths = DepartmentUtils.getAllDepartmentPaths(selectedCategory, departmentCategories);

        // 获取所有同名部门的路径列表
        const allSameNameDepartments = DepartmentUtils.findAllDepartmentsWithName(departmentCategories, selectedCategory.name);

        // 获取当前部门下的所有人员
        const departmentPersons = DepartmentUtils.getAllPersonsInDepartment(selectedCategory);

        // 记录日志
        console.log(`筛选部门: ${selectedCategory.name}, 路径: ${departmentPath}`);
        console.log(`子部门路径数量: ${departmentPaths.length}, 同名部门数量: ${allSameNameDepartments.length}, 部门人员数量: ${departmentPersons.length}`);

        // 筛选出属于这些部门的所有设备
        const result = inventoryList.filter(item => {
          // 检查设备部门字段是否包含当前部门（支持多部门显示）
          if (!item.department) return false;

          // 如果设备部门字段包含多个部门（用逗号分隔）
          const deviceDepartments = item.department.split(',').map(dept => dept.trim());

          // 检查是否包含当前部门
          if (deviceDepartments.includes(selectedCategory.name)) {
            // 如果有多个同名部门，需要进一步判断
            if (allSameNameDepartments.length > 1) {
              // 如果设备有责任人，检查责任人是否属于当前部门
              if (item.responsible) {
                // 检查设备的责任人是否在当前部门的人员列表中
                return departmentPersons.some(person =>
                  DepartmentUtils.isResponsibleMatch(item.responsible!, person)
                );
              } else {
                // 如果设备没有责任人，但当前部门没有人员，则匹配
                return departmentPersons.length === 0;
              }
            }

            // 如果只有一个同名部门，直接匹配
            return true;
          }

          // 检查设备部门名称是否与任何子部门名称匹配
          for (const path of departmentPaths) {
            const pathParts = path.split('/');
            const departmentName = pathParts[pathParts.length - 1]; // 获取路径中的最后一个部门名称

            // 检查设备部门字段是否包含子部门（支持多部门显示）
            const deviceDepartments = item.department.split(',').map(dept => dept.trim());

            if (deviceDepartments.includes(departmentName)) {
              // 如果有多个同名部门，需要进一步判断
              const sameDeptPaths = DepartmentUtils.findAllDepartmentsWithName(departmentCategories, departmentName);

              if (sameDeptPaths.length > 1) {
                // 找到当前路径对应的部门节点
                const deptNode = DepartmentUtils.findDepartmentByPath(path, departmentCategories);
                if (!deptNode) continue;

                // 获取该部门下的所有人员
                const subDeptPersons = DepartmentUtils.getAllPersonsInDepartment(deptNode);

                // 如果设备有责任人，检查责任人是否属于当前部门
                if (item.responsible) {
                  // 检查设备的责任人是否在当前部门的人员列表中
                  if (subDeptPersons.some(person =>
                    DepartmentUtils.isResponsibleMatch(item.responsible!, person)
                  )) {
                    return true;
                  }
                } else {
                  // 如果设备没有责任人，但当前部门没有人员，则匹配
                  if (subDeptPersons.length === 0) {
                    return true;
                  }
                }
              } else {
                // 如果只有一个同名部门，直接匹配
                return true;
              }
            }
          }

          return false;
        });

        // 记录性能日志
        const endTime = performance.now();
        console.log(`部门筛选耗时: ${(endTime - startTime).toFixed(2)}ms, 筛选结果数量: ${result.length}/${inventoryList.length}`);

        return result;
      }

      // 如果是人员节点（以person-开头）
      if (categoryId.startsWith('person-')) {
        // 从人员名称中提取用户名和备注
        const personFullName = selectedCategory.name;
        const personName = DepartmentUtils.extractPersonName(personFullName);
        const personAlias = DepartmentUtils.extractPersonAlias(personFullName);

        // 检查是否是多部门人员节点（格式：person-{personId}-dept-{deptId}）
        const isMultiDeptPerson = categoryId.includes('-dept-');

        if (isMultiDeptPerson) {
          // 多部门人员：显示该人员在所有部门的设备
          console.log(`筛选多部门人员: ${personName}, 备注: ${personAlias}`);

          // 筛选该人员负责的所有设备（不限制部门）
          const result = inventoryList.filter(item => {
            // 只检查责任人是否匹配，不限制部门
            return DepartmentUtils.isResponsibleMatch(item.responsible || '', personFullName);
          });

          // 记录性能日志
          const endTime = performance.now();
          console.log(`多部门人员筛选耗时: ${(endTime - startTime).toFixed(2)}ms, 筛选结果数量: ${result.length}/${inventoryList.length}`);

          return result;
        } else {
          // 单部门人员：使用原有逻辑
          // 找到人员所属的部门
          const parentDept = DepartmentUtils.findParentDepartment(departmentCategories, categoryId);
          if (!parentDept) {
            console.warn('未找到人员所属部门:', categoryId);
            return [];
          }

          // 获取部门名称
          const departmentName = parentDept.name;

          // 检查是否有同名部门
          const allSameNameDepartments = DepartmentUtils.findAllDepartmentsWithName(departmentCategories, departmentName);

          // 记录日志
          console.log(`筛选单部门人员: ${personName}, 备注: ${personAlias}, 部门: ${departmentName}`);
          console.log(`同名部门数量: ${allSameNameDepartments.length}`);

          // 筛选设备 - 需要处理多部门显示的情况
          const result = inventoryList.filter(item => {
            // 检查责任人是否匹配
            if (!DepartmentUtils.isResponsibleMatch(item.responsible || '', personFullName)) {
              return false;
            }

            // 检查部门是否匹配（支持多部门显示）
            if (!item.department) return false;

            // 如果设备部门字段包含多个部门（用逗号分隔）
            const deviceDepartments = item.department.split(',').map(dept => dept.trim());

            // 检查是否包含当前部门
            const departmentMatch = deviceDepartments.includes(departmentName);

            // 如果有多个同名部门，需要进一步判断
            if (departmentMatch && allSameNameDepartments.length > 1) {
              // 已经通过责任人匹配验证，直接返回true
              return true;
            }

            return departmentMatch;
          });

          // 记录性能日志
          const endTime = performance.now();
          console.log(`单部门人员筛选耗时: ${(endTime - startTime).toFixed(2)}ms, 筛选结果数量: ${result.length}/${inventoryList.length}`);

          return result;
        }
      }

      // 如果是责任人节点（以resp-开头） - 兼容旧版逻辑
      if (categoryId.startsWith('resp-')) {
        // 从ID中提取部门和责任人
        const parts = categoryId.split('-');
        if (parts.length >= 3) {
          const departmentName = parts[1];
          const responsibleName = parts.slice(2).join('-'); // 处理责任人名称中可能包含连字符的情况

          const result = inventoryList.filter(item =>
            item.department === departmentName &&
            item.responsible === responsibleName
          );

          // 记录性能日志
          const endTime = performance.now();
          console.log(`责任人筛选耗时: ${(endTime - startTime).toFixed(2)}ms, 筛选结果数量: ${result.length}/${inventoryList.length}`);

          return result;
        }
      }

      return [];
    } catch (error) {
      console.error('部门筛选失败:', error);
      return [];
    }
  }
}
