import React, { useState } from 'react';
import { PersonDetailInfo } from '../types/inventory';
import DepartmentService from '../services/Inventory/departmentService';

interface PersonTooltipProps {
  personId: string;  // 人员节点ID，格式为 "person-{id}"
  children: React.ReactNode;
  className?: string;
}

/**
 * 人员详细信息悬停提示组件
 * 当鼠标悬停在人员节点上时显示详细信息
 */
const PersonTooltip: React.FC<PersonTooltipProps> = ({ personId, children, className = '' }) => {
  const [personInfo, setPersonInfo] = useState<PersonDetailInfo | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  // 从人员ID中提取数字ID（支持新旧格式）
  const extractPersonId = function(nodeId: string): number | null {
    // 新格式：person-1-dept-2 -> 1
    // 旧格式：person-1 -> 1
    const match = nodeId.match(/^person-(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  };

  // 获取人员详细信息
  const fetchPersonInfo = function(nodeId: string) {
    const numericId = extractPersonId(nodeId);
    if (!numericId) {
      console.warn('无效的人员ID:', nodeId);
      return;
    }

    setIsLoading(true);
    const departmentService = DepartmentService.getInstance();

    // 使用缓存机制获取人员信息
    departmentService.getPersonInfo(numericId).then(function(person) {
      if (person) {
        setPersonInfo(person);
      } else {
        console.warn('未找到人员信息:', numericId);
      }
      setIsLoading(false);
    }).catch(function(error: Error) {
      console.error('获取人员信息失败:', error);
      setIsLoading(false);
    });
  };

  // 处理鼠标进入
  const handleMouseEnter = function(e: React.MouseEvent) {
    // 查找最近的树节点元素
    let targetElement = e.currentTarget as HTMLElement;

    // 如果当前元素使用了contents，需要找到实际的父级容器
    while (targetElement && (!targetElement.getBoundingClientRect || targetElement.getBoundingClientRect().width === 0)) {
      targetElement = targetElement.parentElement as HTMLElement;
    }

    // 尝试通过data-node-id找到正确的节点元素
    if (!targetElement || targetElement.getBoundingClientRect().width === 0) {
      const nodeElement = document.querySelector(`[data-node-id="${personId}"]`) as HTMLElement;
      if (nodeElement) {
        targetElement = nodeElement;
      }
    }

    if (!targetElement) {
      console.warn('无法找到目标元素进行定位');
      return;
    }

    const rect = targetElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算提示框的预估宽度和高度
    const tooltipWidth = 320; // 预估宽度 (min-w-64 = 256px, max-w-80 = 320px)
    const tooltipHeight = 200; // 预估高度

    let x = rect.right + 10;
    let y = rect.top + rect.height / 2;

    // 如果右侧空间不够，显示在左侧
    if (x + tooltipWidth > viewportWidth - 20) {
      x = rect.left - tooltipWidth - 10;
    }

    // 如果左侧空间也不够，显示在右侧但调整位置
    if (x < 20) {
      x = rect.right + 10;
      // 如果还是超出，就贴着右边界
      if (x + tooltipWidth > viewportWidth - 20) {
        x = viewportWidth - tooltipWidth - 20;
      }
    }

    // 垂直方向居中，但确保不超出视口
    if (y - tooltipHeight / 2 < 20) {
      y = 20 + tooltipHeight / 2;
    } else if (y + tooltipHeight / 2 > viewportHeight - 20) {
      y = viewportHeight - tooltipHeight / 2 - 20;
    }

    console.log('PersonTooltip定位信息:', {
      personId,
      targetRect: rect,
      calculatedPosition: { x, y }
    });

    setPosition({ x, y });
    setIsVisible(true);
    fetchPersonInfo(personId);
  };

  // 处理鼠标离开
  const handleMouseLeave = function() {
    setIsVisible(false);
    setPersonInfo(null);
  };

  // 获取岗位密级显示文本
  const getSecurityLevelText = function(level: number): string {
    switch (level) {
      case 0: return '非涉密人员';
      case 1: return '一般涉密人员';
      case 2: return '重要涉密人员';
      case 3: return '核心涉密人员';
      default: return '未知';
    }
  };

  // 获取岗位密级颜色
  const getSecurityLevelColor = function(level: number): string {
    switch (level) {
      case 0: return 'text-gray-600';
      case 1: return 'text-blue-600';
      case 2: return 'text-orange-600';
      case 3: return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <>
      <span
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="contents" // 使用contents让子元素直接参与父级布局
      >
        {children}
      </span>

      {/* 悬停提示框 */}
      {isVisible && (
        <div
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-64 max-w-80"
          style={{
            left: position.x + 'px',
            top: position.y + 'px',
            transform: 'translateY(-50%)',
            pointerEvents: 'none' // 防止鼠标事件干扰
          }}
        >
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : personInfo ? (
            <div className="space-y-3">
              {/* 标题 */}
              <div className="border-b border-gray-200 pb-2">
                <h3 className="text-lg font-semibold text-gray-800">人员信息</h3>
              </div>

              {/* 基本信息 */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-600 w-16">姓名:</span>
                  <span className="text-sm text-gray-800">{personInfo.user_name}</span>
                </div>

                {personInfo.alias && (
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-600 w-16">备注:</span>
                    <span className="text-sm text-gray-800">{personInfo.alias}</span>
                  </div>
                )}

                {personInfo.position && (
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-600 w-16">职位:</span>
                    <span className="text-sm text-gray-800">{personInfo.position}</span>
                  </div>
                )}

                {/* 显示所有部门信息 */}
                <div className="flex items-start">
                  <span className="text-sm font-medium text-gray-600 w-16 mt-0.5">部门:</span>
                  <div className="flex-1">
                    {personInfo.departments && personInfo.departments.length > 0 ? (
                      <div className="space-y-1">
                        {personInfo.departments.map((dept) => (
                          <div key={dept.id} className="flex items-center">
                            <span className="text-sm text-gray-800">
                              {dept.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span className="text-sm text-gray-800">{personInfo.primary_department_name || '未分配'}</span>
                    )}
                  </div>
                </div>

                {personInfo.mobile_number && (
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-600 w-16">手机:</span>
                    <span className="text-sm text-gray-800">{personInfo.mobile_number}</span>
                  </div>
                )}

                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-600 w-16">密级:</span>
                  <span className={'text-sm font-medium ' + getSecurityLevelColor(personInfo.position_security_level)}>
                    {personInfo.position_security_level_text || getSecurityLevelText(personInfo.position_security_level)}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <span className="text-gray-600">暂无人员信息</span>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default PersonTooltip;
