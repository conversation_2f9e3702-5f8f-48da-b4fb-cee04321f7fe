import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { useTreeState, useTreeRender } from '../../hooks/Inventory/useTreeState';

/**
 * 树节点数据接口
 */
export interface TreeNode {
  id: string;
  name: string;
  children?: TreeNode[];
  [key: string]: any; // 允许额外的属性
}

/**
 * 树渲染器属性接口
 */
export interface TreeRendererProps {
  // 数据
  nodes: TreeNode[];
  
  // 状态管理
  expandedNodes: Set<string>;
  selectedNode?: string | null;
  
  // 事件处理
  onNodeToggle: (nodeId: string, event?: React.MouseEvent) => void;
  onNodeSelect?: (nodeId: string) => void;
  onNodeContextMenu?: (event: React.MouseEvent, node: TreeNode) => void;
  
  // 渲染配置
  categoryMode?: 'device' | 'department';
  draggable?: boolean;
  
  // 拖拽相关
  dragState?: {
    isDragging: boolean;
    draggedId: string | null;
  };
  hoverTargetId?: string | null;
  canDropOnTarget?: boolean;
  onDragHandlers?: {
    onMouseDown: (e: React.MouseEvent, nodeId: string, node: TreeNode) => void;
    onMouseEnter: (e: React.MouseEvent, nodeId: string) => void;
    onMouseLeave: (e: React.MouseEvent, nodeId: string) => void;
  };
  
  // 自定义渲染
  renderNodeIcon?: (node: TreeNode, depth: number) => React.ReactNode;
  renderNodeContent?: (node: TreeNode, hasChildren: boolean, isExpanded: boolean, depth: number) => React.ReactNode;
  renderNodeActions?: (node: TreeNode) => React.ReactNode;
  
  // 样式
  className?: string;
  nodeClassName?: string;
}

/**
 * 统一的树渲染组件
 * 整合重复的树形结构渲染逻辑
 */
export const TreeRenderer: React.FC<TreeRendererProps> = ({
  nodes,
  expandedNodes,
  selectedNode,
  onNodeToggle,
  onNodeSelect,
  onNodeContextMenu,
  categoryMode = 'device',
  draggable = false,
  dragState,
  hoverTargetId,
  canDropOnTarget = false,
  onDragHandlers,
  renderNodeIcon,
  renderNodeContent,
  renderNodeActions,
  className = '',
  nodeClassName = ''
}) => {
  const { calculateIndent, getNodeClasses } = useTreeRender({ categoryMode, draggable });

  // 默认的节点图标渲染
  const defaultRenderNodeIcon = (node: TreeNode, depth: number) => {
    // 可以根据节点类型返回不同的图标
    return null;
  };

  // 默认的节点内容渲染
  const defaultRenderNodeContent = (node: TreeNode, hasChildren: boolean, isExpanded: boolean, depth: number) => {
    return (
      <>
        {/* 展开/收起图标 */}
        {hasChildren ? (
          <span
            className={`inline-flex items-center justify-center text-gray-500 ${
              categoryMode === 'department' ? 'w-3 h-3 mr-0.5' : 'w-4 h-4 mr-1'
            }`}
            onClick={(e) => onNodeToggle(node.id, e)}
          >
            {isExpanded ? (
              <ChevronDown className={categoryMode === 'department' ? 'w-3 h-3' : 'w-4 h-4'} />
            ) : (
              <ChevronRight className={categoryMode === 'department' ? 'w-3 h-3' : 'w-4 h-4'} />
            )}
          </span>
        ) : (
          <span className={categoryMode === 'department' ? 'w-3 h-3 mr-0.5' : 'w-4 h-4 mr-1'}></span>
        )}

        {/* 节点图标 */}
        {renderNodeIcon ? renderNodeIcon(node, depth) : defaultRenderNodeIcon(node, depth)}

        {/* 节点名称 */}
        <span className="flex-1 truncate">{node.name}</span>

        {/* 节点操作 */}
        {renderNodeActions && renderNodeActions(node)}
      </>
    );
  };

  // 递归渲染树节点
  const renderTreeNodes = (nodeList: TreeNode[], depth = 0): React.ReactNode => {
    return nodeList.map(node => {
      const hasChildren = node.children && node.children.length > 0;
      const isExpanded = expandedNodes.has(node.id);
      const isSelected = selectedNode === node.id;
      const isDraggable = draggable && categoryMode === 'department';
      const isDragging = dragState?.isDragging && dragState?.draggedId === node.id;
      const isDropTarget = hoverTargetId === node.id && canDropOnTarget;

      const nodeClasses = getNodeClasses(isSelected, isDragging, isDropTarget);
      const indentStyle = { paddingLeft: calculateIndent(depth, categoryMode) };

      return (
        <div key={node.id} className={`select-none ${nodeClassName}`}>
          {/* 节点容器 */}
          {isDraggable ? (
            // 可拖拽节点
            <div
              className={isDropTarget ? 'bg-blue-100 border border-blue-300 rounded' : ''}
              onMouseDown={onDragHandlers ? (e) => onDragHandlers.onMouseDown(e, node.id, node) : undefined}
              onMouseEnter={onDragHandlers ? (e) => onDragHandlers.onMouseEnter(e, node.id) : undefined}
              onMouseLeave={onDragHandlers ? (e) => onDragHandlers.onMouseLeave(e, node.id) : undefined}
            >
              <div
                className={nodeClasses}
                style={indentStyle}
                onClick={() => onNodeSelect?.(node.id)}
                onContextMenu={onNodeContextMenu ? (e) => onNodeContextMenu(e, node) : undefined}
                data-node-id={node.id}
              >
                {renderNodeContent 
                  ? renderNodeContent(node, hasChildren, isExpanded, depth)
                  : defaultRenderNodeContent(node, hasChildren, isExpanded, depth)
                }
              </div>
            </div>
          ) : (
            // 普通节点
            <div
              className={nodeClasses}
              style={indentStyle}
              onClick={() => onNodeSelect?.(node.id)}
              onContextMenu={onNodeContextMenu ? (e) => onNodeContextMenu(e, node) : undefined}
              data-node-id={node.id}
            >
              {renderNodeContent 
                ? renderNodeContent(node, hasChildren, isExpanded, depth)
                : defaultRenderNodeContent(node, hasChildren, isExpanded, depth)
              }
            </div>
          )}

          {/* 子节点 */}
          {hasChildren && isExpanded && (
            <div className={categoryMode === 'department' ? 'ml-1' : 'ml-2'}>
              {renderTreeNodes(node.children || [], depth + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <div className={className}>
      {renderTreeNodes(nodes)}
    </div>
  );
};

/**
 * 树渲染器的简化版本
 * 用于简单的树形结构展示
 */
export interface SimpleTreeRendererProps {
  nodes: TreeNode[];
  onNodeSelect?: (nodeId: string) => void;
  selectedNode?: string | null;
  defaultExpandedNodes?: string[];
  className?: string;
}

export const SimpleTreeRenderer: React.FC<SimpleTreeRendererProps> = ({
  nodes,
  onNodeSelect,
  selectedNode,
  defaultExpandedNodes = [],
  className = ''
}) => {
  const treeState = useTreeState({
    defaultExpandedNodes,
    onNodeSelect
  });

  return (
    <TreeRenderer
      nodes={nodes}
      expandedNodes={treeState.expandedNodes}
      selectedNode={selectedNode || treeState.selectedNode}
      onNodeToggle={treeState.toggleNode}
      onNodeSelect={onNodeSelect || treeState.selectNode}
      className={className}
    />
  );
};

export default TreeRenderer;
