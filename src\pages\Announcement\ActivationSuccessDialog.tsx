import React from 'react';

interface ActivationSuccessDialogProps {
  isOpen: boolean;
  onConfirm: () => void;
}

/**
 * 激活成功弹窗组件
 * UI层：只负责渲染界面，不包含业务逻辑
 */
const ActivationSuccessDialog: React.FC<ActivationSuccessDialogProps> = ({
  isOpen,
  onConfirm
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
      <div className="bg-white border border-gray-300 rounded shadow-lg max-w-sm w-full mx-4 relative overflow-hidden">
        <div className="p-6 text-center">
          {/* 成功图标 */}
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 border border-green-200 mb-4">
            <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          {/* 标题 */}
          <h3 className="text-lg font-semibold text-gray-800 mb-6">
            系统激活成功
          </h3>

          {/* 确认按钮 */}
          <button
            onClick={onConfirm}
            className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 font-medium"
          >
            确认
          </button>
        </div>
      </div>
    </div>
  );
};

export default ActivationSuccessDialog;
