import { useState, useEffect, useCallback } from 'react';
import InspectionLogService, {
  InspectionLogServiceState,
  InspectionLogItem,
  InspectionLogFilter,
  InspectionTaskLogItem,
  InspectionDetailItem
} from '../../services/Inspection/inspectionLogService';

// 重新导出类型，这样UI层可以从Hook导入，而不是直接从Service层导入
export type { InspectionLogItem, InspectionLogFilter, InspectionTaskLogItem, InspectionDetailItem };

/**
 * 巡检结果Hook返回值接口
 */
interface UseInspectionLogReturn {
  // 状态
  isLoading: boolean;
  error?: string;
  logs: InspectionLogItem[];
  taskLogs: InspectionTaskLogItem[]; // 新增：巡检任务日志
  filter: InspectionLogFilter;
  filteredLogs: InspectionLogItem[];
  filteredTaskLogs: InspectionTaskLogItem[]; // 新增：过滤后的任务日志
  isReading: boolean;
  readProgress: number;
  selectedTaskDetails?: InspectionDetailItem[]; // 新增：选中任务的详细信息

  // 分页相关
  currentPage: number;
  pageSize: number;
  totalPages: number;
  paginatedLogs: InspectionLogItem[];
  paginatedTaskLogs: InspectionTaskLogItem[]; // 新增：分页后的任务日志

  // 方法
  readInspectionLogs: () => Promise<InspectionLogItem[]>;
  getInspectionResults: () => Promise<InspectionTaskLogItem[]>; // 新增：获取巡检结果
  setFilter: (filter: Partial<InspectionLogFilter>) => void;
  clearFilter: () => void;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setSelectedTaskDetails: (details: InspectionDetailItem[]) => void; // 新增：设置选中任务详情

  // 辅助方法
  getOperationTypes: () => string[];
  getOperators: () => string[];
  getDepartments: () => string[];
}

/**
 * 巡检结果Hook
 * 提供巡检结果功能和状态管理
 */
export function useInspectionLog(): UseInspectionLogReturn {
  // 获取服务实例
  const service = InspectionLogService.getInstance();

  // 状态
  const [state, setState] = useState<InspectionLogServiceState>(service.getState());

  // 分页状态
  const [currentPage, setCurrentPage] = useState<number>(0); // 0-based index
  const [pageSize, setPageSize] = useState<number>(10); // 默认每页10条

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = (newState: InspectionLogServiceState) => {
      setState(newState);
    };

    // 订阅状态变化事件
    service.on('state-change', handleStateChange);

    // 清理函数
    return () => {
      service.off('state-change', handleStateChange);
    };
  }, [service]);

  // 读取巡检结果
  const readInspectionLogs = useCallback(async () => {
    return await service.readInspectionLogs();
  }, [service]);

  // 获取巡检结果
  const getInspectionResults = useCallback(async () => {
    return await service.getInspectionResults();
  }, [service]);

  // 强制刷新巡检结果（忽略缓存）
  const forceRefreshInspectionResults = useCallback(async () => {
    return await service.forceRefreshInspectionResults();
  }, [service]);

  // 设置选中任务的详细信息
  const setSelectedTaskDetails = useCallback((details: InspectionDetailItem[]) => {
    service.setSelectedTaskDetails(details);
  }, [service]);

  // 设置结果过滤条件
  const setFilter = useCallback((filter: Partial<InspectionLogFilter>) => {
    service.setFilter(filter);
  }, [service]);

  // 清除所有过滤条件
  const clearFilter = useCallback(() => {
    service.clearFilter();
  }, [service]);

  // 获取所有巡检类型
  const getOperationTypes = useCallback(() => {
    return service.getOperationTypes();
  }, [service]);

  // 获取所有巡检人
  const getOperators = useCallback(() => {
    return service.getOperators();
  }, [service]);

  // 获取所有部门
  const getDepartments = useCallback(() => {
    return service.getDepartments();
  }, [service]);

  // 计算分页数据
  const getPaginatedLogs = useCallback(() => {
    // 如果pageSize为-1，表示显示全部
    if (pageSize === -1) {
      return state.filteredLogs;
    }

    const start = currentPage * pageSize;
    const end = start + pageSize;
    return state.filteredLogs.slice(start, end);
  }, [state.filteredLogs, currentPage, pageSize]);

  // 计算任务日志分页数据
  const getPaginatedTaskLogs = useCallback(() => {
    // 如果pageSize为-1，表示显示全部
    if (pageSize === -1) {
      return state.filteredTaskLogs;
    }

    const start = currentPage * pageSize;
    const end = start + pageSize;
    return state.filteredTaskLogs.slice(start, end);
  }, [state.filteredTaskLogs, currentPage, pageSize]);

  // 计算总页数（基于任务日志）
  const getTotalPages = useCallback(() => {
    // 如果pageSize为-1，表示显示全部，总页数为1
    if (pageSize === -1) {
      return 1;
    }

    // 优先使用任务日志的长度，如果没有则使用普通日志
    const totalItems = state.filteredTaskLogs.length > 0 ?
      state.filteredTaskLogs.length :
      state.filteredLogs.length;

    return Math.ceil(totalItems / pageSize);
  }, [state.filteredTaskLogs.length, state.filteredLogs.length, pageSize]);

  // 设置页码
  const handleSetPage = useCallback((page: number) => {
    const totalPages = getTotalPages();
    if (page >= 0 && page < totalPages) {
      setCurrentPage(page);
    }
  }, [getTotalPages]);

  // 设置每页显示条数
  const handleSetPageSize = useCallback((size: number) => {
    setPageSize(size);
    // 重置为第一页
    setCurrentPage(0);
  }, []);

  // 当过滤条件变化时，重置为第一页
  useEffect(() => {
    setCurrentPage(0);
  }, [state.filteredLogs.length]);

  // 计算分页后的日志数据
  const paginatedLogs = getPaginatedLogs();
  const paginatedTaskLogs = getPaginatedTaskLogs();
  // 计算总页数
  const totalPages = getTotalPages();

  return {
    // 状态
    isLoading: state.isLoading,
    error: state.error,
    logs: state.logs,
    taskLogs: state.taskLogs,
    filter: state.filter,
    filteredLogs: state.filteredLogs,
    filteredTaskLogs: state.filteredTaskLogs,
    isReading: state.isReading,
    readProgress: state.readProgress,
    selectedTaskDetails: state.selectedTaskDetails,

    // 分页相关
    currentPage,
    pageSize,
    totalPages,
    paginatedLogs,
    paginatedTaskLogs,

    // 方法
    readInspectionLogs,
    getInspectionResults,
    forceRefreshInspectionResults,
    setFilter,
    clearFilter,
    setPage: handleSetPage,
    setPageSize: handleSetPageSize,
    setSelectedTaskDetails,

    // 辅助方法
    getOperationTypes,
    getOperators,
    getDepartments
  };
}

export default useInspectionLog;
