import { useState, useCallback, useEffect, useMemo } from 'react';
import { ExtFieldDefinition, ExtFieldEditMode } from '../../types/inventory';
import TaskManager from '../../utils/taskManager';
import ExtFieldService from '../../services/Inventory/extFieldService';
import ServiceRegistry from '../../services/serviceRegistry';
import InventoryService from '../../services/Inventory/inventoryService';
import {
  buildAddExtFieldParams,
  buildAddExtFieldOptionsParams,
  buildUpdateExtFieldParams,
  buildUpdateExtFieldOptionsParams,
  buildDeleteExtFieldParams,
  ExtFieldDefinitionInput
} from '../../utils/fieldUtils';

// 二级分类接口
interface SubCategory {
  id: number;
  name: string;
  parent_id: number;
  parent_name: string;
}

// 层级选项接口
export interface HierarchicalOption {
  id: string | number;
  value: string;
  label: string;
  isParent?: boolean;
  parentId?: string | number;
  children?: HierarchicalOption[];
}

/**
 * 列设置Hook
 * 用于管理扩展字段列的添加和删除
 */
export default function useColumnSettings() {
  // 状态
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [subCategories, setSubCategories] = useState<SubCategory[]>([]);
  const [extFields, setExtFields] = useState<ExtFieldDefinition[]>([]);
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [fieldName, setFieldName] = useState<string>('');
  const [isRequired, setIsRequired] = useState<boolean>(false);

  // 删除扩展字段相关状态
  const [deleteSubCategory, setDeleteSubCategory] = useState<string>('');
  const [deleteFieldName, setDeleteFieldName] = useState<string>('');
  const [deleteExtFields, setDeleteExtFields] = useState<ExtFieldDefinition[]>([]);

  // 修改扩展字段相关状态
  const [editSubCategory, setEditSubCategory] = useState<string>('');
  const [editFieldName, setEditFieldName] = useState<string>('');
  const [editExtFields, setEditExtFields] = useState<ExtFieldDefinition[]>([]);
  const [editFieldData, setEditFieldData] = useState<{
    isEditable: boolean;
    isSelectOnly: boolean;
    isRequired: boolean;
    options: Array<{ code: string; value: string }>;
  }>({
    isEditable: true,
    isSelectOnly: false,
    isRequired: false,
    options: []
  });

  // 获取TaskManager实例
  const taskManager = TaskManager.getInstance();
  // 获取ExtFieldService实例
  const extFieldService = ExtFieldService.getInstance();

  // 通用的任务提交和等待方法
  const submitAndWaitTask = useCallback(async (action: string, actionParams: any) => {
    // 提交任务
    const taskId = await taskManager.submitTask('AccountTableDll', 'DbFun', {
      action,
      action_params: actionParams
    });

    // 等待任务结果
    return await new Promise<any>((resolve, reject) => {
      const handleTaskUpdate = (taskInfo: any) => {
        if (taskInfo.status === 'completed') {
          taskManager.offTaskUpdate(taskId, handleTaskUpdate);

          // 检查结果中的success字段
          let resultData = taskInfo.result;

          // 如果结果是字符串，尝试解析为JSON
          if (typeof resultData === 'string') {
            try {
              resultData = JSON.parse(resultData);
            } catch (parseError) {
              console.warn(`解析任务结果失败: ${action}, taskId: ${taskId}`, parseError);
            }
          }

          // 检查业务逻辑是否成功
          if (resultData && typeof resultData === 'object' && resultData.success === false) {
            const errorMessage = resultData.error_message || resultData.message || '操作失败';
            console.error(`任务业务逻辑失败: ${action}, taskId: ${taskId}, 错误: ${errorMessage}`);
            reject(new Error(errorMessage));
            return;
          }

          resolve(taskInfo.result);
        } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
          taskManager.offTaskUpdate(taskId, handleTaskUpdate);
          reject(new Error(taskInfo.error || '任务执行失败'));
        }
      };
      taskManager.onTaskUpdate(taskId, handleTaskUpdate);
    });
  }, [taskManager]);

  // 加载二级分类
  const loadSubCategories = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('获取所有二级分类');

      // 使用通用的任务提交和等待方法
      const result = await submitAndWaitTask("get_sub_category", {});

      console.log('获取二级分类结果:', result);

      if (result && result.success && Array.isArray(result.data)) {
        // 确保二级分类数据包含必要的字段
        const validSubCategories = result.data.filter((category: any) =>
          category &&
          category.id !== undefined &&
          category.sub_category_name !== undefined &&
          category.parent_category_id !== undefined &&
          category.parent_category_name !== undefined
        ).map((category: any) => ({
          id: category.id,
          name: category.sub_category_name,
          parent_id: category.parent_category_id,
          parent_name: category.parent_category_name
        }));

        console.log('处理后的二级分类数据:', validSubCategories);
        setSubCategories(validSubCategories);
      } else {
        setError('获取二级分类失败');
      }
    } catch (err) {
      console.error('获取二级分类失败:', err);
      setError(err instanceof Error ? err.message : '获取二级分类失败');
    } finally {
      setIsLoading(false);
    }
  }, [submitAndWaitTask]);

  // 通用的加载扩展字段方法
  const loadCategoryExtFields = useCallback(async (subCategoryName: string, setFields: (fields: ExtFieldDefinition[]) => void) => {
    try {
      setIsLoading(true);
      setError(null);
      setFields([]);

      // 查找二级分类对应的一级分类
      console.log('查找二级分类:', subCategoryName, '当前二级分类列表:', subCategories);
      const subCategory = subCategories.find(cat => cat.name === subCategoryName);
      if (!subCategory) {
        setError(`未找到对应的二级分类: ${subCategoryName}`);
        return;
      }
      console.log('找到二级分类:', subCategory);

      // 获取一级分类名称
      const parentCategoryName = subCategory.parent_name;

      // 使用ExtFieldService获取扩展字段
      const fields = await extFieldService.getCategoryExtFields(parentCategoryName, subCategoryName);
      console.log('获取扩展字段结果:', fields);

      setFields(fields);
    } catch (err) {
      console.error('获取扩展字段失败:', err);
      setError(err instanceof Error ? err.message : '获取扩展字段失败');
    } finally {
      setIsLoading(false);
    }
  }, [extFieldService, subCategories]);

  // 加载扩展字段
  const loadExtFields = useCallback((subCategoryName: string) => {
    return loadCategoryExtFields(subCategoryName, setExtFields);
  }, [loadCategoryExtFields]);

  // 加载删除扩展字段
  const loadDeleteExtFields = useCallback((subCategoryName: string) => {
    return loadCategoryExtFields(subCategoryName, setDeleteExtFields);
  }, [loadCategoryExtFields]);

  // 加载修改扩展字段
  const loadEditExtFields = useCallback((subCategoryName: string) => {
    return loadCategoryExtFields(subCategoryName, setEditExtFields);
  }, [loadCategoryExtFields]);

  // 通用的刷新扩展字段方法（使用前端缓存）
  const refreshExtFields = useCallback(async (subCategoryName: string) => {
    console.log('使用前端缓存刷新扩展字段:', subCategoryName);

    // 获取InventoryService实例
    const inventoryService = ServiceRegistry.getInstance()
      .getOrCreate('inventoryService', () => InventoryService.getInstance());

    // 获取当前选中的分类信息
    const currentSelectedCategory = inventoryService.getCurrentSelectedCategory();

    // 获取当前视图状态
    const currentCategory = inventoryService.getState().currentCategory;

    // 获取当前树状图选中的节点信息
    const treeSelectedNode = inventoryService.getState().treeSelectedNode;

    console.log('扩展字段操作后，当前选中的分类信息:', currentSelectedCategory);
    console.log('当前视图状态:', currentCategory);
    console.log('当前树状图选中的节点:', treeSelectedNode);

    // 重新加载当前分类的扩展字段（从缓存中获取）
    await loadExtFields(subCategoryName);
    await loadDeleteExtFields(subCategoryName);

    // 根据当前树状图选中的节点来决定如何刷新表格
    if (treeSelectedNode) {
      console.log('根据当前树状图选中的节点刷新表格:', treeSelectedNode);

      // 如果当前选中的是设备分类树的节点
      if (treeSelectedNode.type === 'device') {
        // 如果是二级分类节点
        if (treeSelectedNode.level === 2) {
          console.log('当前选中的是二级分类节点，使用二级分类筛选');
          inventoryService.generateTableFields({
            parentCategory: treeSelectedNode.parentName,
            subCategory: treeSelectedNode.name
          });
        }
        // 如果是一级分类节点
        else if (treeSelectedNode.level === 1) {
          console.log('当前选中的是一级分类节点，使用一级分类筛选');
          inventoryService.generateTableFields({
            parentCategory: treeSelectedNode.name
          });
        }
        // 如果是根节点
        else {
          console.log('当前选中的是公司名称，显示总表');
          inventoryService.generateTableFields();
        }
      }
      // 如果当前选中的是部门树的节点，保持当前视图
      else if (treeSelectedNode.type === 'department') {
        console.log('当前选中的是部门树节点，保持当前视图');
        if (currentSelectedCategory) {
          inventoryService.generateTableFields(currentSelectedCategory);
        } else {
          inventoryService.generateTableFields();
        }
      }
    }
    // 如果没有选中的节点，回退到基于当前视图状态的逻辑
    else {
      if (currentCategory === 'all' || !currentCategory) {
        console.log('在总表视图下操作，重置为总表视图');
        // 重新生成表格字段，不传递分类信息，表示使用总表
        inventoryService.generateTableFields();
      } else {
        // 使用当前选中的分类信息重新生成表格字段
        console.log('在分类视图下操作，使用当前选中的分类重新生成表格字段');
        inventoryService.generateTableFields(currentSelectedCategory);
      }
    }

    // 强制触发状态更新，确保UI更新
    inventoryService.forceUpdate();
  }, [loadExtFields, loadDeleteExtFields]);

  // 添加扩展字段
  const addExtendedField = useCallback(async (
    subCategoryName: string,
    fieldName: string,
    isRequired: boolean,
    editMode: ExtFieldEditMode = ExtFieldEditMode.Editable,
    options?: Array<{ code: string; value: string }>
  ) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      // 查找二级分类对应的一级分类
      console.log('查找二级分类:', subCategoryName);
      const subCategory = subCategories.find(cat => cat.name === subCategoryName);
      if (!subCategory) {
        setError(`未找到对应的二级分类: ${subCategoryName}`);
        return false;
      }

      // 获取一级分类名称
      const parentCategoryName = subCategory.parent_name;

      // 确定字段类型
      let fieldType = "text"; // 默认为文本类型

      // 如果是不可编辑模式，则使用select类型
      if (editMode === ExtFieldEditMode.SelectOnly) {
        fieldType = "select";
      }

      // 使用统一的工具函数构建请求参数
      const fieldDefinition: ExtFieldDefinitionInput = {
        name: fieldName,
        type: fieldType,
        required: isRequired,
        editMode: editMode,
        options: options
      };

      const params = buildAddExtFieldParams(subCategoryName, fieldDefinition);

      console.log('添加扩展字段:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await submitAndWaitTask(params.action, params.action_params);

      console.log('添加扩展字段结果:', result);

      if (result && result.success) {
        // 如果有选项数据并且是不可编辑模式，则再调用添加选项的API
        if (options && options.length > 0 && editMode === ExtFieldEditMode.SelectOnly) {
          console.log('添加扩展字段选项:', fieldName);

          // 使用统一的工具函数构建选项请求参数
          const optionsParams = buildAddExtFieldOptionsParams(subCategoryName, fieldName, options);

          // 调用添加选项API
          const optionsResult = await submitAndWaitTask(optionsParams.action, optionsParams.action_params);

          console.log('添加扩展字段选项结果:', optionsResult);

          if (!optionsResult || !optionsResult.success) {
            console.error('添加扩展字段选项失败:', optionsResult?.message);
            // 尽管选项添加失败，我们仍然继续处理，因为字段已经成功添加
          }
        }
        
        setSuccess(`成功添加扩展字段"${fieldName}"`);

        // 直接在前端缓存中添加扩展字段
        const cacheUpdateSuccess = extFieldService.addExtFieldToCache(
          parentCategoryName,
          subCategoryName,
          fieldName,
          isRequired,
          editMode,
          fieldType,
          options
        );

        if (cacheUpdateSuccess) {
          console.log('前端缓存更新成功');

          // 使用通用的刷新方法（从缓存中获取）
          await refreshExtFields(subCategoryName);
        } else {
          console.error('前端缓存更新失败，回退到传统刷新方式');
          // 如果前端缓存更新失败，清除缓存并重新获取所有扩展字段
          extFieldService.clearExtFieldsCache();
          await extFieldService.getAllExtFields(true);
          await refreshExtFields(subCategoryName);
        }

        return true; // 返回成功标志，方便调用者判断
      } else {
        setError(result?.message || '添加扩展字段失败');
        return false;
      }
    } catch (err) {
      console.error('添加扩展字段失败:', err);
      setError(err instanceof Error ? err.message : '添加扩展字段失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [submitAndWaitTask, refreshExtFields, extFieldService, subCategories]);

  // 修改扩展字段
  const updateExtendedField = useCallback(async (
    subCategoryName: string,
    fieldName: string,
    isRequired: boolean,
    editMode: ExtFieldEditMode = ExtFieldEditMode.Editable,
    options?: Array<{ code: string; value: string }>,
    newFieldName?: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      // 查找二级分类对应的一级分类
      console.log('查找二级分类:', subCategoryName);
      const subCategory = subCategories.find(cat => cat.name === subCategoryName);
      if (!subCategory) {
        setError(`未找到对应的二级分类: ${subCategoryName}`);
        return false;
      }

      // 获取一级分类名称
      const parentCategoryName = subCategory.parent_name;

      // 确定字段类型
      let fieldType = "text"; // 默认为文本类型

      // 如果是不可编辑模式，则使用select类型
      if (editMode === ExtFieldEditMode.SelectOnly) {
        fieldType = "select";
      }

      // 使用统一的工具函数构建请求参数
      const fieldDefinition: ExtFieldDefinitionInput = {
        name: fieldName,
        type: fieldType,
        required: isRequired,
        editMode: editMode,
        options: options
      };

      const params = buildUpdateExtFieldParams(subCategoryName, fieldDefinition, newFieldName);

      console.log('修改扩展字段:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await submitAndWaitTask(params.action, params.action_params);

      console.log('修改扩展字段结果:', result);

      if (result && result.success) {
        // 确定最终使用的字段名（如果修改了字段名，则使用新字段名）
        const finalFieldName = newFieldName && newFieldName !== fieldName ? newFieldName : fieldName;
        
        // 如果有选项数据并且是不可编辑模式，则再调用更新选项的API
        if (options && options.length > 0 && editMode === ExtFieldEditMode.SelectOnly) {
          console.log('更新扩展字段选项:', finalFieldName);

          // 使用统一的工具函数构建选项更新请求参数
          const optionsParams = buildUpdateExtFieldOptionsParams(subCategoryName, finalFieldName, options);

          // 调用更新选项API
          const optionsResult = await submitAndWaitTask(optionsParams.action, optionsParams.action_params);

          console.log('更新扩展字段选项结果:', optionsResult);

          if (!optionsResult || !optionsResult.success) {
            console.error('更新扩展字段选项失败:', optionsResult?.message);
            // 尽管选项更新失败，我们仍然继续处理，因为字段已经成功更新
          }
        }
        
        setSuccess(`成功修改扩展字段"${fieldName}${newFieldName ? ' 为 ' + newFieldName : ''}"`);

        // 清除缓存并重新获取所有扩展字段
        extFieldService.clearExtFieldsCache();
        await extFieldService.getAllExtFields(true);
        await refreshExtFields(subCategoryName);

        return true; // 返回成功标志，方便调用者判断
      } else {
        setError(result?.message || '修改扩展字段失败');
        return false;
      }
    } catch (err) {
      console.error('修改扩展字段失败:', err);
      setError(err instanceof Error ? err.message : '修改扩展字段失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [submitAndWaitTask, refreshExtFields, extFieldService, subCategories]);

  // 删除扩展字段
  const deleteExtendedField = useCallback(async (subCategoryName: string, fieldName: string) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      // 查找二级分类对应的一级分类
      console.log('查找二级分类:', subCategoryName);
      const subCategory = subCategories.find(cat => cat.name === subCategoryName);
      if (!subCategory) {
        setError(`未找到对应的二级分类: ${subCategoryName}`);
        return false;
      }

      // 获取一级分类名称
      const parentCategoryName = subCategory.parent_name;

      // 使用统一的工具函数构建请求参数
      const params = buildDeleteExtFieldParams(subCategoryName, fieldName);

      console.log('删除扩展字段:', JSON.stringify(params));

      // 提交任务并等待结果
      const result = await submitAndWaitTask(params.action, params.action_params);

      console.log('删除扩展字段结果:', result);

      if (result && result.success) {
        setSuccess(`成功删除扩展字段"${fieldName}"`);

        // 直接在前端缓存中删除扩展字段
        const cacheUpdateSuccess = extFieldService.deleteExtFieldFromCache(
          parentCategoryName,
          subCategoryName,
          fieldName
        );

        if (cacheUpdateSuccess) {
          console.log('前端缓存更新成功');

          // 使用通用的刷新方法（从缓存中获取）
          await refreshExtFields(subCategoryName);
        } else {
          console.error('前端缓存更新失败，回退到传统刷新方式');
          // 如果前端缓存更新失败，清除缓存并重新获取所有扩展字段
          extFieldService.clearExtFieldsCache();
          await extFieldService.getAllExtFields(true);
          await refreshExtFields(subCategoryName);
        }

        return true; // 返回成功标志，方便调用者判断
      } else {
        // 无论什么错误，都显示友好的错误消息
        setError('该字段正在被使用中无法删除');
        return false;
      }
    } catch (err) {
      console.error('删除扩展字段失败:', err);
      // 无论什么错误，都显示友好的错误消息
      setError('该字段正在被使用中无法删除');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [submitAndWaitTask, refreshExtFields, extFieldService, subCategories]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 清除成功消息
  const clearSuccess = useCallback(() => {
    setSuccess(null);
  }, []);

  // 将二级分类按照一级分类进行分组，形成层级结构
  const hierarchicalCategories = useMemo(() => {
    // 创建一级分类映射
    const parentCategoryMap = new Map<string, HierarchicalOption>();

    // 遍历所有二级分类，按一级分类分组
    subCategories.forEach(subCategory => {
      const parentName = subCategory.parent_name;
      const parentId = subCategory.parent_id;

      // 如果一级分类不存在，创建一个新的一级分类
      if (!parentCategoryMap.has(parentName)) {
        parentCategoryMap.set(parentName, {
          id: `parent-${parentId}`,
          value: parentName,
          label: parentName,
          isParent: true,
          children: []
        });
      }

      // 将二级分类添加到对应的一级分类下
      const parentCategory = parentCategoryMap.get(parentName);
      if (parentCategory && parentCategory.children) {
        parentCategory.children.push({
          id: subCategory.id,
          value: subCategory.name,
          label: subCategory.name,
          parentId: parentId
        });
      }
    });

    // 将Map转换为数组
    return Array.from(parentCategoryMap.values());
  }, [subCategories]);

  return {
    isLoading,
    error,
    success,
    subCategories,
    // 移除冗余的 allSubCategories
    hierarchicalCategories, // 添加层级分类
    extFields,
    selectedSubCategory,
    setSelectedSubCategory,
    fieldName,
    setFieldName,
    isRequired,
    setIsRequired,
    // 删除扩展字段相关
    deleteSubCategory,
    setDeleteSubCategory,
    deleteFieldName,
    setDeleteFieldName,
    deleteExtFields,
    loadDeleteExtFields,
    // 修改扩展字段相关
    editSubCategory,
    setEditSubCategory,
    editFieldName,
    setEditFieldName,
    editExtFields,
    loadEditExtFields,
    editFieldData,
    setEditFieldData,
    // 方法
    addExtendedField,
    updateExtendedField,
    deleteExtendedField,
    loadSubCategories,
    loadExtFields,
    clearError,
    clearSuccess
  };
}
