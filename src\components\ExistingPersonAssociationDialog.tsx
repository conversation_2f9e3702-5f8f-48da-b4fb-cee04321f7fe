import React from 'react';
import DialogBase from './ui/DialogBase';

interface ExistingPersonAssociationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  personName: string;
  alias?: string;
  departmentName?: string;
}

/**
 * 现有人员关联提示对话框
 * 当添加的人员已存在于系统中时显示的专业提示
 */
const ExistingPersonAssociationDialog: React.FC<ExistingPersonAssociationDialogProps> = ({
  isOpen,
  onClose,
  personName,
  alias,
  departmentName
}) => {
  // 构建人员显示名称
  const personDisplayName = personName + (alias ? ` (${alias})` : '');

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={onClose}
      width="100%"
      maxWidth="32rem"
      closeOnOverlayClick={false}
      autoFocus={true}
      restoreFocus={true}
      animation="fade"
    >
      <div className="p-6">
        {/* 标题 */}
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            人员关联成功
          </h3>
        </div>

        {/* 内容 */}
        <div className="mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-blue-800">
                  系统检测到现有人员记录
                </h4>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    人员 <span className="font-semibold">{personDisplayName}</span> 已存在于系统中，
                    {departmentName && (
                      <>已成功关联到 <span className="font-semibold">{departmentName}</span> 部门。</>
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-2">
                <span className="font-medium">个人信息保持不变：</span>
                系统将沿用该人员的现有个人信息记录
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-2">
                <span className="font-medium">部门关联已建立：</span>
                该人员现在可以在当前部门下进行相关操作
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-5 h-5 text-amber-500 mt-0.5">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-2">
                <span className="font-medium">如需修改个人信息：</span>
                请通过更新人员信息功能，手动更新相关信息
              </div>
            </div>
          </div>
        </div>

        {/* 按钮 */}
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            我知道了
          </button>
        </div>
      </div>
    </DialogBase>
  );
};

export default ExistingPersonAssociationDialog;
