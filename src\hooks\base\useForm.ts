import { useState, useCallback, useEffect } from 'react';

// 表单字段验证器类型
export type Validator<T> = (value: T) => string | undefined;

// 表单字段配置接口
export interface FormFieldConfig<T> {
  initialValue: T;
  validators?: Validator<T>[];
  required?: boolean;
  transform?: (value: any) => T;
}

// 表单字段状态接口
export interface FormFieldState<T> {
  value: T;
  error?: string;
  touched: boolean;
  dirty: boolean;
}

// 表单状态接口
export type FormState<T> = {
  [K in keyof T]: FormFieldState<T[K]>;
};

// 表单配置接口
export type FormConfig<T> = {
  [K in keyof T]: FormFieldConfig<T[K]>;
};

// 表单钩子返回值接口
export interface UseFormReturn<T> {
  // 表单状态
  formState: FormState<T>;
  values: T;
  errors: Partial<Record<keyof T, string>>;
  isValid: boolean;
  isDirty: boolean;
  
  // 表单方法
  setValue: <K extends keyof T>(field: K, value: T[K]) => void;
  setValues: (values: Partial<T>) => void;
  setError: <K extends keyof T>(field: K, error: string) => void;
  clearError: <K extends keyof T>(field: K) => void;
  setTouched: <K extends keyof T>(field: K, touched?: boolean) => void;
  reset: () => void;
  validate: () => boolean;
  handleChange: <K extends keyof T>(field: K) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  handleBlur: <K extends keyof T>(field: K) => () => void;
  getFieldProps: <K extends keyof T>(field: K) => {
    name: string;
    value: T[K];
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    onBlur: () => void;
    error?: string;
  };
}

/**
 * 表单钩子
 * 提供表单状态管理、验证和事件处理
 * @param config 表单配置
 * @param options 钩子选项
 */
export function useForm<T extends Record<string, any>>(
  config: FormConfig<T>,
  options: {
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    validateOnMount?: boolean;
    onSubmit?: (values: T) => void | Promise<void>;
  } = {}
): UseFormReturn<T> {
  const {
    validateOnChange = false,
    validateOnBlur = true,
    validateOnMount = false,
    onSubmit
  } = options;

  // 初始化表单状态
  const initialFormState = Object.entries(config).reduce((acc, [key, fieldConfig]) => {
    acc[key as keyof T] = {
      value: fieldConfig.initialValue,
      touched: false,
      dirty: false
    };
    return acc;
  }, {} as FormState<T>);

  // 表单状态
  const [formState, setFormState] = useState<FormState<T>>(initialFormState);

  // 验证单个字段
  const validateField = useCallback(<K extends keyof T>(field: K, value: T[K]): string | undefined => {
    const fieldConfig = config[field];
    
    // 必填字段验证
    if (fieldConfig.required) {
      if (value === undefined || value === null || value === '') {
        return '此字段为必填项';
      }
    }
    
    // 自定义验证器
    if (fieldConfig.validators) {
      for (const validator of fieldConfig.validators) {
        const error = validator(value);
        if (error) {
          return error;
        }
      }
    }
    
    return undefined;
  }, [config]);

  // 验证所有字段
  const validate = useCallback((): boolean => {
    let isValid = true;
    const newFormState = { ...formState };
    
    Object.keys(config).forEach((key) => {
      const field = key as keyof T;
      const value = formState[field].value;
      const error = validateField(field, value);
      
      newFormState[field] = {
        ...newFormState[field],
        error,
        touched: true
      };
      
      if (error) {
        isValid = false;
      }
    });
    
    setFormState(newFormState);
    return isValid;
  }, [config, formState, validateField]);

  // 初始验证
  useEffect(() => {
    if (validateOnMount) {
      validate();
    }
  }, [validateOnMount, validate]);

  // 设置字段值
  const setValue = useCallback(<K extends keyof T>(field: K, value: T[K]) => {
    setFormState((prevState) => {
      // 应用转换函数（如果有）
      const fieldConfig = config[field];
      const transformedValue = fieldConfig.transform ? fieldConfig.transform(value) : value;
      
      // 验证新值（如果需要）
      const error = validateOnChange ? validateField(field, transformedValue) : prevState[field].error;
      
      return {
        ...prevState,
        [field]: {
          value: transformedValue,
          error,
          touched: prevState[field].touched,
          dirty: true
        }
      };
    });
  }, [config, validateField, validateOnChange]);

  // 设置多个字段值
  const setValues = useCallback((values: Partial<T>) => {
    setFormState((prevState) => {
      const newState = { ...prevState };
      
      Object.entries(values).forEach(([key, value]) => {
        const field = key as keyof T;
        const fieldConfig = config[field];
        
        if (fieldConfig) {
          const transformedValue = fieldConfig.transform ? fieldConfig.transform(value) : value;
          const error = validateOnChange ? validateField(field, transformedValue as T[keyof T]) : prevState[field].error;
          
          newState[field] = {
            value: transformedValue as T[keyof T],
            error,
            touched: prevState[field].touched,
            dirty: true
          };
        }
      });
      
      return newState;
    });
  }, [config, validateField, validateOnChange]);

  // 设置字段错误
  const setError = useCallback(<K extends keyof T>(field: K, error: string) => {
    setFormState((prevState) => ({
      ...prevState,
      [field]: {
        ...prevState[field],
        error
      }
    }));
  }, []);

  // 清除字段错误
  const clearError = useCallback(<K extends keyof T>(field: K) => {
    setFormState((prevState) => ({
      ...prevState,
      [field]: {
        ...prevState[field],
        error: undefined
      }
    }));
  }, []);

  // 设置字段触摸状态
  const setTouched = useCallback(<K extends keyof T>(field: K, touched: boolean = true) => {
    setFormState((prevState) => {
      const newState = {
        ...prevState,
        [field]: {
          ...prevState[field],
          touched
        }
      };
      
      // 如果需要在失焦时验证，且字段被标记为已触摸
      if (validateOnBlur && touched) {
        const error = validateField(field, prevState[field].value);
        newState[field].error = error;
      }
      
      return newState;
    });
  }, [validateField, validateOnBlur]);

  // 重置表单
  const reset = useCallback(() => {
    const resetState = Object.entries(config).reduce((acc, [key, fieldConfig]) => {
      acc[key as keyof T] = {
        value: fieldConfig.initialValue,
        touched: false,
        dirty: false,
        error: undefined
      };
      return acc;
    }, {} as FormState<T>);
    
    setFormState(resetState);
  }, [config]);

  // 处理输入变化
  const handleChange = useCallback(<K extends keyof T>(field: K) => 
    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const value = e.target.type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : e.target.value;
      
      setValue(field, value as unknown as T[K]);
    }, [setValue]);

  // 处理失焦事件
  const handleBlur = useCallback(<K extends keyof T>(field: K) => 
    () => {
      setTouched(field, true);
    }, [setTouched]);

  // 获取字段属性
  const getFieldProps = useCallback(<K extends keyof T>(field: K) => ({
    name: String(field),
    value: formState[field].value,
    onChange: handleChange(field),
    onBlur: handleBlur(field),
    error: formState[field].error
  }), [formState, handleChange, handleBlur]);

  // 提取表单值
  const values = Object.entries(formState).reduce((acc, [key, fieldState]) => {
    acc[key as keyof T] = fieldState.value;
    return acc;
  }, {} as T);

  // 提取表单错误
  const errors = Object.entries(formState).reduce((acc, [key, fieldState]) => {
    if (fieldState.error) {
      acc[key as keyof T] = fieldState.error;
    }
    return acc;
  }, {} as Partial<Record<keyof T, string>>);

  // 表单是否有效
  const isValid = Object.values(formState).every(field => !field.error);

  // 表单是否已修改
  const isDirty = Object.values(formState).some(field => field.dirty);

  return {
    formState,
    values,
    errors,
    isValid,
    isDirty,
    setValue,
    setValues,
    setError,
    clearError,
    setTouched,
    reset,
    validate,
    handleChange,
    handleBlur,
    getFieldProps
  };
}
