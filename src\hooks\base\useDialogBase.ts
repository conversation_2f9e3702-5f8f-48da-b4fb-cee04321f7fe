import { useRef, useCallback } from 'react';
import { useDialogFocus } from './useDialogFocus';

interface UseDialogBaseOptions {
  // 对话框是否打开
  isOpen: boolean;
  // 关闭对话框的回调
  onClose: () => void;
  // 点击遮罩层是否关闭对话框
  closeOnOverlayClick?: boolean;
  // 是否自动聚焦到第一个元素
  autoFocus?: boolean;
  // 关闭时是否恢复焦点
  restoreFocus?: boolean;
  // 对话框动画
  animation?: 'fade' | 'slide' | 'scale' | 'none';
  // ESC键处理器的优先级
  escPriority?: number;
  // Tab键处理器的优先级
  tabPriority?: number;
}

interface UseDialogBaseResult {
  // 对话框容器引用
  dialogRef: React.RefObject<HTMLDivElement>;
  // 处理遮罩层点击
  handleOverlayClick: (e: React.MouseEvent) => void;
  // 获取动画类名
  getAnimationClass: () => string;
  // 是否应该渲染对话框
  shouldRender: boolean;
}

/**
 * 对话框基础Hook
 * 提供对话框的基础逻辑，包括焦点管理、遮罩层点击处理、动画类名等
 *
 * 架构说明：
 * 这个Hook负责处理所有对话框的逻辑，而UI组件只负责渲染
 * 遵循"UI层只负责渲染，Hook层只负责处理逻辑"的架构原则
 */
export function useDialogBase({
  isOpen,
  onClose,
  closeOnOverlayClick = true,
  autoFocus = true,
  restoreFocus = true,
  animation = 'fade',
  escPriority = 100,
  tabPriority = 100
}: UseDialogBaseOptions): UseDialogBaseResult {
  // 使用对话框焦点管理
  const { dialogRef } = useDialogFocus({
    isOpen,
    onClose,
    autoFocus,
    restoreFocus,
    escPriority,
    tabPriority
  });

  // 处理遮罩层点击
  const handleOverlayClick = useCallback((e: React.MouseEvent) => {
    // 确保点击的是遮罩层而不是对话框内容
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  }, [closeOnOverlayClick, onClose]);

  // 获取动画类名
  const getAnimationClass = useCallback(() => {
    switch (animation) {
      case 'fade':
        return 'animate-fadeIn';
      case 'slide':
        return 'animate-slideIn';
      case 'scale':
        return 'animate-scaleIn';
      case 'none':
      default:
        return '';
    }
  }, [animation]);

  return {
    dialogRef,
    handleOverlayClick,
    getAnimationClass,
    shouldRender: isOpen
  };
}
