@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础设置 */
@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, "Microsoft YaHei", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    @apply text-gray-800 text-sm leading-relaxed;
  }

  /* 标题样式 */
  h1 { @apply text-2xl font-semibold mb-4 text-gray-900; }
  h2 { @apply text-xl font-medium mb-3 text-gray-800; }
  h3 { @apply text-lg font-medium mb-2 text-gray-800; }

  /* 链接样式 */
  a { @apply text-blue-600 hover:text-blue-800 transition-colors; }
}

/* 窗口布局 */
html.maximize {
  @apply overflow-hidden;
}

html.maximize body {
  @apply overflow-hidden;
}

html.maximize #root {
  @apply fixed inset-0;
}

html.maximize main {
  @apply overflow-auto;
}

html, body {
  @apply overflow-hidden bg-gray-50;
}

#root {
  @apply h-screen flex flex-col;
}

/* 主内容区域 */
main {
  @apply flex-1 p-4 md:p-6 overflow-auto;
  @apply bg-gray-50;
}

/* 窗口控制按钮 */
.window-control-btn {
  @apply p-1.5 rounded-md transition-all duration-200;
  @apply hover:bg-gray-100;
  @apply active:scale-95;
}

.window-control-btn.close {
  @apply hover:bg-red-50;
}

.window-control-btn.close:hover svg {
  @apply text-red-600;
}

/* 窗口拖拽区域 */
.-webkit-app-region-drag {
  -webkit-app-region: drag;
  @apply cursor-move select-none;
}

.-webkit-app-region-no-drag {
  -webkit-app-region: no-drag;
  @apply cursor-default;
}

/* 拖拽手柄增强样式 */
.resize-handle-enhanced {
  position: relative;
  overflow: visible;
}

.resize-handle-enhanced::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1.5px; /* 缩短为一半宽度 */
  height: 10px; /* 缩短为一半高度 */
  background: repeating-linear-gradient(
    to bottom,
    rgba(156, 163, 175, 0.6) 0px,
    rgba(156, 163, 175, 0.6) 1px, /* 调整渐变间距 */
    transparent 1px,
    transparent 2px
  );
  border-radius: 0.75px; /* 调整圆角 */
  transition: all 0.2s ease;
  opacity: 0.7;
}

.resize-handle-enhanced:hover::before {
  background: repeating-linear-gradient(
    to bottom,
    rgba(59, 130, 246, 0.8) 0px,
    rgba(59, 130, 246, 0.8) 1px, /* 调整渐变间距 */
    transparent 1px,
    transparent 2px
  );
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.resize-handle-enhanced.dragging::before {
  background: repeating-linear-gradient(
    to bottom,
    rgba(59, 130, 246, 1) 0px,
    rgba(59, 130, 246, 1) 1px, /* 调整渐变间距 */
    transparent 1px,
    transparent 2px
  );
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 0 6px rgba(59, 130, 246, 0.5); /* 稍微减小阴影 */
}

/* 卡片组件 */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200/80;
  @apply p-4 md:p-5 transition-all duration-200;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  @apply w-1.5;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300/70 rounded-full;
  @apply hover:bg-gray-400/70 transition-colors;
}

/* 移除重复的滚动条隐藏样式 - 已在customScrollbar.css中统一定义 */

/* 主内容区域滚动条 */
main::-webkit-scrollbar {
  @apply w-1.5;
}

main::-webkit-scrollbar-track {
  @apply bg-transparent;
}

main::-webkit-scrollbar-thumb {
  @apply bg-gray-300/60 rounded-full;
  @apply hover:bg-gray-400/60 transition-colors;
}

/* 公告对话框滚动条样式 */
.announcement-content::-webkit-scrollbar {
  @apply w-2;
}

.announcement-content::-webkit-scrollbar-track {
  @apply bg-white/20 rounded-full;
}

.announcement-content::-webkit-scrollbar-thumb {
  @apply bg-gray-400/50 rounded-full;
  @apply hover:bg-gray-500/60 transition-all duration-200;
}

.announcement-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gradient-to-b from-blue-400/60 to-purple-400/60;
}

/* 动画过渡 */
.fade-enter {
  @apply opacity-0;
}

.fade-enter-active {
  @apply opacity-100 transition-opacity duration-200;
}

.fade-exit {
  @apply opacity-100;
}

.fade-exit-active {
  @apply opacity-0 transition-opacity duration-200;
}

/* 表格容器样式 - 与adaptive-table-container保持一致 */
.table-container {
  @apply overflow-hidden rounded-lg border border-gray-200/80 shadow-sm;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  position: relative;
}

/* 通用表格样式 - 移除与customScrollbar.css冲突的定义 */
.data-table {
  @apply w-full;
  min-width: 800px; /* 统一最小宽度 */
}

/* 固定列样式 */
.table-fixed-column-right {
  position: sticky !important;
  right: 0 !important;
  z-index: 20 !important;
  background-color: white !important;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
}

.table-fixed-column-right.table-header {
  background-color: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
  z-index: 30 !important;
}

/* 确保固定列表头在滚动时始终在最上层 */
thead .table-fixed-column-right.table-header {
  z-index: 35 !important;
}

/* 表格行hover时固定列的背景色 */
.table-row:hover .table-fixed-column-right {
  background-color: #f9fafb !important;
}

/* 确保固定列在滚动时有正确的边框 */
.table-fixed-column-right::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #e5e7eb;
  z-index: 1;
}

/* 确保所有表格头部文本不换行 */
th, th * {
  white-space: nowrap !important;
}

/* 移除重复的表格样式定义，统一使用customScrollbar.css中的样式 */

/* 按钮样式 - 降低高度 */
.btn {
  @apply inline-flex items-center px-3 py-1.5 text-sm font-medium rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-orange-600 text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500;
}

/* 状态标签优化 - 降低高度 */
.status-label {
  @apply inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full;
}

.status-success {
  @apply bg-green-100 text-green-800 ring-1 ring-green-200/50;
}

.status-warning {
  @apply bg-yellow-100 text-yellow-800 ring-1 ring-yellow-200/50;
}

.status-error {
  @apply bg-red-100 text-red-800 ring-1 ring-red-200/50;
}

.status-info {
  @apply bg-blue-100 text-blue-800 ring-1 ring-blue-200/50;
}

.status-neutral {
  @apply bg-gray-100 text-gray-800 ring-1 ring-gray-200/50;
}

/* 对话框和菜单表头优化样式 */
.dialog-header-optimized {
  @apply px-6 py-2.5 border-b;
}

.menu-item-optimized {
  @apply px-4 py-1.5 hover:bg-gray-100 cursor-pointer;
}

.dialog-footer-optimized {
  @apply px-6 py-3 border-t;
}

.app-header-optimized {
  @apply h-16;
}

/* 响应式布局调整 */
@media (min-width: 1024px) {
  html {
    @apply text-base;
  }

  main {
    @apply p-6;
  }

  .card {
    @apply p-5;
  }
}

/* 导出对话框小屏幕优化 */
@media (max-height: 600px) {
  /* 小屏幕高度时，进一步压缩间距 */
  .export-dialog-content {
    @apply space-y-2;
  }

  .export-dialog-section {
    @apply mb-2;
  }
}

@media (max-width: 640px) {
  /* 小屏幕宽度时的优化 */
  .export-format-grid {
    @apply grid-cols-1 gap-1;
  }

  .export-settings-grid {
    @apply grid-cols-1 gap-1;
  }
}

/* 高分屏适配 */
@media (min-resolution: 192dpi) {
  html {
    @apply subpixel-antialiased;
  }
}



