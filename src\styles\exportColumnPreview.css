/* 导出列选择区域样式 */

/* 预览区域容器 */
.export-column-preview {
  border-top: 1px solid #e5e7eb;
  padding: 0.5rem 0.75rem;
  background-color: #f9fafb;
}

/* 预览区域标题 */
.export-column-preview-title {
  font-size: 0.75rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

/* 预览区域列表 */
.export-column-preview-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 列分类区域 */
.export-column-section {
  margin-bottom: 0.75rem;
}

/* 列分类标题 */
.export-column-section-title {
  font-size: 0.7rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
  padding-left: 0.25rem;
}

/* 列分类项目容器 */
.export-column-section-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  padding: 0.25rem;
}

/* 预览区域列项 */
.export-column-preview-item {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin: 0.125rem;
}

/* 列序号样式 - 简洁深色数字 */
.column-index {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #2c3e50; /* 深蓝灰色，更柔和的深色调 */
  font-size: 0.75rem;
  margin-right: 0.5rem;
  font-weight: 500; /* 稍微加粗一点，但不是太粗 */
}

/* 选中状态 */
.export-column-preview-item.selected {
  background-color: #dbeafe;
  color: #1e40af;
  cursor: grab;
  border: 1px solid #93c5fd;
  font-weight: 500;
}

/* 未选中状态 */
.export-column-preview-item.unselected {
  background-color: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  font-weight: normal;
}

/* 预览区域列项悬停状态 - 选中 */
.export-column-preview-item.selected:hover {
  background-color: #bfdbfe;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 预览区域列项悬停状态 - 未选中 */
.export-column-preview-item.unselected:hover {
  background-color: #e5e7eb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 预览区域列项拖拽中状态 */
.export-column-preview-item.dragging {
  opacity: 0.5;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
  z-index: 10;
}

/* 预览区域列项可放置状态 */
.export-column-preview-item.drop-target {
  background-color: #93c5fd;
  border: 1px solid #60a5fa;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  transform: scale(1.02);
  z-index: 5;
}

/* 预览区域列项拖拽手柄 */
.export-column-preview-handle {
  width: 0.6rem;
  height: 0.6rem;
  color: #3b82f6;
  margin-left: 0.25rem;
  cursor: grab;
}

/* 拖拽手柄悬停效果 */
.export-column-preview-handle:hover {
  color: #1d4ed8;
  transform: scale(1.2);
}

/* 拖拽图像样式 */
.export-column-drag-image {
  padding: 0.25rem 0.5rem;
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  color: #1e40af;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
