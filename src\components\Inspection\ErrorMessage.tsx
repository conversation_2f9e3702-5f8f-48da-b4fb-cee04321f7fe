import React from 'react';
import { AlertCircle, X } from 'lucide-react';

/**
 * 错误信息属性接口
 */
interface ErrorMessageProps {
  error: string | null;
  onClose?: () => void;
  className?: string;
  variant?: 'card' | 'banner';
}

/**
 * 巡检管理通用错误信息组件
 * 统一的错误显示样式
 */
const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  onClose,
  className = '',
  variant = 'card'
}) => {
  if (!error) {
    return null;
  }

  if (variant === 'banner') {
    return (
      <div className={`m-4 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-md ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-400 mr-3" />
            <span className="text-red-700 font-medium">{error}</span>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-red-400 hover:text-red-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`mx-6 mt-6 bg-white rounded-lg border border-red-200 shadow-md overflow-hidden ${className}`}>
      <div className="px-6 py-4 bg-gradient-to-r from-red-50 to-pink-50 border-b border-red-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">错误信息</span>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
      <div className="p-6">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-600 mr-3 flex-shrink-0" />
          <span className="text-red-700 text-sm">{error}</span>
        </div>
      </div>
    </div>
  );
};

export default ErrorMessage;
