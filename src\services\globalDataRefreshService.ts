import { BaseService, BaseServiceState, BaseServiceEvents } from './base/baseService';
import InventoryService from './Inventory/inventoryService';
import DepartmentService from './Inventory/departmentService';
import ExtFieldService from './Inventory/extFieldService';
import ColumnVisibilityService from './Inventory/columnVisibilityService';
import GlobalConfigService from './globalConfigService';

/**
 * 全局数据刷新服务状态接口
 */
interface GlobalDataRefreshServiceState extends BaseServiceState {
  isRefreshing: boolean;
  refreshProgress: number;
  lastRefreshTime: number;
  refreshSteps: string[];
  currentStep: string;
}

/**
 * 全局数据刷新服务事件接口
 */
interface GlobalDataRefreshServiceEvents extends BaseServiceEvents<GlobalDataRefreshServiceState> {
  'refresh-started': void;
  'refresh-progress': { step: string; progress: number };
  'refresh-completed': void;
  'refresh-failed': { error: string; step: string };
}

/**
 * 全局数据刷新服务
 * 用于在切换备份等场景下重新加载所有模块的数据
 */
class GlobalDataRefreshService extends BaseService<GlobalDataRefreshServiceState, GlobalDataRefreshServiceEvents> {
  private static instance: GlobalDataRefreshService;

  /**
   * 获取全局数据刷新服务实例
   */
  public static getInstance(): GlobalDataRefreshService {
    if (!GlobalDataRefreshService.instance) {
      GlobalDataRefreshService.instance = new GlobalDataRefreshService();
    }
    return GlobalDataRefreshService.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    super('GlobalDataRefresh', {
      isLoading: false,
      isRefreshing: false,
      refreshProgress: 0,
      lastRefreshTime: 0,
      refreshSteps: [],
      currentStep: '',
    });
  }

  /**
   * 执行全局数据刷新
   * 重新加载所有模块的数据，就像重新打开项目一样
   */
  public async refreshAllData(): Promise<void> {
    if (this.state.isRefreshing) {
      console.log('全局数据刷新正在进行中，跳过重复请求');
      return;
    }

    try {
      console.log('开始全局数据刷新...');

      // 更新状态
      this.updateState({
        isRefreshing: true,
        refreshProgress: 0,
        currentStep: '准备刷新',
        refreshSteps: []
      });

      this.emitter.emit('refresh-started');

      // 定义刷新步骤
      const refreshSteps = [
        { name: '清除缓存', action: () => this.clearAllCaches() },
        { name: '刷新全局配置', action: () => this.refreshGlobalConfig() },
        { name: '刷新台账数据', action: () => this.refreshInventoryData() },
        { name: '刷新部门数据', action: () => this.refreshDepartmentData() },
        { name: '刷新扩展字段', action: () => this.refreshExtensionFields() },
        { name: '重新加载列设置', action: () => this.reloadColumnSettings() },
        { name: '重新生成表格字段', action: () => this.regenerateTableFields() },
        { name: '强制更新状态', action: () => this.forceUpdateAllServices() }
      ];

      const totalSteps = refreshSteps.length;

      // 执行每个刷新步骤
      for (let i = 0; i < refreshSteps.length; i++) {
        const step = refreshSteps[i];
        const progress = Math.round(((i + 1) / totalSteps) * 100);

        console.log(`执行刷新步骤 ${i + 1}/${totalSteps}: ${step.name}`);

        // 更新当前步骤
        this.updateState({
          currentStep: step.name,
          refreshProgress: progress
        });

        this.emitter.emit('refresh-progress', {
          step: step.name,
          progress: progress
        });

        try {
          // 执行刷新步骤
          await step.action();
          console.log(`步骤 "${step.name}" 完成`);
        } catch (error) {
          console.error(`步骤 "${step.name}" 失败:`, error);

          // 发出失败事件但继续执行其他步骤
          this.emitter.emit('refresh-failed', {
            error: error instanceof Error ? error.message : String(error),
            step: step.name
          });
        }

        // 添加小延迟，让UI有时间更新
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 完成刷新
      this.updateState({
        isRefreshing: false,
        refreshProgress: 100,
        currentStep: '刷新完成',
        lastRefreshTime: Date.now()
      });

      this.emitter.emit('refresh-completed');
      console.log('全局数据刷新完成');

    } catch (error) {
      console.error('全局数据刷新失败:', error);

      this.updateState({
        isRefreshing: false,
        refreshProgress: 0,
        currentStep: '刷新失败'
      });

      this.emitter.emit('refresh-failed', {
        error: error instanceof Error ? error.message : String(error),
        step: this.state.currentStep
      });

      throw error;
    }
  }

  /**
   * 清除所有服务的缓存
   */
  private async clearAllCaches(): Promise<void> {
    console.log('清除所有服务缓存...');

    // 清除全局配置缓存
    const globalConfigService = GlobalConfigService.getInstance();
    globalConfigService.clearCache();

    // 清除部门服务缓存
    const departmentService = DepartmentService.getInstance();
    await departmentService.refreshPersonCache();

    console.log('所有缓存已清除');
  }

  /**
   * 刷新全局配置
   */
  private async refreshGlobalConfig(): Promise<void> {
    console.log('刷新全局配置...');
    const globalConfigService = GlobalConfigService.getInstance();
    await globalConfigService.getGlobalConfig(true); // 强制刷新
    console.log('全局配置刷新完成');
  }

  /**
   * 刷新台账数据
   */
  private async refreshInventoryData(): Promise<void> {
    console.log('刷新台账数据...');
    const inventoryService = InventoryService.getInstance();
    await inventoryService.loadInventoryList(true); // 强制刷新
    console.log('台账数据刷新完成');
  }

  /**
   * 刷新部门数据
   */
  private async refreshDepartmentData(): Promise<void> {
    console.log('刷新部门数据...');
    const departmentService = DepartmentService.getInstance();
    await departmentService.loadDepartmentTree(true); // 强制刷新
    console.log('部门数据刷新完成');
  }

  /**
   * 刷新扩展字段
   */
  private async refreshExtensionFields(): Promise<void> {
    console.log('刷新扩展字段...');
    const extFieldService = ExtFieldService.getInstance();
    await extFieldService.getAllExtFields(true); // 强制刷新
    console.log('扩展字段刷新完成');
  }

  /**
   * 重新加载列设置
   */
  private async reloadColumnSettings(): Promise<void> {
    console.log('重新加载列设置...');
    const columnVisibilityService = ColumnVisibilityService.getInstance();

    // 列可见性设置已经在服务初始化时从本地存储加载
    // 这里主要是确保设置与当前数据库状态同步
    console.log('列设置重新加载完成');
  }

  /**
   * 重新生成表格字段
   */
  private async regenerateTableFields(): Promise<void> {
    console.log('重新生成表格字段...');
    const inventoryService = InventoryService.getInstance();

    // 强制刷新设备分类树
    await inventoryService.generateCategoryTree(undefined, true);

    // 重新生成表格字段
    inventoryService.generateTableFields();

    console.log('表格字段重新生成完成');
  }

  /**
   * 强制更新所有服务状态
   */
  private async forceUpdateAllServices(): Promise<void> {
    console.log('强制更新所有服务状态...');

    const inventoryService = InventoryService.getInstance();
    const departmentService = DepartmentService.getInstance();
    const extFieldService = ExtFieldService.getInstance();
    const columnVisibilityService = ColumnVisibilityService.getInstance();
    const globalConfigService = GlobalConfigService.getInstance();

    // 强制触发状态更新
    inventoryService.forceUpdate();
    departmentService.forceUpdate();
    extFieldService.forceUpdate();
    columnVisibilityService.forceUpdate();
    globalConfigService.forceUpdate();

    console.log('所有服务状态更新完成');
  }

  /**
   * 获取刷新进度
   */
  public getRefreshProgress(): {
    isRefreshing: boolean;
    progress: number;
    currentStep: string;
  } {
    return {
      isRefreshing: this.state.isRefreshing,
      progress: this.state.refreshProgress,
      currentStep: this.state.currentStep
    };
  }

  /**
   * 检查是否正在刷新
   */
  public isRefreshing(): boolean {
    return this.state.isRefreshing;
  }
}

export default GlobalDataRefreshService;
