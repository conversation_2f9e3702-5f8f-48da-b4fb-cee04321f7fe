import React, { useState, useRef, useEffect, useMemo } from 'react';
import { ChevronRight } from 'lucide-react';
import './customSelect.css';

// 分层选项接口
interface HierarchicalOption {
  id: string | number;
  value: string;
  label: string;
  isParent?: boolean;
  parentId?: string | number;
  children?: HierarchicalOption[];
}

interface HierarchicalSelectProps {
  options: HierarchicalOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

/**
 * 层级下拉选择组件
 * 支持一级分类和二级分类的层级结构
 */
const HierarchicalSelect: React.FC<HierarchicalSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择...',
  disabled = false,
  required = false,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState('');
  const [selectedLabel, setSelectedLabel] = useState('');
  // 将悬停状态改为选中状态
  const [selectedParent, setSelectedParent] = useState<string | null>(null);
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 当选项或值变化时，更新选中的标签
  useEffect(() => {
    // 查找所有选项中匹配的值
    const findSelectedOption = (opts: HierarchicalOption[]): HierarchicalOption | undefined => {
      for (const opt of opts) {
        if (opt.value === value) return opt;
        if (opt.children) {
          const found = findSelectedOption(opt.children);
          if (found) return found;
        }
      }
      return undefined;
    };

    const selectedOption = findSelectedOption(options);
    setSelectedLabel(selectedOption ? selectedOption.label : '');
  }, [options, value]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFilter('');
        setSelectedParent(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 存储匹配的父节点值，用于自动展开
  const [matchedParents, setMatchedParents] = useState<Set<string>>(new Set());

  // 存储第一个匹配项的值，用于自动滚动
  const [firstMatchValue, setFirstMatchValue] = useState<string | null>(null);

  // 过滤选项和计算匹配信息
  const { filteredOptions, matchingParents, firstMatchVal } = useMemo(() => {
    if (!filter) {
      return {
        filteredOptions: options,
        matchingParents: new Set<string>(),
        firstMatchVal: null
      };
    }

    // 用于收集匹配的父节点
    const matchingParents = new Set<string>();
    let foundFirstMatch = false;
    let firstMatchVal: string | null = null;

    // 递归过滤选项
    const filterOptions = (opts: HierarchicalOption[], parentPath: string[] = []): HierarchicalOption[] => {
      return opts.reduce<HierarchicalOption[]>((acc, opt) => {
        const currentPath = [...parentPath, opt.value];
        const isMatch = opt.label.toLowerCase().includes(filter.toLowerCase());

        // 如果当前选项匹配过滤条件
        if (isMatch) {
          // 记录第一个匹配项
          if (!foundFirstMatch) {
            foundFirstMatch = true;
            firstMatchVal = opt.value;

            // 将所有父节点添加到匹配集合中
            parentPath.forEach(parent => matchingParents.add(parent));
          }

          acc.push(opt);
          return acc;
        }

        // 如果有子选项，递归过滤
        if (opt.children && opt.children.length > 0) {
          const filteredChildren = filterOptions(opt.children, currentPath);
          if (filteredChildren.length > 0) {
            // 如果子节点中有匹配项，将当前节点添加到匹配父节点集合
            matchingParents.add(opt.value);

            acc.push({
              ...opt,
              children: filteredChildren
            });
          }
        }

        return acc;
      }, []);
    };

    const filteredOptions = filterOptions(options);

    return {
      filteredOptions,
      matchingParents,
      firstMatchVal
    };
  }, [options, filter]);

  // 处理选项选择
  const handleSelect = (optionValue: string, optionLabel: string) => {
    onChange(optionValue);
    setSelectedLabel(optionLabel);
    setFilter('');
    setIsOpen(false);
    setSelectedParent(null);
  };

  // 处理输入框点击
  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen && inputRef.current) {
        // 聚焦到输入框，允许显示焦点样式
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 0);
      }
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setFilter(newValue);

    // 确保下拉框打开
    if (!isOpen) {
      setIsOpen(true);
    }

    // 如果清空了搜索内容，重置选中的父节点
    if (!newValue) {
      setSelectedParent(null);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      setFilter('');
      setSelectedParent(null);
    } else if (e.key === 'Enter' && isOpen && filteredOptions.length > 0) {
      // 如果有选中的父节点，则展开/收起
      if (selectedParent) {
        setSelectedParent(null);
      } else if (firstMatchValue) {
        // 找到匹配的选项
        const findOption = (opts: HierarchicalOption[]): HierarchicalOption | undefined => {
          for (const opt of opts) {
            if (opt.value === firstMatchValue) return opt;
            if (opt.children) {
              const found = findOption(opt.children);
              if (found) return found;
            }
          }
          return undefined;
        };

        const option = findOption(filteredOptions);
        if (option) {
          handleSelect(option.value, option.label);
        }
      }
    }
  };

  // 处理父选项点击
  const handleParentClick = (parentValue: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡

    // 如果当前已经选中了这个父选项，则关闭子菜单
    if (selectedParent === parentValue) {
      setSelectedParent(null);
    } else {
      // 否则选中这个父选项，显示子菜单
      setSelectedParent(parentValue);
    }
  };

  // 处理子菜单点击
  const handleSubmenuClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡，防止关闭下拉框
  };

  // 存储每个选项的DOM引用
  const optionRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // 计算下拉框位置
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');

  useEffect(() => {
    if (isOpen && selectRef.current) {
      const rect = selectRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = Math.min(300, options.length * 36); // 估计下拉框高度

      if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
        setDropdownPosition('top');
      } else {
        setDropdownPosition('bottom');
      }
    }
  }, [isOpen, options.length]);

  // 更新匹配状态
  useEffect(() => {
    setMatchedParents(matchingParents);
    setFirstMatchValue(firstMatchVal);
  }, [matchingParents, firstMatchVal]);

  // 添加自动滚动到匹配项的功能
  useEffect(() => {
    if (isOpen && firstMatchValue && filter) {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        const matchedElement = optionRefs.current.get(firstMatchValue);
        if (matchedElement) {
          // 滚动到匹配元素
          matchedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }, 100);
    }
  }, [isOpen, firstMatchValue, filter]);

  // 渲染选项
  const renderOptions = (opts: HierarchicalOption[]) => {
    return opts.map(option => {
      const hasChildren = option.children && option.children.length > 0;
      // 如果在搜索结果中，自动展开匹配的父节点
      const isParentSelected = selectedParent === option.value || matchedParents.has(option.value);

      return (
        <div key={option.id} className="relative">
          <div
            ref={el => {
              if (el) optionRefs.current.set(option.value, el);
            }}
            className={`px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer focus:outline-none focus:bg-blue-50 focus:text-blue-700 ${
              option.value === value ? 'bg-blue-50 text-blue-700' :
              (isParentSelected ? 'bg-gray-100' : 'text-gray-700')
            } ${hasChildren ? 'font-medium' : ''}`}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
            onClick={(e) => hasChildren ? handleParentClick(option.value, e) : handleSelect(option.value, option.label)}
            tabIndex={0}
          >
            <span>{option.label}</span>
            {hasChildren && <ChevronRight className={`h-4 w-4 ${isParentSelected ? 'transform rotate-90' : ''}`} />}
          </div>

          {/* 如果有子选项且父选项被选中或在搜索结果中，显示子菜单 */}
          {hasChildren && isParentSelected && (() => {
            // 获取父选项的位置
            const parentEl = optionRefs.current.get(option.value);
            const parentRect = parentEl?.getBoundingClientRect();
            const dropdownEl = selectRef.current?.querySelector('.dropdown-container') as HTMLElement;
            const dropdownRect = dropdownEl?.getBoundingClientRect();

            // 计算子菜单的位置
            const top = parentRect ? parentRect.top + window.scrollY : 0;
            const left = dropdownRect ? dropdownRect.right : 0;

            // 检查是否有足够的空间显示子菜单
            const windowWidth = window.innerWidth;
            const submenuWidth = 180; // 子菜单宽度
            const rightSpace = windowWidth - left;

            // 如果右侧空间不足，则在左侧显示子菜单
            const submenuLeft = rightSpace < submenuWidth ? (dropdownRect ? dropdownRect.left - submenuWidth : 0) : left;

            return (
              <div
                className="submenu-container fixed bg-white border border-gray-300 rounded-md shadow-lg overflow-auto"
                style={{
                  top: `${top}px`,
                  left: `${submenuLeft}px`,
                  minWidth: '180px',
                  maxHeight: '300px',
                  zIndex: 1000002 // 提高z-index确保在最上层
                }}
                onClick={handleSubmenuClick}
              >
                {option.children!.map(child => (
                  <div
                    key={child.id}
                    className={`px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer focus:outline-none focus:bg-blue-50 focus:text-blue-700 ${
                      child.value === value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                    }`}
                    onClick={() => handleSelect(child.value, child.label)}
                    tabIndex={0}
                  >
                    {child.label}
                  </div>
                ))}
              </div>
            );
          })()}
        </div>
      );
    });
  };

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      <div
        className={`flex items-center w-full h-[38px] px-3 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer'
        }`}
        onClick={disabled ? undefined : handleInputClick}
      >
        <input
          ref={inputRef}
          type="text"
          value={isOpen ? filter : selectedLabel}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`w-full bg-transparent border-none select-input-no-focus ${
            disabled ? 'cursor-not-allowed' : 'cursor-pointer'
          }`}
          style={{ caretColor: isOpen ? 'auto' : 'transparent' }}
          readOnly={!isOpen}
          disabled={disabled}
          required={required}
        />
        <svg
          className={`h-4 w-4 text-gray-400 ml-1 ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      {isOpen && (
        <div
          className={`dropdown-container fixed bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 ${
            dropdownPosition === 'top' ? 'dropdown-up' : 'dropdown-down'
          }`}
          style={{
            width: selectRef.current?.offsetWidth,
            left: selectRef.current ? selectRef.current.getBoundingClientRect().left + window.scrollX : 0,
            zIndex: 1000001, // 提高z-index确保在模态对话框之上
            ...(dropdownPosition === 'top'
              ? { bottom: window.innerHeight - (selectRef.current?.getBoundingClientRect().top || 0) + window.scrollY }
              : { top: (selectRef.current?.getBoundingClientRect().bottom || 0) + window.scrollY })
          }}
        >
          {filteredOptions.length === 0 ? (
            <div className="px-4 py-2 text-sm text-gray-500">无匹配选项</div>
          ) : (
            renderOptions(filteredOptions)
          )}
        </div>
      )}
    </div>
  );
};

export default HierarchicalSelect;
