import { useState, useEffect, useCallback } from 'react';
import ImportService, { ImportResult } from '../../../services/Inventory/Import/importService';

// 导入Hook返回类型
interface UseImportReturn {
  // 状态
  isImporting: boolean;
  progress: number;
  lastImportCount: number;
  validationErrors: string[];
  error?: string;

  // 方法
  importInventory: (file: File) => Promise<ImportResult>;
  validateImportFile: (file: File) => Promise<{ isValid: boolean; errors: string[] }>;
  generateImportTemplate: (format: 'csv' | 'excel') => Blob;
  cancelImport: () => void;
  clearValidationErrors: () => void;
  getService: () => ImportService;
}

/**
 * 导入Hook
 * 连接导入服务和UI组件
 */
export function useImport(): UseImportReturn {
  // 获取导入服务实例
  const service = ImportService.getInstance();

  // 本地状态
  const [state, setState] = useState({
    isImporting: service.getState().isImporting,
    progress: service.getState().progress,
    lastImportCount: service.getState().lastImportCount,
    validationErrors: service.getState().validationErrors,
    error: service.getState().error
  });

  // 监听服务状态变化
  useEffect(() => {
    const handleStateChange = () => {
      setState({
        isImporting: service.getState().isImporting,
        progress: service.getState().progress,
        lastImportCount: service.getState().lastImportCount,
        validationErrors: service.getState().validationErrors,
        error: service.getState().error
      });
    };

    // 注册状态变化监听器
    service.on('state-changed', handleStateChange);

    // 清理函数
    return () => {
      service.off('state-changed', handleStateChange);
    };
  }, [service]);

  // 导入设备台账数据
  const importInventory = useCallback(async (
    file: File
  ): Promise<ImportResult> => {
    try {
      return await service.importInventory(file);
    } catch (error: any) {
      console.error('导入设备台账数据失败:', error);
      return {
        success: false,
        importedData: [],
        count: 0,
        errors: [error.message]
      };
    }
  }, [service]);

  // 验证导入文件
  const validateImportFile = useCallback(async (
    file: File
  ): Promise<{ isValid: boolean; errors: string[] }> => {
    try {
      // 读取文件内容
      const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            resolve(e.target.result as ArrayBuffer);
          } else {
            reject(new Error('读取文件失败'));
          }
        };
        reader.onerror = () => {
          reject(new Error('读取文件失败'));
        };
        reader.readAsArrayBuffer(file);
      });

      // 解析文件数据
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const format = fileExt === 'csv' ? 'csv' : 'excel';

      // 验证数据
      const parsedData = service['parseFileData'](fileData, file.name);
      return service['validateData'](parsedData);
    } catch (error: any) {
      console.error('验证导入文件失败:', error);
      return {
        isValid: false,
        errors: [error.message]
      };
    }
  }, [service]);

  // 生成导入模板
  const generateImportTemplate = useCallback((
    format: 'csv' | 'excel'
  ): Blob => {
    try {
      return service.generateImportTemplate(format);
    } catch (error: any) {
      console.error('生成导入模板失败:', error);
      throw error;
    }
  }, [service]);

  // 获取服务实例
  const getService = useCallback(() => {
    return service;
  }, [service]);

  // 取消导入
  const cancelImport = useCallback(() => {
    service.cancelImport();
  }, [service]);

  // 清除验证错误
  const clearValidationErrors = useCallback(() => {
    service.updateState({ validationErrors: [] });
  }, [service]);

  // 返回Hook结果
  return {
    ...state,
    importInventory,
    validateImportFile,
    generateImportTemplate,
    cancelImport,
    clearValidationErrors,
    getService
  };
}

export default useImport;
