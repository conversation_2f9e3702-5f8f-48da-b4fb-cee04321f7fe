import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import './customSelect.css';

// 部门树选项接口
interface DepartmentOption {
  id: string;
  value: string;
  label: string;
  children?: DepartmentOption[];
}

interface DepartmentTreeSelectProps {
  options: DepartmentOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  defaultExpandedNodes?: string[]; // 默认展开的节点ID数组
}

/**
 * 部门树选择组件
 * 专用于部门选择，点击展开图标展开子部门，点击部门名称选择该部门
 */
const DepartmentTreeSelect: React.FC<DepartmentTreeSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择所属部门',
  disabled = false,
  required = false,
  className = '',
  defaultExpandedNodes = []
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(() => {
    // 初始化时，如果提供了默认展开的节点，则将其添加到展开节点集合中
    const initialExpandedNodes = new Set<string>();
    defaultExpandedNodes.forEach(nodeId => initialExpandedNodes.add(nodeId));
    return initialExpandedNodes;
  });
  const [selectedLabel, setSelectedLabel] = useState('');
  const [filter, setFilter] = useState('');
  const [matchedParents, setMatchedParents] = useState<Set<string>>(new Set());
  const [firstMatchValue, setFirstMatchValue] = useState<string | null>(null);
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const optionRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // 当选项或值变化时，更新选中的标签
  useEffect(() => {
    // 查找所有选项中匹配的值
    const findSelectedOption = (opts: DepartmentOption[]): DepartmentOption | undefined => {
      for (const opt of opts) {
        if (opt.value === value) return opt;
        if (opt.children) {
          const found = findSelectedOption(opt.children);
          if (found) return found;
        }
      }
      return undefined;
    };

    const selectedOption = findSelectedOption(options);
    setSelectedLabel(selectedOption ? selectedOption.label : '');
  }, [options, value]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理选项选择
  const handleSelect = (optionValue: string, optionLabel: string) => {
    onChange(optionValue);
    setSelectedLabel(optionLabel);
    setFilter('');
    setIsOpen(false);
  };

  // 处理展开/收起节点
  const toggleNode = (nodeId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡，防止触发选择

    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  // 处理输入框点击
  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen && inputRef.current) {
        // 聚焦但不显示蓝色边框
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 0);
      }
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setFilter(newValue);

    // 确保下拉框打开
    if (!isOpen) {
      setIsOpen(true);
    }

    // 如果清空了搜索内容，重置展开的节点
    if (!newValue) {
      setExpandedNodes(new Set());
    }
  };

  // 过滤选项和计算匹配信息
  const { filteredOptions, matchingParents: newMatchedParents, firstMatchVal } = React.useMemo(() => {
    if (!filter) {
      return {
        filteredOptions: options,
        matchingParents: new Set<string>(),
        firstMatchVal: null
      };
    }

    // 用于收集匹配的父节点
    const matchingParents = new Set<string>();
    let foundFirstMatch = false;
    let firstMatchVal: string | null = null;

    // 递归过滤选项
    const filterOptions = (opts: DepartmentOption[], parentPath: string[] = []): DepartmentOption[] => {
      return opts.reduce<DepartmentOption[]>((acc, opt) => {
        const currentPath = [...parentPath, opt.id];
        const isMatch = opt.label.toLowerCase().includes(filter.toLowerCase());

        // 如果当前选项匹配过滤条件
        if (isMatch) {
          // 记录第一个匹配项
          if (!foundFirstMatch) {
            foundFirstMatch = true;
            firstMatchVal = opt.value;

            // 将所有父节点添加到匹配集合中
            parentPath.forEach(parent => matchingParents.add(parent));
          }

          acc.push(opt);
          return acc;
        }

        // 如果有子选项，递归过滤
        if (opt.children && opt.children.length > 0) {
          const filteredChildren = filterOptions(opt.children, currentPath);
          if (filteredChildren.length > 0) {
            // 如果子节点中有匹配项，将当前节点添加到匹配父节点集合
            matchingParents.add(opt.id);

            acc.push({
              ...opt,
              children: filteredChildren
            });
          }
        }

        return acc;
      }, []);
    };

    const filteredOptions = filterOptions(options);

    return {
      filteredOptions,
      matchingParents,
      firstMatchVal
    };
  }, [options, filter]);

  // 更新匹配状态
  useEffect(() => {
    setMatchedParents(newMatchedParents);
    setFirstMatchValue(firstMatchVal);

    // 自动展开匹配的父节点
    if (filter) {
      setExpandedNodes(prev => {
        const newSet = new Set(prev);
        newMatchedParents.forEach(id => newSet.add(id));
        return newSet;
      });
    }
  }, [newMatchedParents, firstMatchVal, filter]);

  // 计算下拉框位置
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');

  useEffect(() => {
    if (isOpen && selectRef.current) {
      const rect = selectRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = Math.min(300, options.length * 36); // 估计下拉框高度

      if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
        setDropdownPosition('top');
      } else {
        setDropdownPosition('bottom');
      }
    }
  }, [isOpen, options.length]);

  // 添加自动滚动到匹配项的功能
  useEffect(() => {
    if (isOpen && firstMatchValue && filter) {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        const matchedElement = optionRefs.current.get(firstMatchValue);
        if (matchedElement) {
          // 滚动到匹配元素
          matchedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }, 100);
    }
  }, [isOpen, firstMatchValue, filter]);

  // 递归渲染部门树
  const renderDepartmentTree = (departments: DepartmentOption[], level = 0) => {
    return departments.map(dept => {
      const hasChildren = dept.children && dept.children.length > 0;
      const isExpanded = expandedNodes.has(dept.id);

      return (
        <div key={dept.id} className="department-tree-item" style={{ paddingLeft: `${level * 16}px` }}>
          <div
            ref={el => {
              if (el) optionRefs.current.set(dept.value, el);
            }}
            className={`flex items-center py-2 px-2 hover:bg-gray-100 cursor-pointer ${
              dept.value === value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
            }`}
            onClick={() => handleSelect(dept.value, dept.label)}
          >
            {hasChildren ? (
              <span
                className="expand-icon mr-1 flex-shrink-0"
                onClick={(e) => toggleNode(dept.id, e)}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-500" />
                )}
              </span>
            ) : (
              <span className="w-5"></span> // 占位，保持缩进一致
            )}
            <span className="department-label text-sm">{dept.label}</span>
          </div>

          {hasChildren && isExpanded && (
            <div className="department-children">
              {renderDepartmentTree(dept.children, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      <div
        className={`flex items-center w-full h-[38px] px-3 ${
          disabled ? 'cursor-not-allowed' : 'cursor-pointer'
        }`}
        style={{
          border: '1px solid #d1d5db',
          borderRadius: '6px',
          outline: 'none',
          boxShadow: 'none',
          backgroundColor: disabled ? '#f3f4f6' : 'white',
          borderLeft: '1px solid #d1d5db',
          borderRight: '1px solid #d1d5db',
          borderTop: '1px solid #d1d5db',
          borderBottom: '1px solid #d1d5db',
          borderWidth: '1px',
          borderStyle: 'solid',
          borderColor: '#d1d5db'
        }}
        onClick={disabled ? undefined : handleInputClick}
      >
        <input
          ref={inputRef}
          type="text"
          value={isOpen ? filter : selectedLabel}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={`w-full bg-transparent border-none outline-none select-input-no-focus ${
            disabled ? 'cursor-not-allowed' : 'cursor-pointer'
          }`}
          style={{
            caretColor: isOpen ? 'auto' : 'transparent',
            border: 'none',
            borderLeft: 'none',
            borderRight: 'none',
            borderTop: 'none',
            borderBottom: 'none',
            outline: 'none',
            boxShadow: 'none',
            backgroundImage: 'none',
            backgroundColor: 'transparent'
          }}
          readOnly={!isOpen}
          disabled={disabled}
          required={required}
        />
        <svg
          className={`h-4 w-4 text-gray-400 ml-1 ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      {isOpen && (
        <div
          className={`department-dropdown fixed z-[999999] w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto ${
            dropdownPosition === 'top' ? 'dropdown-up' : 'dropdown-down'
          }`}
          style={{
            outline: 'none',
            width: selectRef.current?.offsetWidth,
            left: selectRef.current?.getBoundingClientRect().left,
            ...(dropdownPosition === 'top'
              ? { bottom: window.innerHeight - selectRef.current?.getBoundingClientRect().top }
              : { top: selectRef.current?.getBoundingClientRect().bottom })
          }}
        >
          {filteredOptions.length === 0 ? (
            <div className="px-4 py-2 text-sm text-gray-500">
              {filter ? "无匹配部门" : "无部门数据"}
            </div>
          ) : (
            <div className="department-tree">
              {renderDepartmentTree(filteredOptions)}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DepartmentTreeSelect;
