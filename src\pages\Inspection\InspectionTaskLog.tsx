import React, { useState, useEffect, useRef } from 'react';
import {
  RefreshCw,
  AlertCircle,
  ChevronDown,
  User,
  Building,
  Clock,
  FileText
} from 'lucide-react';
import useInspectionTaskLog, {
  InspectionTaskLogItem
} from '../../hooks/Inspection/useInspectionTaskLog';
import CustomScrollbar from '../../components/ui/CustomScrollbar';
import DepartmentTooltip from '../../components/ui/DepartmentTooltip';
import { useListFocus } from '../../hooks/base';
import { formatInspectionDateTime } from '../../utils/formatUtils';

/**
 * 巡检任务日志页面Props接口
 */
interface InspectionTaskLogProps {
  searchText?: string;
  startDate?: string;
  endDate?: string;
  refreshTrigger?: number;
}

/**
 * 巡检任务日志页面
 * 提供查看巡检任务日志的功能
 */
const InspectionTaskLog: React.FC<InspectionTaskLogProps> = ({
  searchText = '',
  startDate = '',
  endDate = '',
  refreshTrigger = 0
}) => {
  // 表格焦点管理
  const { listRef, handleKeyDown: handleTableKeyDown } = useListFocus({
    itemSelector: 'tr[data-row-id]',
    autoFocusOnAdd: true
  });

  // 使用巡检任务日志Hook
  const {
    error,
    filter,
    filteredLogs,
    isLoading,
    getInspectionTaskLogs,
    forceRefreshInspectionTaskLogs,
    setFilter,
    // 分页相关
    currentPage,
    pageSize,
    totalPages,
    paginatedLogs,
    setPage,
    setPageSize
  } = useInspectionTaskLog();

  // 分页下拉框状态
  const [showPageSizeDropdown, setShowPageSizeDropdown] = useState(false);
  const pageSizeDropdownRef = useRef<HTMLDivElement>(null);

  // 初始化数据加载
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await getInspectionTaskLogs();
      } catch (err) {
        console.error('初始化加载巡检任务日志失败:', err);
      }
    };

    loadInitialData();
  }, [getInspectionTaskLogs]);

  // 响应父组件的刷新触发
  useEffect(() => {
    if (refreshTrigger > 0) {
      handleRefreshLogs();
    }
  }, [refreshTrigger]);

  // 响应父组件的搜索文本变化
  useEffect(() => {
    if (searchText !== (filter.searchText || '')) {
      setFilter({ ...filter, searchText: searchText || undefined });
    }
  }, [searchText, filter, setFilter]);

  // 响应父组件的日期范围变化
  useEffect(() => {
    const newFilter = { ...filter };
    let hasChange = false;

    if (startDate !== (filter.startDate || '')) {
      newFilter.startDate = startDate || undefined;
      hasChange = true;
    }

    if (endDate !== (filter.endDate || '')) {
      newFilter.endDate = endDate || undefined;
      hasChange = true;
    }

    if (hasChange) {
      setFilter(newFilter);
    }
  }, [startDate, endDate, filter, setFilter]);

  // 处理刷新数据（强制刷新，忽略缓存）
  const handleRefreshLogs = async () => {
    try {
      await forceRefreshInspectionTaskLogs();
    } catch (err) {
      console.error('刷新巡检任务日志失败:', err);
    }
  };

  // 使用统一的时间戳格式化函数
  const formatTimestamp = formatInspectionDateTime;

  // 从日志数据中提取部门信息
  const extractDepartments = (log: InspectionTaskLogItem): string[] => {
    const newData = log.new_data || {};
    const originalData = log.original_data || {};

    // 尝试从两个数据源中提取部门信息
    const dataSources = [newData, originalData];

    for (const data of dataSources) {
      // 尝试从不同的字段中提取部门信息
      if (data?.departments && Array.isArray(data.departments)) {
        return data.departments;
      }

      if (data?.department_names && Array.isArray(data.department_names)) {
        return data.department_names;
      }

      if (data?.department_list && Array.isArray(data.department_list)) {
        return data.department_list;
      }

      // 如果有单个部门名称
      if (data?.department_name) {
        return [data.department_name];
      }
    }

    // 如果只有部门数量，生成占位符（临时方案）
    const deptCount = newData.department_count || originalData.department_count;
    if (deptCount && deptCount > 0) {
      return Array.from({ length: deptCount }, (_, i) => `部门${i + 1}`);
    }

    return [];
  };

  // 处理分页大小下拉框点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pageSizeDropdownRef.current && !pageSizeDropdownRef.current.contains(event.target as Node)) {
        setShowPageSizeDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="h-full flex flex-col">





      {/* 错误提示 */}
      {error && (
        <div className="bg-rose-50 p-2 border border-rose-200 rounded-md fixed top-2 right-2 z-50 max-w-md shadow-sm">
          <div className="flex items-center text-rose-700">
            <span className="mr-1.5 text-rose-500">●</span>
            <span className="text-sm">{error}</span>
            <button
              onClick={handleRefreshLogs}
              className="ml-2 text-sm text-rose-600 hover:text-rose-800 underline transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      )}

      {/* 自适应表格容器 */}
      <div className="flex-1 flex flex-col min-h-0 relative">
        {/* 加载指示器 */}
        {isLoading && filteredLogs.length > 0 && (
          <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-20">
            <div className="flex flex-col items-center">
              <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mb-2" />
              <p className="text-blue-600 text-sm font-medium">获取数据中...</p>
            </div>
          </div>
        )}

        {/* 数据表格 */}
        <div className="flex-1 min-h-0 bg-white shadow-sm adaptive-table-container">
          <div className="adaptive-table-content">
            <CustomScrollbar
              className="h-full"
              horizontal={true}
              vertical={true}
            >
              <div ref={listRef as any} onKeyDown={handleTableKeyDown}>
                <table className="w-full adaptive-table" style={{ minWidth: '1200px' }}>
                  {/* 表头 */}
                  <thead>
                    <tr>
                      <th className="table-header-cell table-cell-left w-24">操作类型</th>
                      <th className="table-header-cell table-cell-left w-20">任务ID</th>
                      <th className="table-header-cell table-cell-left w-32">任务名称</th>
                      <th className="table-header-cell table-cell-left w-40">任务描述</th>
                      <th className="table-header-cell table-cell-left w-24">责任人</th>
                      <th className="table-header-cell table-cell-left w-32">巡检部门</th>
                      <th className="table-header-cell table-cell-left w-32">操作时间</th>
                    </tr>
                  </thead>

                  {/* 表体 */}
                  <tbody className="bg-white">
                    {filteredLogs.length === 0 ? (
                      <tr className="adaptive-table-empty">
                        <td colSpan={7} className="px-4 py-12 text-center">
                          <div className="flex flex-col items-center justify-center h-full min-h-[200px]">
                            <RefreshCw className={`h-8 w-8 mb-3 ${
                              isLoading ? 'text-blue-600 animate-spin' : 'text-gray-400'
                            }`} />
                            <p className="text-gray-500 text-sm font-medium">
                              {isLoading ? '获取数据中...' : '暂无巡检任务日志数据'}
                            </p>
                            {!isLoading && (
                              <p className="text-gray-400 text-xs mt-1">
                                数据已自动获取，如需最新数据请点击"刷新数据"按钮
                              </p>
                            )}
                          </div>
                        </td>
                      </tr>
                    ) : (
                      paginatedLogs.map((log, index) => (
                        <tr
                          key={log.id}
                          data-row-id={log.id}
                          className="adaptive-table-row hover:bg-gray-50 transition-colors duration-150"
                        >
                          {/* 操作类型 */}
                          <td className="table-cell whitespace-nowrap w-24">
                            <div className="flex items-center">
                              <FileText className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                              <span className="text-xs text-gray-700">
                                {log.log_type_text || '未知操作'}
                              </span>
                            </div>
                          </td>

                          {/* 任务ID */}
                          <td className="table-cell whitespace-nowrap w-20">
                            <span className="text-xs text-gray-700">
                              {(() => {
                                // 优先从new_data获取，如果没有再从original_data获取
                                const newData = log.new_data || {};
                                const originalData = log.original_data || {};
                                return newData.task_id || originalData.task_id || '-';
                              })()}
                            </span>
                          </td>

                          {/* 任务名称 */}
                          <td className="table-cell w-32">
                            {(() => {
                              const newData = log.new_data || {};
                              const originalData = log.original_data || {};
                              const taskName = newData.task_name || originalData.task_name || '-';
                              return (
                                <div className="truncate" title={taskName}>
                                  <span className="text-xs text-gray-700">{taskName}</span>
                                </div>
                              );
                            })()}
                          </td>

                          {/* 任务描述 */}
                          <td className="table-cell w-40">
                            {(() => {
                              const newData = log.new_data || {};
                              const originalData = log.original_data || {};
                              const taskDesc = newData.task_description || originalData.task_description || '-';
                              return (
                                <div className="truncate" title={taskDesc}>
                                  <span className="text-xs text-gray-700">{taskDesc}</span>
                                </div>
                              );
                            })()}
                          </td>

                          {/* 责任人 */}
                          <td className="table-cell whitespace-nowrap w-24">
                            <div className="flex items-center">
                              <User className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                              <span className="text-xs text-gray-700">
                                {(() => {
                                  const newData = log.new_data || {};
                                  const originalData = log.original_data || {};
                                  const personName = newData.person_name || originalData.person_name || '-';
                                  const personAlias = newData.person_alias || originalData.person_alias || '';
                                  return personName === '-' ? '-' : personName + (personAlias ? ` (${personAlias})` : '');
                                })()}
                              </span>
                            </div>
                          </td>

                          {/* 巡检部门 */}
                          <td className="table-cell w-40">
                            {(() => {
                              const departments = extractDepartments(log);

                              if (departments.length === 0) {
                                return (
                                  <div className="flex items-center">
                                    <Building className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                                    <span className="text-xs text-gray-400">-</span>
                                  </div>
                                );
                              }

                              return (
                                <DepartmentTooltip departments={departments}>
                                  <div className="flex items-center">
                                    <Building className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                                    <span className="inline-block text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded truncate max-w-32" title={departments[0]}>
                                      {departments[0]}
                                    </span>
                                    {departments.length > 1 && (
                                      <span className="ml-1 text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded">
                                        +{departments.length - 1}
                                      </span>
                                    )}
                                  </div>
                                </DepartmentTooltip>
                              );
                            })()}
                          </td>

                          {/* 操作时间 */}
                          <td className="table-cell whitespace-nowrap w-32">
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                              <span className="text-xs text-gray-700" title={formatTimestamp(log.timestamp)}>
                                {formatTimestamp(log.timestamp)}
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CustomScrollbar>
          </div>
        </div>

        {/* 分页组件 */}
        {filteredLogs.length > 0 && (
          <div className="flex-shrink-0 flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-200">
            <div className="flex items-center text-sm text-gray-600">
              <span className="mr-2">每页显示</span>
              <div className="relative inline-block mx-1" style={{ width: '65px' }} ref={pageSizeDropdownRef}>
                <div
                  className="bg-white border border-slate-300 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer relative"
                  style={{ padding: '3px 24px 3px 6px', fontSize: '14px', textAlign: 'center', height: '28px', lineHeight: '22px' }}
                  onClick={() => setShowPageSizeDropdown(!showPageSizeDropdown)}
                >
                  <span className="font-medium text-slate-700">
                    {pageSize === -1 ? '全部' : pageSize}
                  </span>
                  <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                </div>
                {showPageSizeDropdown && (
                  <div className="absolute bottom-full mb-1 left-0 bg-white border border-slate-300 rounded shadow-lg z-50" style={{ minWidth: '65px' }}>
                    {[10, 20, 50, 100, -1].map((size) => (
                      <div
                        key={size}
                        className="px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 cursor-pointer text-center"
                        onClick={() => {
                          setPageSize(size);
                          setShowPageSizeDropdown(false);
                        }}
                      >
                        {size === -1 ? '全部' : size}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <span className="ml-2">条，共 {filteredLogs.length} 条</span>
            </div>

            {pageSize !== -1 && totalPages > 1 && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setPage(Math.max(0, currentPage - 1))}
                  disabled={currentPage === 0}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                <span className="text-sm text-gray-600">
                  第 {currentPage + 1} 页，共 {totalPages} 页
                </span>
                <button
                  onClick={() => setPage(Math.min(totalPages - 1, currentPage + 1))}
                  disabled={currentPage === totalPages - 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default InspectionTaskLog;
