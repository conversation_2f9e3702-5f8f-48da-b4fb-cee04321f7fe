# 时间选择器格式统一更新

## 📋 更新概述

将添加设备台账中的时间选择器显示格式统一为`YYYY-MM-DD HH:mm`格式，使用`-`和`:`分隔符，替代原来的中文`年月日`格式。

## 🎯 修改内容

### **1. 扩展字段时间选择器**
- ✅ **升级为巡检管理风格**：支持日期+时间选择
- ✅ **显示格式统一**：`2024-01-15 14:30`
- ✅ **功能增强**：可同时选择日期和时间

### **2. 基础字段时间选择器**
- ✅ **保持原有功能**：只选择日期
- ✅ **显示格式统一**：`2024-01-15`
- ✅ **API兼容性**：适配原有的事件处理机制

## 🔧 技术实现

### **核心修改文件**
1. **`src/components/Common/DatePicker.tsx`** - 时间选择器组件
2. **`src/pages/Inventory/InventoryManagement/Dialogs/InventoryForm.tsx`** - 表单集成

### **关键代码修改**

#### **显示格式统一**
```typescript
// 修改前（中文格式）
const baseStr = `${year}年${month}月${day}日`;

// 修改后（标准格式）
const baseStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
```

#### **扩展字段时间选择器**
```typescript
<DatePicker
  value={dateValue}
  onChange={(date) => {
    onChange(field.key, date);
  }}
  placeholder={`请选择${field.title}`}
  disabled={disabled}
  showTime={true}        // 启用时间选择
  className="text-sm"
/>
```

#### **基础字段兼容性适配**
```typescript
<DatePicker
  value={formData.startTime}
  onChange={(date) => {
    // 创建合成事件对象以兼容原有的handleInputChange
    const syntheticEvent = {
      target: { name: 'startTime', value: date }
    } as React.ChangeEvent<HTMLInputElement>;
    handleInputChange(syntheticEvent);
  }}
  placeholder="请选择启用时间"
  error={!!errors.startTime}
  disabled={disabled}
  // 注意：没有showTime，只选择日期
/>
```

## 📱 用户界面变化

### **扩展字段（升级后）**
```
┌─────────────────────────────┐
│ 创建时间                   │
├─────────────────────────────┤
│ 2024-01-15 14:30      ▼   │  ← 日期+时间
└─────────────────────────────┘
```

### **基础字段（格式统一）**
```
┌─────────────────────────────┐
│ 启用时间                   │
├─────────────────────────────┤
│ 2024-01-15            ▼   │  ← 只有日期，格式统一
└─────────────────────────────┘
```

## 🎨 格式规范

### **日期格式**
- **标准格式**：`YYYY-MM-DD`
- **示例**：`2024-01-15`
- **分隔符**：使用`-`连接年月日

### **时间格式**
- **标准格式**：`HH:mm`
- **示例**：`14:30`
- **分隔符**：使用`:`连接小时和分钟

### **日期时间组合**
- **完整格式**：`YYYY-MM-DD HH:mm`
- **示例**：`2024-01-15 14:30`
- **分隔符**：日期和时间之间使用空格

## 🔍 功能对比

| 字段类型 | 选择器类型 | 显示格式 | 功能 |
|---------|-----------|---------|------|
| 扩展字段 | 新时间选择器 | `2024-01-15 14:30` | 日期+时间 |
| 基础字段 | 新时间选择器 | `2024-01-15` | 仅日期 |

## 🧪 测试验证

### **扩展字段测试**
1. ✅ 创建包含时间字段的扩展字段
2. ✅ 验证时间选择器显示格式为`YYYY-MM-DD HH:mm`
3. ✅ 确认可以同时选择日期和时间
4. ✅ 验证保存的数据格式正确

### **基础字段测试**
1. ✅ 验证启用时间字段显示格式为`YYYY-MM-DD`
2. ✅ 确认只能选择日期，不显示时间选择
3. ✅ 验证原有功能不受影响
4. ✅ 确认数据兼容性

### **格式一致性测试**
1. ✅ 所有时间选择器使用统一的`-`和`:`分隔符
2. ✅ 不再使用中文`年月日`格式
3. ✅ 表格显示和输入格式保持一致

## 📝 维护说明

### **关键组件**
- **DatePicker组件**：`src/components/Common/DatePicker.tsx`
- **格式化函数**：`formatDate()` 方法
- **显示逻辑**：`forDisplay` 参数控制

### **注意事项**
- 确保新旧数据格式兼容
- 维护API接口的一致性
- 保持与后端数据格式的同步

### **扩展性**
- 支持国际化格式配置
- 可添加更多时间格式选项
- 支持自定义分隔符配置
