import { useState, useCallback } from 'react';
import { ValidationResult, validateField } from '../../utils/fieldValidation';

/**
 * 字段验证配置
 */
export interface FieldValidationConfig {
  [fieldName: string]: string; // fieldName -> validationType
}

/**
 * 字段错误状态
 */
export interface FieldErrors {
  [fieldName: string]: string | undefined;
}

/**
 * 通用字段验证Hook
 * 提供统一的实时验证状态管理
 */
export function useFieldValidation(config: FieldValidationConfig) {
  const [fieldErrors, setFieldErrors] = useState<FieldErrors>({});

  // 验证单个字段
  const validateSingleField = useCallback((fieldName: string, value: string): ValidationResult => {
    const validationType = config[fieldName];
    if (!validationType) {
      return { isValid: true };
    }
    
    return validateField(validationType, value);
  }, [config]);

  // 设置字段错误
  const setFieldError = useCallback((fieldName: string, error: string | undefined) => {
    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: error
    }));
  }, []);

  // 验证字段并设置错误状态
  const validateAndSetError = useCallback((fieldName: string, value: string) => {
    const result = validateSingleField(fieldName, value);
    setFieldError(fieldName, result.isValid ? undefined : result.error);
    return result;
  }, [validateSingleField, setFieldError]);

  // 验证所有字段
  const validateAllFields = useCallback((values: Record<string, string>): boolean => {
    let isValid = true;
    const newErrors: FieldErrors = {};

    Object.keys(config).forEach(fieldName => {
      const value = values[fieldName] || '';
      const result = validateSingleField(fieldName, value);
      if (!result.isValid) {
        newErrors[fieldName] = result.error;
        isValid = false;
      }
    });

    setFieldErrors(newErrors);
    return isValid;
  }, [config, validateSingleField]);

  // 清除所有错误
  const clearErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  // 清除特定字段错误
  const clearFieldError = useCallback((fieldName: string) => {
    setFieldError(fieldName, undefined);
  }, [setFieldError]);

  // 检查是否有错误
  const hasErrors = Object.values(fieldErrors).some(error => !!error);

  return {
    fieldErrors,
    validateAndSetError,
    validateAllFields,
    clearErrors,
    clearFieldError,
    hasErrors,
    setFieldError
  };
}
