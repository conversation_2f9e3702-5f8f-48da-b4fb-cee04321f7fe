import { BaseService, BaseServiceState, BaseServiceEvents } from '../../base/baseService';
import { logError } from '../../../utils/logger';
import InventoryService from '../inventoryService';

// 导入设备服务状态
interface ImportDeviceServiceState extends BaseServiceState {
  isImporting: boolean;
  lastImportResult: ImportResult | null;
  error?: string;
}

// 导入结果类型
export interface ImportResult {
  success: boolean;
  success_count?: number;
  fail_count?: number;
  failure_messages?: string[];
  rawData?: any; // 保存原始返回数据，用于调试和额外信息提取
}

// 导入设备服务事件
interface ImportDeviceServiceEvents extends BaseServiceEvents<ImportDeviceServiceState> {
  'import-started': void;
  'import-completed': ImportResult;
  'import-failed': { error: string };
}

/**
 * 导入设备服务
 * 用于处理设备台账的导入功能
 */
export class ImportDeviceService extends BaseService<ImportDeviceServiceState, ImportDeviceServiceEvents> {
  private static instance: ImportDeviceService;

  /**
   * 获取单例实例
   */
  public static getInstance(): ImportDeviceService {
    if (!ImportDeviceService.instance) {
      ImportDeviceService.instance = new ImportDeviceService();
    }
    return ImportDeviceService.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    super('AccountTableDll', {
      isImporting: false,
      lastImportResult: null
    });
  }

  /**
   * 导入设备台账数据
   * 发送导入请求到后端，后端会自动弹出文件选择对话框
   */
  public async importDevicesFromExcel(): Promise<ImportResult> {
    try {
      // 更新状态为导入中
      this.updateState({ isImporting: true, error: undefined });

      // 触发导入开始事件
      this.emitter.emit('import-started');

      // 发送导入请求
      const taskId = await this.task.submitTask(
        this.dllName,
        'DbFun',
        { action: 'import_devices_from_excel' }
      );

      // 等待任务完成
      const result = await new Promise<ImportResult>((resolve, reject) => {
        const handleTaskUpdate = (taskInfo: any) => {
          if (taskInfo.status === 'completed') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);

            // 解析结果
            try {
              // 后端返回的结果可能是字符串形式的JSON或对象
              let importResult: ImportResult;
              let rawResult = taskInfo.result;

              console.log('原始导入结果:', rawResult);

              if (typeof rawResult === 'string') {
                try {
                  // 尝试解析JSON字符串
                  rawResult = JSON.parse(rawResult);
                } catch (e) {
                  // 如果解析失败，使用原始字符串
                  console.warn('无法解析导入结果JSON:', rawResult);
                }
              }

              // 处理后端返回的格式 {data: {...}, success: true}
              if (rawResult && typeof rawResult === 'object') {
                if (rawResult.success === true) {
                  // 成功情况
                  let successCount = 0;
                  let failCount = 0;
                  let failureMessages: string[] = [];

                  // 尝试从data中提取导入数量
                  if (rawResult.data) {
                    // 尝试从data中提取success_count和fail_count
                    if (rawResult.data.success_count !== undefined) {
                      successCount = rawResult.data.success_count;
                    }
                    if (rawResult.data.fail_count !== undefined) {
                      failCount = rawResult.data.fail_count;
                    }

                    // 如果data中有imported_count字段
                    if (rawResult.data.imported_count !== undefined) {
                      successCount = rawResult.data.imported_count;
                    }

                    // 如果data是一个数组，可能表示导入的记录
                    if (Array.isArray(rawResult.data)) {
                      successCount = rawResult.data.length;
                    }

                    // 尝试提取失败原因
                    if (rawResult.data.failure_messages && Array.isArray(rawResult.data.failure_messages)) {
                      failureMessages = rawResult.data.failure_messages;
                    } else if (rawResult.data.errors && Array.isArray(rawResult.data.errors)) {
                      failureMessages = rawResult.data.errors;
                    } else if (rawResult.data.error_messages && Array.isArray(rawResult.data.error_messages)) {
                      failureMessages = rawResult.data.error_messages;
                    } else if (rawResult.data.error) {
                      failureMessages = [rawResult.data.error];
                    } else if (rawResult.data.message) {
                      failureMessages = [rawResult.data.message];
                    }

                    // 如果有失败记录但没有失败消息，添加一个通用消息
                    if (failCount > 0 && failureMessages.length === 0) {
                      failureMessages = [`导入过程中有 ${failCount} 条记录失败，可能是数据格式不正确或与现有数据冲突`];
                    }
                  }

                  importResult = {
                    success: true,
                    success_count: successCount,
                    fail_count: failCount,
                    failure_messages: failureMessages,
                    rawData: rawResult // 保存原始数据
                  };
                } else {
                  // 失败情况
                  let errorMessages: string[] = [];

                  // 尝试从结果中提取错误信息
                  if (rawResult.message) {
                    errorMessages.push(rawResult.message);
                  }
                  if (rawResult.error) {
                    errorMessages.push(rawResult.error);
                  }
                  if (rawResult.data && rawResult.data.error) {
                    errorMessages.push(rawResult.data.error);
                  }
                  if (rawResult.data && rawResult.data.message) {
                    errorMessages.push(rawResult.data.message);
                  }
                  if (rawResult.data && Array.isArray(rawResult.data.errors)) {
                    errorMessages = [...errorMessages, ...rawResult.data.errors];
                  }

                  importResult = {
                    success: false,
                    success_count: 0,
                    fail_count: 0,
                    failure_messages: errorMessages.length > 0 ? errorMessages : ['导入失败'],
                    rawData: rawResult // 保存原始数据
                  };
                }
              } else {
                // 如果结果格式不符合预期，使用默认值
                importResult = {
                  success: true,
                  success_count: 0,
                  fail_count: 0,
                  failure_messages: [],
                  rawData: rawResult // 保存原始数据
                };
              }

              resolve(importResult);
            } catch (error) {
              reject(new Error('解析导入结果失败'));
            }
          } else if (taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
            this.task.offTaskUpdate(taskId, handleTaskUpdate);

            // 如果是取消状态，使用友好的中文提示
            if (taskInfo.status === 'cancelled') {
              reject(new Error('用户已取消导入'));
              return;
            }

            // 尝试从错误中提取有用信息
            if (taskInfo.result) {
              try {
                // 尝试解析结果，即使任务失败
                let importResult: ImportResult;

                if (typeof taskInfo.result === 'string') {
                  try {
                    importResult = JSON.parse(taskInfo.result);
                    // 如果解析成功且包含必要信息，返回结果
                    if (importResult && (importResult.success !== undefined)) {
                      resolve(importResult);
                      return;
                    }
                  } catch (e) {
                    // 解析失败，继续使用错误信息
                  }
                } else if (typeof taskInfo.result === 'object' && taskInfo.result !== null) {
                  // 如果结果已经是对象，直接使用
                  importResult = taskInfo.result;
                  if (importResult && (importResult.success !== undefined)) {
                    resolve(importResult);
                    return;
                  }
                }
              } catch (e) {
                // 忽略解析错误
              }
            }

            // 如果无法从结果中提取信息，使用错误信息
            reject(new Error(taskInfo.error || '导入失败'));
          }
        };

        this.task.onTaskUpdate(taskId, handleTaskUpdate);
      });

      // 更新状态
      this.updateState({
        isImporting: false,
        lastImportResult: result
      });

      // 触发导入完成事件
      this.emitter.emit('import-completed', result);

      // 重置库存服务中的空数据库标志
      try {
        // 获取库存服务实例
        const inventoryService = InventoryService.getInstance();

        // 使用反射访问私有属性（这是一个技巧，通常不推荐在生产代码中使用）
        if (inventoryService) {
          console.log('重置库存服务的空数据库标志');
          // @ts-ignore - 访问私有属性
          if (typeof inventoryService.isEmptyDatabase !== 'undefined') {
            // @ts-ignore - 修改私有属性
            inventoryService.isEmptyDatabase = false;
            console.log('成功重置库存服务的空数据库标志');
          }
        }
      } catch (error) {
        console.error('重置库存服务的空数据库标志失败:', error);
        // 失败不影响导入功能
      }

      return result;
    } catch (error: any) {
      // 更新状态
      this.updateState({
        isImporting: false,
        error: error.message
      });

      // 触发导入失败事件
      this.emitter.emit('import-failed', { error: error.message });

      // 记录错误
      logError(error, 'ImportDeviceService.importDevicesFromExcel');

      throw error;
    }
  }
}

export default ImportDeviceService;
