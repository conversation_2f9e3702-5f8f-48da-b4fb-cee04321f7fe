import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface Option {
  id: string | number;
  value: string;
  label: string;
}

interface FilterableSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
  disabled?: boolean;
  label?: string;
  isRequired?: boolean;
}

const FilterableSelect: React.FC<FilterableSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择...',
  className = '',
  required = false,
  disabled = false,
  label,
  isRequired = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState('');
  const [selectedLabel, setSelectedLabel] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownListRef = useRef<HTMLDivElement>(null);

  // 下拉框位置状态
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    maxHeight: 240
  });

  // 过滤选项
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(filter.toLowerCase())
  );

  // 当选项或值变化时，更新选中的标签
  useEffect(() => {
    const selectedOption = options.find(option => option.value === value);
    setSelectedLabel(selectedOption ? selectedOption.label : '');
  }, [options, value]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理选项选择
  const handleSelect = (optionValue: string, optionLabel: string) => {
    onChange(optionValue);
    setSelectedLabel(optionLabel);
    setFilter('');
    setIsOpen(false);
  };

  // 计算下拉框位置
  const calculateDropdownPosition = () => {
    if (!dropdownRef.current) return;

    const rect = dropdownRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;

    // 计算最大高度
    const maxDropdownHeight = 240;
    let maxHeight = maxDropdownHeight;
    let top = rect.bottom + window.scrollY;

    // 如果下方空间不足，考虑向上展开
    if (spaceBelow < maxDropdownHeight && spaceAbove > spaceBelow) {
      maxHeight = Math.min(maxDropdownHeight, spaceAbove - 10);
      top = rect.top + window.scrollY - maxHeight;
    } else {
      maxHeight = Math.min(maxDropdownHeight, spaceBelow - 10);
    }

    // 确保下拉框不会超出右边界
    let left = rect.left + window.scrollX;
    const dropdownWidth = rect.width;
    if (left + dropdownWidth > viewportWidth) {
      left = viewportWidth - dropdownWidth - 10;
    }

    setDropdownPosition({
      top,
      left,
      width: rect.width,
      maxHeight
    });
  };

  // 处理输入框点击
  const handleInputClick = () => {
    if (!disabled) {
      // 在打开下拉框之前计算位置
      if (!isOpen && dropdownRef.current) {
        calculateDropdownPosition();
      }
      setIsOpen(!isOpen);
      if (!isOpen && inputRef.current) {
        // 当打开下拉框时，聚焦输入框
        inputRef.current.focus();
      }
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(e.target.value);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    } else if (e.key === 'Enter' && isOpen && filteredOptions.length > 0) {
      e.preventDefault();
      handleSelect(filteredOptions[0].value, filteredOptions[0].label);
    } else if (e.key === 'ArrowDown' && isOpen) {
      e.preventDefault();
      // 可以实现键盘导航，这里简化处理
    }
  };

  // 清除选择
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
    setSelectedLabel('');
    setFilter('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          dropdownListRef.current && !dropdownListRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, true);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={dropdownRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label} {isRequired && <span className="text-red-500">*</span>}
        </label>
      )}
      <div
        className={`relative flex items-center w-full px-3 py-[7px] border border-gray-300 rounded-md focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500 ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer'
        } ${className}`}
        onClick={disabled ? undefined : handleInputClick}
      >
        <input
          ref={inputRef}
          type="text"
          value={isOpen ? filter : selectedLabel}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`w-full bg-transparent border-none focus:outline-none focus:ring-0 ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
          readOnly={!isOpen}
          disabled={disabled}
          required={required}
        />
        {(value || filter) && !disabled && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-8 text-gray-400 hover:text-gray-600"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          <svg
            className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {isOpen && createPortal(
        <div
          ref={dropdownListRef}
          className="fixed z-[9000] bg-white border border-gray-300 rounded-md shadow-lg overflow-auto"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: `${dropdownPosition.maxHeight}px`
          }}
        >
          {filteredOptions.length === 0 ? (
            <div className="px-4 py-2 text-sm text-gray-500">无匹配选项</div>
          ) : (
            filteredOptions.map(option => (
              <div
                key={option.id}
                className="px-4 py-2 text-sm hover:bg-blue-50 cursor-pointer"
                onClick={() => handleSelect(option.value, option.label)}
              >
                {option.label}
              </div>
            ))
          )}
        </div>,
        document.body
      )}
    </div>
  );
};

export default FilterableSelect;
