import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { DictionaryItem } from '../types/inventory';
import './customSelect.css';

interface ResponsiblePersonSelectProps {
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  options: DictionaryItem[];
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
}

/**
 * 责任人选择组件
 * 支持搜索筛选功能
 */
const ResponsiblePersonSelect: React.FC<ResponsiblePersonSelectProps> = ({
  name,
  value,
  onChange,
  options,
  placeholder = '请选择责任人',
  required = false,
  error,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState('');
  const [displayValue, setDisplayValue] = useState(
    () => options.find(opt => opt.code === value)?.value || placeholder || ''
  );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  // 当选项或值变化时，更新显示值
  useEffect(() => {
    const selectedOption = options.find(opt => opt.code === value);
    const newDisplayValue = selectedOption ? selectedOption.value : (placeholder || '');
    setDisplayValue(newDisplayValue);
  }, [value, options, placeholder]);

  // 过滤选项
  const filteredOptions = React.useMemo(() => {
    if (!filter) return options;

    return options.filter(option =>
      option.value.toLowerCase().includes(filter.toLowerCase())
    );
  }, [options, filter]);

  // 选择选项
  const selectOption = (option: DictionaryItem) => {
    setIsOpen(false);
    setFilter('');

    const syntheticEvent = {
      target: { name, value: option.code },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);
  };

  // 计算下拉菜单位置
  useEffect(() => {
    if (isOpen && selectRef.current) {
      const rect = selectRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  }, [isOpen]);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node) &&
          dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFilter('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理输入框点击
  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setFilter(newValue);

    // 确保下拉框打开
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  return (
    <div className="relative">
      <div
        ref={selectRef}
        className={`w-full px-3 py-1.5 border rounded-md flex justify-between items-center focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500
          ${error ? 'border-red-300' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white cursor-pointer'}`}
        onClick={disabled ? undefined : handleInputClick}
        style={{ height: '38px' }} /* 固定高度与原Select组件一致 */
      >
        {isOpen ? (
          <input
            ref={inputRef}
            type="text"
            value={filter}
            onChange={handleInputChange}
            placeholder={placeholder}
            className="w-full bg-transparent border-none text-gray-700 select-input-no-focus"
            style={{ height: '24px', caretColor: 'auto' }}
            disabled={disabled}
            autoFocus
          />
        ) : (
          <span className={!value ? 'text-gray-400' : 'text-gray-700'}>
            {displayValue}
          </span>
        )}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </div>

      {isOpen && !disabled && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[9000] max-h-60 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }}
        >
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option) => (
              <div
                key={option.code}
                className={`px-3 py-1.5 cursor-pointer hover:bg-blue-50 ${
                  option.code === value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                }`}
                onClick={() => selectOption(option)}
              >
                {option.value}
              </div>
            ))
          ) : (
            <div className="px-3 py-1.5 text-gray-500">无匹配选项</div>
          )}
        </div>,
        document.body
      )}

      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

export default ResponsiblePersonSelect;
