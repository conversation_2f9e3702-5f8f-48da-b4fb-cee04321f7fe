import { useEffect, useCallback, useRef } from 'react';

// 键盘事件处理器类型
type KeyHandler = (event: KeyboardEvent) => void;

// 键盘事件信息
interface KeyEventInfo {
  handler: KeyHandler;
  priority: number;
  id: string;
}

// 浏览器键盘兼容性
interface KeyboardCapabilities {
  supportsKeyboardEvents: boolean;
  supportsKeyProperty: boolean;
  supportsWhichProperty: boolean;
  supportsKeyCode: boolean;
  supportsPreventDefault: boolean;
  supportsStopPropagation: boolean;
}

// 增强的键盘事件管理器接口
interface KeyboardManager {
  // 注册ESC键处理器
  registerEscHandler: (handler: KeyHandler, priority?: number) => () => void;
  // 注册Tab键处理器
  registerTabHandler: (handler: KeyHandler, priority?: number) => () => void;
  // 注册Enter键处理器
  registerEnterHandler: (handler: KeyHandler, priority?: number) => () => void;
  // 注册任意键处理器
  registerKeyHandler: (key: string, handler: KeyHandler, priority?: number) => () => void;
  // 注册方向键处理器
  registerArrowHandler: (handler: KeyHandler, priority?: number) => () => void;
  // 注销处理器
  unregisterHandler: (key: string, handlerId: string) => void;
  unregisterAllHandlers: (key: string) => void;
  // 键盘事件模拟
  simulateKeyPress: (key: string, target?: HTMLElement) => void;
  // 浏览器兼容性信息
  capabilities: KeyboardCapabilities;
}

/**
 * 检测浏览器键盘事件支持能力
 */
function detectKeyboardCapabilities(): KeyboardCapabilities {
  try {
    const testEvent = new KeyboardEvent('keydown', { key: 'Tab' });

    return {
      supportsKeyboardEvents: typeof KeyboardEvent !== 'undefined',
      supportsKeyProperty: 'key' in testEvent,
      supportsWhichProperty: 'which' in testEvent,
      supportsKeyCode: 'keyCode' in testEvent,
      supportsPreventDefault: typeof testEvent.preventDefault === 'function',
      supportsStopPropagation: typeof testEvent.stopPropagation === 'function'
    };
  } catch (error) {
    // 降级处理
    return {
      supportsKeyboardEvents: false,
      supportsKeyProperty: false,
      supportsWhichProperty: false,
      supportsKeyCode: false,
      supportsPreventDefault: false,
      supportsStopPropagation: false
    };
  }
}

/**
 * 标准化键名（处理浏览器差异）
 */
function normalizeKey(event: KeyboardEvent): string {
  const capabilities = detectKeyboardCapabilities();

  // 优先使用现代的key属性
  if (capabilities.supportsKeyProperty && event.key) {
    return event.key;
  }

  // 降级到keyCode
  if (capabilities.supportsKeyCode && event.keyCode) {
    const keyCodeMap: { [key: number]: string } = {
      8: 'Backspace',
      9: 'Tab',
      13: 'Enter',
      27: 'Escape',
      32: ' ',
      37: 'ArrowLeft',
      38: 'ArrowUp',
      39: 'ArrowRight',
      40: 'ArrowDown',
      46: 'Delete'
    };

    return keyCodeMap[event.keyCode] || String.fromCharCode(event.keyCode);
  }

  // 最后降级到which属性
  if (capabilities.supportsWhichProperty && event.which) {
    return String.fromCharCode(event.which);
  }

  return 'Unknown';
}

/**
 * 全局键盘事件管理器Hook
 * 提供统一的键盘事件处理机制，支持跨浏览器兼容性
 */
export function useKeyboardManager(): KeyboardManager {
  // 使用ref存储处理器，避免因依赖变化导致的重复注册
  const handlersRef = useRef<Map<string, KeyEventInfo[]>>(new Map());
  const capabilitiesRef = useRef<KeyboardCapabilities | null>(null);
  const handlerIdCounterRef = useRef(0);

  // 初始化浏览器能力检测
  useEffect(() => {
    capabilitiesRef.current = detectKeyboardCapabilities();
    console.log('浏览器键盘事件能力:', capabilitiesRef.current);
  }, []);

  // 生成唯一处理器ID
  const generateHandlerId = useCallback((): string => {
    return `handler_${++handlerIdCounterRef.current}`;
  }, []);

  // 键盘导航模式状态
  const isKeyboardModeRef = useRef(false);
  const lastMouseActivityRef = useRef(Date.now());
  const MOUSE_ACTIVITY_TIMEOUT = 3000; // 3秒后重新启用键盘模式

  // 检查是否应该处理键盘事件
  const shouldProcessKeyboardEvent = useCallback((key: string): boolean => {
    // 重要键盘事件总是处理
    const importantKeys = ['Tab', 'Escape', 'Enter', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
    const isImportantKey = importantKeys.includes(key);

    // 如果是重要键，启用键盘模式
    if (isImportantKey) {
      isKeyboardModeRef.current = true;
      return true;
    }

    // 检查是否有注册的处理器
    const handlers = handlersRef.current.get(key) || [];
    if (handlers.length > 0) {
      return true;
    }

    // 如果在键盘模式下，处理更多键盘事件
    if (isKeyboardModeRef.current) {
      // 检查鼠标活动是否超时
      const timeSinceMouseActivity = Date.now() - lastMouseActivityRef.current;
      if (timeSinceMouseActivity > MOUSE_ACTIVITY_TIMEOUT) {
        return true;
      }
    }

    return false;
  }, []);

  // 全局键盘事件处理函数（优化版）
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const capabilities = capabilitiesRef.current;
    if (!capabilities?.supportsKeyboardEvents) return;

    const key = normalizeKey(event);

    // 智能过滤：只处理必要的键盘事件
    if (!shouldProcessKeyboardEvent(key)) {
      return;
    }

    // 只在开发环境或调试模式下输出日志
    if (process.env.NODE_ENV === 'development' || window.location.search.includes('debug=keyboard')) {
      console.log(`键盘事件: ${key}`, event);
    }

    // 重要键盘事件的默认处理
    const importantKeys = ['Tab', 'Escape', 'Enter', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
    const isImportantKey = importantKeys.includes(key);

    // 获取注册的处理器
    const handlers = handlersRef.current.get(key) || [];

    // 处理键盘事件
    if (isImportantKey || handlers.length > 0) {
      // 对于Tab键，总是阻止默认行为（防止打出空格）
      if (key === 'Tab') {
        event.preventDefault();
        if (process.env.NODE_ENV === 'development') {
          console.log('Tab键被拦截');
        }
      }

      // 对于Escape键，总是阻止默认行为
      if (key === 'Escape') {
        event.preventDefault();
        if (process.env.NODE_ENV === 'development') {
          console.log('Escape键被拦截');
        }
      }

      // 执行注册的处理器
      if (handlers.length > 0) {
        // 按优先级排序（高优先级先执行）
        const sortedHandlers = [...handlers].sort((a, b) => b.priority - a.priority);

        for (const { handler } of sortedHandlers) {
          try {
            handler(event);

            // 如果事件被阻止，停止后续处理
            if (event.defaultPrevented) break;
          } catch (error) {
            console.warn(`键盘事件处理器执行失败 (${key}):`, error);
          }
        }
      } else if (isImportantKey) {
        // 如果没有注册处理器但是重要键，执行默认行为
        handleDefaultKeyBehavior(event, key);
      }
    }
  }, []);

  // 鼠标活动检测
  const handleMouseActivity = useCallback(() => {
    lastMouseActivityRef.current = Date.now();
    // 鼠标活动时禁用键盘模式（除非用户明确使用键盘）
    if (isKeyboardModeRef.current) {
      isKeyboardModeRef.current = false;
      if (process.env.NODE_ENV === 'development') {
        console.log('🖱️ 鼠标活动检测，禁用键盘导航模式');
      }
    }
  }, []);

  // 默认键盘行为处理
  const handleDefaultKeyBehavior = useCallback((event: KeyboardEvent, key: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`执行默认键盘行为: ${key}`);
    }

    switch (key) {
      case 'Tab':
        // 默认Tab键焦点导航
        handleDefaultTabNavigation(event);
        break;
      case 'Escape':
        // 默认Escape键行为（关闭最顶层的对话框等）
        handleDefaultEscapeNavigation(event);
        break;
      case 'Enter':
        // 默认Enter键行为（激活聚焦元素）
        handleDefaultEnterNavigation(event);
        break;
      default:
        // 其他键不做默认处理
        break;
    }
  }, []);

  // 默认Tab键导航
  const handleDefaultTabNavigation = useCallback((event: KeyboardEvent) => {
    const activeElement = document.activeElement as HTMLElement;
    const focusableElements = Array.from(
      document.querySelectorAll(
        'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      )
    ) as HTMLElement[];

    if (focusableElements.length === 0) return;

    const currentIndex = focusableElements.indexOf(activeElement);
    let nextIndex: number;

    if (event.shiftKey) {
      // Shift+Tab: 向前导航
      nextIndex = currentIndex <= 0 ? focusableElements.length - 1 : currentIndex - 1;
    } else {
      // Tab: 向后导航
      nextIndex = currentIndex >= focusableElements.length - 1 ? 0 : currentIndex + 1;
    }

    const nextElement = focusableElements[nextIndex];
    if (nextElement) {
      nextElement.focus();
      if (process.env.NODE_ENV === 'development') {
        console.log('Tab导航到:', nextElement);
      }
    }
  }, []);

  // 默认Escape键导航
  const handleDefaultEscapeNavigation = useCallback((event: KeyboardEvent) => {
    // 查找最顶层的对话框或模态框
    const modals = document.querySelectorAll('[role="dialog"], .modal, .dialog');
    if (modals.length > 0) {
      const topModal = modals[modals.length - 1] as HTMLElement;
      const closeButton = topModal.querySelector('[data-close], .close, .btn-close') as HTMLElement;
      if (closeButton) {
        closeButton.click();
        if (process.env.NODE_ENV === 'development') {
          console.log('Escape关闭对话框');
        }
      }
    }
  }, []);

  // 默认Enter键导航
  const handleDefaultEnterNavigation = useCallback((event: KeyboardEvent) => {
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement) {
      const tagName = activeElement.tagName.toLowerCase();

      // 如果是按钮或链接，触发点击
      if (tagName === 'button' || tagName === 'a') {
        activeElement.click();
        if (process.env.NODE_ENV === 'development') {
          console.log('Enter激活元素:', activeElement);
        }
      }
      // 如果是表单元素，可能需要提交表单
      else if (tagName === 'input' && (activeElement as HTMLInputElement).type === 'submit') {
        activeElement.click();
      }
    }
  }, []);

  // 注册全局键盘和鼠标事件监听
  useEffect(() => {
    const capabilities = capabilitiesRef.current;
    if (!capabilities?.supportsKeyboardEvents) {
      console.warn('浏览器不支持键盘事件，键盘管理器将无法正常工作');
      return;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('注册键盘和鼠标事件监听器');
    }

    // 键盘事件监听
    document.addEventListener('keydown', handleKeyDown, true);
    window.addEventListener('keydown', handleKeyDown, true);

    // 鼠标活动监听（用于智能切换键盘/鼠标模式）
    const mouseEvents = ['mousedown', 'mousemove', 'click'];
    mouseEvents.forEach(eventType => {
      document.addEventListener(eventType, handleMouseActivity, true);
    });

    return () => {
      // 清理键盘事件监听器
      document.removeEventListener('keydown', handleKeyDown, true);
      window.removeEventListener('keydown', handleKeyDown, true);

      // 清理鼠标事件监听器
      mouseEvents.forEach(eventType => {
        document.removeEventListener(eventType, handleMouseActivity, true);
      });

      if (process.env.NODE_ENV === 'development') {
        console.log('注销键盘和鼠标事件监听器');
      }
    };
  }, [handleKeyDown, handleMouseActivity]);

  // 注册键盘事件处理器
  const registerKeyHandler = useCallback((
    key: string,
    handler: KeyHandler,
    priority: number = 50
  ): (() => void) => {
    const handlerId = generateHandlerId();
    const handlers = handlersRef.current.get(key) || [];

    handlers.push({ handler, priority, id: handlerId });
    handlersRef.current.set(key, handlers);

    // 返回注销函数
    return () => {
      const currentHandlers = handlersRef.current.get(key) || [];
      const filteredHandlers = currentHandlers.filter(h => h.id !== handlerId);

      if (filteredHandlers.length === 0) {
        handlersRef.current.delete(key);
      } else {
        handlersRef.current.set(key, filteredHandlers);
      }
    };
  }, [generateHandlerId]);

  // 注册ESC键处理器
  const registerEscHandler = useCallback((handler: KeyHandler, priority: number = 100) => {
    return registerKeyHandler('Escape', handler, priority);
  }, [registerKeyHandler]);

  // 注册Tab键处理器
  const registerTabHandler = useCallback((handler: KeyHandler, priority: number = 100) => {
    return registerKeyHandler('Tab', handler, priority);
  }, [registerKeyHandler]);

  // 注册Enter键处理器
  const registerEnterHandler = useCallback((handler: KeyHandler, priority: number = 50) => {
    return registerKeyHandler('Enter', handler, priority);
  }, [registerKeyHandler]);

  // 注册方向键处理器
  const registerArrowHandler = useCallback((handler: KeyHandler, priority: number = 50) => {
    const keys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
    const cleanupFunctions = keys.map(key => registerKeyHandler(key, handler, priority));

    // 返回统一的注销函数
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [registerKeyHandler]);

  // 注销指定处理器
  const unregisterHandler = useCallback((key: string, handlerId: string) => {
    const handlers = handlersRef.current.get(key) || [];
    const filteredHandlers = handlers.filter(h => h.id !== handlerId);

    if (filteredHandlers.length === 0) {
      handlersRef.current.delete(key);
    } else {
      handlersRef.current.set(key, filteredHandlers);
    }
  }, []);

  // 注销所有处理器
  const unregisterAllHandlers = useCallback((key: string) => {
    handlersRef.current.delete(key);
  }, []);

  // 模拟键盘事件
  const simulateKeyPress = useCallback((key: string, target: HTMLElement = document.body) => {
    const capabilities = capabilitiesRef.current;
    if (!capabilities?.supportsKeyboardEvents) {
      console.warn('浏览器不支持键盘事件模拟');
      return;
    }

    try {
      // 创建键盘事件
      const event = new KeyboardEvent('keydown', {
        key,
        bubbles: true,
        cancelable: true
      });

      // 分发事件
      target.dispatchEvent(event);
    } catch (error) {
      console.warn('键盘事件模拟失败:', error);

      // 降级处理：直接调用处理器
      const handlers = handlersRef.current.get(key) || [];
      const mockEvent = {
        key,
        preventDefault: () => {},
        stopPropagation: () => {},
        defaultPrevented: false
      } as KeyboardEvent;

      handlers.forEach(({ handler }) => {
        try {
          handler(mockEvent);
        } catch (handlerError) {
          console.warn('模拟事件处理器执行失败:', handlerError);
        }
      });
    }
  }, []);

  return {
    registerKeyHandler,
    registerEscHandler,
    registerTabHandler,
    registerEnterHandler,
    registerArrowHandler,
    unregisterHandler,
    unregisterAllHandlers,
    simulateKeyPress,
    capabilities: capabilitiesRef.current || {
      supportsKeyboardEvents: false,
      supportsKeyProperty: false,
      supportsWhichProperty: false,
      supportsKeyCode: false,
      supportsPreventDefault: false,
      supportsStopPropagation: false
    }
  };
}

// 创建全局单例实例
let keyboardManagerInstance: KeyboardManager | null = null;

/**
 * 获取全局键盘事件管理器实例
 * 确保整个应用只有一个键盘事件管理器实例
 */
export function getKeyboardManager(): KeyboardManager {
  if (!keyboardManagerInstance) {
    console.error('KeyboardManager未初始化，当前实例:', keyboardManagerInstance);
    console.error('调用栈:', new Error().stack);
    throw new Error('KeyboardManager未初始化，请在应用根组件中使用useKeyboardManager');
  }
  return keyboardManagerInstance;
}

/**
 * 初始化全局键盘事件管理器
 * 应在应用根组件中调用
 */
export function useInitKeyboardManager(): KeyboardManager {
  const manager = useKeyboardManager();

  useEffect(() => {
    // 立即设置实例，不等待useEffect
    keyboardManagerInstance = manager;

    return () => {
      // 只有在确实需要清理时才清空实例
      // 在StrictMode下，避免过早清理
      if (keyboardManagerInstance === manager) {
        keyboardManagerInstance = null;
      }
    };
  }, [manager]);

  // 确保在组件渲染时立即可用
  if (!keyboardManagerInstance) {
    keyboardManagerInstance = manager;
  }

  return manager;
}
