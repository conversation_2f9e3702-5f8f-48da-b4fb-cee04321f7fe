import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useDropdownPosition, getDropdownStyles } from '../../utils/dropdownPositionCalculator';
import { useClickOutsideMultiple } from '../../hooks/useClickOutside';

interface Option {
  value: string;
  label: string;
}

interface CustomDropdownSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

/**
 * 自定义下拉选择框组件 - 完全自绘，不依赖任何第三方组件
 * 兼容谷歌108m版本浏览器
 */
const CustomDropdownSelect: React.FC<CustomDropdownSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择',
  className = '',
  disabled = false
}) => {
  // 下拉框是否展开
  const [isOpen, setIsOpen] = useState(false);

  // 引用下拉框容器元素
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 获取当前选中的选项
  const selectedOption = options.find(option => option.value === value);

  // 处理选项点击事件
  const handleOptionClick = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  // 计算下拉框位置
  const calculateDropdownPosition = () => {
    if (!dropdownRef.current) return;

    const rect = dropdownRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    const spaceRight = viewportWidth - rect.left;

    // 计算最大高度
    const maxDropdownHeight = 240; // 最大下拉框高度
    let maxHeight = maxDropdownHeight;
    let top = rect.bottom + window.scrollY;
    let direction: 'up' | 'down' = 'down';

    // 如果下方空间不足，考虑向上展开
    if (spaceBelow < maxDropdownHeight && spaceAbove > spaceBelow) {
      direction = 'up';
      maxHeight = Math.min(maxDropdownHeight, spaceAbove - 10);
      top = rect.top + window.scrollY - maxHeight;
    } else {
      maxHeight = Math.min(maxDropdownHeight, spaceBelow - 10);
    }

    // 确保下拉框不会超出右边界
    let left = rect.left + window.scrollX;
    const dropdownWidth = rect.width;
    if (left + dropdownWidth > viewportWidth) {
      left = viewportWidth - dropdownWidth - 10;
    }

    setDropdownPosition({
      top,
      left,
      width: rect.width,
      maxHeight,
      direction
    });
  };

  // 处理下拉框切换事件
  const toggleDropdown = () => {
    if (!disabled) {
      // 在打开下拉框之前计算位置
      if (!isOpen && dropdownRef.current) {
        calculateDropdownPosition();
      }
      setIsOpen(!isOpen);
    }
  };

  // 下拉列表引用
  const dropdownListRef = useRef<HTMLDivElement>(null);

  // 使用统一的点击外部关闭Hook
  useClickOutsideMultiple([dropdownRef, dropdownListRef], () => setIsOpen(false), isOpen);

  // 下拉框位置状态
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    maxHeight: 200,
    direction: 'down' as 'up' | 'down'
  });

  // 处理窗口事件（滚动和resize）
  useEffect(() => {
    if (!isOpen) return;

    // 处理窗口大小变化事件
    const handleResize = () => {
      // 重新计算位置
      calculateDropdownPosition();
    };

    // 处理页面滚动事件（不包括下拉框内部滚动）
    const handleScroll = (event: Event) => {
      // 如果滚动事件来自下拉列表内部，则不关闭下拉框
      if (dropdownListRef.current && (
          dropdownListRef.current === event.target ||
          dropdownListRef.current.contains(event.target as Node)
      )) {
        return;
      }

      // 如果是页面其他部分的滚动，则重新计算位置
      calculateDropdownPosition();
    };

    // 添加事件监听器
    document.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    // 清理事件监听器
    return () => {
      document.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  return (
    <div
      ref={dropdownRef}
      className={`relative ${className}`}
    >
      {/* 下拉框触发器 - 与搜索框样式一致 */}
      <div
        className={`flex items-center justify-between w-full px-3 bg-white border border-gray-300 rounded-md ${
          isOpen ? 'focus:outline-none focus:ring-2 focus:ring-blue-500' : ''
        } cursor-pointer ${disabled ? 'opacity-60 cursor-not-allowed bg-gray-100' : ''} ${className}`}
        onClick={toggleDropdown}
        style={{ height: '38px', outline: 'none', boxShadow: 'none' }}
      >
        <span className={`block truncate text-sm ${selectedOption ? 'text-gray-700' : 'text-gray-400'}`}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <span className="flex items-center ml-2">
          <svg
            className="w-4 h-4 text-gray-500"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </span>
      </div>

      {/* 下拉选项列表 - 使用Portal渲染到body，智能定位 */}
      {isOpen && createPortal(
        <div
          ref={dropdownListRef}
          className="fixed z-[9000] bg-white border border-gray-300 rounded-md shadow-lg overflow-auto"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: `${dropdownPosition.maxHeight}px`,
            minWidth: `${dropdownPosition.width}px`
          }}
        >
          <ul className="py-1">
            {options.map((option) => (
              <li
                key={option.value}
                className={`px-3 py-2 cursor-pointer text-sm hover:bg-gray-100 ${
                  option.value === value
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700'
                }`}
                onClick={() => handleOptionClick(option.value)}
              >
                {option.label}
              </li>
            ))}
          </ul>
        </div>,
        document.body
      )}
    </div>
  );
};

export default CustomDropdownSelect;
