import { InventoryItem, DeviceCategory } from '../../../types/inventory';
import InventoryCategoryService from './inventoryCategoryService';

/**
 * 库存筛选服务
 * 专门负责处理设备筛选和搜索
 */
export class InventoryFilterService {
  private static instance: InventoryFilterService;

  // 常量定义
  private static readonly CATEGORY_ID_PREFIXES = {
    ALL: 'all',
    PARENT: 'parent-',
    SEPARATOR: '-'
  } as const;

  private static readonly MATCH_TYPES = {
    PARENT_ID: 'parentId匹配',
    SUB_ID: 'subId匹配',
    PARENT_NAME: 'parentName匹配',
    SUB_NAME: 'subName匹配',
    ID_MATCH: 'ID匹配',
    NAME_MATCH: '名称匹配',
    TYPE_MATCH: '类型匹配',
    NO_MATCH: '无匹配'
  } as const;

  // 上次筛选的分类ID
  private lastFilteredCategoryId: string | null = null;
  // 上次筛选的结果
  private lastFilteredResult: InventoryItem[] = [];

  private constructor() {}

  public static getInstance(): InventoryFilterService {
    if (!InventoryFilterService.instance) {
      InventoryFilterService.instance = new InventoryFilterService();
    }
    return InventoryFilterService.instance;
  }

  /**
   * 清除筛选缓存
   */
  public clearFilterCache(): void {
    this.lastFilteredCategoryId = null;
    this.lastFilteredResult = [];
  }



  /**
   * 清除特定分类的筛选缓存
   * @param categoryName 分类名称
   */
  public clearCategoryCache(categoryName: string): void {
    console.log(`尝试清除分类 "${categoryName}" 的筛选缓存，当前缓存ID: ${this.lastFilteredCategoryId}`);

    // 由于分类变更会影响多个分类的筛选结果，直接清除所有缓存
    // 这样可以确保所有相关的筛选结果都会重新计算
    if (this.lastFilteredCategoryId) {
      console.log(`强制清除所有筛选缓存，因为分类 "${categoryName}" 发生了变更`);
      this.clearFilterCache();
    }
  }

  /**
   * 根据分类筛选设备列表
   * @param categoryId 分类ID
   * @param inventoryList 设备列表
   * @param deviceCategories 设备分类树
   * @param forceRefresh 是否强制刷新
   */
  public filterByCategory(
    categoryId: string, 
    inventoryList: InventoryItem[], 
    deviceCategories: DeviceCategory[], 
    forceRefresh: boolean = false
  ): InventoryItem[] {
    // 如果强制刷新，清除缓存
    if (forceRefresh) {
      console.log('强制刷新筛选结果，清除缓存');
      this.clearFilterCache();
    }

    // 如果与上次筛选的分类ID相同且不是强制刷新，直接返回缓存的结果
    if (!forceRefresh && this.lastFilteredCategoryId === categoryId && this.lastFilteredResult.length > 0) {
      console.log('使用缓存的筛选结果，分类ID:', categoryId);
      return this.lastFilteredResult;
    }

    // 全部设备不筛选
    if (categoryId === InventoryFilterService.CATEGORY_ID_PREFIXES.ALL) {
      this.lastFilteredCategoryId = categoryId;
      this.lastFilteredResult = inventoryList;
      return inventoryList;
    }

    const categoryService = InventoryCategoryService.getInstance();

    // 查找当前选择的分类
    const selectedCategory = categoryService.findCategory(deviceCategories, categoryId);
    if (!selectedCategory) {
      console.warn('未找到分类:', categoryId, '，可能已被删除，重置为全部设备');

      // 分类不存在时，重置为全部设备，避免无限循环
      this.lastFilteredCategoryId = InventoryFilterService.CATEGORY_ID_PREFIXES.ALL;
      this.lastFilteredResult = inventoryList;

      return inventoryList;
    }

    console.log('已选择分类:', selectedCategory);

    // 获取该分类下所有子分类ID
    const categoryIds = categoryService.getAllChildCategoryIds(selectedCategory);
    console.log('包含的所有分类ID:', categoryIds);

    let result: InventoryItem[] = [];

    // 如果是一级分类（大类）
    if (selectedCategory.children?.length) {
      result = this.filterByParentCategory(selectedCategory, inventoryList);
    }
    // 如果是二级分类（具体设备类型）
    else if (selectedCategory.id.includes('-')) {
      result = this.filterBySubCategory(selectedCategory, inventoryList, deviceCategories);
    }
    else {
      // 兼容旧的分类格式
      result = this.filterByLegacyCategory(selectedCategory, inventoryList);
    }

    // 更新缓存
    this.lastFilteredCategoryId = categoryId;
    this.lastFilteredResult = result;

    return result;
  }

  /**
   * 按一级分类筛选
   * @param selectedCategory 选中的分类
   * @param inventoryList 设备列表
   */
  private filterByParentCategory(selectedCategory: DeviceCategory, inventoryList: InventoryItem[]): InventoryItem[] {
    // 检查分类 ID 格式，判断是否是从数据库获取的分类
    if (selectedCategory.id.startsWith(InventoryFilterService.CATEGORY_ID_PREFIXES.PARENT)) {
      return this.filterByDatabaseParentCategory(selectedCategory, inventoryList);
    } else {
      return this.filterByLegacyParentCategory(selectedCategory, inventoryList);
    }
  }

  /**
   * 按数据库一级分类筛选
   */
  private filterByDatabaseParentCategory(selectedCategory: DeviceCategory, inventoryList: InventoryItem[]): InventoryItem[] {
    const parentId = selectedCategory.id.split(InventoryFilterService.CATEGORY_ID_PREFIXES.SEPARATOR)[1];
    console.log('按一级分类筛选，父类 ID:', parentId, '名称:', selectedCategory.name);

    // 获取该一级分类下的所有二级分类信息
    const { subCategoryIds, subCategoryNames } = this.extractSubCategoryInfo(selectedCategory);

    return inventoryList.filter(item => {
      const matchResult = this.matchItemToParentCategory(item, parentId, selectedCategory.name, subCategoryIds, subCategoryNames);

      if (matchResult.shouldInclude) {
        this.logMatchedItem(item, matchResult.matchType);
      }

      return matchResult.shouldInclude;
    });
  }

  /**
   * 按兼容模式一级分类筛选
   */
  private filterByLegacyParentCategory(selectedCategory: DeviceCategory, inventoryList: InventoryItem[]): InventoryItem[] {
    const categoryIds = [selectedCategory.id];
    if (selectedCategory.children?.length) {
      selectedCategory.children.forEach(child => {
        categoryIds.push(child.id);
      });
    }

    return inventoryList.filter(item => {
      // 当前分类或其子分类下的所有设备
      const categoryMatch = (item.parentCategory && categoryIds.includes(item.parentCategory)) ||
                           (item.parentCategoryId && categoryIds.includes(item.parentCategoryId.toString()));

      // 检查设备的类型是否匹配当前分类的子分类
      const typeMatch = item.type && selectedCategory.children?.some(child => child.name === item.type);

      return categoryMatch || typeMatch;
    });
  }

  /**
   * 提取子分类信息
   */
  private extractSubCategoryInfo(selectedCategory: DeviceCategory): { subCategoryIds: string[], subCategoryNames: string[] } {
    const subCategoryIds: string[] = [];
    const subCategoryNames: string[] = [];

    selectedCategory.children?.forEach((subCategory: any) => {
      subCategoryNames.push(subCategory.name);

      if (subCategory.id.includes('-')) {
        const parts = subCategory.id.split('-');
        if (parts.length >= 3) {
          const subId = parts[parts.length - 1];
          subCategoryIds.push(subId);
        }
      }
    });

    console.log('该一级分类下的二级分类 IDs:', subCategoryIds);
    console.log('该一级分类下的二级分类名称:', subCategoryNames);

    return { subCategoryIds, subCategoryNames };
  }

  /**
   * 匹配设备到一级分类
   */
  private matchItemToParentCategory(
    item: InventoryItem,
    parentId: string,
    parentName: string,
    subCategoryIds: string[],
    subCategoryNames: string[]
  ): { shouldInclude: boolean, matchType: string } {
    // 检查各种匹配条件
    const itemParentId = item.parentCategoryId?.toString();
    const parentIdMatch = itemParentId === parentId;
    const parentNameMatch = item.parentCategory === parentName;
    const itemSubId = item.subCategoryId?.toString();
    const subIdMatch = itemSubId && subCategoryIds.includes(itemSubId);
    const subNameMatch = item.type && subCategoryNames.includes(item.type);

    // 优先使用ID匹配
    if (parentIdMatch || subIdMatch) {
      return {
        shouldInclude: true,
        matchType: parentIdMatch ? InventoryFilterService.MATCH_TYPES.PARENT_ID : InventoryFilterService.MATCH_TYPES.SUB_ID
      };
    }

    // 使用名称匹配
    if (parentNameMatch || subNameMatch) {
      if (parentNameMatch) {
        // 如果设备有二级分类，检查该二级分类是否属于该一级分类
        const shouldInclude = item.type ? subCategoryNames.includes(item.type) : true;
        return { shouldInclude, matchType: InventoryFilterService.MATCH_TYPES.PARENT_NAME };
      } else if (subNameMatch) {
        // 如果设备有一级分类，检查是否与当前选择的一级分类一致
        const shouldInclude = item.parentCategory ? item.parentCategory === parentName : true;
        return { shouldInclude, matchType: InventoryFilterService.MATCH_TYPES.SUB_NAME };
      }
    }

    return { shouldInclude: false, matchType: InventoryFilterService.MATCH_TYPES.NO_MATCH };
  }

  /**
   * 记录匹配到的设备信息
   */
  private logMatchedItem(item: InventoryItem, matchType: string): void {
    console.log('匹配到设备:', {
      id: item.id,
      type: item.type,
      parentCategory: item.parentCategory,
      subCategoryId: item.subCategoryId,
      parentCategoryId: item.parentCategoryId,
      matchType
    });
  }

  /**
   * 按二级分类筛选
   * @param selectedCategory 选中的分类
   * @param inventoryList 设备列表
   * @param deviceCategories 设备分类树
   */
  private filterBySubCategory(selectedCategory: DeviceCategory, inventoryList: InventoryItem[], deviceCategories: DeviceCategory[]): InventoryItem[] {
    const parts = selectedCategory.id.split('-');
    if (parts.length < 3) {
      return [];
    }

    const subId = parts[parts.length - 1];
    console.log('按二级分类筛选，子类 ID:', subId, '名称:', selectedCategory.name);

    const categoryService = InventoryCategoryService.getInstance();

    return inventoryList.filter(item => {
      const matchResult = this.matchItemToSubCategory(item, subId, selectedCategory.name, categoryService, deviceCategories);

      if (matchResult.shouldInclude) {
        this.logMatchedItem(item, matchResult.matchType);
      }

      return matchResult.shouldInclude;
    });
  }

  /**
   * 匹配设备到二级分类
   */
  private matchItemToSubCategory(
    item: InventoryItem,
    subId: string,
    categoryName: string,
    categoryService: InventoryCategoryService,
    deviceCategories: DeviceCategory[]
  ): { shouldInclude: boolean, matchType: string } {
    // 检查设备的子类 ID 是否匹配
    const itemSubId = item.subCategoryId?.toString();
    const idMatch = itemSubId === subId;

    // 检查设备的子类名称是否匹配
    const nameMatch = item.type === categoryName;

    // 优先使用ID匹配
    if (idMatch) {
      return { shouldInclude: true, matchType: InventoryFilterService.MATCH_TYPES.ID_MATCH };
    }

    // 使用名称匹配，但要确保一级分类也匹配
    if (nameMatch) {
      if (item.parentCategory) {
        // 查找该二级分类所属的一级分类
        const parentCategoryName = categoryService.findParentCategoryBySubCategory(categoryName, deviceCategories);
        const shouldInclude = parentCategoryName ? item.parentCategory === parentCategoryName : true;
        return { shouldInclude, matchType: InventoryFilterService.MATCH_TYPES.NAME_MATCH };
      } else {
        // 如果设备没有一级分类，则包含它
        return { shouldInclude: true, matchType: InventoryFilterService.MATCH_TYPES.NAME_MATCH };
      }
    }

    return { shouldInclude: false, matchType: InventoryFilterService.MATCH_TYPES.NO_MATCH };
  }

  /**
   * 兼容旧的分类格式筛选
   * @param selectedCategory 选中的分类
   * @param inventoryList 设备列表
   */
  private filterByLegacyCategory(selectedCategory: DeviceCategory, inventoryList: InventoryItem[]): InventoryItem[] {
    console.log('使用兼容模式筛选，分类名称:', selectedCategory.name);

    return inventoryList.filter(item => {
      const matchResult = this.matchItemToLegacyCategory(item, selectedCategory);

      if (matchResult.shouldInclude) {
        this.logLegacyMatchedItem(item, matchResult.matchType);
      }

      return matchResult.shouldInclude;
    });
  }

  /**
   * 匹配设备到兼容分类
   */
  private matchItemToLegacyCategory(item: InventoryItem, selectedCategory: DeviceCategory): { shouldInclude: boolean, matchType: string } {
    // 检查设备的类型是否匹配
    const typeMatch = item.type === selectedCategory.name;

    // 检查设备的子类ID是否匹配
    const idParts = selectedCategory.id.split('-');
    const expectedId = idParts.length > 1 ? idParts[1] : selectedCategory.id;
    const idMatch = item.subCategoryId?.toString() === expectedId;

    if (typeMatch) {
      return { shouldInclude: true, matchType: InventoryFilterService.MATCH_TYPES.TYPE_MATCH };
    }

    if (idMatch) {
      return { shouldInclude: true, matchType: InventoryFilterService.MATCH_TYPES.ID_MATCH };
    }

    return { shouldInclude: false, matchType: InventoryFilterService.MATCH_TYPES.NO_MATCH };
  }

  /**
   * 记录兼容模式匹配到的设备信息
   */
  private logLegacyMatchedItem(item: InventoryItem, matchType: string): void {
    console.log('兼容模式匹配到设备:', {
      id: item.id,
      type: item.type,
      subCategoryId: item.subCategoryId,
      matchType
    });
  }

  /**
   * 计算筛选结果（不使用缓存）
   * @param categoryId 分类ID
   * @param inventoryList 设备列表
   * @param deviceCategories 设备分类树
   */
  public calculateFilteredResult(categoryId: string, inventoryList: InventoryItem[], deviceCategories: DeviceCategory[]): InventoryItem[] {
    // 临时保存当前缓存
    const tempCategoryId = this.lastFilteredCategoryId;
    const tempResult = this.lastFilteredResult;

    // 清除缓存以强制重新计算
    this.clearFilterCache();

    // 计算新的筛选结果
    const result = this.filterByCategory(categoryId, inventoryList, deviceCategories, true);

    // 恢复之前的缓存
    this.lastFilteredCategoryId = tempCategoryId;
    this.lastFilteredResult = tempResult;

    return result;
  }
}

export default InventoryFilterService;
