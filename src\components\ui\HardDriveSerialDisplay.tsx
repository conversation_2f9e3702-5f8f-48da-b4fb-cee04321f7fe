import React, { useState } from 'react';

interface HardDriveSerialDisplayProps {
  value: string;
  maxDisplayCount?: number; // 最多显示的序列号数量，超出部分折叠
  className?: string;
}

/**
 * 硬盘序列号表格显示组件
 * 用于在表格中友好地显示多个硬盘序列号
 */
const HardDriveSerialDisplay: React.FC<HardDriveSerialDisplayProps> = ({
  value,
  maxDisplayCount = 2,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // 解析序列号列表
  const parseSerialNumbers = (input: string): string[] => {
    if (!input) return [];
    return input.split(';').map(s => s.trim()).filter(s => s.length > 0);
  };

  const serialNumbers = parseSerialNumbers(value);
  
  // 如果没有序列号，返回空
  if (serialNumbers.length === 0) {
    return <span className={className}>-</span>;
  }

  // 如果只有一个序列号，直接显示
  if (serialNumbers.length === 1) {
    return (
      <span className={`${className} font-mono text-sm`} title={serialNumbers[0]}>
        {serialNumbers[0]}
      </span>
    );
  }

  // 多个序列号的处理
  const shouldCollapse = serialNumbers.length > maxDisplayCount;
  const displaySerials = isExpanded ? serialNumbers : serialNumbers.slice(0, maxDisplayCount);
  const hiddenCount = serialNumbers.length - maxDisplayCount;

  return (
    <div className={`${className} space-y-1`}>
      {/* 显示的序列号列表 */}
      <div className="space-y-0.5">
        {displaySerials.map((serial, index) => (
          <div
            key={index}
            className="flex items-center space-x-1"
          >
            <span className="w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></span>
            <span className="font-mono text-sm text-gray-700 truncate" title={serial}>
              {serial}
            </span>
          </div>
        ))}
      </div>

      {/* 展开/收起按钮 */}
      {shouldCollapse && (
        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation(); // 防止触发表格行点击事件
            setIsExpanded(!isExpanded);
          }}
          className="text-xs text-blue-600 hover:text-blue-800 hover:underline focus:outline-none"
          title={isExpanded ? '收起' : `显示全部 ${serialNumbers.length} 个序列号`}
        >
          {isExpanded ? (
            <span className="flex items-center space-x-1">
              <span>收起</span>
              <span>↑</span>
            </span>
          ) : (
            <span className="flex items-center space-x-1">
              <span>+{hiddenCount}个</span>
              <span>↓</span>
            </span>
          )}
        </button>
      )}
    </div>
  );
};

export default HardDriveSerialDisplay;
