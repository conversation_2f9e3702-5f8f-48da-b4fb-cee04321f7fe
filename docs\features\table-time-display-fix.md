# 表格时间显示格式修复

## 📋 问题描述

设备台账表格中的时间显示格式不正确：
- **扩展字段时间**：应该显示`YYYY-MM-DD HH:mm:ss`，但只显示到日期
- **基础字段时间**：应该显示`YYYY-MM-DD`，符合预期

## 🎯 修复内容

### **修复的显示格式**
- ✅ **扩展字段时间**：`2024-01-15 14:30:45`（显示时分秒）
- ✅ **基础字段时间**：`2024-01-15`（只显示日期）

### **修复的文件**
1. **`src/utils/fieldUtils.ts`** - 时间格式化函数
2. **`src/pages/Inventory/InventoryManagement/InventoryTableNew.tsx`** - 新版表格
3. **`src/pages/Inventory/InventoryManagement/InventoryTable.tsx`** - 旧版表格
4. **`src/services/Inventory/Export/exportService.ts`** - 导出服务

## 🔧 技术实现

### **1. 格式化函数升级**

#### **扩展formatTimeValue函数**
```typescript
/**
 * 格式化时间显示值
 * @param value 时间值
 * @param showTime 是否显示时间部分（默认false，只显示日期）
 * @param showSeconds 是否显示秒（默认true）
 * @returns 格式化后的时间字符串
 */
export const formatTimeValue = (value: any, showTime: boolean = false, showSeconds: boolean = true): string => {
  if (!value || value === '') return '';

  try {
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      const baseStr = `${year}-${month}-${day}`;
      
      if (showTime) {
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        let timeStr = `${hours}:${minutes}`;
        
        if (showSeconds) {
          const seconds = String(date.getSeconds()).padStart(2, '0');
          timeStr += `:${seconds}`;
        }
        
        return `${baseStr} ${timeStr}`;
      }
      
      return baseStr;
    }
  } catch (error) {
    console.warn('时间格式化失败:', error);
  }

  return String(value);
};
```

### **2. 表格显示修复**

#### **新版表格（InventoryTableNew.tsx）**
```typescript
// 扩展字段时间格式化 - 显示时分秒
if ((isTimeField(field.title) || isTimeField(fieldKey) || field.type === 'date') && extValue) {
  return formatTimeValue(extValue, true, true); // showTime=true, showSeconds=true
}

// 基础字段时间格式化 - 只显示日期
if (field.key === 'startTime' && value) {
  return formatTimeValue(value, false); // showTime=false，只显示日期
}
```

#### **旧版表格（InventoryTable.tsx）**
```typescript
// 扩展字段时间格式化 - 显示时分秒
if ((isTimeField(field.title) || isTimeField(fieldKey) || field.type === 'date') && value) {
  return formatTimeValue(value, true, true); // showTime=true, showSeconds=true
}
```

### **3. 导出服务修复**

#### **扩展字段导出**
```typescript
// 对扩展字段中的日期字段进行格式化 - 显示时分秒
if (isTimeField(extFieldKey) || isTimeField(fieldName)) {
  fieldValue = formatTimeValue(fieldValue, true, true); // showTime=true, showSeconds=true
}
```

#### **基础字段导出**
```typescript
// 对日期字段进行格式化 - 基础字段只显示日期
if (colId === 'startTime' || isTimeField(fieldName)) {
  fieldValue = formatTimeValue(fieldValue, false); // showTime=false，只显示日期
}
```

## 📱 修复效果对比

### **修复前**
```
┌─────────────────────────────────────┐
│ 扩展字段时间 │ 基础字段时间        │
├─────────────────────────────────────┤
│ 2024-01-15   │ 2025-07-28         │  ← 都只显示日期
└─────────────────────────────────────┘
```

### **修复后**
```
┌─────────────────────────────────────┐
│ 扩展字段时间      │ 基础字段时间    │
├─────────────────────────────────────┤
│ 2024-01-15 14:30:45 │ 2025-07-28   │  ← 扩展字段显示时分秒
└─────────────────────────────────────┘
```

## 🔍 字段类型区分

### **扩展字段时间**
- **识别条件**：`field.key.startsWith('ext_')` 且 `isTimeField()` 返回true
- **显示格式**：`YYYY-MM-DD HH:mm:ss`
- **示例**：`2024-01-15 14:30:45`
- **用途**：创建时间、检修时间、故障时间等

### **基础字段时间**
- **识别条件**：`field.key === 'startTime'`
- **显示格式**：`YYYY-MM-DD`
- **示例**：`2025-07-28`
- **用途**：设备启用时间

## 🧪 测试场景

### **表格显示测试**
1. ✅ **扩展字段时间**：确认显示格式为`YYYY-MM-DD HH:mm:ss`
2. ✅ **基础字段时间**：确认显示格式为`YYYY-MM-DD`
3. ✅ **空值处理**：确认空时间字段显示为空

### **导出功能测试**
1. ✅ **Excel导出**：确认时间格式在导出文件中正确
2. ✅ **CSV导出**：确认时间格式在CSV文件中正确
3. ✅ **格式一致性**：确认导出格式与表格显示一致

### **兼容性测试**
1. ✅ **新旧数据**：确认新旧格式数据都能正确显示
2. ✅ **时区处理**：确认时间显示不受时区影响
3. ✅ **边界值**：确认极值时间能正确处理

## 🚀 扩展性

### **格式配置化**
- **全局配置**：可添加全局时间显示格式配置
- **字段级配置**：可为每个字段单独配置显示格式
- **用户偏好**：可支持用户自定义时间显示偏好

### **国际化支持**
- **多语言**：时间格式支持不同地区标准
- **时区显示**：可扩展支持时区信息显示
- **本地化**：支持本地化的时间格式

## 📝 维护说明

### **关键函数**
- **`formatTimeValue()`**：核心时间格式化函数
- **`isTimeField()`**：时间字段识别函数
- **表格渲染逻辑**：各表格组件中的时间显示逻辑

### **注意事项**
- 确保扩展字段和基础字段使用不同的格式化参数
- 维护导出功能与表格显示的一致性
- 保持与时间选择器输入格式的兼容性

### **性能考虑**
- 时间格式化函数执行效率高
- 避免重复的时间解析操作
- 缓存格式化结果以提高性能

## 🎯 总结

通过这次修复，实现了：
1. **扩展字段时间**：正确显示秒级精度（`YYYY-MM-DD HH:mm:ss`）
2. **基础字段时间**：保持日期显示（`YYYY-MM-DD`）
3. **格式一致性**：表格显示、导出功能、时间选择器格式统一
4. **向后兼容**：不影响现有数据和功能

现在设备台账的时间显示完全符合预期，扩展字段显示精确的时分秒信息，基础字段保持简洁的日期显示。
