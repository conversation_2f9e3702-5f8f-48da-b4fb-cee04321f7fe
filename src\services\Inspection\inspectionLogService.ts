import mitt from 'mitt';
import { BaseService, BaseServiceState, BaseServiceEvents } from '../../services/base/baseService';
import WebSocketManager from '../../utils/websocket';
import TaskManager from '../../utils/taskManager';
import InspectionService from './inspectionService';
import { dataManager, DataManagerConfig } from '../Base/dataManager';

/**
 * 巡检任务项接口（巡检日志主表）
 */
export interface InspectionTaskLogItem {
  task_id: number;
  task_name: string;
  task_description: string;
  person_name: string;
  person_alias: string;
  start_date: number; // 时间戳
  end_date: number; // 时间戳
  device_count: number; // 设备总数
  inspected_count: number; // 已巡检数
  normal_count: number; // 正常数量
  exception_count: number; // 异常数量
  task_status: number; // 任务状态：0正常，1异常
  task_execution_status: string; // 后端返回的执行状态文本（如"进行中"、"待开始"）
  task_result: string; // 任务结果
  departments: string[]; // 巡检部门列表
  inspection_details: InspectionDetailItem[]; // 巡检详细信息
}

/**
 * 巡检详细信息接口
 */
export interface InspectionDetailItem {
  device_id: number;
  confidentiality_code: string; // 保密编号
  device_name: string;
  location: string;
  responsible_person: string;
  responsible_person_alias: string;
  inspection_status: number; // 巡检状态：0正常，1异常
  inspection_result: string;
  rfid: string;
  department_info: {
    department_path: string;
    department_id: number;
  };
  usage_status: string; // 使用状态
  confidentiality_level: string; // 密级
}

/**
 * 巡检日志项接口（兼容旧版本）
 */
export interface InspectionLogItem {
  id: string;
  timestamp: string;
  operationType: string;
  operator: string;
  deviceName?: string;
  deviceType?: string;
  department?: string;
  details: string;
  [key: string]: any; // 允许添加其他属性
}

/**
 * 巡检结果过滤条件接口
 */
export interface InspectionLogFilter {
  startDate?: string;
  endDate?: string;
  operationType?: string;
  operator?: string;
  department?: string;
  searchText?: string;
  task_status?: number; // 任务状态筛选
  person_name?: string; // 责任人筛选
}

/**
 * 巡检结果服务状态接口
 */
export interface InspectionLogServiceState extends BaseServiceState {
  logs: InspectionLogItem[]; // 兼容旧版本
  taskLogs: InspectionTaskLogItem[]; // 新的巡检任务结果
  filter: InspectionLogFilter;
  filteredLogs: InspectionLogItem[]; // 兼容旧版本
  filteredTaskLogs: InspectionTaskLogItem[]; // 新的过滤后任务结果
  isReading: boolean;
  readProgress: number;
  selectedTaskDetails?: InspectionDetailItem[]; // 选中任务的详细信息
}

/**
 * 巡检结果服务事件接口
 */
export interface InspectionLogServiceEvents extends BaseServiceEvents<InspectionLogServiceState> {
  'logs-loaded': InspectionLogItem[];
  'task-logs-loaded': InspectionTaskLogItem[];
  'filter-changed': InspectionLogFilter;
  'read-progress': number;
  'task-details-selected': InspectionDetailItem[];
}

/**
 * 数据缓存键常量
 */
const CACHE_KEYS = {
  INSPECTION_LOGS: 'inspection_logs'
} as const;

/**
 * 数据管理器配置（单机版优化）
 */
const DATA_CONFIG: DataManagerConfig = {
  maxRetries: 3,
  retryDelay: 1000
};

/**
 * 巡检结果服务类
 * 提供巡检结果的读取和过滤功能
 */
class InspectionLogService extends BaseService<InspectionLogServiceState, InspectionLogServiceEvents> {
  private static instance: InspectionLogService;

  // 防重复调用机制
  private isGettingResults = false;
  private lastResultsTime = 0;
  private readonly CACHE_DURATION = 30000; // 30秒缓存

  /**
   * 获取巡检结果服务实例
   * @returns 巡检结果服务实例
   */
  public static getInstance(): InspectionLogService {
    if (!InspectionLogService.instance) {
      InspectionLogService.instance = new InspectionLogService();
    }
    return InspectionLogService.instance;
  }

  /**
   * 构造函数
   * 初始化巡检结果服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      logs: [],
      taskLogs: [],
      filter: {},
      filteredLogs: [],
      filteredTaskLogs: [],
      isReading: false,
      readProgress: 0,
      selectedTaskDetails: undefined
    });
  }

  /**
   * 获取巡检结果数据
   */
  public async getInspectionResults(): Promise<InspectionTaskLogItem[]> {
    try {
      await this.ensureInitialized();

      // 防重复调用检查
      const now = Date.now();
      if (this.isGettingResults) {
        return this.state.taskLogs;
      }

      // 缓存检查（只有在已有数据且在缓存时间内才使用缓存）
      if (this.state.taskLogs.length > 0 &&
          this.lastResultsTime > 0 &&
          (now - this.lastResultsTime) < this.CACHE_DURATION) {
        return this.state.taskLogs;
      }

      this.isGettingResults = true;
      this.updateState({
        isReading: true,
        readProgress: 0,
        error: undefined
      });

      // 调用真实的API
      const taskLogs = await this.fetchInspectionResults();
      this.lastResultsTime = now;

      this.updateState({
        taskLogs,
        filteredTaskLogs: this.applyTaskFilter(taskLogs, this.state.filter),
        isReading: false,
        readProgress: 100
      });

      this.emitter.emit('task-logs-loaded', taskLogs);
      return taskLogs;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取巡检结果失败';
      this.updateState({
        error: errorMessage,
        isReading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    } finally {
      this.isGettingResults = false;
    }
  }

  /**
   * 从手持端读取巡检结果（兼容旧版本）
   */
  public async readInspectionLogs(): Promise<InspectionLogItem[]> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isReading: true,
        readProgress: 0,
        error: undefined
      });

      // 模拟从手持端读取结果
      // 实际项目中，这里应该调用后端API
      const mockLogs = await this.mockReadInspectionLogs();

      this.updateState({
        logs: mockLogs,
        filteredLogs: this.applyFilter(mockLogs, this.state.filter),
        isReading: false,
        readProgress: 100
      });

      this.emitter.emit('logs-loaded', mockLogs);
      return mockLogs;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '读取巡检结果失败';
      this.updateState({
        error: errorMessage,
        isReading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 调用获取巡检结果API
   */
  private async fetchInspectionResults(): Promise<InspectionTaskLogItem[]> {
    try {
      // 构建API参数
      const apiParams = {
        action: 'get_inspection_results',
        action_params: {}
      };

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams) as any;

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      // 检查API返回状态
      if (result.message === 'success' && result.data && result.data.tasks) {
        const tasks = result.data.tasks;

        // 检查每个任务的详情数据
        tasks.forEach((task: any, index: number) => {
          // 确保inspection_details是数组，如果不存在则设为空数组
          if (!task.inspection_details) {
            task.inspection_details = [];
          }
        });

        return tasks;
      } else {
        throw new Error(result.message || '获取巡检结果失败');
      }
    } catch (error) {
      console.error('获取巡检结果API调用失败:', error);
      throw error;
    }
  }

  /**
   * 强制刷新巡检结果数据（忽略缓存）
   */
  public async forceRefreshInspectionResults(): Promise<InspectionTaskLogItem[]> {
    // 重置缓存时间，强制重新获取
    this.lastResultsTime = 0;
    return await this.getInspectionResults();
  }

  /**
   * 设置结果过滤条件
   * @param filter 过滤条件
   */
  public setFilter(filter: Partial<InspectionLogFilter>): void {
    const newFilter = { ...this.state.filter, ...filter };
    const filteredLogs = this.applyFilter(this.state.logs, newFilter);
    const filteredTaskLogs = this.applyTaskFilter(this.state.taskLogs, newFilter);

    this.updateState({
      filter: newFilter,
      filteredLogs,
      filteredTaskLogs
    });

    this.emitter.emit('filter-changed', newFilter);
  }

  /**
   * 设置选中任务的详细信息
   */
  public setSelectedTaskDetails(details: InspectionDetailItem[]): void {
    this.updateState({
      selectedTaskDetails: details
    });
    this.emitter.emit('task-details-selected', details);
  }

  /**
   * 清除所有过滤条件
   */
  public clearFilter(): void {
    this.updateState({
      filter: {},
      filteredLogs: this.state.logs,
      filteredTaskLogs: this.state.taskLogs
    });

    this.emitter.emit('filter-changed', {});
  }

  /**
   * 获取所有操作类型
   */
  public getOperationTypes(): string[] {
    const types = new Set<string>();
    this.state.logs.forEach(log => {
      if (log.operationType) {
        types.add(log.operationType);
      }
    });
    return Array.from(types);
  }

  /**
   * 获取所有操作人员
   */
  public getOperators(): string[] {
    const operators = new Set<string>();
    this.state.logs.forEach(log => {
      if (log.operator) {
        operators.add(log.operator);
      }
    });
    return Array.from(operators);
  }

  /**
   * 获取所有部门
   */
  public getDepartments(): string[] {
    const departments = new Set<string>();
    this.state.logs.forEach(log => {
      if (log.department) {
        departments.add(log.department);
      }
    });
    return Array.from(departments);
  }

  /**
   * 应用过滤条件到任务日志列表
   * @param taskLogs 任务日志列表
   * @param filter 过滤条件
   * @returns 过滤后的任务日志列表
   */
  private applyTaskFilter(taskLogs: InspectionTaskLogItem[], filter: InspectionLogFilter): InspectionTaskLogItem[] {
    return taskLogs.filter(task => {
      // 日期范围过滤
      if (filter.startDate && task.start_date < new Date(filter.startDate).getTime()) {
        return false;
      }
      if (filter.endDate && task.end_date > new Date(filter.endDate).getTime()) {
        return false;
      }

      // 任务状态过滤
      if (filter.task_status !== undefined && task.task_status !== filter.task_status) {
        return false;
      }

      // 责任人过滤
      if (filter.person_name && task.person_name !== filter.person_name) {
        return false;
      }

      // 部门过滤
      if (filter.department && !task.departments.includes(filter.department)) {
        return false;
      }

      // 搜索文本过滤
      if (filter.searchText) {
        const searchText = filter.searchText.toLowerCase();
        return (
          task.task_name.toLowerCase().includes(searchText) ||
          task.task_description.toLowerCase().includes(searchText) ||
          task.person_name.toLowerCase().includes(searchText) ||
          task.task_result.toLowerCase().includes(searchText) ||
          task.departments.some(dept => dept.toLowerCase().includes(searchText))
        );
      }

      return true;
    });
  }

  /**
   * 应用过滤条件到日志列表
   * @param logs 日志列表
   * @param filter 过滤条件
   * @returns 过滤后的日志列表
   */
  private applyFilter(logs: InspectionLogItem[], filter: InspectionLogFilter): InspectionLogItem[] {
    return logs.filter(log => {
      // 日期范围过滤
      if (filter.startDate && new Date(log.timestamp) < new Date(filter.startDate)) {
        return false;
      }
      if (filter.endDate && new Date(log.timestamp) > new Date(filter.endDate)) {
        return false;
      }
      
      // 巡检类型过滤
      if (filter.operationType && log.operationType !== filter.operationType) {
        return false;
      }
      
      // 巡检人过滤
      if (filter.operator && log.operator !== filter.operator) {
        return false;
      }

      // 部门过滤
      if (filter.department && log.department !== filter.department) {
        return false;
      }

      // 搜索文本过滤
      if (filter.searchText) {
        const searchText = filter.searchText.toLowerCase();
        return (
          log.deviceName?.toLowerCase().includes(searchText) ||
          log.deviceType?.toLowerCase().includes(searchText) ||
          log.department?.toLowerCase().includes(searchText) ||
          log.details.toLowerCase().includes(searchText)
        );
      }
      
      return true;
    });
  }

  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    // 确保基础巡检服务已初始化
    const inspectionService = InspectionService.getInstance();
    await inspectionService.ensureInitialized();
  }

  /**
   * 模拟从手持端读取巡检日志
   * 实际项目中应替换为真实API调用
   */
  private async mockReadInspectionLogs(): Promise<InspectionLogItem[]> {
    // 模拟进度更新
    for (let i = 0; i <= 100; i += 10) {
      this.updateState({ readProgress: i });
      this.emitter.emit('read-progress', i);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 返回模拟数据
    return [
      {
        id: '1',
        timestamp: '2023-11-01 08:30:15',
        operationType: '登录',
        operator: '张三',
        details: '用户登录系统'
      },
      {
        id: '2',
        timestamp: '2023-11-01 09:15:22',
        operationType: '数据同步',
        operator: '张三',
        deviceName: '计算机A',
        deviceType: '台式机',
        department: '技术部',
        details: '同步新增设备数据'
      },
      {
        id: '3',
        timestamp: '2023-11-01 10:45:30',
        operationType: '数据同步',
        operator: '李四',
        deviceName: '计算机B',
        deviceType: '笔记本',
        department: '财务部',
        details: '同步修改设备数据'
      },
      {
        id: '4',
        timestamp: '2023-11-01 11:20:05',
        operationType: '数据同步',
        operator: '王五',
        deviceName: '计算机C',
        deviceType: '服务器',
        department: '运维部',
        details: '同步删除设备数据'
      },
      {
        id: '5',
        timestamp: '2023-11-01 12:10:45',
        operationType: '登出',
        operator: '张三',
        details: '用户登出系统'
      }
    ];
  }
}

export default InspectionLogService;
