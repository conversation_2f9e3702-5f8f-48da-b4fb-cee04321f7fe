import mitt from 'mitt';
import { BaseService, BaseServiceState, BaseServiceEvents } from '../../services/base/baseService';
import WebSocketManager from '../../utils/websocket';
import TaskManager from '../../utils/taskManager';
import InspectionService from './inspectionService';
import { dataManager, DataManagerConfig } from '../Base/dataManager';
import InspectionAccountService from './inspectionAccountService';
import { removeDuplicatesByProperty } from '../../utils/arrayUtils';
import { formatInspectionDateTime } from '../../utils/formatUtils';
import DepartmentService from '../Inventory/departmentService';

/**
 * 巡检任务项接口
 */
export interface InspectionTaskItem {
  // 基础标识
  id: string;
  task_id: number;

  // 任务信息
  task_name: string;
  task_description: string;

  // 人员信息
  person_name: string;
  person_alias: string;
  assignee: string; // 格式化后的责任人显示名称

  // 时间信息
  start_date: number;
  end_date: number;
  startTime: string; // 格式化后的开始时间
  endTime: string; // 格式化后的结束时间

  // 进度信息
  device_count: number;
  inspected_count: number;
  progress: number; // 计算后的进度百分比

  // 状态信息
  task_status: number; // 原始状态
  task_execution_status: string; // 后端返回的执行状态文本（如"进行中"）
  status: 'completed' | 'in_progress' | 'pending' | 'overdue'; // 计算后的状态（保留用于兼容）

  // 部门和设备信息
  departments: string[];
  departmentList: string; // 格式化后的部门列表
  devices: InspectionDevice[];
}

/**
 * 巡检设备接口
 */
export interface InspectionDevice {
  device_id: number;
  confidentiality_code: string;
  device_name: string;
  inspection_status: number;
}

/**
 * 创建任务请求数据接口
 */
export interface CreateTaskRequest {
  task_name: string;
  task_description?: string;
  person_name: string;
  person_alias: string;
  start_date: number;
  end_date: number;
  department_paths: string[];
}

/**
 * 巡检任务过滤条件接口
 */
export interface InspectionTaskFilter {
  startDate?: string;
  endDate?: string;
  inspector?: string;
  department?: string;
  status?: string;
  searchText?: string;
}

/**
 * 巡检任务服务状态接口
 */
export interface InspectionTaskServiceState extends BaseServiceState {
  tasks: InspectionTaskItem[];
  filter: InspectionTaskFilter;
  filteredTasks: InspectionTaskItem[];
  isRefreshing: boolean;
  refreshProgress: number;
}

/**
 * 巡检任务服务事件接口
 */
export interface InspectionTaskServiceEvents extends BaseServiceEvents<InspectionTaskServiceState> {
  'tasks-loaded': InspectionTaskItem[];
  'filter-changed': InspectionTaskFilter;
  'refresh-progress': number;
  'task-created': InspectionTaskItem;
  'task-updated': InspectionTaskItem;
  'task-deleted': string;
}

/**
 * 数据缓存键常量
 */
const CACHE_KEYS = {
  INSPECTION_TASKS: 'inspection_tasks',
  INSPECTORS: 'inspectors',
  DEPARTMENTS: 'departments'
} as const;

/**
 * 数据管理器配置（单机版优化）
 */
const DATA_CONFIG: DataManagerConfig = {
  maxRetries: 3,
  retryDelay: 1000
};

/**
 * 巡检任务服务类
 * 提供巡检任务的CRUD操作和过滤功能
 */
class InspectionTaskService extends BaseService<InspectionTaskServiceState, InspectionTaskServiceEvents> {
  private static instance: InspectionTaskService;

  /**
   * 获取巡检任务服务实例
   * @returns 巡检任务服务实例
   */
  public static getInstance(): InspectionTaskService {
    if (!InspectionTaskService.instance) {
      InspectionTaskService.instance = new InspectionTaskService();
    }
    return InspectionTaskService.instance;
  }

  /**
   * 构造函数
   * 初始化巡检任务服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      tasks: [],
      filter: {},
      filteredTasks: [],
      isRefreshing: false,
      refreshProgress: 0
    });
  }

  /**
   * 获取巡检任务数据（智能缓存）
   */
  public async getTasks(forceRefresh = false): Promise<InspectionTaskItem[]> {
    try {
      await this.ensureInitialized();

      const fetcher = () => this.fetchInspectionTasks();

      let tasks: InspectionTaskItem[];
      if (forceRefresh) {
        tasks = await dataManager.refreshData(CACHE_KEYS.INSPECTION_TASKS, fetcher, DATA_CONFIG);
      } else {
        tasks = await dataManager.getData(CACHE_KEYS.INSPECTION_TASKS, fetcher, DATA_CONFIG);
      }

      // 更新本地状态
      this.updateState({
        tasks: tasks,
        filteredTasks: this.applyFilter(tasks, this.state.filter),
        isRefreshing: false
      });

      this.emitter.emit('tasks-loaded', tasks);
      return tasks;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取任务数据失败';
      this.updateState({
        error: errorMessage,
        isRefreshing: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 刷新巡检任务数据（强制刷新）
   */
  public async refreshTasks(): Promise<InspectionTaskItem[]> {
    this.updateState({ isRefreshing: true, refreshProgress: 0 });
    return this.getTasks(true);
  }

  /**
   * 初始化数据监听器（仅用于缓存更新通知）
   */
  public initializeDataListener(): () => void {
    return dataManager.addListener(CACHE_KEYS.INSPECTION_TASKS, (cacheItem) => {
      // 只在缓存数据更新时同步到本地状态
      if (cacheItem.data && !cacheItem.loading && !cacheItem.error) {
        this.updateState({
          tasks: cacheItem.data,
          filteredTasks: this.applyFilter(cacheItem.data, this.state.filter),
          isRefreshing: false
        });
      }
    });
  }

  /**
   * 设置任务过滤条件
   * @param filter 过滤条件
   */
  public setFilter(filter: Partial<InspectionTaskFilter>): void {
    const newFilter = { ...this.state.filter, ...filter };
    const filteredTasks = this.applyFilter(this.state.tasks, newFilter);
    
    this.updateState({ 
      filter: newFilter,
      filteredTasks
    });
    
    this.emitter.emit('filter-changed', newFilter);
  }

  /**
   * 清除所有过滤条件
   */
  public clearFilter(): void {
    this.updateState({ 
      filter: {},
      filteredTasks: this.state.tasks
    });
    
    this.emitter.emit('filter-changed', {});
  }

  /**
   * 创建新任务
   * @param taskData 任务数据
   */
  public async createTask(taskData: CreateTaskRequest): Promise<InspectionTaskItem> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 调用真实的创建巡检任务API
      const newTask = await this.callCreateInspectionTaskAPI(taskData);

      // 检查任务是否已存在，避免重复添加
      const existingTaskIndex = this.state.tasks.findIndex(task => task.task_id === newTask.task_id);

      let updatedTasks: InspectionTaskItem[];
      if (existingTaskIndex >= 0) {
        // 如果任务已存在，替换它（这种情况很少见，但为了健壮性保留）
        console.log(`任务ID ${newTask.task_id} 已存在，替换现有任务`);
        updatedTasks = [...this.state.tasks];
        updatedTasks[existingTaskIndex] = newTask;

        // 更新缓存中的任务
        dataManager.updateItemInCache(CACHE_KEYS.INSPECTION_TASKS, newTask.task_id, () => newTask);
      } else {
        // 正常情况：添加新任务到列表最前面（任务管理需要显示最新任务）
        console.log(`添加新任务ID ${newTask.task_id} 到列表顶部`);
        updatedTasks = [newTask, ...this.state.tasks];

        // 增量更新缓存：添加新任务到顶部
        dataManager.addItemToCache(CACHE_KEYS.INSPECTION_TASKS, newTask, true); // true表示添加到顶部
      }

      // 更新本地状态
      this.updateState({
        tasks: updatedTasks,
        filteredTasks: this.applyFilter(updatedTasks, this.state.filter),
        isLoading: false
      });

      this.emitter.emit('task-created', newTask);
      return newTask;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建任务失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }



  /**
   * 调用删除巡检任务API
   * @param taskId 任务ID
   */
  private async callDeleteInspectionTaskAPI(taskId: string): Promise<{
    delete_time: number;
    deleted_task_id: number;
    deleted_task_name: string;
  }> {
    try {
      console.log('调用删除巡检任务API，任务ID:', taskId);

      const requestData = {
        action: 'delete_inspection_task',
        action_params: {
          task_id: parseInt(taskId)
        }
      };

      console.log('删除任务请求参数:', requestData);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', requestData) as any;

      console.log('删除任务API原始响应:', rawResult);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
          console.log('解析JSON字符串后的结果:', result);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      console.log('删除任务API最终响应:', result);

      // 解析响应数据
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        return result.data;
      } else {
        throw new Error(result.message || result.error_code || 'API调用失败');
      }
    } catch (error) {
      console.error('删除巡检任务API调用失败:', error);
      throw error;
    }
  }

  /**
   * 删除任务
   * @param taskId 任务ID
   */
  public async deleteTask(taskId: string): Promise<void> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 调用真实的删除巡检任务API
      const deleteResult = await this.callDeleteInspectionTaskAPI(taskId);

      console.log('删除任务成功:', deleteResult);

      // 增量更新缓存：从缓存中移除任务
      dataManager.removeItemFromCache(CACHE_KEYS.INSPECTION_TASKS, taskId);

      // 更新本地状态
      const updatedTasks = this.state.tasks.filter(task => task.task_id !== taskId);
      this.updateState({
        tasks: updatedTasks,
        filteredTasks: this.applyFilter(updatedTasks, this.state.filter),
        isLoading: false
      });

      this.emitter.emit('task-deleted', {
        taskId,
        taskName: deleteResult.deleted_task_name,
        deleteTime: deleteResult.delete_time
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除任务失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 获取所有巡检人员（从账户管理获取）
   */
  public async getInspectors(): Promise<string[]> {
    try {
      // 从账户管理服务获取责任人列表
      const accountService = InspectionAccountService.getInstance();

      // 确保账户数据已加载
      await accountService.getAccounts();

      // 获取账户状态
      const accountState = accountService.getState();

      // 从账户列表中提取责任人
      const inspectors = new Set<string>();
      accountState.accounts.forEach(account => {
        if (account.inspector) {
          inspectors.add(account.inspector);
        }
      });

      // 如果账户管理中没有数据，则从任务中获取（兜底方案）
      if (inspectors.size === 0) {
        this.state.tasks.forEach(task => {
          if (task.assignee) {
            inspectors.add(task.assignee);
          }
        });
      }

      return Array.from(inspectors);
    } catch (error) {
      console.error('获取巡检人员列表失败:', error);

      // 出错时使用任务中的数据作为兜底
      const inspectors = new Set<string>();
      this.state.tasks.forEach(task => {
        if (task.assignee) {
          inspectors.add(task.assignee);
        }
      });
      return Array.from(inspectors);
    }
  }

  /**
   * 获取所有部门
   */
  public getDepartments(): string[] {
    const departments = new Set<string>();
    this.state.tasks.forEach(task => {
      if (task.departments && task.departments.length > 0) {
        task.departments.forEach(dept => {
          departments.add(dept);
        });
      }
    });
    return Array.from(departments);
  }

  /**
   * 获取所有任务类型
   */
  public getTaskTypes(): string[] {
    // 巡检任务类型相对固定
    return ['巡检任务', '日常巡检', '安全检查', '维护检查', '设备检查'];
  }

  /**
   * 应用过滤条件到任务列表
   * @param tasks 任务列表
   * @param filter 过滤条件
   * @returns 过滤后的任务列表
   */
  private applyFilter(tasks: InspectionTaskItem[], filter: InspectionTaskFilter): InspectionTaskItem[] {
    return tasks.filter(task => {
      // 日期范围过滤
      if (filter.startDate && new Date(task.start_date) < new Date(filter.startDate)) {
        return false;
      }
      if (filter.endDate && new Date(task.end_date) > new Date(filter.endDate)) {
        return false;
      }

      // 巡检人过滤
      if (filter.inspector && task.assignee !== filter.inspector) {
        return false;
      }

      // 部门过滤
      if (filter.department && !task.departments.includes(filter.department)) {
        return false;
      }

      // 状态过滤
      if (filter.status && task.status !== filter.status) {
        return false;
      }

      // 搜索文本过滤
      if (filter.searchText) {
        const searchText = filter.searchText.toLowerCase();
        return (
          task.task_name.toLowerCase().includes(searchText) ||
          task.task_description.toLowerCase().includes(searchText) ||
          task.assignee.toLowerCase().includes(searchText) ||
          task.departmentList.toLowerCase().includes(searchText) ||
          task.person_name.toLowerCase().includes(searchText) ||
          task.person_alias.toLowerCase().includes(searchText)
        );
      }

      return true;
    });
  }

  /**
   * 调用创建巡检任务API
   * @param taskData 任务数据
   */
  private async callCreateInspectionTaskAPI(taskData: CreateTaskRequest): Promise<InspectionTaskItem> {
    try {
      // 构建API参数
      const apiParams = {
        action: 'create_inspection_task',
        action_params: {
          task_name: taskData.task_name,
          task_description: taskData.task_description || '',
          person_name: taskData.person_name,
          person_alias: taskData.person_alias && taskData.person_alias.trim() !== ''
            ? taskData.person_alias.trim()
            : '', // 没有备注时传递空字符串
          start_date: taskData.start_date,
          end_date: taskData.end_date,
          department_paths: taskData.department_paths
        }
      };

      console.log('调用创建巡检任务API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams) as any;

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      // 处理API返回结果
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        // 成功
        const apiData = result.data;

        // 构建任务对象
        const assignee = taskData.person_alias && taskData.person_alias !== taskData.person_name
          ? `${taskData.person_name} (${taskData.person_alias})`
          : taskData.person_name;

        const newTask: InspectionTaskItem = {
          // 基础标识
          id: apiData.task_id?.toString() || Date.now().toString(),
          task_id: apiData.task_id || 0,

          // 任务信息
          task_name: apiData.task_name || taskData.task_name,
          task_description: taskData.task_description || '',

          // 人员信息
          person_name: taskData.person_name,
          person_alias: taskData.person_alias,
          assignee: assignee,

          // 时间信息
          start_date: taskData.start_date,
          end_date: taskData.end_date,
          startTime: formatInspectionDateTime(taskData.start_date),
          endTime: formatInspectionDateTime(taskData.end_date),

          // 进度信息
          device_count: apiData.device_count || 0,
          inspected_count: 0,
          progress: 0,

          // 状态信息
          task_status: 0,
          task_execution_status: apiData.task_execution_status || '待执行', // 使用后端返回的状态或默认值
          status: 'pending',

          // 部门和设备信息 - 使用处理后的部门显示逻辑
          ...(() => {
            const departmentDisplay = this.processDepartmentDisplay(taskData.department_paths);
            return {
              departments: departmentDisplay.departments,
              departmentList: departmentDisplay.departmentList
            };
          })(),
          devices: []
        };

        console.log('创建任务成功:', newTask);
        return newTask;
      } else {
        // 失败
        throw new Error(result.message || `API错误: ${result.error_code}`);
      }
    } catch (error) {
      console.error('创建巡检任务API调用失败:', error);
      throw error;
    }
  }

  /**
   * 更新巡检任务
   */
  public async updateTask(updateData: {
    task_id: number;
    task_name?: string;
    task_description?: string;
    start_date?: number;
    end_date?: number;
  }): Promise<InspectionTaskItem> {
    try {
      // 构建API参数
      const apiParams = {
        action: 'update_inspection_task',
        action_params: {
          task_id: updateData.task_id,
          ...(updateData.task_name !== undefined && { task_name: updateData.task_name }),
          ...(updateData.task_description !== undefined && { task_description: updateData.task_description }),
          ...(updateData.start_date !== undefined && { start_date: updateData.start_date }),
          ...(updateData.end_date !== undefined && { end_date: updateData.end_date })
        }
      };

      console.log('调用更新巡检任务API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams) as any;

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      // 处理API返回结果
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        console.log('更新任务成功:', result.data);

        // 创建统一的更新函数，避免重复逻辑
        const updateTaskData = (task: InspectionTaskItem): InspectionTaskItem => {
          const updatedTask = { ...task };

          // 更新变化的字段
          if (updateData.task_name !== undefined) {
            updatedTask.task_name = updateData.task_name;
          }
          if (updateData.task_description !== undefined) {
            updatedTask.task_description = updateData.task_description;
          }
          if (updateData.start_date !== undefined) {
            updatedTask.start_date = updateData.start_date;
            updatedTask.startTime = formatInspectionDateTime(updateData.start_date);
          }
          if (updateData.end_date !== undefined) {
            updatedTask.end_date = updateData.end_date;
            updatedTask.endTime = formatInspectionDateTime(updateData.end_date);
            // 重新计算状态（可能因为时间变化而改变）
            updatedTask.status = this.calculateTaskStatus(updatedTask.task_status, updatedTask.start_date, updatedTask.end_date);
          }

          return updatedTask;
        };

        // 增量更新缓存：只更新变化的任务
        dataManager.updateItemInCache(CACHE_KEYS.INSPECTION_TASKS, updateData.task_id, updateTaskData);

        // 更新本地状态：使用相同的更新逻辑
        const updatedTasks = this.state.tasks.map(task =>
          task.task_id === updateData.task_id ? updateTaskData(task) : task
        );

        this.updateState({
          tasks: updatedTasks,
          filteredTasks: this.applyFilter(updatedTasks, this.state.filter)
        });

        // 返回更新后的任务
        const updatedTask = updatedTasks.find(task => task.task_id === updateData.task_id);
        if (updatedTask) {
          this.emitter.emit('task-updated', updatedTask);
          return updatedTask;
        } else {
          throw new Error('更新后未找到任务');
        }
      } else {
        throw new Error(result.message || result.error_code || 'API调用失败');
      }
    } catch (error) {
      console.error('更新巡检任务API调用失败:', error);
      throw error;
    }
  }

  /**
   * 获取巡检任务数据
   */
  private async fetchInspectionTasks(): Promise<InspectionTaskItem[]> {
    try {
      // 构建API参数
      const apiParams = {
        action: 'get_inspection_tasks',
        action_params: {}
      };

      console.log('调用获取巡检任务API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams) as any;

      console.log('获取巡检任务API原始返回结果:', rawResult);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
          console.log('解析JSON字符串后的结果:', result);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      console.log('最终处理的结果:', result);

      // 处理API返回结果
      // 检查是否成功：有data字段且message为success，或者有error_code为SUCCESS/0
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        const apiData = result.data;
        const persons = apiData.persons || [];

        console.log('解析persons数据:', persons);

        // 从persons数组中提取所有任务
        const allTasks: any[] = [];
        persons.forEach((person: any) => {
          if (person.tasks && person.tasks.length > 0) {
            person.tasks.forEach((task: any) => {
              // 将人员信息添加到任务中
              allTasks.push({
                ...task,
                person_name: person.person_name,
                person_alias: person.person_alias
              });
            });
          }
        });

        console.log('解析出的任务列表:', allTasks);

        // 转换API数据为前端数据格式
        const transformedTasks = allTasks.map((task: any) => this.transformApiTaskToFrontend(task));

        // 去重：根据task_id去除重复的任务
        const uniqueTasks = removeDuplicatesByProperty(transformedTasks, 'task_id');

        console.log(`原始任务数量: ${transformedTasks.length}, 去重后数量: ${uniqueTasks.length}`);

        // 按任务ID降序排序，确保新创建的任务（ID较大）显示在前面
        const sortedTasks = uniqueTasks.sort((a, b) => b.task_id - a.task_id);
        console.log('任务列表已按ID降序排序，新任务在前');

        return sortedTasks;
      } else {
        throw new Error(result.message || result.error_code || 'API调用失败');
      }
    } catch (error) {
      console.error('获取巡检任务API调用失败:', error);
      throw error;
    }
  }

  /**
   * 转换API任务数据为前端格式
   */
  private transformApiTaskToFrontend(apiTask: any): InspectionTaskItem {
    // 计算任务状态
    const status = this.calculateTaskStatus(apiTask.task_status, apiTask.start_date, apiTask.end_date);

    // 计算进度
    const progress = apiTask.device_count > 0
      ? Math.round((apiTask.inspected_count / apiTask.device_count) * 100)
      : 0;

    // 格式化责任人
    const assignee = apiTask.person_alias && apiTask.person_alias !== apiTask.person_name
      ? `${apiTask.person_name} (${apiTask.person_alias})`
      : apiTask.person_name;

    console.log('转换任务数据:', {
      task_id: apiTask.task_id,
      task_name: apiTask.task_name,
      person_name: apiTask.person_name,
      person_alias: apiTask.person_alias,
      assignee: assignee,
      departments: apiTask.departments,
      start_date: apiTask.start_date,
      end_date: apiTask.end_date,
      status: status,
      progress: progress
    });

    return {
      // 基础标识
      id: apiTask.task_id.toString(),
      task_id: apiTask.task_id,

      // 任务信息
      task_name: apiTask.task_name || '',
      task_description: apiTask.task_description || '',

      // 人员信息
      person_name: apiTask.person_name || '',
      person_alias: apiTask.person_alias || '',
      assignee: assignee,

      // 时间信息
      start_date: apiTask.start_date || 0,
      end_date: apiTask.end_date || 0,
      startTime: formatInspectionDateTime(apiTask.start_date || 0),
      endTime: formatInspectionDateTime(apiTask.end_date || 0),

      // 进度信息
      device_count: apiTask.device_count || 0,
      inspected_count: apiTask.inspected_count || 0,
      progress: progress,

      // 状态信息
      task_status: apiTask.task_status || 0,
      task_execution_status: apiTask.task_execution_status || '', // 后端返回的执行状态
      status: status,

      // 部门和设备信息 - 使用处理后的部门显示逻辑
      ...(() => {
        const departmentDisplay = this.processDepartmentDisplay(apiTask.departments || []);
        return {
          departments: departmentDisplay.departments,
          departmentList: departmentDisplay.departmentList
        };
      })(),
      devices: apiTask.inspected_devices || []
    };
  }

  /**
   * 获取所有一级部门名称
   */
  private getTopLevelDepartments(): string[] {
    try {
      const departmentService = DepartmentService.getInstance();
      const departmentCategories = departmentService.getState().departmentCategories;
      const rootNode = departmentCategories.find(item => item.id === 'all-dept');

      return rootNode?.children
        ?.filter(child => child.id.startsWith('dept-') && child.name)
        ?.map(child => child.name) || [];
    } catch (error) {
      console.warn('获取一级部门失败:', error);
      return [];
    }
  }

  /**
   * 处理部门显示逻辑
   * @param departments 部门数组
   */
  private processDepartmentDisplay(departments: string[]): { departments: string[], departmentList: string } {
    // 如果部门数组为空或只包含空字符串，表示选择了根节点
    if (!departments || departments.length === 0 || (departments.length === 1 && departments[0] === '')) {
      const topLevelDepts = this.getTopLevelDepartments();
      return {
        departments: topLevelDepts,
        departmentList: topLevelDepts.join(', ')
      };
    }

    // 否则直接使用原始部门数据
    return {
      departments: departments,
      departmentList: departments.join(', ')
    };
  }

  /**
   * 计算任务状态
   */
  private calculateTaskStatus(taskStatus: number, startDate: number, endDate: number): 'completed' | 'in_progress' | 'pending' | 'overdue' {
    const now = Math.floor(Date.now() / 1000); // 转换为秒级时间戳进行比较

    // 根据task_status判断
    if (taskStatus === 2) {
      return 'completed'; // 已完成
    }

    if (now > endDate) {
      return 'overdue'; // 已逾期
    }

    if (now >= startDate && now <= endDate) {
      return 'in_progress'; // 进行中
    }

    return 'pending'; // 待执行
  }

  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    // 确保基础巡检服务已初始化
    const inspectionService = InspectionService.getInstance();
    await inspectionService.ensureInitialized();
  }


}

export default InspectionTaskService;
