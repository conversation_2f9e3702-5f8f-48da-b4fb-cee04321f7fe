import { useEffect } from 'react';
import { useManagersWithDebug } from './useManagers';

interface UseEscapeKeyOptions {
  /** 是否启用ESC键处理 */
  enabled?: boolean;
  /** ESC键处理器的优先级 */
  priority?: number;
  /** 调试标识，用于日志输出 */
  debugId?: string;
}

/**
 * 统一的ESC键处理Hook
 * 简化对话框和其他组件的ESC键处理逻辑
 */
export function useEscapeKey(
  onEscape: (event: KeyboardEvent) => void,
  options: UseEscapeKeyOptions = {}
) {
  const {
    enabled = true,
    priority = 100,
    debugId = 'unknown'
  } = options;

  // 使用统一的管理器获取
  const { getManagers } = useManagersWithDebug(debugId);

  useEffect(() => {
    if (!enabled) return;

    try {
      const managers = getManagers();
      if (!managers) {
        throw new Error('管理器未初始化');
      }

      const { keyboardManager } = managers;
      
      // 注册ESC键处理器
      const cleanup = keyboardManager.registerEscHandler((e) => {
        if (debugId) {
          console.log(`${debugId}: ESC键被按下`);
        }
        e.preventDefault();
        onEscape(e);
      }, priority);

      return cleanup;
    } catch (error) {
      console.warn(`${debugId}: 无法注册ESC键处理器，键盘管理器未初始化`, error);
      
      // 降级处理：直接监听键盘事件
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          if (debugId) {
            console.log(`${debugId}: ESC键被按下（降级处理）`);
          }
          e.preventDefault();
          onEscape(e);
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [enabled, onEscape, priority, debugId, getManagers]);
}

/**
 * 对话框专用的ESC键处理Hook
 * 包含对话框特有的逻辑和优化
 */
export function useDialogEscapeKey(
  isOpen: boolean,
  onClose: () => void,
  options: Omit<UseEscapeKeyOptions, 'enabled'> = {}
) {
  return useEscapeKey(
    (e) => {
      if (isOpen) {
        onClose();
      }
    },
    {
      ...options,
      enabled: isOpen
    }
  );
}
