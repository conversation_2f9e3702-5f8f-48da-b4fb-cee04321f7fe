import React from 'react';
import { usePageFocus } from '../../hooks/base/usePageFocus';

/**
 * 页面标题组件的Props
 */
interface PageTitleProps {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
  id?: string;
}

/**
 * 页面标题组件
 * 自动处理焦点管理和无障碍访问
 */
export const PageTitle: React.FC<PageTitleProps> = ({
  children,
  level = 1,
  className = '',
  id
}) => {
  const { titleRef } = usePageFocus();
  
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  return (
    <Tag
      ref={titleRef}
      id={id}
      tabIndex={-1}
      className={`focus:outline-none focus-ring ${className}`}
      aria-label={typeof children === 'string' ? children : undefined}
    >
      {children}
    </Tag>
  );
};

/**
 * 主要内容区域组件
 * 为页面主要内容提供焦点管理
 */
interface MainContentProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  'aria-label'?: string;
}

export const MainContent: React.FC<MainContentProps> = ({
  children,
  className = '',
  id = 'main-content',
  'aria-label': ariaLabel = '主要内容'
}) => {
  const { pageRef } = usePageFocus();
  
  return (
    <main
      ref={pageRef}
      id={id}
      tabIndex={-1}
      className={`focus:outline-none focus-ring ${className}`}
      aria-label={ariaLabel}
      role="main"
    >
      {children}
    </main>
  );
};
