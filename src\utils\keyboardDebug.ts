/**
 * 键盘调试工具
 * 用于在m108 miniblink环境中调试键盘事件问题
 */

// 键盘事件日志
interface KeyboardEventLog {
  timestamp: number;
  type: string;
  key: string;
  keyCode: number;
  which: number;
  target: string;
  prevented: boolean;
  phase: string;
}

class KeyboardDebugger {
  private logs: KeyboardEventLog[] = [];
  private isEnabled = false;
  private maxLogs = 100;

  /**
   * 启用键盘调试
   */
  enable() {
    if (this.isEnabled) return;
    
    this.isEnabled = true;
    console.log('🔧 键盘调试器已启用');
    
    // 监听所有键盘事件
    this.addEventListeners();
    
    // 添加全局调试方法
    (window as any).keyboardDebug = {
      getLogs: () => this.getLogs(),
      clearLogs: () => this.clearLogs(),
      testKey: (key: string) => this.testKey(key),
      showCapabilities: () => this.showCapabilities(),
      disable: () => this.disable()
    };
    
    console.log('🔧 调试方法已添加到 window.keyboardDebug');
    console.log('🔧 可用方法: getLogs(), clearLogs(), testKey(key), showCapabilities(), disable()');
  }

  /**
   * 禁用键盘调试
   */
  disable() {
    this.isEnabled = false;
    this.removeEventListeners();
    delete (window as any).keyboardDebug;
    console.log('🔧 键盘调试器已禁用');
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners() {
    // 监听所有阶段的键盘事件
    const events = ['keydown', 'keypress', 'keyup'];
    
    events.forEach(eventType => {
      // 捕获阶段
      document.addEventListener(eventType, (e) => this.logEvent(e, 'capture'), true);
      // 冒泡阶段
      document.addEventListener(eventType, (e) => this.logEvent(e, 'bubble'), false);
    });
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners() {
    // 这里简化处理，实际应该保存监听器引用
    console.log('🔧 移除键盘事件监听器');
  }

  /**
   * 记录键盘事件
   */
  private logEvent(event: KeyboardEvent, phase: string) {
    if (!this.isEnabled) return;

    const log: KeyboardEventLog = {
      timestamp: Date.now(),
      type: event.type,
      key: event.key || 'undefined',
      keyCode: event.keyCode || 0,
      which: event.which || 0,
      target: this.getTargetDescription(event.target as Element),
      prevented: event.defaultPrevented,
      phase
    };

    this.logs.push(log);
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // 实时输出重要事件
    if (event.type === 'keydown' && phase === 'capture') {
      console.log(`🎹 ${event.type}(${phase}): ${log.key} (keyCode: ${log.keyCode}, which: ${log.which}) -> ${log.target} [prevented: ${log.prevented}]`);
    }
  }

  /**
   * 获取目标元素描述
   */
  private getTargetDescription(target: Element | null): string {
    if (!target) return 'null';
    
    const tag = target.tagName.toLowerCase();
    const id = target.id ? `#${target.id}` : '';
    const className = target.className ? `.${target.className.split(' ').join('.')}` : '';
    
    return `${tag}${id}${className}`;
  }

  /**
   * 获取事件日志
   */
  getLogs(): KeyboardEventLog[] {
    return [...this.logs];
  }

  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = [];
    console.log('🔧 键盘事件日志已清空');
  }

  /**
   * 测试特定按键
   */
  testKey(key: string) {
    console.log(`🔧 测试按键: ${key}`);
    
    try {
      // 创建键盘事件
      const event = new KeyboardEvent('keydown', {
        key,
        bubbles: true,
        cancelable: true
      });
      
      // 分发事件
      document.dispatchEvent(event);
      console.log(`✅ 按键 ${key} 事件已分发`);
    } catch (error) {
      console.error(`❌ 按键 ${key} 测试失败:`, error);
    }
  }

  /**
   * 显示浏览器能力
   */
  showCapabilities() {
    console.log('🔧 浏览器键盘事件能力检测:');
    
    try {
      const testEvent = new KeyboardEvent('keydown', { key: 'Tab' });
      
      const capabilities = {
        supportsKeyboardEvents: typeof KeyboardEvent !== 'undefined',
        supportsKeyProperty: 'key' in testEvent,
        supportsWhichProperty: 'which' in testEvent,
        supportsKeyCode: 'keyCode' in testEvent,
        supportsPreventDefault: typeof testEvent.preventDefault === 'function',
        supportsStopPropagation: typeof testEvent.stopPropagation === 'function',
        userAgent: navigator.userAgent
      };
      
      console.table(capabilities);
      return capabilities;
    } catch (error) {
      console.error('❌ 能力检测失败:', error);
      return null;
    }
  }

  /**
   * 分析Tab键问题
   */
  analyzeTabIssue() {
    console.log('🔧 分析Tab键问题...');
    
    const tabLogs = this.logs.filter(log => 
      log.key === 'Tab' || log.keyCode === 9 || log.which === 9
    );
    
    if (tabLogs.length === 0) {
      console.log('❌ 没有检测到Tab键事件');
      return;
    }
    
    console.log(`✅ 检测到 ${tabLogs.length} 个Tab键事件:`);
    console.table(tabLogs);
    
    // 检查是否有事件被阻止
    const preventedCount = tabLogs.filter(log => log.prevented).length;
    console.log(`🔧 被阻止的事件: ${preventedCount}/${tabLogs.length}`);
    
    if (preventedCount === 0) {
      console.log('⚠️ Tab键事件没有被阻止，可能导致默认行为（打空格）');
    }
  }
}

// 创建全局调试器实例
const keyboardDebugger = new KeyboardDebugger();

// 导出调试器
export default keyboardDebugger;

// 条件启用调试（只在明确需要时）
if (process.env.NODE_ENV === 'development' && window.location.search.includes('debug=keyboard')) {
  // 延迟启用，确保DOM加载完成
  setTimeout(() => {
    keyboardDebugger.enable();
    console.log('🔧 键盘调试器已自动启用（调试模式）');
    console.log('🔧 使用 window.keyboardDebug 访问调试方法');
  }, 1000);
}

// 添加快捷调试方法
export const debugKeyboard = {
  enable: () => keyboardDebugger.enable(),
  disable: () => keyboardDebugger.disable(),
  getLogs: () => keyboardDebugger.getLogs(),
  clearLogs: () => keyboardDebugger.clearLogs(),
  testKey: (key: string) => keyboardDebugger.testKey(key),
  showCapabilities: () => keyboardDebugger.showCapabilities(),
  analyzeTabIssue: () => keyboardDebugger.analyzeTabIssue()
};
