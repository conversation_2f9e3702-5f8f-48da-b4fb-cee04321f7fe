import React, { useState, useRef, useEffect } from 'react';
import { getByteLength } from '../../utils/fieldValidation';

interface HardDriveSerialFieldProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  error?: string;
  required?: boolean;
}

/**
 * 硬盘序列号专用输入组件
 * 支持多个序列号用分号分隔，提供友好的显示和编辑体验
 */
const HardDriveSerialField: React.FC<HardDriveSerialFieldProps> = ({
  value,
  onChange,
  disabled = false,
  error,
  required = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 处理输入变化（移除内部验证，使用外部错误）
  const handleInputChange = (newValue: string) => {
    onChange(newValue);
  };

  // 解析序列号列表
  const parseSerialNumbers = (input: string): string[] => {
    if (!input) return [];
    return input.split(';').map(s => s.trim()).filter(s => s.length > 0);
  };

  // 格式化显示序列号 - 只在失去焦点时格式化，输入时保持原样
  const formatSerialNumbers = (input: string, shouldFormat: boolean = false): string => {
    if (!shouldFormat) return input; // 输入时不格式化
    const serials = parseSerialNumbers(input);
    if (serials.length <= 1) return input;
    return serials.join('; ');
  };

  // 获取序列号数量
  const getSerialCount = (): number => {
    return parseSerialNumbers(value).length;
  };

  // 切换展开/收起状态
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    // 延迟聚焦，确保DOM更新完成
    setTimeout(() => {
      if (!isExpanded) {
        textareaRef.current?.focus();
      } else {
        inputRef.current?.focus();
      }
    }, 100);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      toggleExpanded();
    }
  };

  const serialCount = getSerialCount();
  const hasMultipleSerials = serialCount > 1;

  return (
    <>
      <label className="block text-sm font-bold text-gray-700 mb-2">
        硬盘序列号 {required && <span className="text-red-500">*</span>}
        {serialCount > 1 && (
          <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded">
            {serialCount}个序列号
          </span>
        )}
      </label>

      <div className="relative">
        {isExpanded ? (
          // 多行文本框模式 - 用于编辑多个序列号
          <div className="relative">
            <textarea
              ref={textareaRef}
              value={value || ''}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder="多个序列号用 ; 号间隔&#10;例如：SN001; SN002; SN003"
              disabled={disabled}
              rows={4}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                error
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300'
              }`}
            />
            <button
              type="button"
              onClick={toggleExpanded}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 text-sm"
              title="收起 (Ctrl+Enter)"
            >
              ↑
            </button>
          </div>
        ) : (
          // 单行输入框模式 - 默认模式
          <div className="relative">
            <input
              ref={inputRef}
              type="text"
              value={isFocused ? (value || '') : formatSerialNumbers(value || '', true)}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => {
                setIsFocused(false);
                // 失去焦点时格式化内容
                const formatted = formatSerialNumbers(value || '', true);
                if (formatted !== value) {
                  handleInputChange(formatted);
                }
              }}
              placeholder="多个序列号用 ; 号间隔"
              disabled={disabled}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                error
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300'
              } ${hasMultipleSerials ? 'pr-16' : 'pr-8'}`}
            />
            
            {/* 展开按钮 - 当有多个序列号或内容较长时显示 */}
            {(hasMultipleSerials || (value && value.length > 30)) && (
              <button
                type="button"
                onClick={toggleExpanded}
                className="absolute top-1/2 right-8 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 text-sm"
                title="展开编辑 (Ctrl+Enter)"
              >
                ↓
              </button>
            )}

            {/* 字符计数器 */}
            <div className="absolute top-1/2 right-2 transform -translate-y-1/2 text-xs text-gray-400">
              {getByteLength(value || '')}/512
            </div>
          </div>
        )}

        {/* 提示信息 - 使用绝对定位，不占用空间，只在聚焦时显示 */}
        {isFocused && (
          <div className="absolute left-0 top-full mt-1 text-xs text-gray-500 pointer-events-none z-10 bg-white px-2 py-1 rounded shadow-sm border border-gray-200">
            多个硬盘序列号用 ; 号间隔，支持 Ctrl+Enter 切换编辑模式
          </div>
        )}
      </div>
    </>
  );
};

export default HardDriveSerialField;
